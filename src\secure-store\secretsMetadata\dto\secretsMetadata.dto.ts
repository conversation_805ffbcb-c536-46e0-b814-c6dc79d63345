import {
  IsBoolean,
  <PERSON><PERSON>num,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>al,
  IsString,
  IsObject,
  IsArray,
  ValidateIf,
  IsDateString,
} from 'class-validator';
import {
  RotationType,
  SecretType,
  Status,
} from '../types/secretsMetadata.enum';

class ErrorDto {
  @IsNumber()
  code: number;

  @IsString()
  message: string;
}

export class CreateSecretMetaDataDto {
  @IsEnum(SecretType)
  type: SecretType;

  @IsString()
  resourceId: string;

  @IsEnum(Status)
  status: Status;

  @IsBoolean()
  active: boolean;

  @IsString()
  vaultNamespace?: string;

  @ValidateIf(
    (o) =>
      o.type === SecretType.VAULT_POLICY ||
      o.type === SecretType.VAULT_PASSWORD_POLICY ||
      o.type === SecretType.ROTATABLE_SECRET,
  )
  @IsNumber()
  secretTTLInHours?: number;

  @IsOptional()
  @IsString()
  description?: string;

  @ValidateIf(
    (o) =>
      o.type === SecretType.VAULT_POLICY ||
      o.type === SecretType.VAULT_PASSWORD_POLICY ||
      o.type === SecretType.ROTATABLE_SECRET,
  )
  @IsString()
  policyId?: string;

  @ValidateIf(
    (o) =>
      o.type === SecretType.VAULT_PASSWORD_POLICY ||
      o.type === SecretType.VAULT_POLICY,
  )
  @IsOptional()
  @IsString()
  policyName?: string;

  @ValidateIf(
    (o) =>
      o.type === SecretType.ROTATABLE_SECRET ||
      o.type === SecretType.NORMAL_SECRET ||
      o.type === SecretType.VAULT_TOKEN,
  )
  @IsString()
  secretId?: string;

  // Fields for ROTATABLE-SECRET type

  @ValidateIf((o) => o.type === SecretType.ROTATABLE_SECRET)
  @IsOptional()
  @IsString()
  reason?: string;

  @ValidateIf((o) => o.type === SecretType.ROTATABLE_SECRET)
  @IsEnum(RotationType)
  rotationType?: RotationType;

  @ValidateIf((o) => o.type === SecretType.ROTATABLE_SECRET)
  @IsDateString()
  nextRotationDate?: string;

  @ValidateIf(
    (o) =>
      o.type === SecretType.ROTATABLE_SECRET ||
      o.type === SecretType.NORMAL_SECRET,
  )
  @IsString()
  vaultPath: string;

  @ValidateIf((o) => o.type === SecretType.ROTATABLE_SECRET)
  @IsOptional()
  @IsString()
  devicePasswordKey?: string;

  @ValidateIf((o) => o.type === SecretType.ROTATABLE_SECRET)
  @IsString()
  deviceUserNameSecretId?: string;

  @ValidateIf((o) => o.type === SecretType.ROTATABLE_SECRET)
  @IsOptional()
  @IsArray()
  error?: ErrorDto[];

  @ValidateIf((o) => o.type === SecretType.VAULT_TOKEN)
  @IsOptional()
  @IsNumber()
  tokenTTLInHours?: number;

  @ValidateIf((o) => o.type === SecretType.VAULT_TOKEN)
  @IsOptional()
  @IsDateString()
  expiryDate?: Date;

  @ValidateIf((o) => o.type === SecretType.VAULT_TOKEN)
  @IsOptional()
  @IsObject()
  resourcesDetails?: object;

  @ValidateIf((o) => o.type === SecretType.VAULT_TOKEN)
  @IsBoolean()
  notificationEnabled?: boolean;

  @ValidateIf((o) => o.type === SecretType.VAULT_TOKEN)
  @IsBoolean()
  tokenRenewByNebula?: boolean;
}
