import { BaseEntity } from '../../../naas/entities/serviceRequest.entity';

export class SecretsMetadataEntity extends BaseEntity {
  secretId: string;
  type: string;
  description?: string;
  parentResourceID?: string;
  resourceId?: string;
  status: string;
  tokenTTLInHours?: number;
  expiryDate?: Date;
  namespace?: string;
  resourcesDetails?: Record<string, any>;
  notificationEnabled?: boolean;
  tokenRenewByNebula?: boolean;
  active?: boolean;
  createdBy?: string;
  updatedBy?: string;
  rotationReason?: string;
  rotationType?: string;
  nextRotationDate?: Date;
  vaultPath?: string;
  vaultHistoryVersion?: number;
  secretVersion?: number;
  error?: Record<string, any>[];
  deactivatedAt?: Date;
  renewedAt?: Date;
}
