import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  NotFoundException,
  ServiceUnavailableException,
} from '@nestjs/common';
import { isValidObjectId } from 'mongoose';
import { IServiceRequestRepository } from '../abstracts/serviceRequest-repository.abstract';
import { RmqService } from '../rmq/rmq.service';
import {
  ApprovalStatus,
  EventPatterns,
  IpType,
  NebulaConfigMap,
  OrganizationName,
  RequestStatus,
  RequestType,
} from '../types';
import { RequestContext } from 'nestjs-request-context';
import { UpdateServiceRequestDto } from './dto/update.servicerequest.dto';
import { Workbook } from 'exceljs';
import * as tmp from 'tmp';
import { NetworkContainerResponse } from './networkContainer.response.dto';
import { ReserveIpResponse } from 'src/naas/dto/ipam/reserveIP.request.dto';
import { ReleaseIpDto } from 'src/naas/dto/ipam/releaseIP.request.dto';
import { IamService } from '../iam/iam.service';
import { PermissionKey } from '../iam/types';
import {
  CreateServiceRequestEntity,
  ServiceRequestEntity,
} from '../naas/entities/serviceRequest.entity';
import { ApprovalsService } from '../approvals/approvals.service';
import {
  AggregateMultiLevelApproval,
  EvaluationResult,
} from '../approvals/types';
import { ApproveOrRejectRequestDto } from './dto/approveOrRejectAsset.request.dto';
import { StatusNotificationService } from '../statusNotification/statusNotification.service';
import { PendingApprovalsListDto } from './dto/pendingApprovalsList.response.dto';
import { PendingApprovalResponseDto } from './dto/pendingApprovals.response.dto';
import { LoggerService } from '../loggers/logger.service';
import { CatalogStepsService } from '../catalog-steps/catalog-steps.service';
import { SubTypeApprovalsService } from '../approvals/sub-type-approval/sub-type-approvals.service';
import {
  PaginatedResponseDto,
  PaginationQueryDto,
} from 'src/utils/pagination/dto/pagination.dto';
import { FirewallRequestDetailsRepository } from '../security/repository/firewallRequestDetails-repository';
import { UpdateDnpDto } from './dto/update.dnp.status.dto';
import { ConfigService } from '@nestjs/config';
import {
  ENVIRONMENT_VARS,
  FirewallV1MigrationStatus,
  NEBULA_CONFIG_KEYS,
} from '../utils/constants';
import { TicketManagementService } from '../ticket-management/ticket-management.service';
import { NebulaConfigRepository } from '../dbaas/nebulaConfig.repository';
import { ApprovalDtoWithRequestType } from '../approvals/dto/approval.dto';
import { TufinDeviceRepository } from '../security/repository/tufin.path-analysis.repository';

@Injectable()
export class AssetsService {
  constructor(
    private readonly serviceRequestRepository: IServiceRequestRepository,
    private readonly rmqService: RmqService,
    private readonly iamService: IamService,
    private readonly ticketManagementService: TicketManagementService,
    private readonly nebulaConfigRepository: NebulaConfigRepository,
    private readonly approvalsService: ApprovalsService,
    private readonly statusNotificationService: StatusNotificationService,
    private readonly logger: LoggerService,
    private readonly catalogStepsService: CatalogStepsService,
    private readonly subTypeApprovalsService: SubTypeApprovalsService,
    private readonly firewallRequestDetailsRepository: FirewallRequestDetailsRepository,
    private readonly configService: ConfigService,
    private readonly pathAnalysisRespository: TufinDeviceRepository,
  ) {}

  /**
   * Returns the active catalogStepsId by request type. Returns null if no active catalog
   * steps can be found or if any error occurs while trying to find. This behavior ensures
   * request gets created even if finding catalog steps fails.
   */
  private async getActiveCatalogStepsIdByRequestType(
    requestType: RequestType,
  ): Promise<string> {
    try {
      const catalogSteps =
        await this.catalogStepsService.findActiveStepsByRequestType(
          requestType,
        );
      if (!catalogSteps) {
        this.logger.log(
          `No catalog steps are configured for request type ${requestType}`,
        );
        return null;
      }
      return catalogSteps.id;
    } catch (error) {
      this.logger.warn(
        `Error in fetching catalogStepsId for request type ${requestType}`,
      );
      this.logger.log(`catalogStepsId will be skipped for this request`);
      this.logger.error(error);
      return null;
    }
  }
  // Pass through method to serviceRequestRepository so that all calls to
  // serviceRequestRepository go via this AssetsService and no other module could
  // import and use serviceRequestRepository directly
  async create(
    item: CreateServiceRequestEntity,
  ): Promise<ServiceRequestEntity> {
    const catalogStepsId = await this.getActiveCatalogStepsIdByRequestType(
      item.requestType,
    );
    return await this.serviceRequestRepository.create({
      ...item,
      catalogStepsId,
    });
  }

  // Pass through method to serviceRequestRepository so that all calls to
  // serviceRequestRepository go via this AssetsService and no other module could
  // import and use serviceRequestRepository directly
  async findByServiceRequestObjectId(
    requestId: string,
  ): Promise<ServiceRequestEntity> {
    return await this.serviceRequestRepository.findByServiceRequestObjectId(
      requestId,
    );
  }

  // Pass through method to serviceRequestRepository so that all calls to
  // serviceRequestRepository go via this AssetsService and no other module could
  // import and use serviceRequestRepository directly
  async findByServiceRequestId(
    serviceRequestId: string,
  ): Promise<ServiceRequestEntity> {
    return await this.serviceRequestRepository.findByServiceRequestId(
      serviceRequestId,
    );
  }

  // Pass through method to serviceRequestRepository so that all calls to
  // serviceRequestRepository go via this AssetsService and no other module could
  // import and use serviceRequestRepository directly
  async update(
    id: string,
    item: Partial<CreateServiceRequestEntity> & { [key: string]: any },
  ): Promise<ServiceRequestEntity> {
    let serviceRequest;
    if (isValidObjectId(id)) {
      serviceRequest = await this.serviceRequestRepository.findById(id);
    } else {
      serviceRequest =
        await this.serviceRequestRepository.findByServiceRequestId(id);
    }
    if (!serviceRequest) {
      throw new NotFoundException();
    }
    return await this.serviceRequestRepository.update(serviceRequest.id, item);
  }

  // Pass through method to serviceRequestRepository so that all calls to
  // serviceRequestRepository go via this AssetsService and no other module could
  // import and use serviceRequestRepository directly
  async findByGenericRequestId(
    genericId: string,
  ): Promise<ServiceRequestEntity> {
    return await this.serviceRequestRepository.findByGenericRequestId(
      genericId,
    );
  }

  // Pass through method to serviceRequestRepository so that all calls to
  // serviceRequestRepository go via this AssetsService and no other module could
  // import and use serviceRequestRepository directly
  async findById(id: string): Promise<ServiceRequestEntity> {
    return await this.serviceRequestRepository.findById(id);
  }

  async updateAsset(id: string, data: UpdateServiceRequestDto) {
    this.logger.debug(`Update asset id: ${id} `, data);
    const serviceRequestData = await this.serviceRequestRepository.findById(id);
    if (!serviceRequestData) {
      throw new NotFoundException();
    }
    const { startedAt, completedAt } = this.getStartedAndCompletedDate(
      data.status,
      id,
    );
    let serviceRequest;
    if (
      data.downstreamError &&
      Object.keys(data.downstreamError).length > 0 &&
      (data.downstreamResponseData &&
        Object.keys(data.downstreamResponseData).length) > 0
    ) {
      this.logger.debug(
        `partially completed,downstreamresponse: ${data?.downstreamResponseData}, downstreamerror: ${data?.downstreamError}`,
      );
      serviceRequest = await this.serviceRequestRepository.update(
        serviceRequestData.id,
        {
          status: data.status,
          startedAt,
          completedAt,
          downstreamError: data.downstreamError,
          downstreamResponseData: data.downstreamResponseData,
        },
      );
    } else if (
      data.downstreamError &&
      Object.keys(data.downstreamError).length > 0
    ) {
      this.logger.debug(`Failed downstreamerror  ${id}`, data?.downstreamError);
      serviceRequest = await this.serviceRequestRepository.update(
        serviceRequestData.id,
        {
          status: data.status,
          startedAt,
          completedAt,
          downstreamError: data.downstreamError,
        },
      );
    } else {
      let updateDoc: any;
      if (
        data.downstreamResponseData &&
        Object.keys(data.downstreamResponseData).length > 0
      ) {
        this.logger.debug('downstreamresponse ', data?.downstreamResponseData);
        updateDoc = {
          status: data.status,
          startedAt,
          completedAt,
          downstreamResponseData: data.downstreamResponseData,
        };
      } else if (data.applicableSubTypes) {
        updateDoc = {
          status: data.status,
          applicableSubTypes: data.applicableSubTypes,
          startedAt,
          completedAt,
        };
      } else {
        updateDoc = {
          status: data.status,
          startedAt,
          completedAt,
        };
      }

      if (data.approvalStatus) {
        updateDoc.approvalStatus = data.approvalStatus;
      }

      if (data.startedAt) {
        updateDoc.startedAt = data.startedAt;
      }

      serviceRequest = await this.serviceRequestRepository.update(
        serviceRequestData.id,
        updateDoc,
      );
    }

    if (!serviceRequest) {
      throw new NotFoundException();
    }
    return {
      updatedStatus: serviceRequest.status,
      message: 'Asset updated successfully!',
    };
  }

  private async canApprove(serviceRequest: ServiceRequestEntity) {
    const allowedRequestTypes =
      await this.iamService.findAllowedRequestTypeByPermissionKey(
        PermissionKey.APPROVE,
      );
    if (
      allowedRequestTypes.length === 0 ||
      !allowedRequestTypes.includes(serviceRequest.requestType)
    ) {
      return false;
    }
    return true;
  }

  async canApproveMultiLevel(
    serviceRequest: ServiceRequestEntity,
    subType?: string,
  ): Promise<EvaluationResult> {
    const user = RequestContext.currentContext.req.user;
    return await this.approvalsService.evaluateRequestApprovals(
      serviceRequest,
      user,
      subType,
    );
  }

  private async isApprovalRequired(serviceRequest: ServiceRequestEntity) {
    const catalogL4Item =
      await this.iamService.findCatalogLevel4ItemByRequestType(
        serviceRequest.requestType,
      );

    this.logger.debug(`Approval Required: ${catalogL4Item?.approvalRequired}`);
    return catalogL4Item.approvalRequired !== false; // To ensure approval is required even if approvalRequired is null
  }

  getStartedAndCompletedDate(status: RequestStatus, id: string) {
    let completedAt, startedAt;
    if (
      [RequestStatus.APPROVED, RequestStatus.AUTO_APPROVED].includes(status)
    ) {
      startedAt = new Date();
      this.logger.debug(`Updated start date as ${startedAt} ${id}`);
    }
    if (
      [
        RequestStatus.SUCCESS,
        RequestStatus.PARTIAL,
        RequestStatus.FAILED,
      ].includes(status)
    ) {
      completedAt = new Date();
      this.logger.debug(`Updated completed date as ${completedAt}`);
    }
    return { completedAt, startedAt };
  }

  mapRequestStatustoApprovalStatus(status: string) {
    switch (status) {
      case RequestStatus.APPROVED:
        return ApprovalStatus.APPROVED;
      case RequestStatus.REJECTED:
        return ApprovalStatus.REJECTED;
      case RequestStatus.AUTO_APPROVED:
        return ApprovalStatus.AUTO_APPROVED;
      case RequestStatus.PARTIALLY_APPROVED:
        return ApprovalStatus.PARTIALLY_APPROVED;
      case RequestStatus.PARTIALLY_REJECTED:
        return ApprovalStatus.PARTIALLY_REJECTED;
      default:
        return ApprovalStatus.PENDING;
    }
  }

  private async updateStatusAndQueueForProcessing(
    serviceRequestId: string,
    status:
      | RequestStatus.APPROVED
      | RequestStatus.REJECTED
      | RequestStatus.AUTO_APPROVED
      | RequestStatus.PARTIALLY_APPROVED
      | RequestStatus.PARTIALLY_REJECTED,
  ) {
    const req = RequestContext.currentContext.req;
    const requestUser = req.user;
    const serviceRequest =
      await this.serviceRequestRepository.findById(serviceRequestId);
    if (!serviceRequest) {
      throw new NotFoundException();
    }
    const { startedAt } = this.getStartedAndCompletedDate(
      status,
      serviceRequestId,
    );
    const updateBody =
      RequestStatus.AUTO_APPROVED == status
        ? {
            status,
            startedAt,
            reviewedBy: requestUser.userId,
            approvalStatus: ApprovalStatus.AUTO_APPROVED,
          }
        : {
            status,
            startedAt,
            reviewedBy: requestUser.userId,
            approvalStatus: this.mapRequestStatustoApprovalStatus(status),
          };
    const updatedServiceRequest = await this.serviceRequestRepository.update(
      serviceRequest.serviceRequestId,
      updateBody,
    );

    if (
      [
        RequestStatus.APPROVED,
        RequestStatus.AUTO_APPROVED,
        RequestStatus.PARTIALLY_APPROVED,
      ].includes(updatedServiceRequest.status) ||
      (updatedServiceRequest.requestType ===
        RequestType.COMMON_FIREWALL_POLICY &&
        updatedServiceRequest.status === RequestStatus.REJECTED) ||
      (updatedServiceRequest.requestType === RequestType.FIREWALL_V2 &&
        updatedServiceRequest.status === RequestStatus.REJECTED) ||
      (updatedServiceRequest.requestType ===
        RequestType.DYNAMIC_ACCESS_POLICIES &&
        updatedServiceRequest.status === RequestStatus.REJECTED) ||
      (updatedServiceRequest.requestType ===
        RequestType.DYNAMIC_ACCESS_POLICIES &&
        updatedServiceRequest.status === RequestStatus.PARTIALLY_REJECTED)
    ) {
      this.logger.debug(`Pushing service request ${serviceRequestId} to queue`);
      let eventPattern: EventPatterns;
      if (
        updatedServiceRequest.requestType === RequestType.COMMON_FIREWALL_POLICY
      ) {
        if (updatedServiceRequest.status === RequestStatus.APPROVED) {
          eventPattern = EventPatterns.COMMON_FIREWALL_POLICY_APPROVED;
        } else if (updatedServiceRequest.status === RequestStatus.REJECTED) {
          eventPattern = EventPatterns.COMMON_FIREWALL_POLICY_REJECTED;
        }
      } else if (
        updatedServiceRequest.requestType ===
        RequestType.DYNAMIC_ACCESS_POLICIES
      ) {
        eventPattern = EventPatterns.DYNAMIC_ACCESS_POLICIES;
      } else if (
        updatedServiceRequest.requestType === RequestType.FIREWALL_V2
      ) {
        if (updatedServiceRequest.status === RequestStatus.APPROVED) {
          eventPattern = EventPatterns.FIREWALL_V2_APPROVED;
        } else if (updatedServiceRequest.status === RequestStatus.REJECTED) {
          eventPattern = EventPatterns.FIREWALL_V2_REJECTED;
        }
      }
      await this.rmqService.pushMessage(
        updatedServiceRequest.requestType,
        {
          id: updatedServiceRequest.id,
          payload: updatedServiceRequest.payload,
          serviceRequestId: updatedServiceRequest.serviceRequestId,
          requestorEmail: updatedServiceRequest.requesterEmail,
          requestorPID: updatedServiceRequest.createdBy,
        },
        eventPattern,
        updatedServiceRequest?.payload?.domain ||
          updatedServiceRequest?.payload[0]?.domain,
      );
    }
    return updatedServiceRequest;
  }

  async checkAndGetServiceRequest(serviceRequest, action) {
    const allSubRequests =
      await this.firewallRequestDetailsRepository.getFirewallTicketDetails(
        serviceRequest.serviceRequestId,
      );

    this.logger.log(`received ${allSubRequests.length} sub requests from db`);

    // excluding no change required and failed subrequest
    const subRequests = allSubRequests.filter((ticket) => {
      return (
        ![OrganizationName.NA_FAILED, OrganizationName.NA_NOIMPACT].includes(
          ticket.organizationName as OrganizationName,
        ) && ticket.status !== RequestStatus.FAILED
      );
    });

    this.logger.log(
      `after filtering we have ${subRequests.length} subrequests `,
    );

    //checking subrequests length because every will return true if array is empty
    if (!subRequests.length) {
      return;
    }

    if (
      subRequests.some((ele) => ele.approvalStatus === ApprovalStatus.PENDING)
    ) {
      this.logger.log(`some subrequests are pending approval`);
      return '';
    } else if (
      subRequests.every(
        (ele) =>
          ele.approvalStatus === ApprovalStatus.APPROVED ||
          ele.approvalStatus === ApprovalStatus.AUTO_APPROVED ||
          ele.approvalStatus === ApprovalStatus.NA, //added this condition because after closing manually approval status will be NA
      )
    ) {
      this.logger.log(`all subrequests are approved`);
      return RequestStatus.APPROVED;
    } else if (
      subRequests.every((ele) => ele.approvalStatus === ApprovalStatus.REJECTED)
    ) {
      this.logger.log(`all subrequests are rejected`);
      return RequestStatus.REJECTED;
    } else if (
      subRequests.every(
        (ele) =>
          ele.approvalStatus === ApprovalStatus.REJECTED ||
          ele.approvalStatus === ApprovalStatus.APPROVED ||
          ele.approvalStatus === ApprovalStatus.AUTO_APPROVED ||
          ele.approvalStatus === ApprovalStatus.NA, //added this condition because after closing manually approval status will be NA
      )
    ) {
      this.logger.log(`all subrequests are rejected or approved`);
      return RequestStatus.PARTIALLY_APPROVED;
    }
  }

  private async updateFirewallRequestStatus(serviceRequestId: string, action) {
    let status;
    const req = RequestContext.currentContext.req;
    const requestUser = req.user;
    const serviceRequest =
      await this.serviceRequestRepository.findById(serviceRequestId);
    if (!serviceRequest) {
      this.logger.log(`No service request found with ${serviceRequestId}`);
      throw new NotFoundException();
    }

    status = await this.checkAndGetServiceRequest(serviceRequest, action);

    if (!status) {
      return;
    }

    this.logger.log(
      `Updating service request ${serviceRequest.serviceRequestId} with ${status}`,
    );

    const { startedAt } = this.getStartedAndCompletedDate(
      status,
      serviceRequestId,
    );
    const updateBody =
      RequestStatus.AUTO_APPROVED == status
        ? {
            status,
            startedAt,
            reviewedBy: requestUser.userId,
            approvalStatus: ApprovalStatus.AUTO_APPROVED,
          }
        : {
            status,
            startedAt,
            reviewedBy: requestUser.userId,
            approvalStatus: this.mapRequestStatustoApprovalStatus(status),
          };

    const updatedServiceRequest = await this.serviceRequestRepository.update(
      serviceRequest.serviceRequestId,
      updateBody,
    );

    //sending notification only for overall service request approval and rejection
    //TODO implement indiviudal subrequest notification
    await this.approverNotification(serviceRequest.id, status);

    return updatedServiceRequest;
  }

  private async FirewallQueueForProcessingSubrequest(
    serviceRequest,
    updatedSubRequests,
    approvalAction,
    approveOrRejectRequestDto,
    crqToggle?: boolean,
  ) {
    const msg = {
      id: serviceRequest.id,
      payload: updatedSubRequests,
      serviceRequestId: serviceRequest.serviceRequestId,
      requestorEmail: serviceRequest.requesterEmail,
      requestorPID: serviceRequest.createdBy,
    };
    let eventPattern: EventPatterns;
    if (serviceRequest.requestType === RequestType.FIREWALL_V2) {
      if (approvalAction === RequestStatus.APPROVED) {
        eventPattern = EventPatterns.FIREWALL_V2_APPROVED_ORGANIZATION;
        msg['crqToggle'] = crqToggle;
        if (
          crqToggle === true &&
          approveOrRejectRequestDto.subType == OrganizationName.RED_APS
        ) {
          let mopId = null;
          try {
            const mopIdRes =
              await this.ticketManagementService.getMopIdFromCrqTicket({
                crqTicketId: approveOrRejectRequestDto.crqTicket,
              });
            mopId = mopIdRes.rows[0]?.MOPID;
          } catch (err) {
            this.logger.error(
              err,
              `Error while fetching mopId from crq ${approveOrRejectRequestDto.crqTicket}`,
            );
          }

          msg['crqTicketId'] = approveOrRejectRequestDto.crqTicket;
          msg['mopId'] = mopId;
          msg['crqStatus'] = mopId ? 'CREATED' : 'FAILED';
        }
      } else if (approvalAction === RequestStatus.REJECTED) {
        eventPattern = EventPatterns.FIREWALL_V2_REJECTED_ORGANIZATION;
      } else {
        this.logger.warn(`No event pattern for the ${approvalAction}`);
        return;
      }
    }

    this.logger.debug(
      `Pushing ${updatedSubRequests.map((ele) => ele.subRequestId)?.join(',')} sub requests to queue`,
    );

    await this.rmqService.pushMessage(
      serviceRequest.requestType,
      msg,
      eventPattern,
    );
  }

  async queueIfApprovalNotRequired(serviceRequest: ServiceRequestEntity) {
    const approvalRequired = await this.isApprovalRequired(serviceRequest);
    if (!approvalRequired) {
      await this.updateStatusAndQueueForProcessing(
        serviceRequest.id,
        RequestStatus.AUTO_APPROVED,
      );
    }

    return !approvalRequired; // approval not required => queued
  }

  async approveOrRejectAsset(
    id: string,
    status:
      | RequestStatus.APPROVED
      | RequestStatus.REJECTED
      | RequestStatus.PARTIALLY_APPROVED
      | RequestStatus.PARTIALLY_REJECTED,
  ) {
    const serviceRequest = await this.serviceRequestRepository.findById(id);
    if (!serviceRequest) {
      throw new NotFoundException();
    }

    if (!(await this.canApprove(serviceRequest))) {
      throw new ForbiddenException();
    }

    if (serviceRequest.status !== RequestStatus.PENDING_APPROVAL) {
      throw new BadRequestException(
        `Assets with status other than ${RequestStatus.PENDING_APPROVAL} cannot be approved/rejected`,
      );
    } else {
      const updatedServiceRequest =
        await this.updateStatusAndQueueForProcessing(id, status);

      return {
        updatedStatus: updatedServiceRequest.status,
        message: `The request is now ${updatedServiceRequest.status.toLowerCase()}`,
      };
    }
  }

  /**
   * Evaluates if the request can be approved based on some request details, not approval details
   * @param {ServiceRequestEntity} serviceRequest:ServiceRequestEntity
   * @returns {EvaluationResult}
   */
  private evaluateMissingDetailsForApproval(
    serviceRequest: ServiceRequestEntity,
  ): EvaluationResult {
    if (
      serviceRequest.requestType === RequestType.AWS_CREATE_SUBACCOUNT &&
      serviceRequest.payload
    ) {
      const payload = serviceRequest.payload;
      const { cidrsBase } = payload['vpcSettings'] ?? {};
      if (!cidrsBase) {
        return {
          canApprove: false,
          reason: 'CIDRS Base must be added before taking any approval action',
        };
      }
    }
  }

  async approveOrRejectMultiLevel(
    id: string,
    approveOrRejectRequestDto: ApproveOrRejectRequestDto,
    crqToggle?: boolean,
  ) {
    const { status, rejectedReason, hostNames } = approveOrRejectRequestDto;
    const serviceRequest = await this.serviceRequestRepository.findById(id);
    if (!serviceRequest) {
      throw new NotFoundException();
    }

    if (serviceRequest.status !== RequestStatus.PENDING_APPROVAL) {
      throw new BadRequestException(
        `Assets with status other than ${RequestStatus.PENDING_APPROVAL} cannot be approved/rejected`,
      );
    }

    const evaluationResult = await this.canApproveMultiLevel(
      serviceRequest,
      approveOrRejectRequestDto.subType,
    );
    if (evaluationResult.canApprove === false) {
      throw new ForbiddenException(evaluationResult.reason);
    }

    const missingDetailsEvaluationResult =
      this.evaluateMissingDetailsForApproval(serviceRequest);
    if (missingDetailsEvaluationResult?.canApprove === false) {
      throw new ForbiddenException(missingDetailsEvaluationResult.reason);
    }

    const user = RequestContext.currentContext.req.user;
    let approvalStatus: ApprovalStatus = null;
    if (
      evaluationResult.readyForProcessingAfterApproval &&
      status === RequestStatus.APPROVED
    ) {
      approvalStatus = ApprovalStatus.APPROVED;
    } else if (status === RequestStatus.REJECTED) {
      approvalStatus = ApprovalStatus.REJECTED;
    } else if (
      evaluationResult.readyForProcessingAfterApproval &&
      status === RequestStatus.PARTIALLY_APPROVED
    ) {
      approvalStatus = ApprovalStatus.PARTIALLY_APPROVED;
    } else if (status === RequestStatus.PARTIALLY_REJECTED) {
      approvalStatus = ApprovalStatus.PARTIALLY_REJECTED;
    }

    if (serviceRequest.requestType === RequestType.DYNAMIC_ACCESS_POLICIES) {
      let payload;
      if (
        status === RequestStatus.APPROVED ||
        status === RequestStatus.PARTIALLY_APPROVED
      ) {
        //if request is approved or partially approved, in payload ui send list of approved hostnames
        //@ts-ignore
        const devices = serviceRequest.payload?.devices;
        for (const device of devices) {
          const flag = hostNames.find(
            (ele: any) => ele === device.deviceHostname,
          );
          if (flag) {
            device.approved = true;
          } else {
            device.approved = false;
          }
        }
        payload = { ...serviceRequest.payload, devices: devices };
      } else if (status === RequestStatus.REJECTED) {
        //If request is rejected,in payload ui sends empty array for hostnames we need to make false for all devices
        //@ts-ignore
        const devices = serviceRequest.payload?.devices;
        for (const device of devices) {
          device.approved = false;
        }
        payload = { ...serviceRequest.payload, devices: devices };
      }

      await this.serviceRequestRepository.update(serviceRequest.id, {
        payload: payload,
      });
    }

    if (serviceRequest.requestType === RequestType.FIREWALL_V2) {
      await this.firewallApproveOrRejectMultiLevel(
        approveOrRejectRequestDto,
        evaluationResult,
        serviceRequest,
        user,
        status,
        approvalStatus,
        crqToggle,
      );
    } else {
      await this.serviceRequestRepository.addApprovals(
        id,
        {
          approvalGroup: evaluationResult.approvalGroup,
          approvalStatus: status,
          approvedOrRejectedAt: new Date(Date.now()),
          approvedOrRejectedBy: user.userId,
          level: evaluationResult.userLevel,
          rejectedReason,
        },
        approvalStatus,
      );

      await this.approverNotification(id, status);

      // Update the request status and queue based on status only when overall status changes
      if (
        approvalStatus === ApprovalStatus.APPROVED ||
        approvalStatus === ApprovalStatus.REJECTED ||
        approvalStatus === ApprovalStatus.PARTIALLY_APPROVED ||
        approvalStatus === ApprovalStatus.PARTIALLY_REJECTED
      ) {
        await this.updateStatusAndQueueForProcessing(id, status);
      }
    }

    return {
      updatedStatus: status,
      message: `The request is now ${status.toLowerCase()}`,
    };
  }

  async firewallApproveOrRejectMultiLevel(
    approveOrRejectRequestDto: ApproveOrRejectRequestDto,
    evaluationResult,
    serviceRequest: ServiceRequestEntity,
    user,
    status,
    approvalStatus: ApprovalStatus,
    crqToggle?: boolean,
  ) {
    await this.serviceRequestRepository.addApprovals(serviceRequest.id, {
      approvalGroup: evaluationResult.approvalGroup,
      approvalStatus: status,
      approvedOrRejectedAt: new Date(Date.now()),
      approvedOrRejectedBy: user.userId,
      level: evaluationResult.userLevel,
      rejectedReason: approveOrRejectRequestDto?.rejectedReason,
    });
    const subType = approveOrRejectRequestDto.subType;

    const subTypeApproval =
      await this.subTypeApprovalsService.findOneByRequestType(
        RequestType.FIREWALL_V2,
      );
    const userGroups: string[] = user['groups'];

    const derivedSubtype =
      subType ??
      this.subTypeApprovalsService.getUserSubTypes(
        userGroups,
        subTypeApproval,
      )?.[0];

    const updatedSubRequests =
      await this.firewallRequestDetailsRepository.updateApprovalStatus(
        serviceRequest.serviceRequestId,
        derivedSubtype,
        status === RequestStatus.APPROVED
          ? ApprovalStatus.APPROVED
          : ApprovalStatus.REJECTED,
      );
    const approvalGroup = subTypeApproval.subTypeToGroupMap.find(
      (item) => item.subType === derivedSubtype,
    );
    this.logger.log('Sending notification to user about approver comments');
    if (approveOrRejectRequestDto.approverComment) {
      await this.firewallV2RequestorNotification(
        serviceRequest.id,
        status,
        updatedSubRequests,
        approveOrRejectRequestDto,
        approvalGroup,
      );
    }

    await this.updateFirewallRequestStatus(serviceRequest.id, status);

    await this.FirewallQueueForProcessingSubrequest(
      serviceRequest,
      updatedSubRequests,
      status,
      approveOrRejectRequestDto,
      crqToggle,
    );
  }

  async approverNotification(id, status) {
    // Added try catch block just to make sure existing functionality wont break because of notification code
    try {
      //get updated service request because of update
      const updatedServiceRequest =
        await this.serviceRequestRepository.findById(id);

      //execute below code only if request id approved
      if (
        status !== RequestStatus.REJECTED &&
        updatedServiceRequest?.status !== RequestStatus.REJECTED
      ) {
        //get the approval details
        const approvalDetailsResponse =
          await this.approvalsService.findApprovalByRequestTypeAndDomainId(
            updatedServiceRequest?.requestType,
            updatedServiceRequest.payload['platformContext']?.domainId,
          );
        this.logger.log(
          `Fetching sub types to determine approver notification groups for service request ${id}`,
        );
        const subTypeApproval =
          await this.subTypeApprovalsService.findOneByRequestType(
            updatedServiceRequest?.requestType,
          );

        //get array of approval details with required count and current count
        const multiLevelApprovals =
          this.approvalsService.aggregateMultiLevelApproval(
            updatedServiceRequest,
            approvalDetailsResponse.approvalDetails,
            subTypeApproval,
          );
        multiLevelApprovals.sort((mla1, mla2) => mla1.level - mla2.level);

        //combine same level objects into one obj
        const approvalCountArray = [];
        for (const approval of multiLevelApprovals) {
          const index = approvalCountArray.findIndex(
            (a) => a.level === approval.level,
          );
          if (index !== -1) {
            approvalCountArray[index] = {
              level: approval.level,
              requiredApprovalsCount:
                approvalCountArray[index].requiredApprovalsCount +
                approval.requiredApprovalsCount,
              currentCount:
                approvalCountArray[index].currentCount + approval.currentCount,
            };
          } else {
            approvalCountArray.push({
              level: approval.level,
              requiredApprovalsCount: approval.requiredApprovalsCount,
              currentCount: approval.currentCount,
            });
          }
        }
        this.logger.debug(
          'Final approvals for multilevel grouped using level id: ',
          approvalCountArray,
        );

        for (let i = 0; i < approvalCountArray.length; i++) {
          if (
            approvalCountArray[i].currentCount <
            approvalCountArray[i].requiredApprovalsCount
          ) {
            //since at current level currentcount is less than requiredapprovalcount no need to check for next level
            break;
          } else if (
            approvalCountArray[i].requiredApprovalsCount ===
              approvalCountArray[i].currentCount &&
            i + 1 < approvalCountArray.length &&
            approvalCountArray[i + 1].currentCount === 0
          ) {
            const level = approvalCountArray[i + 1].level;
            const emailArray = [];
            //get filtered approvals array
            const filteredApprovals = multiLevelApprovals.filter(
              (approval) => approval.level === level,
            );
            //create email list
            for (const approval of filteredApprovals) {
              const group = await this.iamService.findGroupByGroupName(
                approval.groupId,
              );
              if (group?.emailDistribution?.length) {
                emailArray.push(...group.emailDistribution);
              }
            }
            this.logger.debug(`Email array  ${AssetsService.name}`, emailArray);
            if (emailArray.length) {
              await this.statusNotificationService.approverNotification(
                updatedServiceRequest,
                emailArray.join(','),
              );
            } else {
              this.logger.debug(
                `Emailarray is empty, email notification cannot be sent`,
              );
            }
            break;
          }
        }
      }
    } catch (err) {
      this.logger.error(
        err,
        `error while sending notification after approving the request ${id} ${status}`,
      );
    }
  }

  async approveWrapper(
    id: string,
    approveOrRejectAssetDto: ApproveOrRejectRequestDto,
  ) {
    const { status, hostNames } = approveOrRejectAssetDto;
    const serviceRequest = await this.serviceRequestRepository.findById(id);
    if (!serviceRequest) {
      throw new NotFoundException();
    }
    let crqToggle = undefined;
    if (serviceRequest.requestType === RequestType.FIREWALL_V2) {
      const crqConfig = await this.nebulaConfigRepository.getNebulaConfigByType(
        NebulaConfigMap.FIREWALL_V2,
      );
      crqToggle = crqConfig?.config[NEBULA_CONFIG_KEYS.CRQ_FEATURE_TOGGLE_UI];
      if (
        crqToggle === true &&
        approveOrRejectAssetDto.status === RequestStatus.APPROVED &&
        approveOrRejectAssetDto.subType === OrganizationName.RED_APS &&
        !approveOrRejectAssetDto.crqTicket
      ) {
        throw new BadRequestException('CRQ ticket is mandatory for approval');
      }
      await this.checkCRQToggleAndAddComment(
        crqToggle,
        approveOrRejectAssetDto,
        serviceRequest.serviceRequestId,
      );
    }
    if (
      serviceRequest.requestType !== RequestType.DYNAMIC_ACCESS_POLICIES &&
      hostNames?.length
    ) {
      throw new BadRequestException(
        `Hostnames can be passed only for ${RequestType.DYNAMIC_ACCESS_POLICIES}`,
      );
    }

    //invoke old or new approve method based on schema version
    if (serviceRequest.schemaVersion === 2) {
      return await this.approveOrRejectMultiLevel(
        id,
        approveOrRejectAssetDto,
        crqToggle,
      );
    }
    return await this.approveOrRejectAsset(id, status);
  }

  private buildMultiLevelApprovalForUI(
    multiLevelApprovals: AggregateMultiLevelApproval[],
    evaluationResult?: EvaluationResult,
  ) {
    let userLevel: number;
    let userGroup: string;
    let cannotApproveReason: string;
    if (evaluationResult?.canApprove === true) {
      userLevel = evaluationResult.userLevel;
      userGroup = evaluationResult.approvalGroup;
    } else {
      cannotApproveReason = evaluationResult?.reason;
    }
    const multiLevelApprovalsForUI = [];
    multiLevelApprovals.forEach((mla) => {
      const { currentCount, requiredApprovalsCount } = mla;
      multiLevelApprovalsForUI.push(
        ...mla.existingApprovals.map((approval) => ({
          ...approval,
          currentCount,
          requiredApprovalsCount,
        })),
      );
      if (mla.requiredApprovalsCount !== mla.currentCount) {
        const { groupId: approvalGroup, level, ..._ } = mla;
        multiLevelApprovalsForUI.push({
          level,
          approvalGroup,
          currentCount,
          requiredApprovalsCount,
          approvalStatus: ApprovalStatus.PENDING,
          ...(userLevel === level && userGroup === approvalGroup
            ? {
                canApprove: evaluationResult.canApprove,
                ...(cannotApproveReason ? { reason: cannotApproveReason } : {}),
              }
            : {
                canApprove: false,
                reason:
                  'You cannot approve/reject the request for this level and group',
              }),
        });
      }
    });
    return multiLevelApprovalsForUI;
  }

  private getSubTypeApprovalGroupForCurrentUser(
    allSubTypeApprovals,
    user,
  ): Partial<Record<RequestType, string[]>> {
    const requestTypeToUserSubTypesMap: Partial<Record<RequestType, string[]>> =
      {};
    this.logger.log(`Fetching sub type approvals applicable for current user`);
    for (const subTypeApproval of allSubTypeApprovals) {
      const userSubTypes = this.subTypeApprovalsService.getUserSubTypes(
        user['groups'],
        subTypeApproval,
      );
      requestTypeToUserSubTypesMap[subTypeApproval.requestType] = userSubTypes;
    }
    return requestTypeToUserSubTypesMap;
  }

  // Since approval required is at a catalog level and not at (catalog, domain) level
  // this method requires no change
  private getFilteredAllowedRequests(
    allowedRequestApprovals: ApprovalDtoWithRequestType[],
    catalogL4items,
  ) {
    const approvalRequiredCatalogItems = catalogL4items.filter(
      (catalogL4) => catalogL4.approvalRequired,
    );
    this.logger.log(
      `   Items that Need Approval: ${approvalRequiredCatalogItems?.length}`,
    );

    const filteredAllowedRequests = allowedRequestApprovals.filter(
      (approval) => {
        return approvalRequiredCatalogItems.find(
          (catalogL4) => catalogL4.shortName === approval.catalogL4ShortName,
        );
      },
    );
    return filteredAllowedRequests;
  }

  // Main method to list all appovals
  async getAllowedRequestsForApproval(
    customSortKey: string = '',
    sortingOrder: string[] = [],
  ) {
    // Find all catalog l4 with request type - NO CHANGE (After Check: This does not have request type)
    const catalogL4items = await this.iamService.findAllCatalogL4();

    // Find out the configured approvals where user belongs to the group,
    // This does not accounts for the domain
    // Make this function return approvals split by (domain, requestType)
    const allowedCatalogDomainCombos =
      await this.approvalsService.findAllowedApprovalsForCurrentUser();

    // Filter out the catalogs where approval is not required
    const filteredAllowedCatalogsDomainCombos = this.getFilteredAllowedRequests(
      allowedCatalogDomainCombos,
      catalogL4items,
    );
    // Create a list of approval allowed request types
    // This should be updated to (requestType, domain) combo
    const allowedRequestTypesAndDomainCombos =
      filteredAllowedCatalogsDomainCombos.map((catalogDomainCombo) => ({
        requestType: catalogDomainCombo.requestType as RequestType,
        domainId: catalogDomainCombo.domainId,
      }));
    this.logger.debug(
      `CatalogL4 Items: ${catalogL4items?.length}
      Allowed Request For User: ${allowedCatalogDomainCombos?.length}`,
    );
    const fieldsToSkip = [];
    if (!sortingOrder.length && !customSortKey) {
      sortingOrder = Object.values(RequestStatus);
      customSortKey = 'status';
    }
    this.logger.debug(
      `Sorting on Key: ${customSortKey}, Order of Sorting: ${sortingOrder}`,
    );
    this.logger.log(`Fetching sub types to for my approvals api`);
    const user = RequestContext.currentContext.req.user;

    const allSubTypeApprovals = await this.subTypeApprovalsService.findAll();
    const requestTypeToUserSubTypesMap =
      this.getSubTypeApprovalGroupForCurrentUser(allSubTypeApprovals, user);

    this.logger.log('Fetching all sub type approvals');

    this.logger.log(
      `Fetched sub type approvals applicable for current user`,
      JSON.stringify(requestTypeToUserSubTypesMap),
    );

    this.logger.log(
      `Fetched sub types successfully. Found ${allSubTypeApprovals?.length} `,
      JSON.stringify(allSubTypeApprovals),
    );
    const allowedServiceRequests =
      await this.serviceRequestRepository.findByRequestTypeAndDomainComboAndUserAllowedSubTypes(
        allowedRequestTypesAndDomainCombos,
        requestTypeToUserSubTypesMap,
        fieldsToSkip,
        customSortKey,
        sortingOrder,
      );

    const evaluatedRequests = allowedServiceRequests.map((serviceRequest) => {
      // Add evaluationResult and multilevel approval only for schema version 2 requests
      if (serviceRequest.schemaVersion !== 2) {
        let approvalStatus: ApprovalStatus;
        switch (serviceRequest.status) {
          case RequestStatus.PENDING_APPROVAL:
            approvalStatus = ApprovalStatus.PENDING;
            break;
          case RequestStatus.REJECTED:
            approvalStatus = ApprovalStatus.REJECTED;
            break;
          default:
            approvalStatus = ApprovalStatus.APPROVED;
        }
        return { ...serviceRequest, approvalStatus };
      }
      // Using == to comapre domain as comparison between any falsy values should return true here
      const requiredApprovals = allowedCatalogDomainCombos.find(
        (approval) =>
          approval.requestType === serviceRequest.requestType &&
          approval.domainId ==
            serviceRequest.payload['platformContext']?.domainId,
      );

      const subTypeApproval = allSubTypeApprovals.find(
        (subTypeApproval) =>
          subTypeApproval.requestType === serviceRequest.requestType,
      );
      const evaluationResult =
        this.approvalsService.evaluateMultiLevelApprovals(
          serviceRequest,
          requiredApprovals?.approvalDetails || [],
          user,
          subTypeApproval,
        );
      const multiLevelApprovals =
        this.approvalsService.aggregateMultiLevelApproval(
          serviceRequest,
          requiredApprovals?.approvalDetails || [],
          subTypeApproval,
        );
      const multiLevelApprovalsForUI = this.buildMultiLevelApprovalForUI(
        multiLevelApprovals,
        evaluationResult,
      );

      const currentUserApproval =
        serviceRequest.approvalDetails.find(
          (approval) => approval.approvedOrRejectedBy === user.userId,
        )?.approvalStatus ?? ApprovalStatus.PENDING;

      delete serviceRequest.payload;
      return {
        ...serviceRequest,
        evaluationResult,
        currentUserApproval,
        multiLevelApprovals: multiLevelApprovalsForUI,
      };
    });
    return evaluatedRequests;
  }

  async getAllowedRequestsForApprovalPaginated(
    paginationOptions: PaginationQueryDto,
  ): Promise<PaginatedResponseDto<ServiceRequestEntity>> {
    // Find all catalog l4 with request type - NO CHANGE (After Check: This does not have request type)
    const catalogL4items = await this.iamService.findAllCatalogL4();

    // Find out the configured approvals where user belongs to the group,
    // This does not accounts for the domain
    // Make this function return approvals split by (domain, requestType)
    const allowedCatalogDomainCombos =
      await this.approvalsService.findAllowedApprovalsForCurrentUser();

    // Filter out the catalogs where approval is not required
    const filteredAllowedCatalogsDomainCombos = this.getFilteredAllowedRequests(
      allowedCatalogDomainCombos,
      catalogL4items,
    );
    // Create a list of approval allowed request types
    // This should be updated to (requestType, domain) combo
    const allowedRequestTypesAndDomainCombos =
      filteredAllowedCatalogsDomainCombos.map((catalogDomainCombo) => ({
        requestType: catalogDomainCombo.requestType as RequestType,
        domainId: catalogDomainCombo.domainId,
      }));
    this.logger.debug(
      `CatalogL4 Items: ${catalogL4items?.length}
      Allowed Request For User: ${allowedCatalogDomainCombos?.length}`,
    );

    this.logger.log(`Fetching sub types to for my approvals api`);
    const user = RequestContext.currentContext.req.user;

    const allSubTypeApprovals = await this.subTypeApprovalsService.findAll();
    const requestTypeToUserSubTypesMap =
      this.getSubTypeApprovalGroupForCurrentUser(allSubTypeApprovals, user);

    this.logger.log('Fetching all sub type approvals');

    this.logger.log(
      `Fetched sub type approvals applicable for current user`,
      JSON.stringify(requestTypeToUserSubTypesMap),
    );

    this.logger.log(
      `Fetched sub types successfully. Found ${allSubTypeApprovals?.length} `,
      JSON.stringify(allSubTypeApprovals),
    );
    const allowedServiceRequests =
      await this.serviceRequestRepository.findByRequestTypeAndDomainComboAndUserAllowedSubTypesPaginated(
        allowedRequestTypesAndDomainCombos,
        requestTypeToUserSubTypesMap,
        paginationOptions,
      );

    const evaluatedRequests = allowedServiceRequests.items.map(
      (serviceRequest) => {
        // Add evaluationResult and multilevel approval only for schema version 2 requests
        if (serviceRequest.schemaVersion !== 2) {
          let approvalStatus: ApprovalStatus;
          switch (serviceRequest.status) {
            case RequestStatus.PENDING_APPROVAL:
              approvalStatus = ApprovalStatus.PENDING;
              break;
            case RequestStatus.REJECTED:
              approvalStatus = ApprovalStatus.REJECTED;
              break;
            default:
              approvalStatus = ApprovalStatus.APPROVED;
          }
          return { ...serviceRequest, approvalStatus };
        }
        // Using == to comapre domain as comparison between any falsy values should return true here
        const requiredApprovals = allowedCatalogDomainCombos.find(
          (approval) =>
            approval.requestType === serviceRequest.requestType &&
            approval.domainId ==
              serviceRequest.payload['platformContext']?.domainId,
        );

        const subTypeApproval = allSubTypeApprovals.find(
          (subTypeApproval) =>
            subTypeApproval.requestType === serviceRequest.requestType,
        );
        const evaluationResult =
          this.approvalsService.evaluateMultiLevelApprovals(
            serviceRequest,
            requiredApprovals.approvalDetails,
            user,
            subTypeApproval,
          );
        const multiLevelApprovals =
          this.approvalsService.aggregateMultiLevelApproval(
            serviceRequest,
            requiredApprovals.approvalDetails,
            subTypeApproval,
          );
        const multiLevelApprovalsForUI = this.buildMultiLevelApprovalForUI(
          multiLevelApprovals,
          evaluationResult,
        );

        const currentUserApproval =
          serviceRequest.approvalDetails.find(
            (approval) => approval.approvedOrRejectedBy === user.userId,
          )?.approvalStatus ?? ApprovalStatus.PENDING;

        delete serviceRequest.payload;
        return {
          ...serviceRequest,
          evaluationResult,
          currentUserApproval,
          multiLevelApprovals: multiLevelApprovalsForUI,
        };
      },
    );

    const pageInfo = allowedServiceRequests.pageInfo;
    return { items: evaluatedRequests, pageInfo };
  }

  async getUserRequestsMultiLevelPaginated(
    paginationOptions: PaginationQueryDto,
  ): Promise<PaginatedResponseDto<ServiceRequestEntity>> {
    // Create api in approvals service to return approvals for given catalog items
    // Add multi level approval details for new documents
    const req = RequestContext.currentContext.req;
    const requestUser = req.user;
    const userEnvsWithPermissions =
      await this.iamService.findUserEnvironmentsWithPermissions();
    const envIds = userEnvsWithPermissions.map((envPerm) => envPerm.envId);

    const serviceRequests =
      await this.serviceRequestRepository.findByEnvIdsAndCreatorPaginated(
        requestUser.userId,
        envIds,
        paginationOptions,
      );

    const allMultiLevelApprovals =
      await this.approvalsService.findAllMultiLevelApprovals();

    this.logger.log(`Fetching sub types to for my requests api`);

    const allSubTypeApprovals = await this.subTypeApprovalsService.findAll();

    this.logger.log(
      `Fetched sub types successfully. Found ${allSubTypeApprovals?.length} `,
      JSON.stringify(allSubTypeApprovals),
    );
    const enrichedServiceRequests = serviceRequests.items.map(
      (serviceRequest) => {
        if (serviceRequest.schemaVersion !== 2) {
          return serviceRequest;
        }
        const requiredApprovals = allMultiLevelApprovals.find(
          (approval) => approval.requestType === serviceRequest.requestType,
        );
        const subTypeApproval = allSubTypeApprovals.find(
          (subTypeApproval) =>
            subTypeApproval.requestType === serviceRequest.requestType,
        );
        if (!requiredApprovals) {
          return serviceRequest;
        }
        const multiLevelApprovals =
          this.approvalsService.aggregateMultiLevelApproval(
            serviceRequest,
            requiredApprovals.approvalDetails,
            subTypeApproval,
          );
        const multiLevelApprovalsForUI =
          this.buildMultiLevelApprovalForUI(multiLevelApprovals);

        return {
          ...serviceRequest,
          multiLevelApprovals: multiLevelApprovalsForUI,
        };
      },
    );
    const pageInfo = serviceRequests.pageInfo;
    return { items: enrichedServiceRequests, pageInfo };
  }

  async getRequestByIdV2(assetId: string) {
    let asset;
    if (isValidObjectId(assetId)) {
      asset = await this.serviceRequestRepository.findById(assetId);
    } else {
      asset =
        await this.serviceRequestRepository.findByServiceRequestId(assetId);
    }
    if (!asset) {
      throw new NotFoundException();
    }
    if (
      asset.requestType == RequestType.FIREWALL &&
      asset.payload.jiraIssueLink != undefined
    ) {
      if (!Array.isArray(asset.payload.jiraIssueLink)) {
        this.logger.debug(`Updating JiraIssueLink to Array ${assetId}`);
        asset.payload.jiraIssueLink = [`${asset.payload.jiraIssueLink}`];
      }
    }
    return asset;
  }

  async getByServiceRequestId(serviceRequestId: string) {
    this.logger.log(
      `Calling service request repository to fetch details for serviceRequestId: ${serviceRequestId}`,
    );
    let asset: any =
      await this.serviceRequestRepository.findByServiceRequestId(
        serviceRequestId,
      );
    if (!asset) {
      if (isValidObjectId(serviceRequestId)) {
        /**
         * serviceRequestId can hold the _id value for the older request where serviceRequestId is not available
         */
        this.logger.log(
          `Calling service request repository to fetch details for _id: ${serviceRequestId}`,
        );
        asset = await this.serviceRequestRepository.findById(serviceRequestId);
      }

      if (!asset) {
        this.logger.error(
          `No record found for serviceRequestId/_id: ${serviceRequestId}`,
        );
        throw new NotFoundException(
          `Request details not found for: '${serviceRequestId}'`,
        );
      }
    }
    if (
      asset.requestType == RequestType.FIREWALL &&
      asset.payload.jiraIssueLink != undefined
    ) {
      if (!Array.isArray(asset.payload.jiraIssueLink)) {
        this.logger.log(`Updating JiraIssueLink to Array ${serviceRequestId}`);
        asset.payload.jiraIssueLink = [`${asset.payload.jiraIssueLink}`];
      }
    }
    if (asset.requestType == RequestType.FIREWALL_V2) {
      const tufinDevices =
        await this.pathAnalysisRespository.getAllTufinDevices(serviceRequestId);

      const errorMap = tufinDevices.reduce((acc, device) => {
        if (device.ruleId && device.error?.length) {
          acc[device.ruleId] = device.error.at(-1);
        }
        return acc;
      }, {});

      for (const rule of asset.payload.firewallRules.ipv4.firewallRules) {
        if (errorMap[rule.ruleId]) {
          rule.error = errorMap[rule.ruleId].errorMsg;
        }
      }
    }
    return asset;
  }

  async updateByServiceRequestId(data: any) {
    this.logger.log(
      `Calling service request repository to fetch details for serviceRequestId: ${data.serviceRequestId}`,
    );
    const asset: any =
      await this.serviceRequestRepository.findByServiceRequestId(
        data.serviceRequestId,
      );

    const updateItems = {
      ...data,
      editedAt: new Date(),
    };
    this.logger.log(`Updated Payload for serviceRequestId: ${asset}`);
    const updatedResponse: any = await this.serviceRequestRepository.update(
      asset.id,
      updateItems,
    );

    return updatedResponse;
  }

  async getReleaseIPDetail() {
    const requestContext = RequestContext.currentContext.req;
    const requestUser = requestContext.user;
    this.logger.debug('logged in user ', requestUser);

    // get the ip address and host name which user reserved
    const reserveIpDetail =
      await this.serviceRequestRepository.getRequestByType(
        requestUser.userId,
        RequestType.RESERVE_IP,
      );

    const releaseIpDetail =
      await this.serviceRequestRepository.getRequestByType(
        requestUser.userId,
        RequestType.RELEASE_IP,
      );
    const releasedIpPayload = releaseIpDetail.map((item) => {
      return item.payload;
    });
    const releasedHost = releasedIpPayload.map((item: ReleaseIpDto) => {
      return item.name;
    });
    const reserveRequestIdFromReleaseIP = releasedIpPayload.map(
      (item: ReleaseIpDto) => {
        return item.reserveRequestId;
      },
    );
    const reserveIdFromReleaseIP = reserveRequestIdFromReleaseIP.flat();
    const downstreamResponseData = reserveIpDetail.map((item) => {
      return {
        reserveRequestId: item.id,
        downstreamResponseData: item.downstreamResponseData,
      };
    });

    const reserveIpResponse = [];
    for (const data of downstreamResponseData) {
      const responseData = <ReserveIpResponse[]>data.downstreamResponseData;

      const responseIpAddr = [];
      responseData.forEach(function (item) {
        //Exclude hostname which is already released by the user
        if (
          !releasedHost.includes(item.name) ||
          !reserveIdFromReleaseIP.includes(data.reserveRequestId)
        ) {
          if (item.ipv6addrs) {
            item.ipv6addrs.map((obj) => {
              responseIpAddr.push({
                hostName: obj.host,
                ipAddress: obj.ipv6addr,
              });
            });
          } else if (item.ipv4addrs) {
            item.ipv4addrs.map((obj) => {
              responseIpAddr.push({
                hostName: obj.host,
                ipAddress: obj.ipv4addr,
              });
            });
          }
          reserveIpResponse.push({
            reserveRequestId: data.reserveRequestId,
            responseIpAddr: responseIpAddr,
            name: item.name,
          });
        }
      });
    }
    this.logger.debug('reserveippayload: ', reserveIpResponse);
    const hostNames: string[] = [];
    hostNames.push(...reserveIpResponse.map((obj) => obj.name));
    const uniqueHostName = hostNames.reduce(
      (unique, item) => (unique.includes(item) ? unique : [...unique, item]),
      [],
    );
    this.logger.debug(`unique hostname ${uniqueHostName}`);
    const releaseipDetail = [];
    for (const hostName of uniqueHostName) {
      const ipAddress = [];
      const requestId = [];
      reserveIpResponse.map((item) => {
        item.responseIpAddr.map((obj) => {
          if (obj.hostName == hostName) {
            requestId.push(item.reserveRequestId);
            ipAddress.push(obj.ipAddress);
          }
        });
      });
      const uniqueRequestId = requestId.reduce(
        (unique, item) => (unique.includes(item) ? unique : [...unique, item]),
        [],
      );
      const uniqueIpAddress = ipAddress.reduce(
        (unique, item) => (unique.includes(item) ? unique : [...unique, item]),
        [],
      );

      releaseipDetail.push({
        name: hostName,
        ipAddress: uniqueIpAddress,
        reserveRequestId: uniqueRequestId,
      });
    }

    return releaseipDetail;
  }

  async getTemplateFile(id?: string): Promise<any> {
    let network: string = '';
    const file = `${__dirname}/../../template/reserveIPTemplate.xlsx`;
    if (!!id) {
      this.logger.debug(`download template for id: ${id}`);
      const request = await this.serviceRequestRepository.findById(id);
      if (request.status == RequestStatus.SUCCESS) {
        if (!!request.downstreamResponseData) {
          const downstreamResponseData: NetworkContainerResponse = <
            NetworkContainerResponse
          >request.downstreamResponseData;
          network = downstreamResponseData.network;
        }
      }
    }
    if (network != '') {
      return this.updateTemplateWithNetwork(network, id, file);
    } else {
      return file;
    }
  }
  private async updateTemplateWithNetwork(
    network: string,
    id: string,
    file: string,
  ): Promise<any> {
    const workbook = new Workbook();
    await workbook.xlsx.readFile(file);
    const sheet = workbook.getWorksheet('Reserve IP List');
    sheet.getRow(2).getCell(4).value = network;
    const File = await new Promise((resolve) => {
      tmp.file(
        {
          discardDescriptor: true,
          prefix: !!id ? id : 'reserveIPTemplate',
          postfix: '.xlsx',
          mode: parseInt('0600', 8),
        },
        async (err, file) => {
          if (err) {
            this.logger.error(err, `Error on tmp file , ${id}`, file);
          }

          //write tmp file
          // _ is unused intentionally
          /* eslint-disable @typescript-eslint/no-unused-vars */
          workbook.xlsx
            .writeFile(file)
            .then((_) => {
              resolve(file);
            })
            .catch((err) => {
              this.logger.error(err, `Error while creating excel  ${id}`, file);
              throw err;
            });
          /* eslint-enable @typescript-eslint/no-unused-vars */
        },
      );
    });
    return File;
  }

  async appendDownstreamResponseData(
    serviceRequestId: string,
    downstreamResponseDataDto: Record<string, any>,
  ): Promise<ServiceRequestEntity> {
    this.logger.log(
      `Calling appendDownstreamResponseData service request repository to append DownstreamResponseData for serviceRequestId: ${serviceRequestId}`,
    );

    return await this.serviceRequestRepository.appendDownstreamResponseData(
      serviceRequestId,
      downstreamResponseDataDto,
    );
  }

  async resetServiceRequestForNewExecution(
    serviceRequestId: string,
    retryPayload: object,
  ): Promise<ServiceRequestEntity> {
    const updatedServiceRequest =
      await this.serviceRequestRepository.resetServiceRequestForNewExecution(
        serviceRequestId,
        retryPayload,
      );
    if (!updatedServiceRequest) {
      throw new NotFoundException(
        `Service request: "${serviceRequestId}" not found`,
      );
    }
    return updatedServiceRequest;
  }
  async updateDnpStatus(id, updateDnpDto) {
    const dnpUpdateDoc = {
      payload: updateDnpDto,
    };
    this.logger.log(`Updating dnp status for service request id ${id}`);
    await this.update(id, dnpUpdateDoc);
  }

  async getFirewallV1RequestByTypeAndStatus(paginationQueryDto) {
    const requestStatus = this.configService
      .get(ENVIRONMENT_VARS.FIREWALL_V1_REQUEST_STATUS)
      .split(',');

    const asset =
      await this.serviceRequestRepository.findByRequestTypesPaginated(
        [RequestType.FIREWALL],
        paginationQueryDto,
        requestStatus,
      );
    this.logger.log('service requests result', asset);
    if (!asset) {
      throw new NotFoundException();
    }
    if (asset.items) {
      for (const serviceRequest of asset.items) {
        const serviceRequestId = serviceRequest.serviceRequestId;
        if (
          serviceRequest.status != RequestStatus.MIGRATED &&
          serviceRequest.status != RequestStatus.PARTIALLY_MIGRATED &&
          serviceRequest.status != RequestStatus.MIGRATION_FAILED
        ) {
          const firewallV1UpdateDoc = {
            systemUpdate: {
              migrationStatus: FirewallV1MigrationStatus.NOT_STARTED,
            },
          };
          this.logger.log('Updating firewall v1 migration status in db');
          await this.update(serviceRequestId, firewallV1UpdateDoc);
        }
      }
      return asset;
    }
  }
  async get(id: string): Promise<ServiceRequestEntity> {
    const serviceRequest = this.serviceRequestRepository.getByIdOrRequestId(id);
    return serviceRequest;
  }

  async firewallV2RequestorNotification(
    id,
    status,
    subRequest,
    approveOrRejectRequestDto,
    approvalGroup,
  ) {
    try {
      const updatedServiceRequest =
        await this.serviceRequestRepository.findById(id);
      await this.statusNotificationService.firewallV2RequestorNotification(
        updatedServiceRequest,
        subRequest,
        updatedServiceRequest.requesterEmail,
        approveOrRejectRequestDto,
        approvalGroup,
      );
    } catch (err) {
      this.logger.error(
        err,
        `error while sending notification after approving the request ${id} ${status}`,
      );
    }
  }

  async updateServiceRequestStatus(data) {
    try {
      this.logger.log('Updating status in serviceRequests');
      return await this.serviceRequestRepository.updateServiceRequestStatus(
        data,
      );
    } catch (error) {
      this.logger.error(
        error,
        `Updating status in service request failed: ${error.stack} ${error.message}`,
      );
      throw error;
    }
  }

  async checkCRQToggleAndAddComment(
    crqToggle: boolean,
    approveOrRejectAssetDto: ApproveOrRejectRequestDto,
    serviceRequestId: string,
  ) {
    if (
      crqToggle === true &&
      approveOrRejectAssetDto.status === RequestStatus.APPROVED &&
      approveOrRejectAssetDto.subType === OrganizationName.RED_APS &&
      approveOrRejectAssetDto.crqTicket
    ) {
      const getAllSubrequests =
        await this.firewallRequestDetailsRepository.getFirewallTicketDetails(
          serviceRequestId,
        );
      const getRedApsSubRequest = getAllSubrequests.filter(
        (subrequest) =>
          subrequest.organizationName === OrganizationName.RED_APS,
      );
      const subRequestWithCRQ =
        await this.firewallRequestDetailsRepository.findByFilter({
          'ticketDetails.remedy.ticketId': approveOrRejectAssetDto.crqTicket,
        });
      for (const subrequest of getRedApsSubRequest) {
        const addComments = {
          mopId: subRequestWithCRQ[0]?.ticketDetails?.remedy?.mopId,
          comments: [
            `${subrequest.ticketDetails?.jira?.ticketId},${serviceRequestId}`,
          ],
        };
        this.logger.log(
          `Attempting to add comments for RedAPS subrequest: ${JSON.stringify(addComments)}`,
        );
        try {
          await this.ticketManagementService.addCommentInCrqTicket(addComments);
          this.logger.log(
            `Successfully added comments for MOP ID: ${addComments.mopId}`,
          );
        } catch (error) {
          this.logger.error(
            `Failed to add comments for MOP ID: ${addComments.mopId}. Error: ${error.message}`,
          );
        }
      }
    }
  }
}
