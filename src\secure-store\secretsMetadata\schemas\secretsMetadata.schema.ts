import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, SchemaTypes } from 'mongoose';
import {
  RotationType,
  SecretType,
  Status,
} from '../types/secretsMetadata.enum';
import * as mongooseAggregatePaginate from 'mongoose-aggregate-paginate-v2';
import { ActionStatus } from '../../../action/enums/ActionStatus.enum';

export type SecretsMetaDataDocument = SecretsMetaData & Document;

@Schema()
class ErrorDto {
  @Prop({ required: true })
  code: number;

  @Prop({ required: true })
  message: string;
}

@Schema({ timestamps: true })
export class SecretsMetaData {
  @Prop({ type: SchemaTypes.ObjectId, auto: true })
  _id: string;

  @Prop({ required: true, unique: true })
  secretId: string;

  @Prop({ required: true, enum: SecretType })
  type: SecretType;

  @Prop()
  description?: string;

  @Prop()
  parentResourceID?: string;

  @Prop()
  tokenTTLInHours?: number;

  @Prop()
  expiryDate?: Date;

  @Prop()
  completedAt?: Date;

  @Prop()
  namespace?: string;

  @Prop({ type: Object })
  resourcesDetails?: object;

  @Prop()
  notificationEnabled?: boolean;

  @Prop()
  notifyBeforeTokenExpiry?: boolean;

  @Prop()
  tokenRenewByNebula?: boolean;

  @Prop()
  resourceId?: string;

  @Prop()
  secretTTLInHours?: number;

  @Prop()
  reason?: string;

  @Prop({ enum: RotationType })
  rotationType?: RotationType;

  @Prop()
  nextRotationDate?: Date;

  @Prop()
  vaultNamespace?: string;

  @Prop()
  vaultPath?: string;

  @Prop()
  vaultKey?: string;

  @Prop()
  vaultHistoryVersion?: number;

  @Prop()
  secretVersion?: number;

  @Prop({ type: [ErrorDto], _id: false })
  error?: ErrorDto[];

  @Prop()
  deviceUserNameSecretId?: string;

  @Prop()
  devicePasswordKey?: string;

  @Prop()
  policyName?: string;

  @Prop()
  policyId?: string;

  @Prop({ required: true, enum: Status })
  status: Status;

  @Prop({ required: true })
  active: boolean;

  @Prop()
  version?: number;

  @Prop()
  isDeleted?: boolean;

  @Prop({ type: String })
  createdBy: string;

  @Prop({ type: String })
  updatedBy?: string;

  @Prop()
  updatedAt?: Date;

  @Prop()
  deactivatedAt?: Date;

  @Prop()
  renewedAt?: Date;

  @Prop({
    type: String,
    required: true,
  })
  actionId: string;

  @Prop({
    type: String,
    enum: ActionStatus,
    default: ActionStatus.NOT_STARTED,
  })
  lastDeviceSyncStatus: ActionStatus;
}

export const SecretsMetaDataSchema = SchemaFactory.createForClass(
  SecretsMetaData,
).plugin(mongooseAggregatePaginate);
