import { Modu<PERSON> } from '@nestjs/common';
import { SecretsMetadataModule } from '../secure-store/secretsMetadata/secretsMetadata.module';
import { SecureStoreJobsService } from './secure-store-jobs.service';
import { SecureStoreModule } from '../secure-store/secure-store.module';
import { ScheduleModule } from '@nestjs/schedule';
import { SecretsRenewalJobsService } from './secrets-renewal-jobs.service';

@Module({
  imports: [ScheduleModule.forRoot(), SecretsMetadataModule, SecureStoreModule],
  providers: [SecureStoreJobsService, SecretsRenewalJobsService],
  exports: [SecretsRenewalJobsService],
})
export class ScheduledJobsModule {}
