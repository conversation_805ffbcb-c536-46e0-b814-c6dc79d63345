import { BaseEntity } from '../../../naas/entities/serviceRequest.entity';

export class SecretsPoliciesEntity extends BaseEntity {
  type: string;
  policyName: string;
  description?: string;
  policyRules: PolicyRulesEntity;
  resourceId?: string;
  status: string;
  createdBy?: string;
  updatedBy?: string;
}

export class PolicyRulesEntity {
  passwordDescription: string;
  acceptedSpecialCharacters: string;
  totalCharactersLength: number;
  specialCharactersCount: number;
  lowerCaseLettersCount: number;
  upperCaseLettersCount: number;
  numericalCharactersCount: number;
}
