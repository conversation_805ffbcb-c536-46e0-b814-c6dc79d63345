import { Module } from '@nestjs/common';
import { StatusController } from './status.controller';
import { StatusService } from './status.service';
import { IntegrationNotificationModule } from '../naas/integration.notification.module';
import { ApprovalsModule } from '../approvals/approvals.module';
import { StatusNotificationModule } from '../statusNotification/statusNotification.module';
import { LoggerModule } from '../loggers/logger.module';
import { WebhookHandlerFactory } from './webhook-handler.factory';
import { DapUpdateWebhookHandler } from './dap-update-webhook.handler';
import { IServiceRequestRepository } from '../abstracts/serviceRequest-repository.abstract';
import { ServiceRequestRepository } from '../naas/serviceRequest.repository';
import { CatalogStepsModule } from '../catalog-steps/catalog-steps.module';
import { GitUpdateWebhookHandler } from './git-update-webhook.handler';
import { WebhookController } from './webhook.controller';
import { SecurityModule } from '../security/security.module';
import { DapDeploymentUpdateWebhookHandler } from './dap-deployment-update-webhook.handler';
import { ActivityLoggerWrapperModule } from '../activity-logs-wrapper/activity-logger-wrapper.module';
import { RiskAnalysisDTOMapper } from './risk-analysis-dto-mapper.service';
import { DesignerResultDtoMapper } from './designer-result-dto-mapper.service';
import { WorkflowService } from '../security/firewallv2/firewallv2.tufinworkflow.service';
import { FirewallRequestDetailsRepository } from '../security/repository/firewallRequestDetails-repository';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';
import { TokenService } from '../auth/token.service';
import { REQUEST } from '@nestjs/core';
import { ENVIRONMENT_VARS } from '../utils/constants';
import { createAxiosInstance } from '../utils/helpers';
import { AuthModule } from '../auth/auth.module';
import { RmqModule } from '../rmq/rmq.module';
import { TufinObjectRenameService } from '../security/firewallv2/firewallv2.tufin.object-rename.service';
import { DnpDapUpdateWebhookHandler } from './dnp-dap-update-webhook.handler';
import { AWXActivityWebhookHandler } from './awx-activity-webhook.handler';

@Module({
  imports: [
    LoggerModule,
    IntegrationNotificationModule,
    ApprovalsModule,
    StatusNotificationModule,
    CatalogStepsModule,
    SecurityModule,
    ActivityLoggerWrapperModule,
    AuthModule,
    RmqModule,
  ],
  controllers: [StatusController, WebhookController],
  providers: [
    StatusService,
    WebhookHandlerFactory,
    DapUpdateWebhookHandler,
    GitUpdateWebhookHandler,
    DnpDapUpdateWebhookHandler,
    AWXActivityWebhookHandler,
    DapDeploymentUpdateWebhookHandler,
    { provide: IServiceRequestRepository, useClass: ServiceRequestRepository },
    RiskAnalysisDTOMapper,
    DesignerResultDtoMapper,
    WorkflowService,
    FirewallRequestDetailsRepository,
    TufinObjectRenameService,
    {
      provide: 'TUFIN_SERVICE_API',
      inject: [ConfigService, TokenService, REQUEST],
      useFactory: async (
        configService: ConfigService,
        tokenService: TokenService,
        req: Request,
      ): Promise<AxiosInstance> => {
        const baseUrlKey = configService.get(
          ENVIRONMENT_VARS.TUFIN_SERVICE_BASE_URL,
        );
        const nebulaHeader = req.headers['x-nebula-authorization']
          ? (req.headers['x-nebula-authorization'] as string)
          : (req.headers['x-client-jwt'] as string);
        return await createAxiosInstance(
          configService,
          tokenService,
          nebulaHeader,
          baseUrlKey,
        );
      },
    },
  ],
})
export class StatusModule {}
