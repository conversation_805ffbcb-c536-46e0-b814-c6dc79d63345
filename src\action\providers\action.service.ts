import { Injectable } from '@nestjs/common';
import { ActionRepository } from '../repositories/action.repository';
import { CreateActionDto } from '../dto/create-action.dto';
import { UpdateActionDto } from '../dto/update-action.dto';
import { getUserContext } from '../../utils/helpers';
import { RequestContext } from 'nestjs-request-context';
import { ActionEntity } from '../entities/action.entity';
import { LoggerService } from '../../loggers/logger.service';

@Injectable()
export class ActionService {
  constructor(
    private readonly actionRepository: ActionRepository,
    private readonly logger: LoggerService,
  ) {}

  async create(actionDto: CreateActionDto): Promise<ActionEntity> {
    const createdBy =
      actionDto.createdBy || getUserContext(RequestContext)?.userId;
    return this.actionRepository.create({
      ...actionDto,
      createdBy,
    });
  }

  async update(actionDto: UpdateActionDto) {
    this.logger.log(
      'request received to update action in action-service',
      actionDto,
    );
    return this.actionRepository.update(actionDto);
  }

  async findAllActionByIds(ids: string[]) {
    return await this.actionRepository.findAllActionByIds(ids);
  }
}
