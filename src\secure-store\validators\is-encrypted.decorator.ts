import {
  registerDecorator,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  ValidationArguments,
} from 'class-validator';
import { Injectable } from '@nestjs/common';
import { EncryptionService } from '../../encryption/encryption.service';


@ValidatorConstraint({ name: 'IsEncrypted', async: false })
@Injectable()
export class IsEncryptedConstraint implements ValidatorConstraintInterface {
  constructor(private readonly encryptionService: EncryptionService) {}

  validate(value: string): boolean {
    return this.encryptionService?.isEncrypted(value) ?? false;
  }

  defaultMessage(): string {
    return `Value must be an encrypted string.`;
  }
}


export function IsEncrypted(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsEncryptedConstraint,
    });
  };
}

