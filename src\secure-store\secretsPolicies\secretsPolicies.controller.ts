import { Body, Controller, Get, Param, Post, UseGuards } from '@nestjs/common';
import { SecretsPoliciesService } from './secretsPolicies.service';
import { SecretsPoliciesDto } from './dto/secretPolicies.dto';
import { ApiSecurity, ApiTags } from '@nestjs/swagger';
import { AuthenticationGuard } from '../../auth/authentication.guard';

@ApiTags('Secrets policies')
@ApiSecurity('x-nebula-authorization')
@UseGuards(AuthenticationGuard)
@Controller('secrets-policies')
export class SecretsPoliciesController {
  constructor(private readonly secretPoliciesService: SecretsPoliciesService) {}

  @Post()
  async create(@Body() body: SecretsPoliciesDto) {
    return await this.secretPoliciesService.create(body);
  }
}
