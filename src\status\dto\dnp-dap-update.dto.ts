import {
  IsString,
  IsArray,
  IsObject,
  ValidateNested,
  IsNotEmpty,
  IsBoolean,
  IsOptional,
  IsUrl,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { API_RESPONSE_STATUS } from 'src/types';

export class InterfaceStatusDto {
  @ApiProperty({
    description: 'Interface name',
    example: 'Ethernet1',
  })
  @IsString()
  @IsNotEmpty()
  interface: string;

  @ApiProperty({
    description: 'Indicates if the interface was updated',
    example: true,
  })
  @IsBoolean()
  @IsNotEmpty()
  updated: boolean;
}

export class TaskResultDto {
  @ApiProperty({
    description: 'Status of the task execution for a device',
    enum: ['successful', 'partially successful', 'failed'],
    example: 'successful',
  })
  @IsString()
  @IsNotEmpty()
  status: string;

  @ApiProperty({
    description: 'Hostname of the device',
    example: 'sample.netops.charter.com',
  })
  @IsString()
  @IsNotEmpty()
  device: string;

  @ApiProperty({
    description: 'List of interface statuses',
    type: [InterfaceStatusDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => InterfaceStatusDto)
  interfaces: InterfaceStatusDto[];
}

export class TaskActivityDto {
  @ApiProperty({
    description: 'List of task results for each device',
    type: [TaskResultDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TaskResultDto)
  results: TaskResultDto[];
}

export class DapWebhookRequestDto {
  @ApiProperty({
    description: 'Details of the task activity',
    type: [TaskActivityDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TaskActivityDto)
  taskActivity: TaskActivityDto[];

  @ApiProperty({
    description: 'Callback URL to notify about the task status',
    example: 'https://sample.com/api/v1/callback/123e456',
  })
  @IsUrl()
  @IsNotEmpty()
  callbackUrl: string;

  @ApiProperty({
    description: 'Comment associated with the task',
    example: 'NEB-IAAS-DNP-1234',
  })
  @IsString()
  comment: string;
}

export class DapWebhookResponseDto {
  @ApiProperty({
    description: 'Status of the task execution for a device',
    enum: API_RESPONSE_STATUS,
    example: 'SUCCESS|FAILED|PARTIALLY COMPLETED',
  })
  @IsString()
  status: string;

  @ApiProperty({
    description: 'message associated with the task',
    example: 'request updated successfully',
  })
  @IsString()
  message: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => DapWebhookRequestDto)
  data?: DapWebhookRequestDto;
}

export type DnpStatus = {
  status: 'SUCCESS' | 'FAILED' | 'PARTIALLY COMPLETED';
  message?: string;
};
