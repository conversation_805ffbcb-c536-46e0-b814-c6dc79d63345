db.n8nconfig.insertOne({
  "workflow": "dnp-dap-STG",
  "baseUrl": "https://nebula.stage.charter.com/",
  "generateUUIDWorkflow": "0i8p5DF44BTbrQwG",
  "catalogStepsWorkflow": "92vU920KUcRRVe0a",
  "eventSourceHostName": "cdptpabb04-caas-mgmt-v3.stage.charter.com",
  "activityLogUrl":"http://activity-log-producer-service.nebula.svc.cluster.local:80/activity-log-producer/v1/log",
  "cloudApiUrl": "http://cloud-api-service.nebula.svc.cluster.local:80/nebula-api/",
  "updateInterfaceDelay":"20"
})