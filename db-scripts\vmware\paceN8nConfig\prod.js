const session = db.getMongo().startSession();
session.startTransaction();

function runScript() {
  const paceDocWEST = db.n8nconfig.findOne({
    workflow: 'VMWARE-Create-PROD-WEST',
  });

  if (paceDocWEST) {
    // Remove the _id field to avoid duplicate key error
    delete paceDocWEST._id;

    // Change the name to "BLUE"
    paceDocWEST.workflow = 'PACE-VMWARE-Create-PROD-WEST';

    // Insert the new document
    db.n8nconfig.insertOne(paceDocWEST);
  } else {
    print('vmware create doc not found in n8n config');
  }

  const paceDocEAST = db.n8nconfig.findOne({
    workflow: 'VMWARE-Create-PROD-EAST',
  });

  if (paceDocEAST) {
    // Remove the _id field to avoid duplicate key error
    delete paceDocEAST._id;

    // Change the name to "BLUE"
    paceDocEAST.workflow = 'PACE-VMWARE-Create-PROD-EAST';

    // Insert the new document
    db.n8nconfig.insertOne(paceDocEAST);
  } else {
    print('vmware create doc not found in n8n config');
  }

  session.commitTransaction();
  print(`Script ran successfully`);
}

try {
  runScript();
} catch (e) {
  session.abortTransaction();
  print('Transaction aborted due to error', e);
} finally {
  session.endSession();
}
