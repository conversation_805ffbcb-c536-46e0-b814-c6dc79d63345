import { BadRequestException, Injectable } from '@nestjs/common';
import { connect, Model } from 'mongoose';
import { deepSanitize } from '../utils/helpers';
import { Sitemap, SitemapSchema } from './schema/sitemap.schema';
import { SitemapEntity } from './entity/sitemap.entity';
import { LoggerService } from '../loggers/logger.service';
import { paginateAggregateResults } from '../utils/pagination/pagination';
import {
  PaginatedResponseDto,
  PaginationQueryDto,
} from 'src/utils/pagination/dto/pagination.dto';

@Injectable()
export class SitemapRepository {
  constructor(private readonly logger: LoggerService) {}

  private async connectToDB() {
    return await connect(process.env.MONGODB_URI);
  }

  private async getModel() {
    const conn = await this.connectToDB();
    return conn.model<
      Sitemap & { createdAt: Date; updatedAt: Date; __v: number }
    >(Sitemap.name, SitemapSchema);
  }

  private async getPaginatedModel() {
    const conn = await this.connectToDB();
    interface ISiteMapModel extends Model<SitemapEntity> {
      aggregatePaginate: (agg: any, options: any) => Promise<any>;
    }
    return conn.model<Sitemap, ISiteMapModel>(Sitemap.name, SitemapSchema);
  }

  async createManySitemap(payload: SitemapEntity[]): Promise<any> {
    const model = await this.getModel();
    const siteMapArray = [];
    payload.forEach((data) => {
      siteMapArray.push(deepSanitize(data));
    });
    const bulkOperations = siteMapArray.map((sitemap) => ({
      updateOne: {
        filter: { SAPFunctionalName: sitemap.SAPFunctionalName },
        update: { $set: sitemap },
        upsert: true,
      },
    }));

    try {
      return await model.bulkWrite(bulkOperations);
    } catch (error) {
      this.logger.error(error, 'Failed to add sitemap data!');
      throw error;
    }
  }

  async updateSiteMapData(id: string, siteMap: SitemapEntity) {
    const model = await this.getModel();
    try {
      const sitemapData = await model.findByIdAndUpdate(id, siteMap);
      if (sitemapData) {
        return {
          message: `Data updated successfully`,
          id: id,
          statusCode: 200,
          // data: sitemapData,
        };
      }
    } catch (error) {
      this.logger.error(error);
      throw new BadRequestException('Invalid Id');
    }
  }

  async getSiteMapData(
    pageQuery: PaginationQueryDto,
  ): Promise<PaginatedResponseDto<SitemapEntity>> {
    const model = await this.getPaginatedModel();
    try {
      const sitemapData = await paginateAggregateResults<SitemapEntity>(
        [{ $match: {} }, { $sort: { createdAt: -1 } }],
        model,
        pageQuery,
      );
      return sitemapData;
    } catch (err) {
      this.logger.error(err);
      throw err;
    }
  }

  async getAllSiteMapData(): Promise<any> {
    const model = await this.getModel();
    try {
      const sitemapData = await model.find();
      return sitemapData;
    } catch (err) {
      this.logger.error(err);
      throw err;
    }
  }

  async deleteSiteMapData(id: string): Promise<any> {
    const model = await this.getModel();
    try {
      const deletedSiteMap = await model.findByIdAndDelete({ _id: id });
      if (deletedSiteMap) {
        return {
          message: `Record deleted successfully`,
          id: id,
          statusCode: 200,
          // data: deletedSiteMap,
        };
      }
    } catch (error) {
      this.logger.error(error);
      throw new BadRequestException('Invalid Id');
    }
  }
}
