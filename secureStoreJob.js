const axios = require('axios');

const logger = {
  log: (msg) => console.log(`[INFO] ${msg}`),
  error: (msg) => console.error(`[ERROR] ${msg}`),
};

const API_BASE_URL = 'http://localhost:3005/nebula-api/v1/secure-store';

const API_BEARER_TOKEN = 'yeyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.3J1-yMdxCklmIeHRdTLM-59hO4ZtrVGlXLsD2s-qR-s';

const axiosConfig = {
  headers: {
    'x-nebula-authorization': API_BEARER_TOKEN,
    'Content-Type': 'application/json',
  },
};

const rotateSecretsPayload = [
  {
    "secretId": "NEB-VAULT-ROT-SECRET-1234",
    "newPassword": "7cee167ff0d345f767d178099ac737f4"
  },
  {
    "secretId": "NEB-VAULT-ROT-SECRET-**********",
    "newPassword": "7cee167ff0d345f767d178099ac737f4"
  },
  {
    "secretId": "NEB-VAULT-ROT-SECRET-*********",
    "newPassword": "7cee167ff0d345f767d178099ac737f4"
  },
  {
    "secretId": "NEB-VAULT-ROT-SECRET-***********",
    "newPassword": "7cee167ff0d345f767d178099ac737f4"
  }
];
async function rotateSecretsJob() {
    const url = `${API_BASE_URL}/rotate-secrets`;
  console.log('Calling:', url);
  try {
    await axios.post(`${API_BASE_URL}/secret-rotate`,rotateSecretsPayload,axiosConfig);
    logger.log(`Secrets rotation job completed successfully at ${new Date().toISOString()}`);
  } catch (error) {
    logger.error(`Secrets rotation job failed at ${new Date().toISOString()}. Error: ${error.message}`);
  }
}

async function renewVaultTokens() {
  try {
    await axios.post(`${API_BASE_URL}/renew-vault-tokens`);
    logger.log(`Vault token renewal job completed successfully at ${new Date().toISOString()}`);
  } catch (error) {
    logger.error(`Vault token renewal job failed at ${new Date().toISOString()}. Error: ${error.message}`);
  }
}

async function reactivateSecrets() {
  try {
    await axios.post(`${API_BASE_URL}/reactivate-secrets`);
    logger.log(`Reactivate vault secrets job completed successfully at ${new Date().toISOString()}`);
  } catch (error) {
    logger.error(`Reactivate vault secrets job failed at ${new Date().toISOString()}. Error: ${error.message}`);
  }
}

async function runAllJobs() {
  await rotateSecretsJob();
//   await renewVaultTokens();
//   await reactivateSecrets();
  logger.log('All jobs executed');
}

runAllJobs();
