import { Test } from '@nestjs/testing';
import { SecretsPoliciesService } from './secretsPolicies.service';
import { SecretsPoliciesRepository } from './secretsPolicies.repository';
jest.mock('src/utils/helpers', () => ({
  getUserContext: jest.fn().mockReturnValue({ userId: 'P3270043' }),
}));
describe('SecretsPoliciesService', function () {
  let service: SecretsPoliciesService;
  let mockSecretPoliciesRepository = {
    getPolicy: jest.fn(),
    create: jest.fn(),
    find: jest.fn(),
    getSecretPoliciesWithResourceId: jest.fn(),
  };
  beforeEach(async function () {
    const module = await Test.createTestingModule({
      providers: [
        SecretsPoliciesService,
        {
          provide: SecretsPoliciesRepository,
          useValue: mockSecretPoliciesRepository,
        },
      ],
    }).compile();
    service = module.get(SecretsPoliciesService);
  });
 
  it('should be defined', async function () {
    expect(service).toBeDefined();
  });
 
  describe('getPolicy', function () {
    let mockResult = {
      _id: '686619cbd68e443c316c063d',
      policyId: 'NEB-VAULT-PASSWORD-POLICY-12234',
      policyName: 'TestUI2',
      type: 'VAULT-PASSWORD-POLICY',
      description: 'Password should have 20  chars',
      policyRules: {
        passwordDescription: 'Password should have 20  chars',
        acceptedSpecialCharacters: '!@#$%',
        totalCharactersLength: 30,
        specialCharactersCount: 5,
        lowerCaseLettersCount: 10,
        upperCaseLettersCount: 3,
        numericalCharactersCount: 5,
        _id: '686619cbd68e443c316c063e',
      },
      resourceId: 'NEB-RES-VAULT-NAMESPACE-8716',
      status: 'ACTIVE',
      createdAt: '2025-07-03T05:48:59.198+00:00',
      updatedAt: '2025-07-03T05:48:59.198+00:00',
      __v: 0,
    };
    let mockid = 'NEB-VAULT-PASSWORD-POLICY-12234';
    it('should return single secrets policy if provided value of the id param is string', async function () {
      mockSecretPoliciesRepository.getPolicy.mockResolvedValue(mockResult);
      const result = await service.getPolicy(mockid);
      expect(result).toEqual(mockResult);
    });
    it('should return array secrets policies if provided value of the id param is array', async function () {
      mockSecretPoliciesRepository.getPolicy.mockResolvedValue([mockResult]);
      const result = await service.getPolicy([mockid]);
      expect(result).toEqual([mockResult]);
    });
  });
 
  describe('create', () => {
    it('should save the policy payload and return it as response', async () => {
      const mockPayload = {
        policyName: 'TestUI2',
        type: 'VAULT-PASSWORD-POLICY',
        description: 'Password should have 20  chars',
        policyRules: {
          passwordDescription: 'Password should have 20  chars',
          acceptedSpecialCharacters: '!@#$%',
          totalCharactersLength: 30,
          specialCharactersCount: 5,
          lowerCaseLettersCount: 10,
          upperCaseLettersCount: 3,
          numericalCharactersCount: 5,
          _id: '686619cbd68e443c316c063e',
        },
        resourceId: 'NEB-RES-VAULT-NAMESPACE-8716',
        status: 'ACTIVE',
      };
      let mockResult = {
        _id: '686619cbd68e443c316c063d',
        policyId: 'NEB-VAULT-PASSWORD-POLICY-12234',
        policyName: 'TestUI2',
        type: 'VAULT-PASSWORD-POLICY',
        description: 'Password should have 20  chars',
        policyRules: {
          passwordDescription: 'Password should have 20  chars',
          acceptedSpecialCharacters: '!@#$%',
          totalCharactersLength: 30,
          specialCharactersCount: 5,
          lowerCaseLettersCount: 10,
          upperCaseLettersCount: 3,
          numericalCharactersCount: 5,
          _id: '686619cbd68e443c316c063e',
        },
        resourceId: 'NEB-RES-VAULT-NAMESPACE-8716',
        status: 'ACTIVE',
        createdAt: '2025-07-03T05:48:59.198+00:00',
        updatedAt: '2025-07-03T05:48:59.198+00:00',
        __v: 0,
      };
      mockSecretPoliciesRepository.create.mockResolvedValueOnce(mockResult);
      const result = await service.create(mockPayload);
      expect(result).toBe(mockResult);
    });
  });
 
  describe('getSecretPoliciesWithResourceId', () => {
    it('should return policies based on resourceId', async () => {
      let mockResult = {
        _id: '686619cbd68e443c316c063d',
        policyId: 'NEB-VAULT-PASSWORD-POLICY-12234',
        policyName: 'TestUI2',
        type: 'VAULT-PASSWORD-POLICY',
        description: 'Password should have 20  chars',
        policyRules: {
          passwordDescription: 'Password should have 20  chars',
          acceptedSpecialCharacters: '!@#$%',
          totalCharactersLength: 30,
          specialCharactersCount: 5,
          lowerCaseLettersCount: 10,
          upperCaseLettersCount: 3,
          numericalCharactersCount: 5,
          _id: '686619cbd68e443c316c063e',
        },
        resourceId: 'NEB-RES-VAULT-NAMESPACE-8716',
        status: 'ACTIVE',
        createdAt: '2025-07-03T05:48:59.198+00:00',
        updatedAt: '2025-07-03T05:48:59.198+00:00',
        __v: 0,
      };
      mockSecretPoliciesRepository.getSecretPoliciesWithResourceId.mockResolvedValueOnce(
        [mockResult],
      );
      const result = await service.getSecretPoliciesWithResourceId(
        mockResult.resourceId,
      );
      expect(result).toEqual([mockResult]);
    });
  });
 
  describe('validateSecret', () => {
    let mockPolicy = {
      _id: '686619cbd68e443c316c063d',
      policyId: 'NEB-VAULT-PASSWORD-POLICY-12234',
      policyName: 'TestUI2',
      type: 'VAULT-PASSWORD-POLICY',
      description: 'Password should have 20  chars',
      policyRules: {
        passwordDescription: 'Password should have 20  chars',
        acceptedSpecialCharacters: '!@#$%',
        totalCharactersLength: 14,
        specialCharactersCount: 1,
        lowerCaseLettersCount: 1,
        upperCaseLettersCount: 1,
        numericalCharactersCount: 1,
        _id: '686619cbd68e443c316c063e',
      },
      resourceId: 'NEB-RES-VAULT-NAMESPACE-8716',
      status: 'ACTIVE',
      createdAt: '2025-07-03T05:48:59.198+00:00',
      updatedAt: '2025-07-03T05:48:59.198+00:00',
      __v: 0,
    };
    it('should return boolean true with no message', async () => {
      mockSecretPoliciesRepository.getPolicy.mockResolvedValueOnce(mockPolicy);
      const result = await service.validateSecret(
        'Ch@rter1234567',
        mockPolicy.policyId,
      );
      const mockResult = { valid: true, message: [] };
      expect(result).toEqual(mockResult);
    });
  });
 
  describe('validateSecretWithPolicyRules', () => {
    let mockPolicy = {
      _id: '686619cbd68e443c316c063d',
      policyId: 'NEB-VAULT-PASSWORD-POLICY-12234',
      policyName: 'TestUI2',
      type: 'VAULT-PASSWORD-POLICY',
      description: 'Password should have 20  chars',
      policyRules: {
        passwordDescription: 'Password should have 20  chars',
        acceptedSpecialCharacters: '!@#$%',
        totalCharactersLength: 14,
        specialCharactersCount: 1,
        lowerCaseLettersCount: 1,
        upperCaseLettersCount: 1,
        numericalCharactersCount: 1,
        _id: '686619cbd68e443c316c063e',
      },
      resourceId: 'NEB-RES-VAULT-NAMESPACE-8716',
      status: 'ACTIVE',
      createdAt: '2025-07-03T05:48:59.198+00:00',
      updatedAt: '2025-07-03T05:48:59.198+00:00',
      __v: 0,
    };
    it('should validate secret and return true with no message', async () => {
      const result = service.validateSecretWithPolicyRules(
        'Ch@rter1234567',
        mockPolicy.policyRules.lowerCaseLettersCount,
        mockPolicy.policyRules.upperCaseLettersCount,
        mockPolicy.policyRules.numericalCharactersCount,
        mockPolicy.policyRules.specialCharactersCount,
        mockPolicy.policyRules.acceptedSpecialCharacters,
        mockPolicy.policyRules.totalCharactersLength,
      );
      const mockResult = { valid: true, message: [] };
      expect(result).toEqual(mockResult);
    });
    it('should validate secret and return false with character length message', async () => {
      const result = service.validateSecretWithPolicyRules(
        'Ch@rter123456',
        mockPolicy.policyRules.lowerCaseLettersCount,
        mockPolicy.policyRules.upperCaseLettersCount,
        mockPolicy.policyRules.numericalCharactersCount,
        mockPolicy.policyRules.specialCharactersCount,
        mockPolicy.policyRules.acceptedSpecialCharacters,
        mockPolicy.policyRules.totalCharactersLength,
      );
      const mockResult = {
        valid: false,
        message: [
          `Password should have atleast ${mockPolicy.policyRules.totalCharactersLength} characters.`,
        ],
      };
      expect(result).toEqual(mockResult);
    });
    it('should validate secret and return false with upper character length message', async () => {
      const result = service.validateSecretWithPolicyRules(
        'ch@rter1234567',
        mockPolicy.policyRules.lowerCaseLettersCount,
        mockPolicy.policyRules.upperCaseLettersCount,
        mockPolicy.policyRules.numericalCharactersCount,
        mockPolicy.policyRules.specialCharactersCount,
        mockPolicy.policyRules.acceptedSpecialCharacters,
        mockPolicy.policyRules.totalCharactersLength,
      );
      const mockResult = {
        valid: false,
        message: [
          `Password should have atleast ${mockPolicy.policyRules.upperCaseLettersCount} upper Case letters.`,
        ],
      };
      expect(result).toEqual(mockResult);
    });
    it('should validate secret and return false with lower character length message', async () => {
      const result = service.validateSecretWithPolicyRules(
        'CH@RTER1234567',
        mockPolicy.policyRules.lowerCaseLettersCount,
        mockPolicy.policyRules.upperCaseLettersCount,
        mockPolicy.policyRules.numericalCharactersCount,
        mockPolicy.policyRules.specialCharactersCount,
        mockPolicy.policyRules.acceptedSpecialCharacters,
        mockPolicy.policyRules.totalCharactersLength,
      );
      const mockResult = {
        valid: false,
        message: [
          `Password should have atleast ${mockPolicy.policyRules.lowerCaseLettersCount} lower Case letters.`,
        ],
      };
      expect(result).toEqual(mockResult);
    });
    it('should validate secret and return false with character max length message', async () => {
      const result = service.validateSecretWithPolicyRules(
        `<EMAIL>'sstandarddummytexteversincethe1500s,whenanunknownprintertookagalleyoftypeandscrambledittomakeatypespecimenbook.Ithassurvivednotonlyfivecenturies,butalsotheleapintoelectronictypesetting,remainingessentiallyunchanged.Itwaspopularisedinthe1960swiththereleaseofLetrasetsheetscontainingLoremIpsumpassages,andmorerecentlywithdesktoppublishingsoftwarelikeAldusPageMakerincludingversionsofLoremIpsum`,
        mockPolicy.policyRules.lowerCaseLettersCount,
        mockPolicy.policyRules.upperCaseLettersCount,
        mockPolicy.policyRules.numericalCharactersCount,
        mockPolicy.policyRules.specialCharactersCount,
        mockPolicy.policyRules.acceptedSpecialCharacters,
        mockPolicy.policyRules.totalCharactersLength,
      );
      const mockResult = {
        valid: false,
        message: [
          `Password cannot be more than 30 characters.`,
          'Password can only have the following special characters - !@#$%',
        ],
      };
      expect(result).toEqual(mockResult);
    });
  });
});
