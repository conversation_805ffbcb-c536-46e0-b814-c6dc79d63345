import { Test } from '@nestjs/testing';
import { SecretsPoliciesService } from './secretsPolicies.service';
import { SecretsPoliciesRepository } from './secretsPolicies.repository';

describe('SecretsPoliciesService', function () {
  let service: SecretsPoliciesService;
  let mockSecretPoliciesRepository = {
    getPolicy: jest.fn(),
  };
  beforeEach(async function () {
    const module = await Test.createTestingModule({
      providers: [
        SecretsPoliciesService,
        {
          provide: SecretsPoliciesRepository,
          useValue: mockSecretPoliciesRepository,
        },
      ],
    }).compile();
    service = module.get(SecretsPoliciesService);
  });

  it('should be defined', async function () {
    expect(service).toBeDefined();
  });

  describe('getPolicy', function () {
    let mockResult = {
      _id: '686619cbd68e443c316c063d',
      policyId: 'NEB-VAULT-PASSWORD-POLICY-12234',
      policyName: 'TestUI2',
      type: 'VAULT-PASSWORD-POLICY',
      description: 'Password should have 20  chars',
      policyRules: {
        passwordDescription: 'Password should have 20  chars',
        acceptedSpecialCharacters: '!@#$%',
        totalCharactersLength: 30,
        specialCharactersCount: 5,
        lowerCaseLettersCount: 10,
        upperCaseLettersCount: 3,
        numericalCharactersCount: 5,
        _id: '686619cbd68e443c316c063e',
      },
      resourceId: 'NEB-RES-VAULT-NAMESPACE-8716',
      status: 'ACTIVE',
      createdAt: '2025-07-03T05:48:59.198+00:00',
      updatedAt: '2025-07-03T05:48:59.198+00:00',
      __v: 0,
    };
    let mockid = 'NEB-VAULT-PASSWORD-POLICY-12234';
    it('should return single secrets policy if provided value of the id param is string', async function () {
      mockSecretPoliciesRepository.getPolicy.mockResolvedValue(mockResult);
      const result = await service.getPolicy(mockid);
      expect(result).toEqual(mockResult);
    });
    it('should return array secrets policies if provided value of the id param is array', async function () {
      mockSecretPoliciesRepository.getPolicy.mockResolvedValue([mockResult]);
      const result = await service.getPolicy([mockid]);
      expect(result).toEqual([mockResult]);
    });
  });
});
