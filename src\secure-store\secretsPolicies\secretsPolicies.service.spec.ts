import { Test } from '@nestjs/testing';
import { SecretsPoliciesService } from './secretsPolicies.service';
import { SecretsPoliciesRepository } from './secretsPolicies.repository';

describe('SecretsPoliciesService', function () {
  let service: SecretsPoliciesService;
  let mockSecretPoliciesRepository = {
    getPolicy: jest.fn(),
  };
  beforeEach(async function () {
    const module = await Test.createTestingModule({
      providers: [
        SecretsPoliciesService,
        {
          provide: SecretsPoliciesRepository,
          useValue: mockSecretPoliciesRepository,
        },
      ],
    }).compile();
    service = module.get(SecretsPoliciesService);
  });

  it('should be defined', async function () {
    expect(service).toBeDefined();
  });

  describe('getPolicy', function () {
    let mockResult = {
      _id: '6849cc71864a08117c6949fd',
      policyId: 'NEB-VAULT-PASSWORD-POLICY-12234',
      type: 'VAULT-PASSWORD-POLICY',
      policyName: 'sample-dev-passwordmay201',
      description: 'test',
      resourceId: 'NEB-RES-VAULT-NAMESPACE-1000',
      status: 'ACTIVE',
      secretTTLInHours: 700,
      active: true,
      createdBy: 'P3226653',
      createdDate: '2025-06-11T18:35:29.421Z',
      updatedBy: 'P3226653',
      updatedDate: '2025-06-11T18:35:29.421Z',
      namespace: 'nebula-stamp',
    };
    let mockid = 'NEB-VAULT-PASSWORD-POLICY-12234';
    it('should return single secrets policy if provided value of the id param is string', async function () {
      mockSecretPoliciesRepository.getPolicy.mockResolvedValue(mockResult);
      const result = await service.getPolicy(mockid);
      expect(result).toEqual(mockResult);
    });
    it('should return array secrets policies if provided value of the id param is array', async function () {
      mockSecretPoliciesRepository.getPolicy.mockResolvedValue([mockResult]);
      const result = await service.getPolicy([mockid]);
      expect(result).toEqual([mockResult]);
    });
  });
});
