function runScript() {
  const catalog = db.menvcataloglevel04.findOne({
    shortName: 'addfirewallrulesv2',
  });

  if (!catalog) {
    print('Catalog document does not exists, aborting the transaction');
    throw new Error(
      'Catalog document does not exists, aborting the transaction',
    );
  }

  const catalogSteps = {
    catalogLevel04Id: catalog._id,
    version: 7,
    active: true,
    activityLogSteps: [
      {
        name: 'Receive Firewall v2 Request - Nebula',
        eventCode: 'NEB-EVENT-VM-CREATE-1001',
        sequence: 1,
      },
      {
        name: 'Create Jira Ticket IPV6 - Jira',
        eventCode: 'NEB-EVENT-FIREWALLV2-1001',
        sequence: 2,
      },
      {
        name: 'Create Cher<PERSON> Ticket IPV6 - Cherwell',
        eventCode: 'NEB-EVENT-FIREWALLV2-2001',
        sequence: 3,
      },
      {
        name: 'Create Subrequest IPV6 - Nebula',
        eventCode: 'NEB-EVENT-FIREWALLV2-3001',
        sequence: 4,
      },
      {
        name: 'Poll Process For IPV6 Jira Ticket(s) - Nebula',
        eventCode: 'NEB-EVENT-FIREWALLV2-4001',
        sequence: 5,
      },
      {
        name: 'Identify AWS subnets IPV4 - Nebula',
        eventCode: 'NEB-EVENT-FIREWALLV2-24001',
        sequence: 6,
      },
      {
        name: 'Path/Impact Identification IPV4 - Tufin',
        eventCode: 'NEB-EVENT-FIREWALLV2-5001',
        sequence: 7,
      },
      {
        name: 'Owner Identification IPV4 - Tufin',
        eventCode: 'NEB-EVENT-FIREWALLV2-6001',
        sequence: 8,
      },
      {
        name: 'Create Jira Ticket(s) CBO IPV4 - Jira',
        eventCode: 'NEB-EVENT-FIREWALLV2-13001',
        sequence: 9,
      },
      {
        name: 'Poll Process For CBO Jira Ticket(s) - Nebula',
        eventCode: 'NEB-EVENT-FIREWALLV2-14001',
        sequence: 10,
      },
      {
        name: 'Create Subrequest(s) IPV4 - Nebula',
        eventCode: 'NEB-EVENT-FIREWALLV2-8001',
        sequence: 11,
      },
      {
        name: 'Create Secure Change Ticket(s) IPV4 - Tufin',
        eventCode: 'NEB-EVENT-FIREWALLV2-7001',
        sequence: 12,
      },
      {
        name: 'Create Jira Ticket(s) IPV4 - Jira',
        eventCode: 'NEB-EVENT-FIREWALLV2-9001',
        sequence: 13,
      },
      {
        name: 'Create Chrewell Ticket(s) IPV4 - Cherwell',
        eventCode: 'NEB-EVENT-FIREWALLV2-10001',
        sequence: 14,
      },
      {
        name: 'Risk Analysis Update IPV4 - Tufin',
        eventCode: 'NEB-EVENT-FIREWALLV2-11001',
        sequence: 15,
      },
      {
        name: 'Designer Result Update IPV4 - Tufin',
        eventCode: 'NEB-EVENT-FIREWALLV2-12001',
        sequence: 16,
      },
      {
        name: 'Approve Request IPV4 - Nebula',
        eventCode: 'NEB-EVENT-APPROVAL-1001',
        sequence: 17,
      },
      {
        name: 'Create CRQ Ticket(s) IPV4 - Remedy',
        eventCode: 'NEB-EVENT-FIREWALLV2-15001',
        sequence: 18,
      },
      {
        name: 'Apply Process Change IPV4 - Tufin',
        eventCode: 'NEB-EVENT-FIREWALLV2-16001',
        sequence: 19,
      },
      {
        name: 'Close Jira Ticket(s) IPV4 - Jira',
        eventCode: 'NEB-EVENT-FIREWALLV2-17001',
        sequence: 20,
      },
      {
        name: 'Close Cherwell Ticket(s) IPV4 - Cherwell',
        eventCode: 'NEB-EVENT-FIREWALLV2-18001',
        sequence: 21,
      },
      {
        name: 'Close CRQ Ticket(s) IPV4 - Remedy',
        eventCode: 'NEB-EVENT-FIREWALLV2-19001',
        sequence: 22,
      },
      {
        name: 'Complete Request Processing - Nebula',
        eventCode: 'NEB-EVENT-FIREWALLV2-20001',
        sequence: 23,
      },
      {
        name: 'Cancel Tufin Ticket(s) - Tufin',
        eventCode: 'NEB-EVENT-FIREWALLV2-21001',
        sequence: 24,
      },
      {
        name: 'Cancel Jira Ticket(s) - Jira',
        eventCode: 'NEB-EVENT-FIREWALLV2-22001',
        sequence: 25,
      },
      {
        name: 'Cancel Cherwell Ticket(s) - Nebula',
        eventCode: 'NEB-EVENT-FIREWALLV2-23001',
        sequence: 26,
      },
    ],
  };
  db.catalogsteps.findOneAndUpdate(
    { catalogLevel04Id: catalog._id, version: 6 },
    { $set: { active: false } },
  );

  db.catalogsteps.insertOne(catalogSteps);
  print(`Script ran successfully`);
}

let session;
try {
  session = db.getMongo().startSession();
  session.startTransaction();
  runScript();
  session.commitTransaction();
} catch (e) {
  session.abortTransaction();
  print('Transaction aborted due to error', e);
} finally {
  session.endSession();
}
