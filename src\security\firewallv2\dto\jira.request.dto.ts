import { ApiProperty } from '@nestjs/swagger';
import { IsDate, IsObject, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';
import { ObjectStorageDto } from '../../../objectStorage/object.storage.dto';

export class FirewallDto {
  @ApiProperty()
  storageInfo: ObjectStorageDto;

  @ApiProperty()
  @IsOptional()
  fileName?: string;

  @ApiProperty()
  summary: string;

  @ApiProperty()
  description: string;

  @ApiProperty()
  supportOrganization: string;

  @ApiProperty()
  region: string[];

  @ApiProperty()
  userName: string;

  @ApiProperty()
  netopsaskTicket?: string;

  @ApiProperty()
  @IsDate({ message: 'Business Request Date must be a valid date' })
  @Type(() => Date)
  businessRequestDate?: Date;

  @ApiProperty()
  nebulaProject: string;

  @ApiProperty()
  jiraIssueLink?: string[];

  @ApiProperty()
  @IsOptional()
  projectKey?: string;

  @ApiProperty()
  @IsOptional()
  assigneeName?: string;

  @ApiProperty()
  @IsOptional()
  comments?: string[];
}

export class JiraRequestDto {
  @ApiProperty()
  serviceRequestId: string;

  @ApiProperty()
  subRequestId: string;

  @ApiProperty()
  firewallRequestInfo: FirewallDto;
}

export class GenericRequestDto {
  @ApiProperty()
  requestingOrganization: string;

  @ApiProperty()
  userName: string;

  @ApiProperty()
  @IsObject()
  @IsOptional()
  user: {
    userId?: string;
    email?: string;
    firstName?: string;
    lastName?: string;
    groups?: string[];
  };

  @ApiProperty()
  @IsOptional()
  capabilityArea?: string;

  @ApiProperty()
  @IsOptional()
  priority?: string;

  @ApiProperty()
  @IsOptional()
  justification?: string;
}

export class JiraGenericRequestDto {
  @ApiProperty()
  @IsOptional()
  serviceRequestId: string;

  @ApiProperty()
  summary: string;

  @ApiProperty()
  description: string;

  @ApiProperty()
  storyType: string;

  @ApiProperty()
  issueType: string;

  @ApiProperty()
  userName: string;

  @ApiProperty()
  @IsDate({ message: 'Expected date must be a valid date' })
  @Type(() => Date)
  @IsOptional()
  date?: Date;

  @ApiProperty()
  additionalRequestData: Partial<GenericRequestDto>;
}

export class JiraTicketLinkDto {
  @ApiProperty()
  @IsOptional()
  type: string;

  @ApiProperty()
  inwardIssue: string;

  @ApiProperty()
  outwardIssue: string;

  @ApiProperty()
  @IsOptional()
  comment: string;
}
