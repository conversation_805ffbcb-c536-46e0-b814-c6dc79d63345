import { BadRequestException, Injectable } from '@nestjs/common';
import { LoggerService } from '../loggers/logger.service';
import {
  ApprovalStatus,
  RequestStatus,
  RequestType,
  ServiceCatalogName,
  ServiceCatalogType,
} from '../types';
import * as YAML from 'yamljs';
import { AssetsService } from '../assets/assets.service';
import { plainToInstance } from 'class-transformer';
import { CreateZtpRequestDTO } from './dto/ztpbulk.request.dto';
import { validateSync } from 'class-validator';
import { RequestContext } from 'nestjs-request-context';

@Injectable()
export class ZtpService {
  constructor(
    private logger: LoggerService,
    private assestsService: AssetsService,
  ) {}

  async createZtpRequest(createZtpRequestDto: CreateZtpRequestDTO) {
    this.logger.log(`Create Ztp request received: ${createZtpRequestDto}`);

    try {
      const req = RequestContext.currentContext.req;
      const requestorPID = req?.user?.userId || null;
      const requestorEmail = req.user?.email || null;

      const serviceRequest = {
        metadata: {
          serviceCatalog: {
            catalogName: ServiceCatalogName.CREATE_ZTP,
            catalogType: ServiceCatalogType.NAAS,
          },
        },
        requestType: RequestType.ZERO_TOUCH_PROVISIONING,
        status: RequestStatus.CREATED,
        approvalStatus: ApprovalStatus.PENDING,
        payload: createZtpRequestDto,
        createdBy: requestorPID,
        requesterEmail: requestorEmail,
      };
      this.logger.debug(
        `Constructed service request:  ${JSON.stringify(serviceRequest)}`,
      );

      const dbResponse = await this.assestsService.create(serviceRequest);
      this.logger.log(
        `Created service request with id: ${dbResponse.serviceRequestId}`,
      );

      this.logger.debug(
        `Updating service request to Auto Approved and Pushing service request ${dbResponse.id} to queue`,
      );
      await this.assestsService.queueIfApprovalNotRequired(dbResponse);
      this.logger.debug(
        `Published to RMQ: requestorPID=${requestorPID}, requestorEmail=${requestorEmail}`,
      );

      return {
        id: dbResponse.id,
        serviceRequestId: dbResponse?.serviceRequestId,
        message: `Request submitted for processing`,
      };
    } catch (error) {
      this.logger.error(
        error,
        `Failed to create ZTP request`,
        error.stack || error.message,
      );
      throw error;
    }
  }

  async ztpBulkImport(buffer: Buffer) {
    const yamlContent = buffer.toString('utf-8');
    let parsed: CreateZtpRequestDTO;
    try {
      parsed = YAML.parse(yamlContent);
      this.logger.debug(
        `Parsed Yaml Content: ${JSON.stringify(parsed, null, 2)}`,
      );
    } catch (error) {
      this.logger.error(error, 'Failed to parse YAML content', error.stack);
      throw new BadRequestException('Invalid Yaml format');
    }

    const dto = plainToInstance(CreateZtpRequestDTO, parsed);
    const errors = validateSync(dto, {
      whitelist: true,
      forbidNonWhitelisted: true,
    });

    if (errors.length > 0) {
      this.logger.error('Validation failed', JSON.stringify(errors, null, 2));
      throw new BadRequestException(errors);
    }

    this.logger.log('ZTP bulk job file validated successfully');

    return {
      message: 'Bulk ZTP jobs validated and parsed',
      data: dto,
    };
  }
}
