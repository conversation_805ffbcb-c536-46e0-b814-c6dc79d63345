import { Body, Controller, Post, UseGuards, UsePipes, ValidationPipe } from '@nestjs/common';
import { GroupOnboardService } from './group-onboard.service';
import { AuthenticationGuard } from 'src/auth/authentication.guard';
import { ApiBearerAuth, ApiSecurity, ApiTags } from '@nestjs/swagger';
import { OnboardGroupRequest } from './dto/group-onboard.request.dto';

@Controller('onboard-group')
@ApiBearerAuth()
@ApiSecurity('x-nebula-authorization')
@ApiTags('Onboard Group')
@UseGuards(AuthenticationGuard)
@UsePipes(new ValidationPipe({ whitelist: true, stopAtFirstError: true }))
export class GroupOnboardController {
    constructor(private readonly groupOnboardService: GroupOnboardService) {}

    @Post('')
    async acceptOnboardGroupRequest(@Body() onboardGroupRequest: OnboardGroupRequest) {
        return this.groupOnboardService.acceptOnboardGroupRequest(onboardGroupRequest)
    }
}
