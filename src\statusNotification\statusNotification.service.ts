import { Injectable } from '@nestjs/common';
import { ApprovalsService } from '../approvals/approvals.service';
import { IamService } from '../iam/iam.service';
import { IntegrationNotificationService } from '../naas/integration.notification.service';
import { LoggerService } from '../loggers/logger.service';
import { RequestStatus, RequestType } from '../types';
import { ServiceRequestEntity } from '../naas/entities/serviceRequest.entity';
import { ActivityLoggerWrapperService } from '../activity-logs-wrapper/activity-logger-wrapper.service';
import * as uuid from 'uuid';

import { SubTypeApprovalsService } from '../approvals/sub-type-approval/sub-type-approvals.service';
import { ProcessStatus } from '../activity-logs/types/process-status.enum';
import { FirewallRequestDetailsRepository } from '../security/repository/firewallRequestDetails-repository';
import { FirewallRequestEntity } from '../security/entity/firewallRequest.entity';
import { JobStatusEnum } from '../security/schema/firewallRequest.schema';

@Injectable()
export class StatusNotificationService {
  constructor(
    private readonly integrationNotificationService: IntegrationNotificationService,
    private readonly iamService: IamService,
    private readonly approvalsService: ApprovalsService,
    private readonly logger: LoggerService,
    private readonly activityLoggerWrapperService: ActivityLoggerWrapperService,
    private readonly subTypeApprovalsService: SubTypeApprovalsService,
    private readonly firewallRequestRepository: FirewallRequestDetailsRepository,
  ) {}

  async createRequestStatusNotification(serviceRequest: ServiceRequestEntity) {
    //to send requestor notification
    await this.requestorNotification(
      serviceRequest,
      'Your request has been created',
      'CREATE',
    );

    if (serviceRequest.requestType === RequestType.DYNAMIC_ACCESS_POLICIES) {
      await this.activityLogs(serviceRequest);
    }

    if (
      serviceRequest.requestType !== RequestType.COMMON_FIREWALL_POLICY &&
      serviceRequest.requestType !== RequestType.FIREWALL_V2
    ) {
      //to send approver notification
      await this.approverNotificationDetails(serviceRequest);
    }
  }

  async updateRequestStatusNotification(serviceRequest: ServiceRequestEntity) {
    //to send requestor notification
    await this.requestorNotification(
      serviceRequest,
      'Your request status is changed to ' +
        serviceRequest?.status.toLowerCase(),
      'UPDATE',
    );

    if (
      serviceRequest?.requestType === RequestType.COMMON_FIREWALL_POLICY ||
      serviceRequest?.requestType === RequestType.DYNAMIC_ACCESS_POLICIES ||
      serviceRequest?.requestType === RequestType.FIREWALL_V2
    ) {
      if (serviceRequest.status === RequestStatus.PENDING_APPROVAL) {
        //to send approver notification
        await this.approverNotificationDetails(serviceRequest);
      }
      await this.activityLogs(serviceRequest);
    }
  }

  async activityLogs(serviceRequest: ServiceRequestEntity) {
    const eventStep = [
      {
        eventCode: 'NEB-EVENT-APPROVAL-1001',
        name: 'Approve Request - Nebula',
      },
    ];
    const traceId = uuid.v4();
    if (serviceRequest.status === RequestStatus.PENDING_APPROVAL) {
      await this.activityLoggerWrapperService.sendActivity(
        ProcessStatus.STARTED,
        eventStep,
        serviceRequest.serviceRequestId,
        {},
        traceId,
        0,
        serviceRequest,
        serviceRequest.requestType,
        [],
        undefined,
        undefined,
        undefined,
      );
    } else if (
      serviceRequest.status === RequestStatus.APPROVED ||
      serviceRequest.status === RequestStatus.REJECTED ||
      serviceRequest.status === RequestStatus.PARTIALLY_APPROVED ||
      serviceRequest.status === RequestStatus.PARTIALLY_REJECTED
    ) {
      await this.activityLoggerWrapperService.sendActivity(
        ProcessStatus.COMPLETED,
        eventStep,
        serviceRequest.serviceRequestId,
        {},
        traceId,
        0,
        serviceRequest,
        serviceRequest.requestType,
        [],
        undefined,
        'Service request updated successfully',
        undefined,
      );
    } else if (
      serviceRequest.status === RequestStatus.CLOSED_MANUALLY ||
      serviceRequest.status === RequestStatus.CANCELLED
    ) {
      await this.activityLoggerWrapperService.sendActivity(
        ProcessStatus.SKIPPED,
        eventStep,
        serviceRequest.serviceRequestId,
        {},
        traceId,
        0,
        serviceRequest,
        serviceRequest.requestType,
        [],
        undefined,
        undefined,
        undefined,
      );
    }
  }

  async requestorNotification(serviceRequest, message, action) {
    try {
      if (serviceRequest.status != RequestStatus.AUTO_APPROVED) {
        await this.integrationNotificationService.notifyServiceRequestStatusToIntegrationApi(
          serviceRequest,
          message,
          action,
        );
      } else {
        this.logger.log(
          `Skipping notifications for AUTO_APPROVED serviceRequest- ${serviceRequest.serviceRequestId}`,
        );
      }
    } catch (error) {
      this.logger.error(error, 'Failed to send notification');
    }
  }

  async approverNotification(serviceRequest, emailList) {
    console.log('statusNotifia', serviceRequest.requestType);

    const requestType =
      await this.integrationNotificationService.getNetworkRequestType(
        serviceRequest.requestType,
      );
    try {
      await this.integrationNotificationService.notifyApprovalServiceRequestStatusToIntegrationApi(
        serviceRequest,
        emailList,
        `${requestType.replace(/_/g, ' ')} request is submitted for approval`,
      );
    } catch (error) {
      this.logger.error(error, 'Failed to send notification for approvers');
    }
  }
  async approverSubRequestNotification(serviceRequest, subRequest, emailList) {
    const requestType =
      await this.integrationNotificationService.getNetworkRequestType(
        RequestType.FIREWALL_V2,
      );
    try {
      await this.integrationNotificationService.notifyApprovalOfFirewallSubRequestToIntegrationApi(
        serviceRequest,
        subRequest,
        emailList,
        `${requestType.replace(/_/g, ' ')} user invalidated request`,
      );
    } catch (error) {
      this.logger.error(
        error,
        'Failed to send sub request notification for approvers',
      );
      throw error;
    }
  }

  async firewallSubrequestNotification(serviceRequest, subRequest) {
    try {
      this.logger.log('sending firewall sub request notification');
      const organizationName = subRequest?.organizationName;
      const emailArray = [];
      //get approvalDetails from approvals collection
      this.logger.log('getting approval details');

      const approvalDetailsResponse =
        await this.subTypeApprovalsService.findOneByRequestType(
          RequestType.FIREWALL_V2,
        );

      this.logger.log('fetched aprroval details response');

      for (const approval of approvalDetailsResponse?.subTypeToGroupMap) {
        if (approval?.subType === organizationName) {
          const group = await this.iamService.findGroupByGroupName(
            approval.groups.toString(),
          );

          if (group?.emailDistribution?.length) {
            emailArray.push(...group.emailDistribution);
          }

          this.logger.log('recieved email list', emailArray);

          if (emailArray?.length) {
            await this.approverSubRequestNotification(
              serviceRequest,
              subRequest,
              emailArray.join(','),
            );
          } else {
            this.logger.debug(
              'email array is empty, email notification cannot be sent',
            );
          }
        }
      }
    } catch (error) {
      this.logger.error(
        error,
        'Failed to send sub request notification for approvers',
      );
      throw error;
    }
  }

  async approverNotificationDetails(serviceRequest: ServiceRequestEntity) {
    let subRequests = [];
    try {
      //get catalog level 4 using request to check whether whether approval is need or not
      const catalogLevel4 =
        await this.iamService.findCatalogLevel4ItemByRequestType(
          serviceRequest.requestType,
        );
      if (catalogLevel4?.approvalRequired) {
        //if approval required is true then only send notifications
        this.logger.debug(
          `sending approval email notification for ${serviceRequest.serviceRequestId}`,
        );
        const emailArray = [];
        //get approvalDetails from approvals collection
        const approvalDetailsResponse =
          await this.approvalsService.findApprovalByRequestTypeAndDomainId(
            serviceRequest.requestType,
            serviceRequest.payload['platformContext']?.domainId,
          );

        if (serviceRequest.requestType === RequestType.FIREWALL_V2) {
          this.logger.log(`fetch pending approval subrequests`);
          subRequests = await this.fetchPenidngApprovalSubRequest(
            serviceRequest.serviceRequestId,
          );
          const applicableGroups = await this.fetchApplicableSubTypeGroups(
            serviceRequest,
            subRequests,
          );
          approvalDetailsResponse.approvalDetails =
            approvalDetailsResponse.approvalDetails.filter((approval) =>
              applicableGroups.includes(approval.groupName),
            );

          this.logger.log(
            `applicable groups after filtering using appicable subtypes`,
            JSON.stringify(
              approvalDetailsResponse.approvalDetails.map(
                (ele) => ele.groupName,
              ),
            ),
          );
        }

        //find lowest level and filter approvaldetails array
        const levelsArray: any = approvalDetailsResponse.approvalDetails?.map(
          (approval) => {
            return approval.level;
          },
        );
        this.logger.debug(
          `level required for ${serviceRequest?.requestType} -> ${JSON.stringify(levelsArray)}`,
        );
        const minLevel = Math.min(...levelsArray);
        this.logger.debug(`min level found ${minLevel}`);
        const approvalsArray = approvalDetailsResponse.approvalDetails?.filter(
          (approval) => {
            return approval.level === minLevel;
          },
        );
        this.logger.debug(
          `approvals array after filtering with min level ${JSON.stringify(approvalsArray)}`,
        );
        for (const approval of approvalsArray) {
          const group = await this.iamService.findGroupByGroupName(
            approval.groupName,
          );
          if (group?.emailDistribution?.length) {
            emailArray.push(...group.emailDistribution);
          }
        }
        this.logger.debug('Array ', JSON.stringify(emailArray));
        if (emailArray?.length) {
          await this.approverNotification(serviceRequest, emailArray.join(','));
          if (serviceRequest.requestType === RequestType.FIREWALL_V2) {
            await this.updateNotificationDetails(
              subRequests,
              JobStatusEnum.SUCCESS,
              minLevel,
              serviceRequest,
            );
          }
        } else {
          this.logger.debug(
            `email array is empty, email notification cannot be sent for ${serviceRequest.serviceRequestId}`,
          );
        }
      }
    } catch (err) {
      this.logger.error(
        err,
        `Error while sending notification to approvers for ${serviceRequest.serviceRequestId}`,
      );
      if (serviceRequest.requestType === RequestType.FIREWALL_V2) {
        await this.updateNotificationDetails(
          subRequests,
          JobStatusEnum.FAILURE,
          null,
          serviceRequest,
        );
      }
    }
  }

  async firewallV2RequestorNotification(
    serviceRequest,
    subRequest,
    email,
    approveOrRejectRequestDto,
    approvalGroup,
  ) {
    const requestType =
      await this.integrationNotificationService.getNetworkRequestType(
        RequestType.FIREWALL_V2,
      );
    try {
      await this.integrationNotificationService.notifyApproverCommentToIntegrationApi(
        serviceRequest,
        subRequest,
        email,
        approveOrRejectRequestDto,
        `${requestType.replace(/_/g, ' ')} approver commented on the request`,
        approvalGroup,
      );
    } catch (error) {
      this.logger.error(
        error,
        'Failed to send sub request notification for approvers',
      );
      throw error;
    }
  }

  async fetchPenidngApprovalSubRequest(requestId: string) {
    try {
      this.logger.log(`Fetching pending approval subrequest for ${requestId}`);
      const subRequests =
        await this.firewallRequestRepository.findPendingApprovalSubRequests(
          requestId,
          true,
        );
      return subRequests;
    } catch (err) {
      this.logger.error(
        err,
        `Error while fetching pending approval subrequests for ${requestId}`,
      );
      return [];
    }
  }

  async fetchApplicableSubTypeGroups(
    serviceRequest: ServiceRequestEntity,
    subRequests: FirewallRequestEntity[],
  ) {
    this.logger.log(
      `fetching subtype approval for ${serviceRequest?.requestType}`,
    );
    const subTypeApproval =
      await this.subTypeApprovalsService.findOneByRequestType(
        serviceRequest?.requestType,
      );

    const orgNames = [
      ...new Set(subRequests.map((ticket) => ticket.organizationName)),
    ];

    const applicableSubTypeApprovalsGroups = subTypeApproval.subTypeToGroupMap
      .filter((subTypeApproval) => orgNames.includes(subTypeApproval.subType))
      .flatMap((subTypeApproval) => subTypeApproval.groups);

    return applicableSubTypeApprovalsGroups;
  }

  async updateNotificationDetails(
    subRequests,
    status: JobStatusEnum,
    level,
    serviceRequest,
  ) {
    const date = new Date();
    for (const subRequest of subRequests) {
      try {
        await this.firewallRequestRepository.updateApprovalNotifcation(
          {
            notificationStatus: status,
            notifiedAt: date,
            notifiedLevel: level,
            notifiedGroup: await this.fetchApplicableSubTypeGroups(
              serviceRequest,
              [subRequest],
            ),
          },
          subRequest.subRequestId,
        );
      } catch (err) {
        this.logger.error(
          err,
          `Error while updating approval notification details for ${subRequest.subRequestId}`,
        );
      }
    }
  }
}
