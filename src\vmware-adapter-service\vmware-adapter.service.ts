import { AxiosInstance } from 'axios';
import {
  BadRequestException,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { withResponseErrorHandler } from '../utils/helpers';
import { GetNetworksMorpheusResponseDto } from '../compute-wrapper/dto/getNetworksMorpheus.response.dto';
import { ConfigService } from '@nestjs/config';
import { ENVIRONMENT_VARS } from '../utils/constants';
import {
  HostNameType,
  ValidateHostNamesRequestDto,
} from '../compute-wrapper/dto/virtual.machine.request.dto';
import { LoggerService } from '../loggers/logger.service';
import { VMwareConsoleDTO } from 'src/compute/dto/compute.dto';
import { ErrorDetails } from 'src/vmware-error-loggers/vmware-error-logger.service';
import { ERROR_DETAILS } from 'src/vmware-error-loggers/vmware-error-details';

@Injectable()
export class VmwareAdapterService {
  private serviceRequestId: string | null = null;
  constructor(
    @Inject('VMWARE_ADAPTER_SERVICE_API')
    private readonly vmwareApi: AxiosInstance,
    private readonly configService: ConfigService,
    private readonly logger: LoggerService,
    private readonly vmwareErrorlogService: ErrorDetails,
  ) {}

  async createVm(domain, datacenter, payload) {
    this.logger.log(
      'calling vmware-adapter-service api to create vm for datacenter - ',
      datacenter,
    );
    return withResponseErrorHandler(
      this.vmwareApi.post(
        `domain/${domain}/datacenter/${datacenter}/vm/provision`,
        payload,
      ),
    );
  }

  async fetchResources(domain: string, datacenter: string, cloudId: string) {
    this.logger.log('Vmware-fetch-resources');
    const maxResources = this.configService.get<number>(
      ENVIRONMENT_VARS.VMWARE_MAX_CLUSTERES,
    );
    this.logger.log('maxResources count:', maxResources);
    return await withResponseErrorHandler(
      this.vmwareApi.get(
        `/domain/${domain}/datacenter/${datacenter}/clusters/${cloudId}`,
        {
          params: { max: maxResources },
        },
      ),
    );
  }
  async fetchResourcesByNetworkId(
    domain: string,
    datacenter: string,
    cloudId: string,
    networkId: string,
  ) {
    this.logger.log('calling Vmware-fetch-resources by networkid');

    return await withResponseErrorHandler(
      this.vmwareApi.get(
        `/domain/${domain}/datacenter/${datacenter}/clusters/${cloudId}/network/${networkId}`,
      ),
    );
  }

  async fetchVMwareNetwork(
    domain: string,
    datacenter: string,
    networkId: string,
  ) {
    this.logger.log('Vmware-fetch-networks:', networkId);

    return await withResponseErrorHandler(
      this.vmwareApi.get(
        `/domain/${domain}/datacenter/${datacenter}/networks/${networkId}`,
      ),
    );
  }

  async fetchLayouts(domain: string, datacenter: string, catalogId: string) {
    this.logger.log('Vmware-fetch-layouts');
    const maxLayouts = this.configService.get<number>(
      ENVIRONMENT_VARS.VMWARE_MAX_LAYOUTS,
    );
    this.logger.log('maxLayouts count:', maxLayouts);
    return await withResponseErrorHandler(
      this.vmwareApi.get(
        `/domain/${domain}/datacenter/${datacenter}/layouts/catalog/${catalogId}`,
        {
          params: { max: maxLayouts },
        },
      ),
    );
  }

  getGeneratedHostNames(
    validateHostNamesRequestDto: ValidateHostNamesRequestDto,
  ) {
    let allGeneratedHostNames = [];
    const hostPrefix = validateHostNamesRequestDto.hostName;
    const vmCount = validateHostNamesRequestDto.vmCount;
    const sequenceNumber = validateHostNamesRequestDto.sequenceNumber;

    this.logger.log(
      'validateHostNamesRequestDto :',
      validateHostNamesRequestDto,
    );

    if (validateHostNamesRequestDto.type === HostNameType.VM) {
      if (
        vmCount == 1 &&
        (sequenceNumber == null || sequenceNumber == undefined)
      ) {
        this.logger.debug(
          `Generating ${vmCount} hostnames from prefix ${hostPrefix}`,
        );
        allGeneratedHostNames.push(hostPrefix);
      } else {
        if (
          sequenceNumber + vmCount >
          this.configService.get(ENVIRONMENT_VARS.VM_SEQUENCE_NUMBER_MAX_VALUE)
        ) {
          throw new BadRequestException(
            `Too many VMs with similar name. Sequence number can never be greater than ${this.configService.get(ENVIRONMENT_VARS.VM_SEQUENCE_NUMBER_MAX_VALUE)}`,
          );
        }
        this.logger.debug(
          `Generating ${vmCount} hostnames of prefix ${hostPrefix} from sequenceNumber ${sequenceNumber}`,
        );
        for (let i = sequenceNumber; i < sequenceNumber + vmCount; i++) {
          const generatedHostname =
            hostPrefix + '-' + i.toString().padStart(2, '0');
          allGeneratedHostNames.push(generatedHostname);
        }
      }
    } else {
      allGeneratedHostNames = this.getDbHostName(
        validateHostNamesRequestDto.hostName,
        validateHostNamesRequestDto.sequenceNumber,
        validateHostNamesRequestDto.vmCount,
      );
    }

    this.logger.log('allGeneratedHostNames :', allGeneratedHostNames);
    return allGeneratedHostNames;
  }

  getDbHostName(hostPrefix: string, sequenceNumber: number, vmCount: number) {
    this.logger.log('generating db hostnames for hostprefix:', hostPrefix);
    const allGeneratedHostNames = [];
    for (let i = sequenceNumber; i < sequenceNumber + vmCount; i++) {
      const serverCount = i.toString().padStart(3, '0');
      const generatedHostname = hostPrefix.replaceAll(
        '<SERVER_COUNT>',
        serverCount,
      );
      allGeneratedHostNames.push(generatedHostname);
    }
    this.logger.log('db generated hostnames :', allGeneratedHostNames);
    return allGeneratedHostNames;
  }

  async getSuggestedSequenceNumber(
    domain: string,
    datacenter: string,
    validateHostNamesRequestDto: ValidateHostNamesRequestDto,
  ) {
    this.logger.log(
      "Generating random sequenceNumber then will check it's validity",
    );
    const newSequenceNumber = validateHostNamesRequestDto.sequenceNumber + 1;
    const newValidateHostNamesRequestDto: ValidateHostNamesRequestDto = {
      hostName: validateHostNamesRequestDto.hostName,
      vmCount: validateHostNamesRequestDto.vmCount,
      sequenceNumber: newSequenceNumber,
      type: validateHostNamesRequestDto.type,
    };
    this.logger.log(
      'newValidateHostNamesRequestDto for validation',
      newValidateHostNamesRequestDto,
    );
    const checkvCenterForValidityResponse =
      await this.checkVmwareForHostNamesValidity(
        domain,
        datacenter,
        newValidateHostNamesRequestDto,
      );
    if (checkvCenterForValidityResponse.isValid) {
      return {
        newSequenceNumber: newSequenceNumber,
        hostNames: checkvCenterForValidityResponse.hostNames,
      };
    }
    return { newSequenceNumber: -1, hostNames: [] };
  }

  async checkVmwareForHostNamesValidity(
    domain: string,
    datacenter: string,
    validateHostNamesRequestDto: ValidateHostNamesRequestDto,
  ) {
    const hostPrefix = validateHostNamesRequestDto.hostName;
    const vmCount = validateHostNamesRequestDto.vmCount;
    const sequenceNumber = validateHostNamesRequestDto.sequenceNumber;
    let availableHostNames: string[] = [];
    let attempts = 0;
    const maxAttempts =
      this.configService.get<number>(
        ENVIRONMENT_VARS.SEARCH_SEQUENCE_NUMBER_RETRY_COUNT,
      ) || 50;

    // Check if the original batch is fully available
    let originalBatchAvailable = true;
    for (let i = 0; i < vmCount; i++) {
      const hostName = `${hostPrefix}-${(sequenceNumber + i).toString().padStart(2, '0')}`;

      const vCenterResponse = await withResponseErrorHandler(
        this.vmwareApi.get(
          `domain/${domain}/datacenter/${datacenter}/vms?hostName=${hostName}`,
        ),
      );
      let vms: any[] = [];
      if (Array.isArray(vCenterResponse)) {
        vms = vCenterResponse;
      }
      const exists = vms.some((e) => e.hostName === hostName);
      if (exists) {
        originalBatchAvailable = false;
        break;
      }
    }

    // Always return the next N available host names by checking each candidate
    availableHostNames = [];
    let nextSeq = sequenceNumber;
    attempts = 0;
    while (availableHostNames.length < vmCount && attempts < maxAttempts) {
      const hostName = `${hostPrefix}-${nextSeq.toString().padStart(2, '0')}`;
      const vCenterResponse = await withResponseErrorHandler(
        this.vmwareApi.get(
          `domain/${domain}/datacenter/${datacenter}/vms?hostName=${hostName}`,
        ),
      );

      let vms: any[] = [];
      if (Array.isArray(vCenterResponse)) {
        vms = vCenterResponse;
      }
      const exists = vms.some((e) => e.hostName === hostName);
      if (!exists) {
        availableHostNames.push(hostName);
      }
      nextSeq++;
      attempts++;
    }
    return {
      isValid: originalBatchAvailable,
      hostNames: availableHostNames,
      suggestedSequenceNumber: nextSeq,
    };
  }

  async checkVcenterForHostNamesValidityWithoutSequenceNumber(
    domain: string,
    datacenter: string,
    validateHostNamesRequestDto: ValidateHostNamesRequestDto,
  ) {
    const allGeneratedHostNames = this.getGeneratedHostNames(
      validateHostNamesRequestDto,
    );
    const availableHostNames: string[] = [];
    let allExist = true;

    for (const hostName of allGeneratedHostNames) {
      let vCenterResponse;

      if (validateHostNamesRequestDto.type === HostNameType.VM) {
        vCenterResponse = await withResponseErrorHandler(
          this.vmwareApi.get(
            `domain/${domain}/datacenter/${datacenter}/vms?hostName=${hostName}`,
          ),
        );

        if (
          !vCenterResponse ||
          vCenterResponse.length === 0 ||
          !vCenterResponse.some((e) => e.hostName === hostName)
        ) {
          availableHostNames.push(hostName);
          allExist = false;
        }
      } else {
        const dbNameToBeSearched = hostName.replaceAll('<SERVER_COUNT>', '%25');
        vCenterResponse = await withResponseErrorHandler(
          this.vmwareApi.get(
            `domain/${domain}/datacenter/${datacenter}/vms?hostName=${dbNameToBeSearched}`,
          ),
        );
        if (
          !vCenterResponse ||
          vCenterResponse.length === 0 ||
          !vCenterResponse.some((e) => e.name === hostName)
        ) {
          availableHostNames.push(hostName);
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          allExist = false;
        }
      }
    }
    const isValid = availableHostNames.length === allGeneratedHostNames.length;
    return { isValid, hostNames: availableHostNames };
  }

  async validateHostNames(
    domain: string,
    datacenter: string,
    validateHostNamesRequestDto: ValidateHostNamesRequestDto,
  ) {
    try {
      if (validateHostNamesRequestDto.type === HostNameType.DBaaS) {
        validateHostNamesRequestDto.sequenceNumber = 1;
      }

      this.logger.log(`hostNames to be validated after Vcenter call`);
      let checkVcenterForValidityResponse;
      if (validateHostNamesRequestDto.vmCount > 1) {
        try {
          checkVcenterForValidityResponse =
            await this.checkVmwareForHostNamesValidity(
              domain,
              datacenter,
              validateHostNamesRequestDto,
            );
        } catch (error) {
          this.vmwareErrorlogService.logError(
            error,
            'checkVmwareForHostNamesValidity',
            ERROR_DETAILS.VMWARE_VALIDATE_HOSTNAME_ERROR_CODE,
            ERROR_DETAILS.VMWARE_VALIDATE_HOSTNAME_ERROR_MESSAGE,
            this.serviceRequestId,
          );
        }
      } else {
        try {
          checkVcenterForValidityResponse =
            await this.checkVcenterForHostNamesValidityWithoutSequenceNumber(
              domain,
              datacenter,
              validateHostNamesRequestDto,
            );
        } catch (error) {
          this.vmwareErrorlogService.logError(
            error,
            'checkVcenterForHostNamesValidityWithoutSequenceNumber',
            ERROR_DETAILS.VMWARE_VALIDATE_HOSTNAME_ERROR_CODE,
            ERROR_DETAILS.VMWARE_PROVISION_VCENTER_VALIDITY_WITHOUT_SEQUENCE_ERROR_MESSAGE,
            this.serviceRequestId
          );
        }
      }
      const isValid = checkVcenterForValidityResponse.isValid;
      let hostNames = checkVcenterForValidityResponse.hostNames;

      let suggestedSequenceNumber;
      if ('suggestedSequenceNumber' in checkVcenterForValidityResponse) {
        suggestedSequenceNumber =
          checkVcenterForValidityResponse.suggestedSequenceNumber -
          hostNames.length;
      }

      if (!isValid) {
        let sequenceNumberSearchCount = 0;

        while (
          sequenceNumberSearchCount <
          this.configService.get<number>(
            ENVIRONMENT_VARS.SEARCH_SEQUENCE_NUMBER_RETRY_COUNT,
          )
        ) {
          this.logger.log(
            'Since hostNames are unavailable, trying to get a valid sequenceNumber',
          );

          let getSuggestedSequenceNumberResponse;
          try {
            getSuggestedSequenceNumberResponse =
              await this.getSuggestedSequenceNumber(
                domain,
                datacenter,
                validateHostNamesRequestDto,
              );
          } catch (error) {
            this.vmwareErrorlogService.logError(
              error,
              'getSuggestedSequenceNumber',
              ERROR_DETAILS.VMWARE_VALIDATE_HOSTNAME_ERROR_CODE,
              ERROR_DETAILS.VMWARE_PROVISION_GET_SEQUENCE_ERROR_MESSAGE,
              this.serviceRequestId
            );
          }

          suggestedSequenceNumber =
            getSuggestedSequenceNumberResponse.newSequenceNumber;
          validateHostNamesRequestDto.sequenceNumber = suggestedSequenceNumber;
          this.logger.debug(
            `recieved ${suggestedSequenceNumber} as suggestion, checking it's validity`,
          );
          hostNames = getSuggestedSequenceNumberResponse.hostNames;
          sequenceNumberSearchCount++;
        }
      }

      return {
        isValid,
        hostNames,
        suggestedSequenceNumber,
      };
    } catch (error) {
      this.vmwareErrorlogService.logError(
        error,
        'validateHostNames',
        ERROR_DETAILS.VMWARE_VALIDATE_HOSTNAME_ERROR_CODE,
        ERROR_DETAILS.VMWARE_VALIDATE_HOSTNAME_ERROR_MESSAGE,
        this.serviceRequestId
      );
    }
  }

  async getVMStatus(
    domain: string,
    datacenter: string,
    requestId: string,
    trackingId: string,
  ) {
    try {
      const processes = await withResponseErrorHandler(
        this.vmwareApi.get(
          `/domain/${domain}/datacenter/${datacenter}/vm/provision-status/request/${requestId}/trackingId/${trackingId}`,
        ),
      );
      this.logger.log('processes :', processes);
      return processes;
    } catch (err) {
      if (err.response?.status === 404) {
        throw new NotFoundException(
          `Cannot find vm creation status with requestId ${requestId} and taskId ${trackingId}`,
        );
      }
      throw err;
    }
  }

  async getVmDetails(
    domain: string,
    datacenter: string,
    requestId: string,
    taskId: string,
  ) {
    try {
      const processes = await withResponseErrorHandler(
        this.vmwareApi.get(
          `/datacener/${domain}/datacenter/${datacenter}/vm/vm-details/request/${requestId}/task/${taskId}`,
        ),
      );
      this.logger.log('processes :', processes);
      return processes;
    } catch (err) {
      if (err.response?.status === 404) {
        throw new NotFoundException(
          `Cannot find vm details with requestId ${requestId} and taskId ${taskId}`,
        );
      }
      throw err;
    }
  }

  async getNetworks(cloudIds?: number[]) {
    const maxNetworks = this.configService.get<number>(
      ENVIRONMENT_VARS.VMWARE_MAX_NETWORKS,
    );
    this.logger.log('maxNetworks :', maxNetworks);
    const res: GetNetworksMorpheusResponseDto = (
      await this.vmwareApi.get('networks', {
        params: { max: maxNetworks },
      })
    ).data;
    if (!cloudIds) return res.networks;

    const cloudIdsSet = new Set(cloudIds);
    return res.networks.filter((network) => cloudIdsSet.has(network.zone?.id));
  }

  async getCurrentStatus(
    domain: string,
    datacenter: string,
    cloudId: string,
    hostName: string,
  ) {
    this.logger.log('fatching latest status of VMWare Vms');

    const latestStatus = await withResponseErrorHandler(
      this.vmwareApi.get(
        `domain/${domain}/datacenter/${datacenter}/cloud/${cloudId}/vm/details/hostname/${hostName}`,
      ),
    );
    this.logger.log('latest status of the VM is:', latestStatus);
    return latestStatus;
  }

  async getDnsDetails(domain: string, datacenter: string) {
    this.logger.log('calling vmware service to get dns details');
    const dnsDetails = await withResponseErrorHandler(
      this.vmwareApi.get(
        `domain/${domain}/datacenter/${datacenter}/dns?defaultDomain=true`,
      ),
    );
    this.logger.log('dns details received from vmware in vmware-adapter');
    return dnsDetails;
  }

  async getVMWareConsoleDetails(
    domain: string,
    datacenter: string,
    cloudId: string,
    vmwareConsoleDTO: VMwareConsoleDTO,
  ) {
    this.logger.log('fetching console details of VMWare Vms');
    return await withResponseErrorHandler(
      this.vmwareApi.post(
        `domain/${domain}/datacenter/${datacenter}/cloud/${cloudId}/vm/console`,
        vmwareConsoleDTO,
      ),
    );
  }
}
