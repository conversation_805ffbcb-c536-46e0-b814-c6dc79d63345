import { Type } from 'class-transformer';
import {
  IsBoolean,
  IsIn,
  IsIP,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Matches,
  ValidateNested,
} from 'class-validator';
import { PlatformType } from '../types/ztp.enum';

class VariablesDto {
  @IsOptional()
  @IsNumber()
  as?: number;

  @IsOptional()
  @IsBoolean()
  pim?: boolean;

  @IsOptional()
  @IsBoolean()
  netconf?: boolean;
}

export class ZtpRequestDTO {
  @IsString()
  @IsNotEmpty()
  @Matches(/^[a-zA-z0-9]+\.(netops)\.(charter)\.com$/, {
    message: 'Hostname must match the pattern: *.netops.charter.com',
  })
  hostname: string;

  @IsString()
  @IsNotEmpty()
  @IsIP('6', { message: 'Must be a valid IPv6 address' })
  ipv6_address: string;

  @IsOptional()
  @IsString()
  @IsIP('4', { each: true })
  ipv4_address: string;

  @IsString()
  @IsNotEmpty()
  serial_number: string;

  @IsString()
  @IsNotEmpty()
  @IsIn(Object.values(PlatformType), {
    message: 'Enter the valid platformtype',
  })
  platform: string;

  @IsString()
  @IsOptional()
  role?: string;

  @IsOptional()
  createdBy?: string;

  @IsOptional()
  requesterEmail?: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => VariablesDto)
  variables?: VariablesDto;
}
