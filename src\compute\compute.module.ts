import { Module } from '@nestjs/common';
import { RequestContextModule } from 'nestjs-request-context';
import { LoggerModule } from '../loggers/logger.module';
import { ScienceLogicModule } from 'src/scienceLogic/scienceLogic.module';
import { ApprovalsModule } from '../approvals/approvals.module';
import { CachingModule } from '../caching/caching.module';
import { ComputeWrapperModule } from '../compute-wrapper/compute-wrapper.module';
import { NebulaConfigRepository } from '../dbaas/nebulaConfig.repository';
import { EncryptionService } from '../encryption/encryption.service';
import { IpmModule } from '../ipm/ipm.module';
import { ItentialModule } from '../itential/itential.module';
import { IntegrationNotificationModule } from '../naas/integration.notification.module';
import { StatusNotificationModule } from '../statusNotification/statusNotification.module';
import { ComputeController } from './compute.controller';
import { ComputeService } from './compute.service';
import { DatacentersRepository } from './datacenters.repository';
import { VmTemplateConfigRepository } from './vmTemplateConfig.repository';
import { RmqModule } from 'src/rmq/rmq.module';
import { ResourcesModule } from 'src/resources/resources.module';
import { ErrorDetails } from 'src/vmware-error-loggers/vmware-error-logger.service';

@Module({
  imports: [
    LoggerModule,
    RequestContextModule,
    ItentialModule,
    ScienceLogicModule,
    ComputeWrapperModule,
    IntegrationNotificationModule,
    CachingModule,
    ApprovalsModule,
    StatusNotificationModule,
    IpmModule,
    RmqModule,
    ResourcesModule,
  ],
  controllers: [ComputeController],
  providers: [
    EncryptionService,
    ComputeService,
    VmTemplateConfigRepository,
    NebulaConfigRepository,
    DatacentersRepository,
    ErrorDetails,
  ],
  exports: [ComputeService],
})
export class ComputeModule {}
