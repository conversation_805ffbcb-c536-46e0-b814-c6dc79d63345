import { Module } from '@nestjs/common';
import { AwsCherwellService } from './aws-cherwell.service';
import { AwsCherwellController } from './aws-cherwell.controller';
import { LoggerModule } from '../loggers/logger.module';
import { TicketManagementWrapperModule } from '../ticket-management-service-wrapper/ticket-management-wrapper-module';
import { IntegrationNotificationModule } from '../naas/integration.notification.module';

@Module({
  imports: [
    LoggerModule,
    TicketManagementWrapperModule,
    IntegrationNotificationModule,
  ],
  controllers: [AwsCherwellController],
  providers: [AwsCherwellService],
  exports: [AwsCherwellService],
})
export class AwsCherwellModule {}
