import { Inject, Injectable } from '@nestjs/common';
import { AxiosInstance } from 'axios';
import { withResponseErrorHandler } from '../utils/helpers';
import { LoggerService } from '../loggers/logger.service';
import { GenericRetryTicketRequestDto } from '../security/firewallv2/dto/retryTicketDetails.dto';
import { RETRY_TICKET_ACTIONS } from '../types';
import {
  AddCommentRequestDTO,
  AddCommentResponsetDTO,
  GetMopIdFromCrqResponseDto,
  ValidateCrqDto,
  ValidateCrqTicketRequestDto,
} from '../ticket-management/dto/ticket-management.dto';

@Injectable()
export class TicketManagementWrapperService {
  constructor(
    @Inject('TICKET_MANAGEMENT_SERVICE_API')
    private readonly ticketManagementServiceApi: AxiosInstance,
    private readonly logger: LoggerService,
  ) {}

  async closeTicket(data): Promise<any> {
    this.logger.log(`Going to close ticket. Data: ${JSON.stringify(data)}`);
    const response = await withResponseErrorHandler(
      this.ticketManagementServiceApi.put(`v1/tickets/complete`, data),
    );
    this.logger.log(`Ticket management response: ${JSON.stringify(response)}`);
    return response;
  }

  async cancelTicket(data): Promise<any> {
    this.logger.log(`Going to cancel ticket. Data: ${JSON.stringify(data)}`);
    const response = withResponseErrorHandler(
      this.ticketManagementServiceApi.put(`v1/tickets/cancel`, data),
    );
    this.logger.log(`Ticket management response: ${JSON.stringify(response)}`);
    return response;
  }

  async callInTicket(data): Promise<any> {
    this.logger.log(`Going to call In ticket. Data: ${JSON.stringify(data)}`);
    const response = withResponseErrorHandler(
      this.ticketManagementServiceApi.put(`v1/tickets/call-in`, data),
    );
    this.logger.log(`Ticket call in response: ${JSON.stringify(response)}`);
    return response;
  }

  async authorizeTicket(data): Promise<any> {
    this.logger.log(`Going to authorize ticket. Data: ${JSON.stringify(data)}`);
    const response = withResponseErrorHandler(
      this.ticketManagementServiceApi.get(`v1/tickets/authorize/${data.ticketingSystem}/${data.ticketId}
        `, data,
      ),
    );
    this.logger.log(`Ticket authorization response: ${JSON.stringify(response)}`);
    return response;
  }

  async getTicketDetails(data): Promise<any> {
    this.logger.log(`fetching ticket Details. Data: ${JSON.stringify(data)}`);
    const response = withResponseErrorHandler(
      this.ticketManagementServiceApi.get(`v1/tickets/${data.ticketingSystem}/${data.ticketId}`),
    );
    this.logger.log(`Ticket Details response: ${JSON.stringify(response)}`);
    return response;
  }

  async retryTicket(data: GenericRetryTicketRequestDto): Promise<any> {
    if (data.action === RETRY_TICKET_ACTIONS.CREATED) {
      this.logger.log(
        `Going to retry ${data.ticketingSystem} create ticket. Data: ${JSON.stringify(data)}`,
      );
      const response = await withResponseErrorHandler(
        this.ticketManagementServiceApi.post(`v1/tickets`, data),
      );
      this.logger.log(
        `Retry ${data.ticketingSystem} Create Ticket response: ${JSON.stringify(response)}`,
      );
      return response;
    } else if (data.action === RETRY_TICKET_ACTIONS.CANCELLED) {
      const { ticketingSystem, ticketId, cancelReason } = data;
      const retryData = { ticketingSystem, ticketId, cancelReason };
      this.logger.log(
        `Going to retry cancel ticket. Data: ${JSON.stringify(retryData)}`,
      );
      const response = await withResponseErrorHandler(
        this.ticketManagementServiceApi.put(`v1/tickets/cancel`, retryData),
      );
      this.logger.log(
        `Ticket management Retry Cancel response: ${JSON.stringify(response)}`,
      );
      return response;
    } else if (data.action === RETRY_TICKET_ACTIONS.CLOSED) {
      const { ticketingSystem, ticketId, completeReason } = data;
      const retryData = { ticketingSystem, ticketId, completeReason };
      this.logger.log(
        `Going to retry close ticket. Data: ${JSON.stringify(retryData)}`,
      );
      const response = await withResponseErrorHandler(
        this.ticketManagementServiceApi.put(`v1/tickets/complete`, retryData),
      );
      this.logger.log(
        `Ticket management Retry Close response: ${JSON.stringify(response)}`,
      );
      return response;
    }
  }

  async validateCrqTicket(
    validateCrqTicketDto: ValidateCrqTicketRequestDto,
  ): Promise<ValidateCrqDto> {
    return await withResponseErrorHandler(
      this.ticketManagementServiceApi.post(
        `v1/tickets/validate-crq`,
        validateCrqTicketDto,
      ),
    );
  }

  async getMopIdFromCrqTicket(
    crqTicketDto: ValidateCrqTicketRequestDto,
  ): Promise<GetMopIdFromCrqResponseDto> {
    return await withResponseErrorHandler(
      this.ticketManagementServiceApi.post(
        `v1/tickets/get-mopId-from-crq`,
        crqTicketDto,
      ),
    );
  }

  async addCommentsInCrqTicket(
    addCrqCommentDto: AddCommentRequestDTO,
  ): Promise<AddCommentResponsetDTO> {
    return await withResponseErrorHandler(
      this.ticketManagementServiceApi.post(
        `v1/tickets/comments`,
        addCrqCommentDto,
      ),
    );
  }
}
