const session = db.getMongo().startSession();

session.startTransaction();
try {
  const catalog1 = db.menvcataloglevel04.findOne({ shortName: 'linux8&9v3' });
  if (!catalog1 || !catalog1._id) {
    throw Error('Catalog "linux8&9v3" not found');
  }

  const doc1 = {
    name: 'Linux 8 & 9 V3',
    type: 'VIRTUALMACHINE',
    shortName: 'linux8&9v3',
    config: {
      nebulaToMorpheusFieldMap: {
        targetLayout: 'linux89TargetLayouts',
        targetImage: 'linux89TargetImage',
        artifactory: 'artifactory',
      },
      defaultQuota: {
        storage: 965780,
        cpu: 66310,
        memory: 142780,
      },
      size: {
        small: {
          customMemory: 8,
          root: 20,
          home: 4,
          opt: 8,
          var: 12,
          var_log: 4,
          var_log_audit: 4,
          tmp: 4,
          var_tmp: 4,
          customCores: 4,
          total: 60,
          vmSequence: 1,
        },
        medium: {
          customMemory: 16,
          root: '20',
          home: '8',
          opt: '16',
          var: '24',
          var_log: '8',
          var_log_audit: '8',
          var_tmp: '8',
          tmp: '8',
          customCores: 8,
          total: 100,
          vmSequence: 2,
        },
        large: {
          customMemory: 16,
          root: 32,
          home: 32,
          opt: 16,
          var: 32,
          var_log: 32,
          var_log_audit: 32,
          tmp: 32,
          var_tmp: 32,
          customCores: 8,
          total: 240,
          vmSequence: 3,
        },
      },
      memory: [
        { option: 4 },
        { option: 8 },
        { option: 16 },
        { option: 32 },
        { option: 64 },
      ],
      noOfCores: [{ option: 4 }, { option: 8 }, { option: 16 }, { option: 28 }],
      operatingSystem: 'Linux 8 & 9',
    },
    isDeleted: false,
    catalogId: catalog1._id,
    displayName: 'Linux 8 & 9 V3',
  };

  db.nebulaconfigs.insertOne(doc1);
  print(`Inserted 'Linux 8 & 9 V3' config.`);

  const catalog2 = db.menvcataloglevel04.findOne({
    shortName: 'linux8&9v3admin',
  });
  if (!catalog2 || !catalog2._id) {
    throw Error('Catalog "linux8&9v3admin" not found');
  }

  const doc2 = {
    name: 'Linux 8 & 9 V3 Admin',
    type: 'VIRTUALMACHINE',
    shortName: 'linux8&9v3admin',
    config: doc1.config,
    isDeleted: false,
    catalogId: catalog2._id,
    updatedAt: new Date(),
    displayName: 'Linux 8 & 9 V3',
  };

  db.nebulaconfigs.insertOne(doc2);
  print(`Inserted 'Linux 8 & 9 V3 Admin' config.`);

  session.commitTransaction();
} catch (e) {
  session.abortTransaction();
  print('Transaction aborted due to error:', e);
} finally {
  session.endSession();
}
