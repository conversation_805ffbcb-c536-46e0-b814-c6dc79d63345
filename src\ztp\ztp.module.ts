import { Module } from '@nestjs/common';
import { ZtpController } from './ztp.controller';
import { ZtpService } from './ztp.service';
import { LoggerModule } from '../loggers/logger.module';
import { RmqModule } from '../rmq/rmq.module';
import { ObjectStorageModule } from '../objectStorage/object.storage.module';
import { StorageRepository } from '../objectStorage/storage.repository';

@Module({
  imports: [LoggerModule, RmqModule, ObjectStorageModule],
  controllers: [ZtpController],
  providers: [ZtpService, StorageRepository],
  exports: [ZtpService],
})
export class ZtpModule {}
