import { Injectable } from '@nestjs/common';
import { SecretsPoliciesRepository } from './secretsPolicies.repository';
import { SecretsPoliciesDto } from './dto/secretPolicies.dto';
import { RequestContext } from 'nestjs-request-context';
import { getUserContext } from '../../utils/helpers';
import { SecretsPolicies } from './schemas/secretsPolicies.schema';

@Injectable()
export class SecretsPoliciesService {
  constructor(private readonly secretPoliciesRepo: SecretsPoliciesRepository) {}

  async create(payload: SecretsPoliciesDto) {
    payload['createdBy'] = getUserContext(RequestContext)?.userId;
    return await this.secretPoliciesRepo.create(payload as SecretsPolicies);
  }

  async getPolicy(policyId: string | string[]) {
    return await this.secretPoliciesRepo.getPolicy(policyId);
  }
}
