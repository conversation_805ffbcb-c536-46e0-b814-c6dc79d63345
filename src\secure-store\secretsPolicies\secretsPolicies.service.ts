import { Injectable } from '@nestjs/common';
import { SecretsPoliciesRepository } from './secretsPolicies.repository';
import { SecretsPoliciesDto } from './dto/secretPolicies.dto';
import { RequestContext } from 'nestjs-request-context';
import { getUserContext } from '../../utils/helpers';
import { SecretsPolicies } from './schemas/secretsPolicies.schema';

@Injectable()
export class SecretsPoliciesService {
  constructor(private readonly secretPoliciesRepo: SecretsPoliciesRepository) {}

  async create(payload: SecretsPoliciesDto) {
    payload.policyName = payload?.policyName?.toLowerCase();
    payload['createdBy'] = getUserContext(RequestContext)?.userId;
    return await this.secretPoliciesRepo.create(payload as SecretsPolicies);
  }

  async getPolicy(policyId: string | string[]) {
    return await this.secretPoliciesRepo.getPolicy(policyId);
  }

  async getSecretPoliciesWithResourceId(resourceId: string) {
    return await this.secretPoliciesRepo.getSecretPoliciesWithResourceId(
      resourceId,
    );
  }

  async validateSecret(
    secret: string,
    policyId: string | string[],
  ): Promise<{ valid: boolean; message: string[] }> {
    const policy = await this.secretPoliciesRepo.getPolicy(policyId);
    if (!policy || !policy.policyRules) {
      return { valid: false, message: ['Policy not found or invalid'] };
    }
    return this.validateSecretWithPolicyRules(
      secret,
      policy.policyRules.lowerCaseLettersCount,
      policy.policyRules.upperCaseLettersCount,
      policy.policyRules.numericalCharactersCount,
      policy.policyRules.specialCharactersCount,
      policy.policyRules.acceptedSpecialCharacters,
      policy.policyRules.totalCharactersLength,
    );
  }

  validateSecretWithPolicyRules(
    str,
    lcLimit,
    ucLimit,
    digitsLimit,
    specialCharLimit,
    allowedSpecialChars,
    secretLength,
  ) {
    const lowercaseCount = (str.match(/[a-z]/g) || []).length;
    const uppercaseCount = (str.match(/[A-Z]/g) || []).length;
    const digitCount = (str.match(/[0-9]/g) || []).length;
    const specialCharCount = (
      str.match(
        new RegExp(
          `[${allowedSpecialChars.replace(/[-[\]/{}()*+?.\\^$|]/g, '\\$&')}]`,
          'g',
        ),
      ) || []
    ).length;

    const totalSpecialChars =
      str.length - lowercaseCount - uppercaseCount - digitCount;

    let message = [];
    let valid =
      lowercaseCount >= lcLimit &&
      uppercaseCount >= ucLimit &&
      digitCount >= digitsLimit &&
      totalSpecialChars == specialCharCount &&
      specialCharCount >= specialCharLimit &&
      str.length >= secretLength &&
      str.length <= 30;
    if (!valid) {
      if (str.length < secretLength) {
        message.push(
          `Password should have atleast ${secretLength} characters.`,
        );
      }
      if (str.length > 30) {
        message.push(`Password cannot be more than 30 characters.`);
      }
      if (lowercaseCount < lcLimit) {
        message.push(
          `Password should have atleast ${lcLimit} lower Case letters.`,
        );
      }
      if (uppercaseCount < ucLimit) {
        message.push(
          `Password should have atleast ${ucLimit} upper Case letters.`,
        );
      }
      if (digitCount < digitsLimit) {
        message.push(`Password should have atleast ${digitsLimit} digits.`);
      }
      if (totalSpecialChars < specialCharLimit) {
        message.push(
          `Password should have atleast ${specialCharLimit} special characters.`,
        );
      }
      if (totalSpecialChars > specialCharCount) {
        message.push(
          `Password can only have the following special characters - ${allowedSpecialChars}`,
        );
      }
    }
    return { valid: valid, message: message };
  }
}
