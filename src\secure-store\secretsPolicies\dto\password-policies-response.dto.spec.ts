// password-policies-response.dto.spec.ts
import { PasswordPoliciesResponseDto } from './passwordpolicies.response.dto';

describe('PasswordPoliciesResponseDto', () => {
  it('should create an instance with default values', () => {
    const dto = new PasswordPoliciesResponseDto();
    expect(dto.policyId).toBeUndefined();
    expect(dto.policyName).toBeUndefined();
    expect(dto.description).toBeUndefined();
  });

  it('should create an instance with provided values', () => {
    const dto = new PasswordPoliciesResponseDto();
    dto.policyId = '123';
    dto.policyName = 'Strong Password Policy';
    dto.description =
      'Requires at least 8 characters, including a number and a symbol';

    expect(dto.policyId).toBe('123');
    expect(dto.policyName).toBe('Strong Password Policy');
    expect(dto.description).toBe(
      'Requires at least 8 characters, including a number and a symbol',
    );
  });
});
