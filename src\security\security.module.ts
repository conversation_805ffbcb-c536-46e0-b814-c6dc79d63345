import { Module } from '@nestjs/common';
import { SecurityService } from './security.service';
import { SecurityController } from './security.controller';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';
import { IntegrationNotificationModule } from '../naas/integration.notification.module';
import { ApprovalsModule } from '../approvals/approvals.module';
import { StatusNotificationModule } from '../statusNotification/statusNotification.module';
import { LoggerModule } from '../loggers/logger.module';
import { FirewallV2Controller } from './firewallv2/firewallv2.controller';
import { FirewallV2Service } from './firewallv2/firewallv2.service';
import { FirewallRequestDetailsRepository } from './repository/firewallRequestDetails-repository';
import { RmqModule } from '../rmq/rmq.module';
import { ObjectStorageModule } from '../objectStorage/object.storage.module';
import { StorageRepository } from '../objectStorage/storage.repository';
import { RestService } from '../rest/rest.service';
import { CatalogStepsModule } from '../catalog-steps/catalog-steps.module';
import { ActivityLoggerWrapperModule } from '../activity-logs-wrapper/activity-logger-wrapper.module';
import { TufinDeviceRepository } from './repository/tufin.path-analysis.repository';
import { PathAnalysisService } from './pathAnalysis/pathAnalysis.service';
import { PathAnalysisController } from './pathAnalysis/pathAnalysis.controller';
import { TicketManagementWrapperModule } from '../ticket-management-service-wrapper/ticket-management-wrapper-module';
import { TokenService } from '../auth/token.service';
import { ENVIRONMENT_VARS } from '../utils/constants';
import { REQUEST } from '@nestjs/core';
import { createAxiosInstance } from '../utils/helpers';
import { AuthModule } from '../auth/auth.module';
import { ResubmitFirewallRequestRepository } from './repository/resubmitFirewallRequest.repository';
import { JiraManagementModule } from '../jira-management/jira-management.module';
import { AwsIpamModule } from '../aws-ipam/aws.ipam.module';
import { TufinModule } from '../tufin/tufin.module';
import { DbaasModule } from '../dbaas/dbaas.module';
import { NebulaConfigRepository } from '../dbaas/nebulaConfig.repository';

@Module({
  imports: [
    LoggerModule,
    ObjectStorageModule,
    IntegrationNotificationModule,
    ApprovalsModule,
    StatusNotificationModule,
    RmqModule,
    CatalogStepsModule,
    ActivityLoggerWrapperModule,
    TicketManagementWrapperModule,
    AuthModule,
    JiraManagementModule,
    AwsIpamModule,
    TufinModule,
    DbaasModule,
  ],
  controllers: [
    SecurityController,
    FirewallV2Controller,
    PathAnalysisController,
  ],
  providers: [
    SecurityService,
    FirewallV2Service,
    PathAnalysisService,
    FirewallRequestDetailsRepository,
    ResubmitFirewallRequestRepository,
    TufinDeviceRepository,
    RestService,
    { provide: StorageRepository, useClass: StorageRepository },
    {
      provide: NebulaConfigRepository,
      useClass: NebulaConfigRepository,
    },
    {
      provide: 'JIRA_SERVICE_API',
      inject: [ConfigService, TokenService, REQUEST],
      useFactory: async (
        configService: ConfigService,
        tokenService: TokenService,
        req: Request,
      ): Promise<AxiosInstance> => {
        const baseUrlKey = configService.get(
          ENVIRONMENT_VARS.JIRA_API_BASE_URL,
        );
        const nebulaHeader = req.headers['x-nebula-authorization']
          ? (req.headers['x-nebula-authorization'] as string)
          : (req.headers['x-client-jwt'] as string);
        return await createAxiosInstance(
          configService,
          tokenService,
          nebulaHeader,
          baseUrlKey,
        );
      },
    },
    {
      provide: 'TUFIN_SERVICE_API',
      inject: [ConfigService, TokenService, REQUEST],
      useFactory: async (
        configService: ConfigService,
        tokenService: TokenService,
        req: Request,
      ): Promise<AxiosInstance> => {
        const baseUrlKey = configService.get(
          ENVIRONMENT_VARS.TUFIN_SERVICE_BASE_URL,
        );
        const nebulaHeader = req.headers['x-nebula-authorization']
          ? (req.headers['x-nebula-authorization'] as string)
          : (req.headers['x-client-jwt'] as string);
        return await createAxiosInstance(
          configService,
          tokenService,
          nebulaHeader,
          baseUrlKey,
        );
      },
    },
  ],
  exports: [SecurityService, FirewallV2Service, PathAnalysisService],
})
export class SecurityModule {}
