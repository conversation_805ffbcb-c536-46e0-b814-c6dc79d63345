import { IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { ActionStatus } from '../enums/ActionStatus.enum';
import { Transform } from 'class-transformer';

export class UpdateActionDto {
  @IsString()
  @IsNotEmpty()
  actionId: string;

  @IsString()
  @IsOptional()
  entityId?: string;

  @Transform((params) => (params.value === '' ? null : params.value))
  @IsEnum(ActionStatus)
  @IsNotEmpty()
  status: ActionStatus;
}
