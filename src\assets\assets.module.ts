import { Global, Module } from '@nestjs/common';
import { RequestContextModule } from 'nestjs-request-context';
import { LoggerModule } from '../loggers/logger.module';
import { IServiceRequestRepository } from '../abstracts/serviceRequest-repository.abstract';
import { ApprovalsModule } from '../approvals/approvals.module';
import { IntegrationNotificationModule } from '../naas/integration.notification.module';
import { ServiceRequestRepository } from '../naas/serviceRequest.repository';
import { RmqModule } from '../rmq/rmq.module';
import { StatusNotificationModule } from '../statusNotification/statusNotification.module';
import { AssetsController } from './assets.controller';
import { AssetsService } from './assets.service';
import { CatalogStepsModule } from '../catalog-steps/catalog-steps.module';
import { FirewallRequestDetailsRepository } from '../security/repository/firewallRequestDetails-repository';
import { TicketManagementModule } from '../ticket-management/ticket-management.module';
import { TicketManagementService } from '../ticket-management/ticket-management.service';
import { DbaasModule } from '../dbaas/dbaas.module';
import { NebulaConfigRepository } from '../dbaas/nebulaConfig.repository';
import { TicketManagementWrapperModule } from '../ticket-management-service-wrapper/ticket-management-wrapper-module';
import { SecurityModule } from '../security/security.module';

@Global()
@Module({
  imports: [
    LoggerModule,
    RmqModule,
    RequestContextModule,
    ApprovalsModule,
    IntegrationNotificationModule,
    StatusNotificationModule,
    CatalogStepsModule,
    TicketManagementModule,
    DbaasModule,
    TicketManagementWrapperModule,
    SecurityModule,
  ],
  controllers: [AssetsController],
  providers: [
    TicketManagementService,
    AssetsService,
    { provide: IServiceRequestRepository, useClass: ServiceRequestRepository },
    {
      provide: FirewallRequestDetailsRepository,
      useClass: FirewallRequestDetailsRepository,
    },
    {
      provide: NebulaConfigRepository,
      useClass: NebulaConfigRepository,
    },
  ],
  exports: [AssetsService],
})
export class AssetsModule {}
