import {
  IsBoolean,
  IsDefined,
  IsEnum,
  IsNotEmpty,
  IsObject,
  isObject,
  IsOptional,
  IsString,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { ApprovalStatus, IpType, RequestStatus } from '../../../types';
import { Type } from 'class-transformer';
import { SecureChangeRequestDto } from './secure.change.dto';

export class TicketDetailsDto {
  @IsOptional()
  tufin: object;

  @IsOptional()
  jira: object;

  @IsOptional()
  cherwell: object;

  @IsOptional()
  remedy: object;
}

export class FirewallRequestCreateDto {
  @IsString()
  @IsDefined()
  @IsNotEmpty()
  requestType: string;

  @IsDefined()
  @IsNotEmpty()
  ruleIds: Number[];

  @IsEnum(IpType)
  @IsNotEmpty()
  @IsDefined()
  ipType: IpType;

  @IsString()
  @IsDefined()
  @IsNotEmpty()
  requestId: string;

  @IsString()
  @IsDefined()
  @IsNotEmpty()
  startedAt: Date;

  @IsEnum(RequestStatus)
  @IsDefined()
  @IsNotEmpty()
  status: RequestStatus;

  @IsEnum(ApprovalStatus)
  @IsDefined()
  @IsNotEmpty()
  approvalStatus: ApprovalStatus;

  @IsString()
  @IsDefined()
  @IsNotEmpty()
  organizationName: string;

  @IsOptional()
  ticketDetails?: TicketDetailsDto;

  @IsOptional()
  @IsBoolean()
  createSecureChange?: boolean;

  @ValidateIf((o) => o.createSecureChange === true)
  @ValidateNested({ each: true })
  @Type(() => SecureChangeRequestDto)
  @IsObject()
  secureChangePayload?: SecureChangeRequestDto;
}
