import {
  HttpException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { WebhookHandlerInterface } from './webhook-handler.interface';
import { IServiceRequestRepository } from '../abstracts/serviceRequest-repository.abstract';
import { API_RESPONSE_STATUS, RequestStatus, RequestType } from '../types';
import * as uuid from 'uuid';
import { CatalogStepsService } from '../catalog-steps/catalog-steps.service';
import { AssetsService } from '../assets/assets.service';
import { ConfigService } from '@nestjs/config';
import {
  DapWebhookRequestDto,
  DapWebhookResponseDto,
  DnpStatus,
} from './dto/dnp-dap-update.dto';
import { DapStatus, ENVIRONMENT_VARS } from '../utils/constants';
import { ProcessStatus } from 'src/activity-logs/types';
import { ActivityLoggerWrapperService } from '../activity-logs-wrapper/activity-logger-wrapper.service';
import { ServiceRequestEntity } from '../naas/entities/serviceRequest.entity';
import { CatalogStepsEntity } from '../catalog-steps/catalog-steps.entity';

@Injectable()
export class DnpDapUpdateWebhookHandler
  implements
    WebhookHandlerInterface<DapWebhookRequestDto, DapWebhookResponseDto>
{
  private readonly logger = new Logger(DnpDapUpdateWebhookHandler.name);

  constructor(
    private readonly serviceRequestRepository: IServiceRequestRepository,
    private readonly catalogStepsService: CatalogStepsService,
    private readonly assetsService: AssetsService,
    private readonly configService: ConfigService,
    private readonly activityLoggerService: ActivityLoggerWrapperService,
  ) {}

  async handle(
    data: DapWebhookRequestDto,
    servicerequestId: string,
  ): Promise<DapWebhookResponseDto> {
    const { comment } = data;

    // Perform update logic (e.g., database update, business logic processing)
    this.logger.log(
      `Updating DAP task for ${comment} with data: ${JSON.stringify(data)}`,
    );

    const dapStatus = data.taskActivity[0]?.results[0]?.status;
    let dnpStatus;
    if (dapStatus === DapStatus.SUCCESSFUL) {
      dnpStatus = API_RESPONSE_STATUS.SUCCESS;
    } else if (dapStatus === DapStatus.PARTIALLY_SUCCESSFUL) {
      dnpStatus = API_RESPONSE_STATUS.PARTIAL_COMPLETED;
    } else {
      dnpStatus = API_RESPONSE_STATUS.FAILED;
    }

    try {
      await this.updateHostDnpStatus(
        servicerequestId,
        data.taskActivity[0]?.results[0]?.device,
        dnpStatus,
        data,
      );
      return {
        status: dnpStatus,
        message: 'DAP request updated successfully',
      };
    } catch (error) {
      if (error instanceof HttpException) {
        this.logger.error(
          error,
          `Failed while updating HostDnpStatus for service request ID: ${servicerequestId}, ${error.message}`,
        );
        throw error;
      }
      this.logger.error(error, 'Error while updating dnp status');
      throw new InternalServerErrorException(error);
    }
  }

  async updateHostDnpStatus(
    serviceRequestId: string,
    hostname: string,
    dnpStatus: string,
    data: DapWebhookRequestDto,
  ) {
    const dnpRequestDocument =
      await this.assetsService.findByServiceRequestId(serviceRequestId);
    this.logger.log('dnpRequestDocument :', dnpRequestDocument);
    const serviceRequest: ServiceRequestEntity =
      await this.assetsService.getByServiceRequestId(serviceRequestId);
    const traceId = uuid.v4();
    let catalogStepsObj: CatalogStepsEntity;
    let steps = [];
    catalogStepsObj =
      await this.catalogStepsService.findActiveStepsByRequestType(
        RequestType.DNP,
      );
    steps = catalogStepsObj.activityLogSteps.sort(
      (a, b) => a.sequence - b.sequence,
    );

    if (!dnpRequestDocument) {
      this.logger.warn(
        `No DNP service request found for ID: ${serviceRequestId}.`,
      );
      throw new NotFoundException(
        `No DNP service request found for ID: ${serviceRequestId}.`,
        'DNP_SERVICE_REQUEST_NOT_FOUND',
      );
    }
    const activityLogSequence = this.configService.get(
      ENVIRONMENT_VARS.DAP_DNP_ACITIVITY_LOG_SEQUENCE,
    );
    const updatedDocuments = dnpRequestDocument.payload.hosts
      .filter((host) => host.hostname === hostname)
      .map((device) => {
        device.dnpStatus = { status: dnpStatus };
        device.interfaces = device.interfaces.map((existingInterface) => {
          const existingInterfaceNumber = existingInterface.number;

          const matchingNewInterface =
            data.taskActivity[0].results[0].interfaces.find(
              (newInt) => newInt.interface === existingInterfaceNumber,
            );

          if (matchingNewInterface) {
            return {
              ...existingInterface,
              updated: matchingNewInterface.updated,
            };
          } else {
            return {
              ...existingInterface,
              updated: false,
            };
          }
        });
      });
    let resourceName = hostname;
    await this.activityLoggerService.sendActivity(
      ProcessStatus.STARTED,
      steps,
      serviceRequestId,
      data,
      traceId,
      activityLogSequence,
      serviceRequest,
      RequestType.DNP,
      0,
      0,
      undefined,
      undefined,
      resourceName,
    );
    await this.assetsService.update(serviceRequestId, {
      payload: dnpRequestDocument.payload,
    });

    this.logger.log(
      `Webhook api activity log started for hostname: ${hostname}`,
    );
    if (['COMPLETED', 'PARTIALLY COMPLETED'].includes(dnpStatus)) {
      await this.activityLoggerService.sendActivity(
        ProcessStatus.COMPLETED,
        steps,
        serviceRequestId,
        data,
        traceId,
        activityLogSequence,
        serviceRequest,
        RequestType.DNP,
        0,
        0,
        undefined,
        undefined,
        resourceName,
      );
    } else {
      await this.activityLoggerService.sendActivity(
        ProcessStatus.FAILED,
        steps,
        serviceRequestId,
        data,
        traceId,
        activityLogSequence,
        serviceRequest,
        RequestType.DNP,

        0,
        0,
        undefined,
        undefined,
        resourceName,
      );
    }

    if (updatedDocuments.length == 0) {
      this.logger.warn(
        `No DNP device found with hostname '${hostname}' for service request ID: ${serviceRequestId}.`,
      );
      throw new NotFoundException(
        `No DNP device found with hostname '${hostname}' for service request ID: ${serviceRequestId}. Please verify the hostname.`,
        'DNP_DEVICE_NOT_FOUND_FOR_HOSTNAME',
      );
    }

    this.logger.log(
      'dnpRequestDocument.payload.hosts :',
      dnpRequestDocument.payload.hosts,
    );
    const id = dnpRequestDocument.id;

    try {
      const hosts = dnpRequestDocument.payload.hosts;
      const uniqueStatuses = new Set(
        hosts.map((host) => host?.dnpStatus?.status ?? null),
      );

      this.logger.log(
        `Webhook api activity log completed for hostname: ${hostname}`,
      );
      if (uniqueStatuses.has(null)) {
        this.logger.warn(
          `All or some hosts have null dnpStatus. Skipping update for request id ${serviceRequestId}`,
        );
        return;
      }
      let serviceRequestStatus;
      if (uniqueStatuses.size === 1 && uniqueStatuses.has('SUCCESS')) {
        serviceRequestStatus = RequestStatus.SUCCESS;
      } else if (uniqueStatuses.size === 1 && uniqueStatuses.has('FAILED')) {
        serviceRequestStatus = RequestStatus.FAILED;
      } else if (uniqueStatuses) {
        serviceRequestStatus = RequestStatus.PARTIAL_SUCCESS;
      }
      dnpRequestDocument.status = serviceRequestStatus;

      this.logger.log(
        `Updating the serviceRequest status for serviceRequestId: ${serviceRequestId}`,
      );

      await this.activityLoggerService.sendActivity(
        ProcessStatus.STARTED,
        steps,
        serviceRequestId,
        data,
        traceId,
        4,
        serviceRequest,
        RequestType.DNP,
        0,
        0,
        undefined,
        undefined,
      );
      this.logger.log(
        `Updating the dnp status for hostname : ${hostname} and serviceRequestStatus : ${serviceRequestStatus} in serviceRequestId : ${serviceRequestId}`,
      );
      await this.assetsService.update(id, { status: serviceRequestStatus });
      if (
        [RequestStatus.SUCCESS, RequestStatus.PARTIAL_SUCCESS].includes(
          serviceRequestStatus,
        )
      ) {
        await this.activityLoggerService.sendActivity(
          ProcessStatus.COMPLETED,
          steps,
          serviceRequestId,
          data,
          traceId,
          4,
          serviceRequest,
          RequestType.DNP,
          0,
          0,
          undefined,
          undefined,
        );
      } else {
        await this.activityLoggerService.sendActivity(
          ProcessStatus.FAILED,
          steps,
          serviceRequestId,
          data,
          traceId,
          4,
          serviceRequest,
          RequestType.DNP,
          0,
          0,
          undefined,
          undefined,
        );
      }
    } catch (error) {
      this.logger.error(
        error,
        `Error in update dnpStatus for hostname ${hostname}, request id ${serviceRequestId}`,
      );
      this.logger.log(
        `Webhook api failed activity log for hostname: ${hostname}`,
      );
      await this.activityLoggerService.sendActivity(
        ProcessStatus.FAILED,
        steps,
        serviceRequestId,
        data,
        traceId,
        activityLogSequence,
        serviceRequest,
        RequestType.DNP,

        0,
        0,
        undefined,
        undefined,
        resourceName,
      );
      throw new Error(
        `Failed to update host dnpStatus for request ${serviceRequestId}`,
      );
    }
  }
}
