import {
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import {
  RequestStatus,
  OrganizationName,
  JiraActions,
  ApprovalStatus,
} from '../../types';
import { ObjectStorageDto } from './dto/firewallv2.request.dto';
import * as ipRangeCheck from 'ip-range-check';
import * as IpAddress from 'ip-address';
export const ipv4 = IpAddress.Address4;
export const ipv6 = IpAddress.Address6;
export const ipRange = ipRangeCheck;
import { IPv4CidrRange, IPv6CidrRange } from 'ip-num/IPRange';
import { IPv4 } from 'ip-num/IPNumber';
import * as tmp from 'tmp';
import { StorageRepository } from '../../objectStorage/storage.repository';
import { LoggerService } from '../../loggers/logger.service';
import {
  ENVIRONMENT_VARS,
  JiraStatus,
  TicketType,
} from '../../utils/constants';
import { TicketManagementWrapperService } from '../../ticket-management-service-wrapper/ticket-management-wrapper-service';
import { JiraService } from '../../jira-management/jira-managment.service';
import { JiraRequestDto } from './dto/jira.request.dto';
import { FirewallV2Service } from './firewallv2.service';
import { ProcessStatus } from '../../activity-logs/types';
import { JiraStatusRequestDto } from '../../jira-management/dto/jira.status.request.dto';
import { PollRequestService } from '../../pollRequest/pollRequest.service';

@Injectable()
export class FirewallV2JiraService {
  constructor(
    private readonly storageRepository: StorageRepository,
    private readonly logger: LoggerService,
    private readonly ticketManagementWrapperService: TicketManagementWrapperService,
    private readonly jiraWrapperServiceApi: JiraService,
    private readonly firewallV2Service: FirewallV2Service,
    private readonly pollRequestService: PollRequestService,
  ) {}

  async getJiraTicketStatus(data: JiraStatusRequestDto[]) {
    try {
      let response = await this.jiraWrapperServiceApi.getJiraStatus(data);
      return response;
    } catch (err) {
      this.logger.log(err, `Error while fetching jira status`);
      return null;
    }
  }

  async addCommentInJira(jiraId: string, comment: string) {
    try {
      await this.jiraWrapperServiceApi.addCommentInJira({
        jiraId: jiraId,
        location: comment,
      });
    } catch (err) {
      this.logger.error(err, `Error while adding ${comment} in ${jiraId}`);
    }
  }

  async checkAndCreateJiraIssue(data: JiraRequestDto) {
    try {
      let jiraResponse;
      this.logger.log(
        `fetch subrequest using service request id ${data.serviceRequestId}`,
      );
      await this.firewallV2Service.sendActivity(
        data.serviceRequestId,
        data.subRequestId,
        ENVIRONMENT_VARS.FW_CREATE_JIRA_TICKET_STEP,
        ProcessStatus.STARTED,
        0,
      );

      const subrequests =
        await this.firewallV2Service.getFirewallTicketDetailsN8N(
          data.serviceRequestId,
        );

      this.logger.log(`filtering subrequests for ipv4 subrequests`);

      const filteredSubrequests = subrequests.filter((subrequest) =>
        [
          OrganizationName.NA_FAILED,
          OrganizationName.NA_NOIMPACT,
          OrganizationName.RED_APS,
          OrganizationName.UNKNOWN,
        ].includes(subrequest.organizationName as OrganizationName),
      );

      this.logger.log(
        `check if jira exists or not for ${data.serviceRequestId}`,
      );

      if (filteredSubrequests.length) {
        this.logger.log(
          `jira ticket already exists for for ${data.serviceRequestId}, uploading excel`,
        );
        const jiraTicketDetails = filteredSubrequests.find(
          (subrequest) => subrequest.ticketDetails?.jira?.ticketId,
        )?.ticketDetails?.jira;
        if (jiraTicketDetails?.ticketId) {
          try {
            const response = await this.jiraWrapperServiceApi.uploadExcelInJira(
              {
                issueId: jiraTicketDetails.ticketId,
                issuekey: jiraTicketDetails.ticketId,
                projectKey: data.firewallRequestInfo.projectKey,
                storageInfo: data.firewallRequestInfo.storageInfo,
                fileName: data.firewallRequestInfo?.fileName,
              },
            );
            jiraResponse = {
              key: jiraTicketDetails.ticketId,
              href: jiraTicketDetails.ticketUrl,
            };
          } catch (err) {
            this.logger.error(
              err,
              `Error while uploading excel in jira for ${data.subRequestId}`,
            );
            throw new InternalServerErrorException(
              `Error while upload excel in jira for ${data.subRequestId}`,
            );
          }
        } else {
          this.logger.log(
            `ipv4 subrequests are present for ${data.serviceRequestId}, but jira info is missing, so creating new ticket`,
          );
          jiraResponse = await this.createJiraTicketForFirewallRequest(data);
        }
      } else {
        jiraResponse = await this.createJiraTicketForFirewallRequest(data);
      }

      //update subrequest with jira details
      const updatePayload = {
        'ticketDetails.jira': {
          ticketId: jiraResponse.key,
          ticketUrl: jiraResponse.href,
          status: JiraStatus.NEW,
          error: [],
        },
      };
      //@ts-ignore
      await this.firewallV2Service.update(data.subRequestId, updatePayload);
      await this.firewallV2Service.sendActivity(
        data.serviceRequestId,
        data.subRequestId,
        ENVIRONMENT_VARS.FW_CREATE_JIRA_TICKET_STEP,
        ProcessStatus.COMPLETED,
        0,
        jiraResponse,
      );
      this.logger.log(
        `updated subrequest ${data.subRequestId} with jira ticket details`,
      );
      return jiraResponse;
    } catch (err) {
      //update subrequest with jira details
      const updatePayload = {
        'ticketDetails.jira': {
          ticketId: '',
          ticketUrl: '',
          status: RequestStatus.FAILED,
          error: [
            {
              errorCode: err?.response?.statusCode ?? '500',
              errorMessage:
                err?.response?.message ?? 'Error while creating jira ticket',
            },
          ],
        },
      };
      //@ts-ignore
      await this.firewallV2Service.update(data.subRequestId, updatePayload);
      await this.firewallV2Service.sendActivity(
        data.serviceRequestId,
        data.subRequestId,
        ENVIRONMENT_VARS.FW_CREATE_JIRA_TICKET_STEP,
        ProcessStatus.FAILED,
        0,
        undefined,
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: err?.response?.message ?? 'Error while creating common jira',
        },
      );
      throw err;
    }
  }

  async createJiraTicketForFirewallRequest(data: JiraRequestDto) {
    this.logger.log(
      `jira ticket doesn't exists for ${data.serviceRequestId}, creating new jira ticket`,
    );
    try {
      const response =
        await this.jiraWrapperServiceApi.createJiraIssueForFirewallRequest(
          data,
        );
      //whenever we create jira ticket upload original dfm
      await this.uploadOriginalDFM(
        data.serviceRequestId,
        response,
        data.firewallRequestInfo.projectKey,
      );
      return response;
    } catch (err) {
      this.logger.error(err, `Error while creating jira ticket`);
      throw new InternalServerErrorException(
        `Error while creating jira ticket`,
      );
    }
  }

  async getStorageInfo(serviceRequestId: string) {
    const initialStorageEntities =
      await this.storageRepository.findV2RuleStorageByRequestId(
        serviceRequestId,
      );
    for (const storageEntity of initialStorageEntities) {
      this.logger.log('Storge Entity', storageEntity);
      const fileNameArray = storageEntity.filePath.split('/');
      if (storageEntity && fileNameArray.includes('allRules')) {
        const filePath = storageEntity.filePath;
        const bucketName = storageEntity.bucketName;
        const fileName = storageEntity.fileName;
        this.logger.debug(
          `storage location bucket:${bucketName}, file:${fileName}, path:${filePath} `,
        );
        const objectStorageDto: ObjectStorageDto = {
          bucketName,
          fileName,
          filePath,
        };
        return objectStorageDto;
      }
    }
  }

  async uploadOriginalDFM(serviceRequestId: string, jiraInfo, projectKey) {
    try {
      //get original dfm details
      const storageInfo = await this.getStorageInfo(serviceRequestId);
      //upload dfm
      const payload = {
        issueId: jiraInfo.id,
        issuekey: jiraInfo.key,
        projectKey: projectKey,
        storageInfo: storageInfo,
        fileName: `${serviceRequestId}_original`,
      };
      this.logger.log(JSON.stringify(payload));
      const response =
        await this.jiraWrapperServiceApi.uploadExcelInJira(payload);
      this.logger.log(`uploaded original dfm`);
    } catch (err) {
      this.logger.error(
        err,
        `Error while uploading original dfm for ${serviceRequestId}`,
      );
    }
  }

  async checkAndUpdateJiraTicket(
    jiraId: string,
    subRequestId: string,
    serviceRequestId: string,
    action: JiraActions,
  ) {
    const NONNETDCOPSORGS = [
      OrganizationName.AWS,
      OrganizationName.RED_CBO,
      OrganizationName.IPV6,
    ];

    const subRequests =
      await this.firewallV2Service.getFirewallTicketDetailsN8N(
        serviceRequestId,
      );

    //filter subRequest to cancel
    const subRequest = subRequests.find(
      (request) => request.subRequestId === subRequestId,
    );

    if (!subRequest) {
      this.logger.error(`${subRequestId} doesn't exists`);
      throw new NotFoundException(`${subRequestId} doesn't exists`);
    }

    if (!subRequest.ticketDetails?.jira?.ticketId) {
      this.logger.error(`Jira doesn't exists in ${subRequest.subRequestId}`);
      throw new NotFoundException(
        `Jira doesn't exists in ${subRequest.subRequestId}`,
      );
    }

    if (
      NONNETDCOPSORGS.includes(subRequest.organizationName as OrganizationName)
    ) {
      return await this.handleNonNETDCOPSFWOrgs(
        subRequest,
        serviceRequestId,
        jiraId,
        action,
      );
    } else {
      this.logger.log(
        `org name is ${subRequest.organizationName} for ${subRequestId}. It needs further check`,
      );
      return await this.handleNETDCOPSFWOrgs(
        subRequest,
        subRequests,
        serviceRequestId,
        jiraId,
        action,
      );
    }
  }

  async handleNonNETDCOPSFWOrgs(subRequest, serviceRequestId, jiraId, action) {
    this.logger.log(
      `org name is ${subRequest.organizationName} for ${subRequest.subReqestId}. It can be cancelled without further check`,
    );
    await this.updateJiraAndSubRequests(
      jiraId,
      action,
      [subRequest.subRequestId],
      serviceRequestId,
    );
    this.logger.log(
      `updated Jira status action ${action} for ${subRequest.subReqestId}`,
    );

    return {
      statusCode: HttpStatus.OK,
      message: `Updated ${action} action for ${jiraId} and subrequest ${subRequest.subRequestId}`,
    };
  }

  async handleNETDCOPSFWOrgs(
    subRequest,
    subRequests,
    serviceRequestId,
    jiraId,
    action,
  ) {
    //if jira action is cancel, add comment in jira that subrequest is cancelled
    if (action === JiraActions.CANCEL) {
      this.logger.log(
        `Adding comment in jira, ${subRequest.subRequestId} is cancelled`,
      );
      await this.addCommentInJira(
        subRequest?.ticketDetails?.jira?.ticketId,
        `${subRequest.subRequestId} ${subRequest.organizationName} is cancelled by User`,
      );
      this.logger.log(`Added comment in jira, ${subRequest.subRequestId}`);
    }

    this.logger.log(`Get latest jira status`);
    let statusResponse = await this.getJiraTicketStatus([
      {
        id: 0,
        requestId: serviceRequestId,
        issue: {
          id: 0,
          key: subRequest.ticketDetails?.jira?.ticketId,
          self: subRequest.ticketDetails?.jira?.ticketUrl,
        },
      },
    ]);
    this.logger.log(`Latest jira status ${statusResponse?.[0]?.status}`);

    if (statusResponse && statusResponse.length) {
      if (
        [
          JiraStatus.CANCELLED,
          JiraStatus.COMPLETED_WITHOUT_CUSTOMER_VALIDATION,
          JiraStatus.COMPLETED_WITH_CUSTOMER_VALIDATION,
        ].includes(statusResponse[0].status)
      ) {
        if (
          [
            JiraStatus.CANCELLED,
            JiraStatus.COMPLETED_WITHOUT_CUSTOMER_VALIDATION,
            JiraStatus.COMPLETED_WITH_CUSTOMER_VALIDATION,
            RequestStatus.CANCELLED,
            RequestStatus.SUCCESS as string,
          ].includes(subRequest.ticketDetails.jira.status)
        ) {
          this.logger.log(
            `Jira is already in end status ${statusResponse[0].status}`,
          );
          throw new InternalServerErrorException(
            `Jira is already in end status ${statusResponse[0].status}`,
          );
        } else {
          //update latest jira status in subrequest
          this.logger.log(
            `Updating only jira status for ${subRequest.subRequestId}`,
          );
          await this.updateJiraStatusInSubRequest(
            subRequest.subRequestId,
            subRequest.requestId,
            statusResponse[0].status,
            subRequest.approvalStatus === ApprovalStatus.PENDING
              ? ApprovalStatus.AUTO_APPROVED
              : '',
          );

          return {
            statusCode: HttpStatus.OK,
            message: `Updated ${action} action for ${jiraId} and subrequest ${subRequest.subRequestId}`,
          };
        }
      }
    }

    //if subrequest org is red aps, updated unknown, na failed and no change as well
    //else update only that specific org
    if (subRequest.organizationName === OrganizationName.RED_APS) {
      return await this.handleRedApsJiraUpdate(
        subRequest,
        subRequests,
        serviceRequestId,
        jiraId,
        action,
      );
    } else {
      return await this.handleOtherOrgsJiraUpdate(
        subRequest,
        subRequests,
        serviceRequestId,
        jiraId,
        action,
      );
    }
  }

  async handleRedApsJiraUpdate(
    subRequest,
    subRequests,
    serviceRequestId,
    jiraId,
    action,
  ) {
    // cancel or close unkown, na failed, no change subrequests based on action
    const subRequestsToBeUpdated = subRequests.filter((request) =>
      [
        OrganizationName.NA_NOIMPACT,
        OrganizationName.NA_FAILED,
        OrganizationName.UNKNOWN,
      ].includes(request.organizationName as OrganizationName),
    );

    let successfullyUpdatedList = [];

    for (let request of subRequestsToBeUpdated) {
      if (
        [
          RequestStatus.SUCCESS,
          RequestStatus.FAILED,
          RequestStatus.REJECTED,
          RequestStatus.CLOSED_MANUALLY,
          RequestStatus.CANCELLED,
          RequestStatus.CANCELLING,
        ].includes(request.status)
      ) {
        await this.updateJiraStatusInSubRequest(
          request.subRequestId,
          request.requestId,
          action === JiraActions.CANCEL
            ? JiraStatus.CANCELLED
            : JiraStatus.COMPLETED_WITHOUT_CUSTOMER_VALIDATION,
          subRequest.approvalStatus === ApprovalStatus.PENDING
            ? ApprovalStatus.AUTO_APPROVED
            : '',
        );
        successfullyUpdatedList.push(request.subRequestId);
      } else {
        if (action === JiraActions.CLOSE) {
          try {
            await this.firewallV2Service.closeSubRequestV2(
              serviceRequestId,
              request.subRequestId,
              subRequest.status,
              true,
            );
            successfullyUpdatedList.push(request.subRequestId);
          } catch (err) {
            this.logger.error(
              err,
              `Error while closing ${request.subReqestId}`,
            );
          }
        } else {
          try {
            await this.firewallV2Service.cancelSubRequest(
              serviceRequestId,
              request.subRequestId,
              subRequest.status,
              true,
            );
            successfullyUpdatedList.push(request.subRequestId);
          } catch (err) {
            this.logger.error(
              err,
              `Error while cancelling ${request.subReqestId}`,
            );
          }
        }
      }
    }

    await this.updateJiraAndSubRequests(
      jiraId,
      action,
      [subRequest.subRequestId, ...successfullyUpdatedList],
      serviceRequestId,
    );

    return {
      statusCode: HttpStatus.OK,
      message: `Updated ${action} action for ${jiraId} and for subrequest ${subRequest.subRequestId}, ${successfullyUpdatedList.map((request) => request.subRequestId).join(', ')}`,
    };
  }

  async handleOtherOrgsJiraUpdate(
    subRequest,
    subRequests,
    serviceRequestId,
    jiraId,
    action,
  ) {
    const NETDCOPSORGS = [
      OrganizationName.NA_FAILED,
      OrganizationName.NA_NOIMPACT,
      OrganizationName.RED_APS,
      OrganizationName.UNKNOWN,
    ];
    const possibleEndStatus = [
      RequestStatus.CANCELLED,
      RequestStatus.CLOSED_MANUALLY,
      RequestStatus.CLOSED,
      RequestStatus.CANCELLING,
      RequestStatus.SUCCESS,
      RequestStatus.REJECTED,
    ];

    //fetch all subrequest which not allowed to cancel
    const filteredSubRequests = subRequests.filter((request) =>
      [OrganizationName.RED_APS, OrganizationName.UNKNOWN].includes(
        request.organizationName as OrganizationName,
      ),
    );
    //check if all subrequests status are having end status
    if (
      filteredSubRequests.every((request) =>
        possibleEndStatus.includes(request.status),
      )
    ) {
      this.logger.log(
        `All subrequests are in end status we can update the jira ticket`,
      );
      const subRequestsList = subRequests.filter((request) =>
        NETDCOPSORGS.includes(request.organizationName as OrganizationName),
      );

      //derive jira status for common jira ticket
      const redApsSubrequests = subRequests.filter(
        (request) => request.organizationName === OrganizationName.RED_APS,
      );
      const unknownSubrequets = subRequests.filter(
        (request) => request.organizationName === OrganizationName.UNKNOWN,
      );
      if (redApsSubrequests.length) {
        action = this.deriveJiraAction(redApsSubrequests, action);
      } else if (unknownSubrequets.length) {
        action = this.deriveJiraAction(unknownSubrequets, action);
      }
      return await this.updateJiraAndSubRequests(
        jiraId,
        action,
        subRequestsList.map((ele) => ele.subRequestId),
        serviceRequestId,
      );
    } else {
      this.logger.log(
        `All subrequests are not in end status, we can't cancel the jira`,
      );
      throw new InternalServerErrorException(
        `All subrequests should have end status to cancel`,
      );
    }
  }

  deriveJiraAction(subRequests, action) {
    if (
      subRequests.some((request) =>
        [RequestStatus.SUCCESS, RequestStatus.CLOSED_MANUALLY].includes(
          request.status,
        ),
      )
    ) {
      action = JiraActions.CLOSE;
    } else if (
      subRequests.every((request) =>
        [
          RequestStatus.CANCELLED,
          RequestStatus.CANCELLING,
          RequestStatus.REJECTED,
        ].includes(request.status),
      )
    ) {
      action = JiraActions.CANCEL;
    }
    return action;
  }

  async updateJiraAndSubRequests(
    jiraId: string,
    action: JiraActions,
    subRequestIds: string[],
    serviceRequestId,
  ) {
    try {
      const response = await this.jiraWrapperServiceApi.updateJiraIssue(
        jiraId,
        action,
      );
      this.logger.log(`${action} completed for Jira ${jiraId}`);
      for (const id of subRequestIds) {
        try {
          this.logger.log(`updating ${action} status for ${id}`);
          //to maintain consistent status of jira updating status with actual jira status instead of 'CLOSED'
          await this.firewallV2Service.sendActivity(
            serviceRequestId,
            id,
            action === JiraActions.CLOSE
              ? ENVIRONMENT_VARS.FW_CLOSE_JIRA_TICKET_STEP
              : ENVIRONMENT_VARS.FW_CANCEL_JIRA_TICKET_STEP,
            ProcessStatus.STARTED,
            0,
          );
          const updatePayload = {
            'ticketDetails.jira.status':
              action === JiraActions.CLOSE
                ? JiraStatus.COMPLETED_WITHOUT_CUSTOMER_VALIDATION
                : JiraStatus.CANCELLED,
          };
          //@ts-ignore
          await this.firewallV2Service.update(id, updatePayload);
          await this.firewallV2Service.sendActivity(
            serviceRequestId,
            id,
            action === JiraActions.CLOSE
              ? ENVIRONMENT_VARS.FW_CLOSE_JIRA_TICKET_STEP
              : ENVIRONMENT_VARS.FW_CANCEL_JIRA_TICKET_STEP,
            ProcessStatus.COMPLETED,
            0,
            updatePayload,
          );
        } catch (err) {
          this.logger.error(
            err,
            `Error while upadting ${action} status for ${id}`,
          );
        }
      }

      //after cancel or close delete record from poll request collection as well
      await this.deletePollRequestDocuments(subRequestIds);

      return response;
    } catch (err) {
      this.logger.error(err, `Error while ${action} action for ${jiraId}`);
      for (const id of subRequestIds) {
        try {
          this.logger.log(`updating  failed ${action} status for ${id}`);
          await this.firewallV2Service.sendActivity(
            serviceRequestId,
            id,
            action === JiraActions.CLOSE
              ? ENVIRONMENT_VARS.FW_CLOSE_JIRA_TICKET_STEP
              : ENVIRONMENT_VARS.FW_CANCEL_JIRA_TICKET_STEP,
            ProcessStatus.STARTED,
            0,
          );
          const updatePayload = {
            'ticketDetails.jira.status':
              action === JiraActions.CLOSE
                ? RequestStatus.CLOSED_FAILED
                : RequestStatus.CANCEL_FAILED,
            'ticketDetails.jira.error': [
              {
                errorCode: err?.response?.statusCode ?? '500',
                errorMessage:
                  err?.response?.message ??
                  `Error while updating ${action} jira ticket`,
              },
            ],
          };
          //@ts-ignore
          await this.firewallV2Service.update(id, updatePayload);
          await this.firewallV2Service.sendActivity(
            serviceRequestId,
            id,
            action === JiraActions.CLOSE
              ? ENVIRONMENT_VARS.FW_CLOSE_JIRA_TICKET_STEP
              : ENVIRONMENT_VARS.FW_CANCEL_JIRA_TICKET_STEP,
            ProcessStatus.FAILED,
            0,
            undefined,
            {
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
              message:
                err?.response?.message ?? 'Error while updating common jira',
            },
          );
        } catch (err) {
          this.logger.error(
            err,
            `Error while upadting failed ${action} status for ${id}`,
          );
        }
      }
      throw err;
    }
  }

  async updateJiraStatusInSubRequest(
    id,
    serviceRequestId,
    status,
    approvalStatus?,
  ) {
    try {
      this.logger.log(`updating ${status} status for ${id}`);
      //to maintain consistent status of jira updating status with actual jira status instead of 'CLOSED'
      await this.firewallV2Service.sendActivity(
        serviceRequestId,
        id,
        status === JiraStatus.CANCELLED
          ? ENVIRONMENT_VARS.FW_CANCEL_JIRA_TICKET_STEP
          : ENVIRONMENT_VARS.FW_CLOSE_JIRA_TICKET_STEP,
        ProcessStatus.STARTED,
        0,
      );
      let updatePayload: any = {
        'ticketDetails.jira.status': status,
      };
      if (approvalStatus) {
        updatePayload = { ...updatePayload, approvalStatus };
      }
      //@ts-ignore
      await this.firewallV2Service.update(id, updatePayload);

      //after cancel or close delete record from poll request collection as well
      await this.deletePollRequestDocuments([id]);

      await this.firewallV2Service.sendActivity(
        serviceRequestId,
        id,
        status === JiraStatus.CANCELLED
          ? ENVIRONMENT_VARS.FW_CANCEL_JIRA_TICKET_STEP
          : ENVIRONMENT_VARS.FW_CLOSE_JIRA_TICKET_STEP,
        ProcessStatus.COMPLETED,
        0,
        updatePayload,
      );
    } catch (err) {
      this.logger.error(err, `Error while upadting ${status} status for ${id}`);
    }
  }

  async getSubRequestStorageInfo(subRequestId: string) {
    const storageInfo =
      await this.storageRepository.findSubRequestStorageById(subRequestId);
    if (!storageInfo) {
      throw new NotFoundException(`Storage info not found for ${subRequestId}`);
    }

    return {
      bucketName: storageInfo.bucketName,
      filePath: storageInfo.filePath,
      fileName: storageInfo.fileName,
    };
  }

  async uploadAttachmentsForCorporate(serviceRequestId, subRequestId) {
    const subRequests =
      await this.firewallV2Service.getFirewallTicketDetailsN8N(
        serviceRequestId,
      );

    //filter subRequest to cancel
    const subRequest = subRequests.find(
      (request) => request.subRequestId === subRequestId,
    );

    const filteredSubRequests = subRequests.filter((subrequest) =>
      [
        OrganizationName.NA_FAILED,
        OrganizationName.NA_NOIMPACT,
        OrganizationName.RED_APS,
        OrganizationName.UNKNOWN,
      ].includes(subrequest.organizationName as OrganizationName),
    );

    if (!subRequest) {
      this.logger.error(`${subRequestId} doesn't exists`);
      throw new NotFoundException(`${subRequestId} doesn't exists`);
    }

    //upload original dfm if we dont have netdcopsfw subrequests also
    await this.uploadOriginalDfmInCherwell(
      serviceRequestId,
      subRequest?.ticketDetails?.cherwell?.ticketId,
    );

    if (!filteredSubRequests.length) {
      this.logger.warn(`Not found NETDCOPSFW org subrequests`);
      throw new NotFoundException(`Not found NETDCOPSFW org subrequests`);
    }

    //upload corporate excel in unique jira
    await this.checkAndUploadCorporateDfm(
      subRequestId,
      filteredSubRequests,
      serviceRequestId,
      subRequest.ticketDetails?.tufin?.ticketUrl,
    );

    if (subRequest.ticketDetails?.cherwell?.ticketId) {
      await this.checkAndUploadDfmInCherwell(
        subRequest.ticketDetails?.cherwell?.ticketId,
        filteredSubRequests,
        serviceRequestId,
      );
    } else {
      this.logger.warn(
        `Cherwell doesn't exists for ${subRequestId}, Skipping dfm's uploading to cherwell`,
      );
      return {
        status: HttpStatus.BAD_REQUEST,
        message: `Cherwell doesn't exists for ${subRequestId}`,
      };
    }

    return {
      status: HttpStatus.OK,
      message: 'Successfully uploaded attachments',
    };
  }

  async checkAndUploadDfmInCherwell(
    cherwellTicketId,
    subRequests,
    serviceRequestId,
  ) {
    this.logger.log(`uploading dfm's in ${cherwellTicketId}`);
    for (let request of subRequests) {
      try {
        const dfmStorageInfo = await this.getSubRequestStorageInfo(
          request.subRequestId,
        );
        if (dfmStorageInfo) {
          await this.ticketManagementWrapperService.uploadAttachment({
            ticketingSystem: TicketType.CHERWELL,
            serviceRequestId: serviceRequestId,
            attachmentInfo: dfmStorageInfo,
            cherwellTicketId: cherwellTicketId,
            attachmentFileName: `${serviceRequestId}_${request.subRequestId}_${request.organizationName}`,
          });
        } else {
          this.logger.warn(
            `Storage info not found for ${request.subRequestId} to upload in ${cherwellTicketId}`,
          );
        }
      } catch (err) {
        this.logger.error(
          err,
          `Error while uploading ${request.subRequestId} dfm in ${cherwellTicketId}`,
        );
      }
    }
  }

  async checkAndUploadCorporateDfm(
    subRequestId,
    filteredSubRequests,
    serviceRequestId,
    tufinTicketUrl?,
  ) {
    try {
      const dfmStorageInfo = await this.getSubRequestStorageInfo(subRequestId);
      const jiraTicketDetails = filteredSubRequests.find(
        (subrequest) => subrequest.ticketDetails?.jira?.ticketId,
      )?.ticketDetails?.jira;
      if (dfmStorageInfo && jiraTicketDetails?.ticketId) {
        try {
          const response = await this.jiraWrapperServiceApi.uploadExcelInJira({
            issueId: jiraTicketDetails.ticketId,
            issuekey: jiraTicketDetails.ticketId,
            projectKey: 'NETDCOPSFW',
            storageInfo: dfmStorageInfo,
            fileName: `${serviceRequestId}_${subRequestId}_${OrganizationName.CORPORATE}`,
          });
          this.logger.log(
            `Uploaded ${subRequestId} dfm in ${jiraTicketDetails?.ticketId}`,
          );

          if (tufinTicketUrl) {
            try {
              await this.jiraWrapperServiceApi.addCommentInJira({
                jiraId: jiraTicketDetails.ticketId,
                location: `organization: ${OrganizationName.CORPORATE} Tufin ticket for DC Access use only: ${tufinTicketUrl}`,
              });
              this.logger.log(
                `added tufin url for ${subRequestId}  in ${jiraTicketDetails?.ticketId}`,
              );
            } catch (err) {
              this.logger.log(
                err,
                `Error while adding tufin url ${tufinTicketUrl} in ${jiraTicketDetails.ticketId}`,
              );
            }
          }
        } catch (err) {
          this.logger.error(
            err,
            `Error while uploading excel in jira for ${subRequestId}`,
          );
          throw new InternalServerErrorException(
            `Error while upload excel in jira for ${subRequestId}`,
          );
        }
      } else {
        this.logger.warn(`Skipped uploading corporate dfm for ${subRequestId}`);
      }
    } catch (err) {
      this.logger.error(
        err,
        `Error while uploading ${subRequestId} dfm in jira`,
      );
    }
  }

  async uploadOriginalDfmInCherwell(serviceRequestId, cherwellId) {
    //upload original dfm in cherwell
    try {
      const storageInfo = await this.getStorageInfo(serviceRequestId);
      if (storageInfo && cherwellId) {
        await this.ticketManagementWrapperService.uploadAttachment({
          ticketingSystem: TicketType.CHERWELL,
          serviceRequestId: serviceRequestId,
          attachmentInfo: storageInfo,
          cherwellTicketId: cherwellId,
          attachmentFileName: `${serviceRequestId}_original`,
        });
        this.logger.log(`Uploaded original dfm for ${cherwellId}`);
      }
    } catch (err) {
      this.logger.error(
        err,
        `Error while attaching original dfm in cherwell ${cherwellId}`,
      );
    }
  }

  async deletePollRequestDocuments(subRequestIds: string[]) {
    try {
      //delete records from poll request collection as well
      await this.pollRequestService.deletePollRequestV2(
        subRequestIds.map((id) => {
          return { subRequestId: id };
        }),
      );
    } catch (err) {
      this.logger.error(
        err,
        `Error while deleting records from poll request collection for ${JSON.stringify(subRequestIds)}`,
      );
    }
  }
}
