const session = db.getMongo().startSession();

session.startTransaction();

try {
  db.n8nconfig.updateOne(
    { workflow: 'VMWARE-Create-DEV' },
    {
      $set: {
        vmDeleteWorkflow: 'FTNM2S7CexU5Dt5G',
        paceDeleteWorkflow: 'PzCYD5rUSKwmpez1',
      },
    },
  );

  print(`Document was updated`);
  session.commitTransaction();
} catch (e) {
  session.abortTransaction();

  print('Transaction aborted due to error', e);
} finally {
  session.endSession();
}
