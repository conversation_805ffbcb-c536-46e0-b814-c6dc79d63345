import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ConfigModule } from '@nestjs/config';
import * as <PERSON><PERSON> from 'joi';
import { LoggerModule } from './loggers/logger.module';
import { RequestIdMiddleware } from './filters/requestId.middleware';
import { NaasModule } from './naas/naas.module';
import { SecurityModule } from './security/security.module';
import { AssetsModule } from './assets/assets.module';
import { AuthModule } from './auth/auth.module';
import { APP_FILTER } from '@nestjs/core';
import { AllExceptionsFilter } from './utils/filters/allException.filter';
import { ComputeModule } from './compute/compute.module';
import { ObjectStorageModule } from './objectStorage/object.storage.module';
import { ResourcesModule } from './resources/resources.module';
import { IamModule } from './iam/iam.module';
import { ProjectsModule } from './projects/projects.module';
import { EncryptionModule } from './encryption/encryption.module';
import { PollRequestModule } from './pollRequest/pollRequest.module';
import { DbaasModule } from './dbaas/dbaas.module';
import { CachingModule } from './caching/caching.module';
import { StatusModule } from './status/status.module';
import { GoldenConfigModule } from './golden-config/golden-config.module';
import { GoldenConfigWrapperModule } from './golden-config-wrapper/golden-config-wrapper.module';
import { LoggerMiddleware } from './loggers/logger.middleware';
import { ActivityLogsModule } from './activity-logs/activity-logs.module';
import { TufinModule } from './tufin/tufin.module';
import { TicketManagementModule } from './ticket-management/ticket-management.module';
import { CapacityPlanningModule } from './capacity-planning/capacity-planning.module';
import { CommonFirewallModule } from './naas/common-firewall-policy/commonFirewall.module';
import { StorageModule } from './storage/storage.module';
import { RestModule } from './rest/rest.module';
import { AwsModule } from './public-cloud/aws/aws.module';
import { JiraManagementModule } from './jira-management/jira-management.module';
import { LoadBalancerModule } from './load-balancer/load.balancer.module';
import { CertificateModule } from './security/certificates/certificates.module';
import { SitemapModule } from './sitemap/sitemap.module';
import { ImportVMModule } from './import-vm/import-vm.module';
import { NebulaMetricsModule } from './nebula-metrics/nebula-metrics.module';
import { ProjectModule } from './multi-env/project-onboard/project-onboard.module';
import { CatalogAccessModule } from './multi-env/catalog-access/catalog-access.module';
import { GroupModule } from './multi-env/group-onboard/group-onboard.module';
import { MultiEnvModule } from './multi-env/multi-env.module';
import { SearchModule } from './search/search.module';
import { CmdbModule } from './cmdb/cmdb.module';
import { SpecFlowModule } from './spec-flow/spec-flow.module';
import { VmwareModule } from './vmware-service/vmware.module';
import { SecureStoreModule } from './secure-store/secure-store.module';
import { ZtpModule } from './ztp/ztp.module';
import { ScheduledJobsModule } from './scheduled-jobs/scheduled-jobs.module';
import { ActionModule } from './action/action.module';
import { VenafiModule } from './certificate-management/venafi-certificate.module';
import { MetadataModule } from './metadata-service/metadata.module';
import { VmwareAdminModule } from './vmware-admin-service/vmware.admin.module';
import { AwsCherwellModule } from './aws-cherwell/aws-cherwell.module';

@Module({
  imports: [
    AuthModule,
    ScheduledJobsModule,
    ConfigModule.forRoot({
      cache: true,
      isGlobal: true,
      validationSchema: Joi.object({
        MONGODB_URI: Joi.string().required(),
        PORT: Joi.number().required(),
        RMQ_URI: Joi.string().required(),
        RMQ_IPAM_QUEUE: Joi.string().required(),
        RMQ_IPAM_QUEUE_DURABLE: Joi.boolean().default(true),
        RMQ_MONITORING_QUEUE: Joi.string().required(),
        RMQ_MONITORING_QUEUE_DURABLE: Joi.boolean().default(true),
        RMQ_DNP_QUEUE: Joi.string().required(),
        RMQ_DNP_QUEUE_DURABLE: Joi.boolean().default(true),
        IPM_SERVICE_BASE_URL: Joi.string().required(),
        ITENTIAL_SERVICE_BASE_URL: Joi.string().required(),
        INTEGRATION_API_SERVICE_BASE_URL: Joi.string().required(),
        MONITORING_SERVICE_BASE_URL: Joi.string().required(),
        GLOBAL_ROUTE_PREFIX: Joi.string().required(),
        RMQ_COMPUTE_QUEUE: Joi.string().required(),
        RMQ_COMPUTE_QUEUE_DURABLE: Joi.boolean().default(true),
        RMQ_FIREWALL_QUEUE: Joi.string().required(),
        RMQ_FIREWALL_QUEUE_DURABLE: Joi.boolean().default(true),
        OBJECTSTORAGE_BASE_URL: Joi.string().required(),
        OBJECTSTORAGE_USERNAME: Joi.string().required(),
        OBJECTSTORAGE_PASSWORD: Joi.string().required(),
        OBJECTSTORAGE_BUCKET: Joi.string().required(),
        OBJECTSTORAGE_PATH: Joi.string().required(),
        COMPUTE_API_BASE_URL: Joi.string().required(),
        VM_MAX_COUNT: Joi.string().required(),
        OBJECTSTORAGE_FIREWALL_PATH: Joi.string().required(),
        IAM_SERVICE_BASE_URL: Joi.string().required(),
        TAGGING_SERVICE_API_BASE_URL: Joi.string().required(),
        APPROVALS_SERVICE_BASE_URL: Joi.string().required(),
        METADATA_SERVICE_BASE_URL: Joi.string().required(),
        PASSWORD_ENCRYPTION_KEY: Joi.string().required(),
        REQUESTER_TEMPLATE_ID: Joi.string().required(),
        CACHING_SERVICE_BASE_URL: Joi.string().required(),
        ENABLE_CACHE: Joi.number().required(),
        RMQ_DELETE_VM_QUEUE: Joi.string().required(),
        RMQ_DELETE_PACE_VM_QUEUE: Joi.string().required(),
        RMQ_DELETE_VM_QUEUE_DURABLE: Joi.string().required(),
        RMQ_DELETE_PACE_VM_QUEUE_DURABLE: Joi.string().required(),
        RMQ_CREATE_DB_QUEUE: Joi.string().required(),
        RMQ_CREATE_DB_QUEUE_DURABLE: Joi.string().required(),
        APPROVE_OWN_REQUEST: Joi.boolean().default(false),
        GOLDEN_CONFIG_SERVICE_BASE_URL: Joi.string().required(),
        DB_PASSWORD_LENGTH: Joi.number().required(),
        ENVIRONMENT_BASE_URL: Joi.string().required(),
        LOG_LEVEL: Joi.string().required(),
        CAPACITY_PLANNING_SERVICE_BASE_URL: Joi.string().required(),
        NEBULA_METRICS_SERVICE_BASE_URL: Joi.string().required(),
        RMQ_COMMON_FIREWALL_POLICY_QUEUE: Joi.string().required(),
        RMQ_COMMON_FIREWALL_POLICY_QUEUE_DURABLE: Joi.string().required(),
        RMQ_FIREWALL_V2_QUEUE: Joi.string().required(),
        RMQ_FIREWALL_V2_QUEUE_DURABLE: Joi.string().required(),
        VM_START_STOP_DELAY: Joi.string().required(),
        VM_RESTART_DELAY: Joi.string().required(),
        RMQ_STORAGE_S3_QUEUE: Joi.string().required(),
        RMQ_STORAGE_S3_QUEUE_DURABLE: Joi.string().required(),
        RMQ_STORAGE_NFS_QUEUE: Joi.string().required(),
        RMQ_STORAGE_NFS_QUEUE_DURABLE: Joi.string().required(),
        RMQ_START_STOP_RESTART_VM_QUEUE: Joi.string().required(),
        RMQ_START_STOP_RESTART_VM_QUEUE_DURABLE: Joi.string().required(),
        RMQ_BLUE_COMPUTE_QUEUE: Joi.string().required(),
        RMQ_BLUE_COMPUTE_QUEUE_DURABLE: Joi.string().required(),
        STORAGE_SERVICE_BASE_URL: Joi.string().required(),
        DEFAULT_CREATED_BY_FOR_CHILD: Joi.string().required(),
        RMQ_AWS_CREATE_SUBACCOUNT_QUEUE: Joi.string().required(),
        RMQ_AWS_CREATE_SUBACCOUNT_QUEUE_DURABLE: Joi.boolean().required(),
        ACTIVITY_LOGS_SERVICE_BASE_URL: Joi.string().required(),
        RISK_ANALYSIS_LOG_STEP: Joi.number().required(),
        GIT_PIPELINE_LOG_STEP: Joi.number().required(),
        DAP_UPDATE_LOG_STEP: Joi.number().required(),
        DAP_DEPLOYMENT_LOG_STEP: Joi.number().required(),
        DAP_DEPLOYMENT_CHILD_LOG_STEP: Joi.number().required(),
        DESIGNER_RESULTS_LOG_STEP: Joi.number().required(),
        COMPLETING_LOG_FW2_CODE: Joi.string().required(),
        COMPLETING_LOG_FW2_NAME: Joi.string().required(),
        RMQ_AWS_CREATE_SUBACCOUNT_GROUP_QUEUE: Joi.string().required(),
        RMQ_AWS_CREATE_SUBACCOUNT_GROUP_QUEUE_DURABLE: Joi.boolean().required(),
        TICKET_MANAGEMENT_SERVICE_BASE_URL: Joi.string().required(),
        JIRA_API_BASE_URL: Joi.string().required(),
        RMQ_AWS_CREATE_USER_QUEUE: Joi.string().required(),
        RMQ_AWS_CREATE_USER_QUEUE_DURABLE: Joi.boolean().required(),
        OBJECTSTORAGE_F5_LOAD_BALANCER_PATH: Joi.string().required(),
        RMQ_MANAGE_SUB_ACCOUNT_GROUP_PERMISSION_QUEUE: Joi.string().required(),
        RMQ_MANAGE_SUB_ACCOUNT_GROUP_PERMISSION_QUEUE_DURABLE:
          Joi.boolean().required(),
        RMQ_MANAGE_PERMISSION_SET_CREATE_QUEUE: Joi.string().required(),
        RMQ_MANAGE_PERMISSION_SET_CREATE_QUEUE_DURABLE:
          Joi.boolean().required(),
        RMQ_MANAGE_PERMISSION_SET_REMOVE_QUEUE: Joi.string().required(),
        RMQ_MANAGE_PERMISSION_SET_REMOVE_QUEUE_DURABLE:
          Joi.boolean().required(),
        RMQ_MODIFY_STORAGE_S3_QUEUE: Joi.string().required(),
        RMQ_MODIFY_STORAGE_S3_QUEUE_DURABLE: Joi.boolean().required(),
        RMQ_DELETE_S3_QUEUE: Joi.string().required(),
        RMQ_DELETE_S3_QUEUE_DURABLE: Joi.string().required(),
        RMQ_DELETE_NFS_QUEUE: Joi.string().required(),
        RMQ_DELETE_NFS_QUEUE_DURABLE: Joi.string().required(),
        RMQ_F5_LOAD_BALANCE_QUEUE: Joi.string().required(),
        RMQ_F5_LOAD_BALANCE_QUEUE_DURABLE: Joi.boolean().required(),
        RMQ_WORKFLOW_RETRY_QUEUE: Joi.string().required(),
        RMQ_WORKFLOW_RETRY_QUEUE_DURABLE: Joi.boolean().required(),
        QUALYS_SERVER_URL: Joi.string().required(),
        QUALYS_CUSTOMER_ID: Joi.string().required(),
        RMQ_CHARTER_LAB_COMPUTE_QUEUE: Joi.string().required(),
        RMQ_CHARTER_LAB_COMPUTE_QUEUE_DURABLE: Joi.boolean().default(true),
        RMQ_VMWARE_COMPUTE_QUEUE: Joi.string().required(),
        RMQ_VMWARECOMPUTE_QUEUE_DURABLE: Joi.boolean().default(true),
        RMQ_VMWARE_LAB_COMPUTE_QUEUE: Joi.string().required(),
        RMQ_VMWARE_LAB_COMPUTE_QUEUE_DURABLE: Joi.boolean().default(true),
        CHARTER_LAB_INFOBLOX_ENABLED: Joi.boolean().default(true),
        MAX_RETRY_COUNT: Joi.number().default(10),
        PINXT_TOKEN_URL: Joi.string().required(),
        PINXT_TOKEN_AUTHORIZATION: Joi.string().required(),
        USE_PROXY: Joi.boolean().default(false),
        PROXY_HOST: Joi.string().when('USE_PROXY', {
          is: true,
          then: Joi.required(),
        }),
        PROXY_PORT: Joi.number()
          .port()
          .when('USE_PROXY', { is: true, then: Joi.required() }),
        PROXY_PROTOCOL: Joi.string()
          .valid('http', 'https')
          .when('USE_PROXY', { is: true, then: Joi.required() }),
        AWS_MANAGEMENT_BASE_URL: Joi.string().required(),
        RMQ_DELETE_DB_QUEUE: Joi.string().required(),
        RMQ_DELETE_DB_QUEUE_DURABLE: Joi.string().required(),
        RMQ_BULK_VM_IMPORT_QUEUE: Joi.string().required(),
        RMQ_BULK_VM_IMPORT_QUEUE_DURABLE: Joi.string().required(),
        RMQ_ONBOARD_PROJECT_QUEUE: Joi.string().required(),
        RMQ_ONBOARD_PROJECT_QUEUE_DURABLE: Joi.boolean().default(true),
        RMQ_CATALOG_ACCESS_REQUEST_QUEUE: Joi.string().required(),
        RMQ_CATALOG_ACCESS_REQUEST_QUEUE_DURABLE: Joi.boolean().default(true),
        RMQ_SPEC_FLOW_QUEUE: Joi.string().required(),
        RMQ_SPEC_FLOW_QUEUE_DURABLE: Joi.boolean().default(true),
        APPROVALS_EMAIL_URL: Joi.string().required(),
        PROCESSING_START_LINE_FOR_DFM_WITH_INSTRUCTIONS:
          Joi.number().default(12),
        PROCESSING_START_LINE_FOR_DFM_WITHOUT_INSTRUCTIONS:
          Joi.number().default(11),
        RMQ_ONBOARD_GROUP_QUEUE: Joi.string().required(),
        RMQ_ONBOARD_GROUP_QUEUE_DURABLE: Joi.boolean().default(true),
        RMQ_CREATE_NAMESPACE_QUEUE: Joi.string().required(),
        RMQ_CREATE_NAMESPACE_QUEUE_DURABLE: Joi.boolean().default(true),
        CMDB_SERVICE_BASE_URL: Joi.string().required(),
        CHERWELL_RETRY_REQUESTING_USER: Joi.string().required(),
        REMEDY_RETRY_REQUESTING_USER: Joi.string().required(),
        OPTIMIZATION_SPLIT_LENGTH: Joi.number().default(1800),
        AWS_IPAM_REFRESH_MINUTES: Joi.number().default(30),
        USE_AWS_IPAM_PROXY: Joi.boolean().default(false),
        RMQ_DELETE_PUBLIC_CLOUD_QUEUE: Joi.string().required(),
        RMQ_DELETE_PUBLIC_CLOUD_QUEUE_DURABLE: Joi.boolean().default(true),
        RMQ_DELETE_VM_VMWARE_QUEUE: Joi.string().required(),
        RMQ_DELETE_VM_VMWARE_QUEUE_DURABLE: Joi.boolean().default(true),
        AWS_IPAM_PROXY_HOST: Joi.string().when('USE_AWS_IPAM_PROXY', {
          is: true,
          then: Joi.required(),
        }),
        AWS_IPAM_PROXY_PORT: Joi.number()
          .port()
          .when('USE_AWS_IPAM_PROXY', { is: true, then: Joi.required() }),
        AWS_IPAM_PROXY_PROTOCOL: Joi.string()
          .valid('http', 'https')
          .when('USE_AWS_IPAM_PROXY', { is: true, then: Joi.required() }),
        SECRETS_MANAGEMENT_SERVICE_BASE_URL: Joi.string().required(),
        ENABLE_DNP_SERVICE: Joi.boolean().default(false),
        DNPDAP_SERVICE_BASE_URL: Joi.string().required(),
        NEBULA_VMWARE_SERVICE_BASE_URL: Joi.string(),
        RMQ_RECONFIGURE_VM_QUEUE: Joi.string().required(),
        RMQ_PACE_RECONFIGURE_VM_QUEUE: Joi.string().required(),
        RMQ_RECONFIGURE_VM_QUEUE_DURABLE: Joi.boolean().required(),
        RMQ_RECONFIGURE_PACE_VM_QUEUE_DURABLE: Joi.boolean().required(),
        TOTAL_MEMORY_VALUE: Joi.number().required(),
        APPEND_CONSOLE_URL: Joi.string().required(),
        ADHOC_LOCATION: Joi.string().required(),
        ADHOC_NAMESPACE: Joi.string().required(),
        RMQ_EDIT_VM_VMWARE_QUEUE: Joi.string().required(),
        RMQ_EDIT_VM_VMWARE_QUEUE_DURABLE: Joi.boolean().required(),
        RMQ_CREATE_SECRET_QUEUE: Joi.string().required(),
        RMQ_CREATE_SECRET_QUEUE_DURABLE: Joi.boolean().required(),
        RMQ_EDIT_PACE_VM_VMWARE_QUEUE: Joi.string().required(),
        RMQ_EDIT_PACE_VM_VMWARE_QUEUE_DURABLE: Joi.boolean().required(),
        RETRY_MAX_COUNT: Joi.number().optional(),
        RETRY_TIME_DELAY: Joi.number().optional(),
        RMQ_WORKFLOW_RETRY_VMWARE_QUEUE: Joi.string().required(),
        RMQ_WORKFLOW_RETRY_VMWARE_QUEUE_DURABLE: Joi.boolean().required(),
        WATCHER_SERVICE_BASE_URL: Joi.string(),
        SECRETS_ROTATION_INTERVAL_IN_MINS: Joi.string().required(),
        TOKEN_EXPIRY_NOTIFICATION_TEMPLATE_ID: Joi.string().required(),
        FW_CREATE_JIRA_TICKET_STEP: Joi.number().required(),
        FW_CANCEL_JIRA_TICKET_STEP: Joi.number().required(),
        FW_CLOSE_JIRA_TICKET_STEP: Joi.number().required(),
        FW_CANCEL_TUFIN_TICKET_STEP: Joi.number().required(),
        REACTIVATE_SECRET_DURATION_HRS: Joi.number().required(),
        VAULT_TOKEN_RENEWAL_RATIO: Joi.number().required(),
        VAULT_MASTER_NAMESPACE: Joi.string().required(),
        MASTER_TOKEN_FOLDER: Joi.string().required(),
        VMWARE_ADMIN_SERVICE_BASE_URL: Joi.string().required(),
        VALID_SPECIAL_CHARS: Joi.string().required(),
        DELAY_FAILED_SECRET_ROTATION_HOURS: Joi.number().required(),
        ENVIRONMENT: Joi.string().required(),
        SECRET_PASSWORD_MIN_LENGTH: Joi.string().required(),
        RMQ_UPDATE_SECRET_QUEUE: Joi.string().required(),
        RMQ_UPDATE_SECRET_QUEUE_DURABLE: Joi.boolean().required(),
      }),
    }),
    LoggerModule,
    NaasModule,
    SecurityModule,
    AssetsModule,
    ComputeModule,
    ObjectStorageModule,
    ResourcesModule,
    IamModule,
    ProjectsModule,
    EncryptionModule,
    PollRequestModule,
    DbaasModule,
    CachingModule,
    StatusModule,
    GoldenConfigModule,
    GoldenConfigWrapperModule,
    ActivityLogsModule,
    TufinModule,
    TicketManagementModule,
    CapacityPlanningModule,
    NebulaMetricsModule,
    CommonFirewallModule,
    StorageModule,
    RestModule,
    AwsModule,
    JiraManagementModule,
    LoadBalancerModule,
    CertificateModule,
    SitemapModule,
    ImportVMModule,
    ProjectModule,
    CatalogAccessModule,
    GroupModule,
    MultiEnvModule,
    SearchModule,
    CmdbModule,
    SpecFlowModule,
    VmwareModule,
    SecureStoreModule,
    ZtpModule,
    ActionModule,
    VenafiModule,
    MetadataModule,
    VmwareAdminModule,
    AwsCherwellModule,
  ],
  controllers: [AppController],
  providers: [
    { provide: APP_FILTER, useClass: AllExceptionsFilter },
    AppService,
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(RequestIdMiddleware).forRoutes('*');
    consumer.apply(LoggerMiddleware).forRoutes('*');
  }
}
