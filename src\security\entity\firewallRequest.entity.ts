import { ApprovalStatus, RequestStatus, RequestType } from '../../types';
import {
  IsArray,
  IsDate,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { DesignerResults } from '../dto/firewall.designer-result.request.dto';
import { Prop } from '@nestjs/mongoose';

export class BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

class RiskViolations {
  sources: string[];
  destinations: string[];
  violatingServices: string[];
}

class SecurityRequirements {
  policy: string;
  fromZone: string;
  toZone: string;
  allowedServices: string[];
  flowsAllowed?: string[];
  blockedAllServices?: string;
}

class RiskAnalysis {
  severity: string;
  violations: RiskViolations;
  securityRequirements: SecurityRequirements;
}

class TufinTaskUpdateDto {
  requestId: string;

  tufinTicketId: string;

  taskId: string;

  taskName: string;

  status: JobStatusEnum;

  message: string;

  errors?: ErrorDetailDto[];

  timestamp: Date;
}

class WorkflowStepResult {
  stepName: string;
  taskResult: Record<string, any>;
}

class Tufin {
  ticketId: string;
  status?: string;
  ticketUrl?: string;
  riskAnalysisUpdated?: boolean;
  riskAnalaysis?: RiskAnalysis[];
  designerResults?: DesignerResults;
  designerResultsUpdated?: boolean;
  jobUpdates?: TufinTaskUpdateDto[];
  workflowStepsResults?: WorkflowStepResult[];
}

class Jira {
  ticketId?: string;
  status?: string;
  ticketUrl?: string;
}

class Cherwell {
  ticketId?: string;
  status?: string;
  ticketUrl?: string;
}

class Remedy {
  ticketId?: string;
  status?: string;
  ticketUrl?: string;
  mopId?: string;
  error?: ErrorDetailDto[];
}

enum JobStatusEnum {
  SUCCESS = 'success',
  FAILURE = 'failure',
  IN_PROGRESS = 'in_progress',
  PENDING = 'pending',
}

class ErrorDetailDto {
  errorCode: string;
  errorMessage: string;
}

export class ReflowHistory {
  reflowedAt: Date;
  reflowedBy: string;
  reflowedStatus: JobStatusEnum;
}

export class ApprovalNotification {
  notifiedAt: Date;
  notifiedGroup: string[];
  notifiedLevel: number;
  notificationStatus: JobStatusEnum;
}

class TufinTask {
  taskId: string;

  taskName: string;

  status: JobStatusEnum;

  message: string;

  timestamp: Date;

  errors?: ErrorDetailDto[];
}

export class TicketDetails {
  tufin: Tufin;
  jira: Jira;
  cherwell: Cherwell;
  remedy: Remedy;
}

export class FirewallRequestEntity extends BaseEntity {
  requestType: RequestType;
  requestId: string;
  ipType: string;
  ruleIds: Number[];
  organizationName: string;
  approvalStatus: ApprovalStatus;
  subRequestId: string;
  status: RequestStatus;
  ticketDetails: TicketDetails;
  startedAt?: Date;
  completedAt?: Date;
  isSubRequestInValidated: boolean;
  reflowHistory: ReflowHistory[];
  approvalNotification: ApprovalNotification[];
}

export type CreateFirewallRequestEntity = Omit<
  FirewallRequestEntity,
  'createdBy' | 'updatedAt' | keyof BaseEntity
>;
