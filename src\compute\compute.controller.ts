import {
  Controller,
  Post,
  Body,
  UsePipes,
  ValidationPipe,
  UseGuards,
  Get,
  Param,
  Query,
  Patch,
  Put,
} from '@nestjs/common';

import { ComputeService } from './compute.service';
import { ComputeWrapperService } from '../compute-wrapper/compute-wrapper.service';
import {
  ValidateHostNamesRequestDto,
  MorpheusData,
  ReferenceData,
  ValidateHostNamesResponseDto,
  BlueReferenceData,
  VmwareReferenceData,
  VmwareCluster,
  VmwareData,
  VMwareConsoleDTO,
} from './dto/compute.dto';
import {
  ProvisionVmRequestDto,
  IpReservationDownstreamResponseDto,
  VmRetryRequestDto,
  ProvisionVmwareVmRequestDto,
} from './dto/provisionVm.dto';
import { ServiceRequestResponseDto } from '../security/dto/firewall.request.dto';
import { Permission } from '../iam/permission.decorator';
import {
  envIdResolver,
  projectIdResolver,
  VMRquestTypeResolver,
} from './utils';
import { PermissionKey } from '../iam/types';
import { PermissionGuard } from '../iam/permission.guard';
import { ConfigService } from '@nestjs/config';
import { ENVIRONMENT_VARS, IpModes } from '../utils/constants';
import { ApiBearerAuth, ApiBody, ApiSecurity, ApiTags } from '@nestjs/swagger';
import { LoggerService } from '../loggers/logger.service';
import { GetIpvAddressDto } from './dto/getAvailableIp.dto';
import { ProvisionBlueVmRequestDto } from './dto/provisionBlueVm.dto';
import { RequestContext } from 'nestjs-request-context';
import { ServiceRequestEntity } from '../naas/entities/serviceRequest.entity';
import { NebulaConfigMap, RequestType } from '../types';
import { SearchVMQueryDTO } from './dto/searchquery.dto';
import { EnvPermissionV2 } from '../iam/envPermissionV2.decorator';
import { OptionTypeListDTOByCloud } from './dto/optionTypeListByCloud.dto';
import { ReConfigureDto } from './dto/reConfigure.dto';
import { ReConfigureSuccessVmDto } from './dto/reConfigureVm.dto';
import { EditVmRequestDto } from './dto/editVmRequest.dto';
import { EditVmSuccessVmDto } from './dto/editVmResponse.dto';

@ApiTags('Compute')
@ApiBearerAuth()
@ApiSecurity('x-nebula-authorization')
@Controller('compute')
@UseGuards(PermissionGuard)
@UsePipes(new ValidationPipe({ whitelist: true, stopAtFirstError: true }))
export class ComputeController {
  constructor(
    private readonly computeService: ComputeService,
    private readonly computeWrapperService: ComputeWrapperService,
    private configService: ConfigService,
    private readonly logger: LoggerService,
  ) {}

  @Post('validate-hostnames')
  @UsePipes(new ValidationPipe())
  async validateHostNames(
    @Body() validateHostNamesRequestDto: ValidateHostNamesRequestDto,
  ) {
    const response: ValidateHostNamesResponseDto =
      await this.computeService.validateHostNames(validateHostNamesRequestDto);
    return response;
  }

  @Post('virtual-machine')
  @Permission(VMRquestTypeResolver, PermissionKey.CREATE)
  async provisionVm(
    @Body(ValidationPipe) provisionVmRequestDto: ProvisionVmRequestDto,
  ): Promise<ServiceRequestResponseDto> {
    if (provisionVmRequestDto.ipModes === IpModes.STATIC_MANUAL) {
      const staticIps = {
        vmCount: provisionVmRequestDto.vmCount,
        ipv4: provisionVmRequestDto.ipv4,
        ipv6: provisionVmRequestDto.ipv6,
        ipv4Addresses: provisionVmRequestDto.ipv4Addresses,
        ipv6Addresses: provisionVmRequestDto.ipv6Addresses,
        network: provisionVmRequestDto.network,
      };
      await this.computeService.validateStaticIps(staticIps);
    }
    if (
      provisionVmRequestDto?.secondaryNetwork?.ipModes === IpModes.STATIC_MANUAL
    ) {
      const secondaryStaticIps = {
        vmCount: provisionVmRequestDto.vmCount,
        ipv4: provisionVmRequestDto.secondaryNetwork.ipv4,
        ipv6: provisionVmRequestDto.secondaryNetwork.ipv6,
        ipv4Addresses: provisionVmRequestDto.secondaryNetwork.ipv4Addresses,
        ipv6Addresses: provisionVmRequestDto.secondaryNetwork.ipv6Addresses,
        network: provisionVmRequestDto.secondaryNetwork.network,
      };
      await this.computeService.validateStaticIps(secondaryStaticIps);
    }
    return await this.computeService.provisionVm(provisionVmRequestDto);
  }

  @Post('blue/virtual-machine')
  @Permission(VMRquestTypeResolver, PermissionKey.CREATE)
  async provisionBlueVm(
    @Body(ValidationPipe) provisionBlueVmRequestDto: ProvisionBlueVmRequestDto,
  ): Promise<ServiceRequestResponseDto> {
    this.logger.log('passing provisionBlueVmRequestDto to compute service');
    return await this.computeService.provisionBlueVm(provisionBlueVmRequestDto);
  }

  @Post('virtual-machine-retry')
  @EnvPermissionV2(envIdResolver, PermissionKey.UPDATE)
  async provisionVmRetry(
    @Body(ValidationPipe) vmRetryRequestDto: VmRetryRequestDto,
  ) {
    this.logger.log('passing vmRetryRequestDto to compute service');
    return await this.computeService.provisionVmRetry(
      RequestType.RETRY_VM,
      vmRetryRequestDto,
    );
  }

  // @Anonymous()
  @Post('retrigger-vm-creation')
  async retriggerVmCreation(
    @Body(ValidationPipe) vmRetryRequestDto: VmRetryRequestDto,
  ) {
    this.logger.log('Retriggering VM creation');
    return await this.computeService.retriggerVmCreation(vmRetryRequestDto);
  }

  @Get('reference-data/:catalogItemShortName')
  async fetchRefernceData(
    @Param('catalogItemShortName') catalogItemShortName: string,
  ): Promise<ReferenceData> {
    return await this.computeService.fetchReferenceData(catalogItemShortName);
  }
  @Get('blue/reference-data/:catalogItemShortName')
  async fetchBlueRefernceData(
    @Param('catalogItemShortName') catalogItemShortName: string,
  ): Promise<BlueReferenceData> {
    return await this.computeService.fetchBlueReferenceData(
      catalogItemShortName,
    );
  }

  @Get('blue/resources')
  async fetchBlueResources(
    @Query('cloudId') cloudId: number,
    @Query('network') network: string,
  ): Promise<MorpheusData[]> {
    this.logger.debug(' trying to get it resource from Blue morpheus');
    return await this.computeWrapperService.fetchBlueResources(
      cloudId,
      network,
    );
  }

  @Get('resources/:cloudId')
  async fetchResources(
    @Param('cloudId') cloudId: number,
  ): Promise<MorpheusData[]> {
    //check if cache is enabled or not, if not get it from morpheus
    this.logger.debug(
      `Cache is ${this.configService?.get(ENVIRONMENT_VARS?.ENABLE_CACHE)}`,
    );
    const req = RequestContext.currentContext.req;
    const requestUser = req.user;
    if (this.configService.get(ENVIRONMENT_VARS.ENABLE_CACHE)) {
      this.logger.debug('Cache is enabled, trying to get it from cache');
      const resources = this.computeService?.fetchCacheResourceData(
        cloudId,
        requestUser.email,
      );
      return resources;
    } else {
      this.logger.debug('Cache is disbaled, trying to get it from mmorpheus');
      return await this.computeWrapperService?.fetchResources(
        cloudId,
        requestUser.email,
      );
    }
  }

  @ApiBearerAuth()
  @Get('ipAddresses')
  async getAvailableIps(@Body() data: GetIpvAddressDto) {
    this.logger.debug('Received request to get all available ips', data);
    return await this.computeService.getAvailableIps(data);
  }

  @Get('generic/:environment/reference-data/:catalogItemShortName')
  async fetchRefernceDataGeneric(
    @Param('environment') environment: string,
    @Param('catalogItemShortName') catalogItemShortName: string,
  ): Promise<ReferenceData> {
    return await this.computeService.fetchReferenceDataGeneric(
      environment,
      catalogItemShortName,
    );
  }

  @Post('generic/:environment/virtual-machine')
  @Permission(VMRquestTypeResolver, PermissionKey.CREATE)
  async provisionVmGeneric(
    @Param('environment') environment: string,
    @Body(ValidationPipe) provisionVmRequestDto: ProvisionVmRequestDto,
  ): Promise<ServiceRequestResponseDto> {
    if (
      this.configService.get<boolean>(
        ENVIRONMENT_VARS.CHARTER_LAB_INFOBLOX_ENABLED,
      )
    ) {
      if (provisionVmRequestDto.ipModes === IpModes.STATIC_MANUAL) {
        const staticIps = {
          vmCount: provisionVmRequestDto.vmCount,
          ipv4: provisionVmRequestDto.ipv4,
          ipv6: provisionVmRequestDto.ipv6,
          ipv4Addresses: provisionVmRequestDto.ipv4Addresses,
          ipv6Addresses: provisionVmRequestDto.ipv6Addresses,
          network: provisionVmRequestDto.network,
        };
        await this.computeService.validateStaticIps(staticIps);
      }
      if (
        provisionVmRequestDto?.secondaryNetwork?.ipModes ===
        IpModes.STATIC_MANUAL
      ) {
        const secondaryStaticIps = {
          vmCount: provisionVmRequestDto.vmCount,
          ipv4: provisionVmRequestDto.secondaryNetwork.ipv4,
          ipv6: provisionVmRequestDto.secondaryNetwork.ipv6,
          ipv4Addresses: provisionVmRequestDto.secondaryNetwork.ipv4Addresses,
          ipv6Addresses: provisionVmRequestDto.secondaryNetwork.ipv6Addresses,
          network: provisionVmRequestDto.secondaryNetwork.network,
        };
        await this.computeService.validateStaticIps(secondaryStaticIps);
      }
    }
    return await this.computeService.provisionVmGeneric(
      environment,
      provisionVmRequestDto,
    );
  }

  @Get('generic/:environment/network')
  async getGenericNetwork(
    @Param('environment') environment: string,
    @Query('cloudId') cloudId: string,
    @Query('catalogName') catalogName: string,
  ) {
    this.logger.log(
      `request received for getting networks for ${environment} with cloudId: ${cloudId}`,
    );
    return this.computeService.getGenericNetwork(
      environment,
      cloudId,
      catalogName,
    );
  }
  @Get('generic/:environment/appRef')
  async getGenericAppRef(
    @Query('appRef') appRef: string,
    @Param('environment') environment: string,
  ) {
    this.logger.log(
      `request received for getting networks for getting app refId ${appRef}`,
    );
    return this.computeService.getGenericAppRef(environment, appRef);
  }

  @Get('generic/:environment/adGroup')
  async getGenericADGroup(
    @Query('adGroup') adGroup: string,
    @Param('environment') environment: string,
  ) {
    this.logger.log(
      `request received for getting networks for getting adGrouup ${adGroup}`,
    );
    return this.computeService.getGenericADGroup(environment, adGroup);
  }

  @Post('generic/:environment/virtual-machine/validate-hostnames')
  @UsePipes(new ValidationPipe())
  async validateHostNamesGeneric(
    @Param('environment') environment: string,
    @Body() validateHostNamesRequestDto: ValidateHostNamesRequestDto,
  ) {
    const response: ValidateHostNamesResponseDto =
      await this.computeService.validateHostNamesGeneric(
        environment,
        validateHostNamesRequestDto,
      );
    return response;
  }

  @Get('generic/:envrionment/resources/:cloudId')
  async fetchResourcesGeneric(
    @Param('envrionment') envrionment: string,
    @Param('cloudId') cloudId: number,
  ): Promise<MorpheusData[]> {
    //check if cache is enabled or not, if not get it from morpheus
    this.logger.debug(
      `Cache is ${this.configService?.get(ENVIRONMENT_VARS?.ENABLE_CACHE)}`,
    );
    const req = RequestContext.currentContext.req;
    const requestUser = req.user;
    this.logger.debug('Cache is disbaled, trying to get it from mmorpheus');
    return await this.computeWrapperService?.fetchResourcesGeneric(
      envrionment,
      cloudId,
      requestUser.email,
    );
  }
  // @Anonymous()
  @Patch('downstream-response/ip-reservation/:serviceRequestId')
  @ApiBody({ type: IpReservationDownstreamResponseDto })
  async appendIpReservationDownstreamResponse(
    @Param('serviceRequestId') serviceRequestId: string,
    @Body()
    ipReservationDownstreamResponseDto: IpReservationDownstreamResponseDto,
  ): Promise<ServiceRequestEntity> {
    return this.computeService.appendIpReservationDownstreamResponse(
      serviceRequestId,
      ipReservationDownstreamResponseDto,
    );
  }

  @Get('/virtual-machine/:environment/search')
  async searchVmDetails(
    @Param('environment') environment: string,
    @Query() searchVMQueryDTO: SearchVMQueryDTO,
  ) {
    this.logger.log('search vm params', searchVMQueryDTO, environment);
    const searchVmDetails = await this.computeService.searchVmDetails(
      searchVMQueryDTO,
      environment,
    );
    return searchVmDetails;
  }

  @Get('/userRoles')
  async fetchUserRoles() {
    this.logger.log('request recevied for fetching user roles');
    return await this.computeService.fetchUsersRoles();
  }

  @Get('/network/:id')
  async getNetworkById(@Param('id') id: number) {
    this.logger.log('request received for fetching network id: ', id);
    return await this.computeService.getNetworkByNetworkId(id);
  }

  @Get(
    'vm/domain/:domain/datacenter/:datacenter/reference-data/:catalogItemShortName',
  )
  async fetchRefernceDataVmware(
    @Param('domain') domain: string,
    @Param('datacenter') datacenter: string,
    @Param('catalogItemShortName') catalogItemShortName: string,
  ): Promise<VmwareReferenceData> {
    const response = await this.computeService.fetchReferenceDataVmware(
      domain,
      datacenter,
      catalogItemShortName,
    );
    this.logger.log('fetchRefernceDataVmware.response ', response);
    return response;
  }

  @Post('vm/domain/:domain/datacenter/:datacenter/provision')
  @Permission(VMRquestTypeResolver, PermissionKey.CREATE)
  async provisionVmVmware(
    @Param('domain') domain: string,
    @Param('datacenter') datacenter: string,
    @Body(ValidationPipe) provisionVmRequestDto: ProvisionVmwareVmRequestDto,
  ): Promise<ServiceRequestResponseDto> {
    if (
      this.configService.get<boolean>(
        ENVIRONMENT_VARS.CHARTER_LAB_INFOBLOX_ENABLED,
      )
    ) {
      if (provisionVmRequestDto.ipModes === IpModes.STATIC_MANUAL) {
        const staticIps = {
          vmCount: provisionVmRequestDto.vmCount,
          ipv4: provisionVmRequestDto.ipv4,
          ipv6: provisionVmRequestDto.ipv6,
          ipv4Addresses: provisionVmRequestDto.ipv4Addresses,
          ipv6Addresses: provisionVmRequestDto.ipv6Addresses,
          network: provisionVmRequestDto.network,
        };
        await this.computeService.validateStaticIps(staticIps);
      }
      if (
        provisionVmRequestDto?.secondaryNetwork?.ipModes ===
        IpModes.STATIC_MANUAL
      ) {
        const secondaryStaticIps = {
          vmCount: provisionVmRequestDto.vmCount,
          ipv4: provisionVmRequestDto.secondaryNetwork.ipv4,
          ipv6: provisionVmRequestDto.secondaryNetwork.ipv6,
          ipv4Addresses: provisionVmRequestDto.secondaryNetwork.ipv4Addresses,
          ipv6Addresses: provisionVmRequestDto.secondaryNetwork.ipv6Addresses,
          network: provisionVmRequestDto.secondaryNetwork.network,
        };
        await this.computeService.validateStaticIps(secondaryStaticIps);
      }
    }
    return await this.computeService.provisionVmVmware(
      domain,
      datacenter,
      provisionVmRequestDto,
    );
  }

  @Post('vm/domain/:domain/datacenter/:datacenter/validate-hostnames')
  @UsePipes(new ValidationPipe())
  async validateHostNamesVmware(
    @Param('domain') domain: string,
    @Param('datacenter') datacenter: string,
    @Body() validateHostNamesRequestDto: ValidateHostNamesRequestDto,
  ) {
    const response: ValidateHostNamesResponseDto =
      await this.computeService.validateHostNamesVmware(
        domain,
        datacenter,
        validateHostNamesRequestDto,
      );
    return response;
  }

  @Put('vm/domain/:domain/datacenter/:datacenter/reconfigure')
  //@EnvPermission(MultipleRequestTypeResolver, PermissionKey.UPDATE)
  async reConfigureVm(
    @Param('domain') domain: string,
    @Param('datacenter') datacenter: string,
    @Body() reConfigureDto: ReConfigureDto,
  ): Promise<ReConfigureSuccessVmDto> {
    this.logger.log('reconfigure Vm request', reConfigureDto);
    return await this.computeService.reConfigureVm(
      domain,
      datacenter,
      reConfigureDto,
    );
  }

  @Get('vm/domain/:domain/datacenter/:datacenter/cloudId/:cloudId/resources')
  async fetchResourcesVmware(
    @Param('domain') domain: string,
    @Param('datacenter') datacenter: string,
    @Param('cloudId') cloudId: string,
  ): Promise<VmwareCluster[]> {
    const req = RequestContext.currentContext.req;
    const requestUser = req.user;
    this.logger.debug(
      'Caching service is disabled for now , trying to get it from vCenter',
      cloudId,
    );
    const vmwareData = await this.computeWrapperService?.fetchResourcesVmware(
      domain,
      datacenter,
      cloudId,
      requestUser.email,
    );

    return vmwareData;
  }

  @Get(
    'vm/domain/:domain/datacenter/:datacenter/cloudId/:cloudId/resources/network/:networkId',
  )
  async fetchResourcesVmwareByNetworkId(
    @Param('domain') domain: string,
    @Param('datacenter') datacenter: string,
    @Param('cloudId') cloudId: string,
    @Param('networkId') networkId: string,
  ): Promise<VmwareCluster[]> {
    this.logger.log(
      'request received for getting cluster by networkId',
      domain,
      datacenter,
      networkId,
    );
    const vmwareData =
      await this.computeWrapperService?.fetchResourcesVmwareByNetworkId(
        domain,
        datacenter,
        cloudId,
        networkId,
      );

    return vmwareData;
  }

  @Get('vm/domain/:domain/datacenter/:datacenter/networks/:networkId')
  async fetchVMwareNetwork(
    @Param('domain') domain: string,
    @Param('datacenter') datacenter: string,
    @Param('networkId') networkId: string,
  ): Promise<VmwareCluster[]> {
    this.logger.debug('getting networks  vCenter');
    const vmwareData = await this.computeWrapperService?.fetchVMwareNetwork(
      domain,
      datacenter,
      networkId,
    );

    return vmwareData;
  }

  @Get('generic/:environment/optionlist/resources/cloud')
  async getGenericCloudResourcesByCloud(
    @Param('environment') environment: string,
    @Query() optionTypeListDTOByCloud: OptionTypeListDTOByCloud,
  ) {
    this.logger.log(
      `request received for getting cloud resources . CloudID : ${optionTypeListDTOByCloud.cloudId} , Env : ${environment}`,
    );
    return this.computeWrapperService.getGenericCloudResourcesByCloud(
      environment,
      optionTypeListDTOByCloud,
    );
  }

  @Get('vm/domain/:domain/datacenter/:datacenter/dnsdetails')
  async getDnsDetailsFromVmware(
    @Param('domain') domain: string,
    @Param('datacenter') datacenter: string,
  ) {
    this.logger.log('request recieved to get dns details from vmware');
    const dnsDetails = await this.computeWrapperService.getDnsDetailsFromVMWare(
      domain,
      datacenter,
    );
    this.logger.log(
      'dns details received from vmware in controller',
      dnsDetails,
    );
    return dnsDetails;
  }

  @Post(
    'vm/domain/:domain/datacenter/:datacenter/cloud/:cloudId/vmware/console',
  )
  async fetchVMwareConsole(
    @Param('domain') domain: string,
    @Param('datacenter') datacenter: string,
    @Param('cloudId') cloudId: string,
    @Body() vmwareConsoleDTO: VMwareConsoleDTO,
  ) {
    this.logger.debug(
      `fetch console details from vCenter for cloudId ${cloudId} and hostname ${vmwareConsoleDTO.hostName}`,
    );
    const vmwareData =
      await this.computeWrapperService?.getVMWareConsoleDetails(
        domain,
        datacenter,
        cloudId,
        vmwareConsoleDTO,
      );

    return vmwareData;
  }

  @Patch('vm/domain/:domain/datacenter/:datacenter/editVm')
  async editVmVmware(
    @Param('domain') domain: string,
    @Param('datacenter') datacenter: string,
    @Body() editVmRequestDto: EditVmRequestDto,
  ): Promise<EditVmSuccessVmDto> {
    this.logger.log('request received to edit vm', editVmRequestDto);
    const editVm = await this.computeService.editVmVmware(
      domain,
      datacenter,
      editVmRequestDto,
    );
    this.logger.log('requested successfully for edit vm');
    return editVm;
  }
}
