db.notificationtemplates.insertOne(
    {
    "templateId": "1014",
    "templateLocation": "",
    "notifications": [
      {
        "type": "email",
        "address": "",
        "message": "<html xmlns:v='urn:schemas-microsoft-com:vml' xmlns:o='urn:schemas-microsoft-com:office:office' xmlns:w='urn:schemas-microsoft-com:office:word' xmlns:x='urn:schemas-microsoft-com:office:excel' xmlns:m='http://schemas.microsoft.com/office/2004/12/omml' xmlns='http://www.w3.org/TR/REC-html40'> <head> <meta http-equiv=Content-Type content='text/html; charset=windows-1252'> <meta name=ProgId content=Word.Document> <meta name=Generator content='Microsoft Word 15'> <meta name=Originator content='Microsoft Word 15'> <style> td, th { word-break: break-word; } </style> </head> <body lang=EN-US link='#0563C1' vlink='#954F72' style='tab-interval:.5in'> <div class=WordSection1> <p class=MsoNormal> <o:p> </o:p> </p> <p class=MsoNormal>Dear Team,</p> <p class=MsoNormal><REPLACE_MESSAGE></p><br> <p class=MsoNormal><span style='color:#1F497D'> <o:p> </o:p> </span></p> <p class=MsoNormal>Please find the details below:</p> <table class=MsoNormalTable border=0 cellspacin\u001fg=0 cellpadding=0 style='margin-left:-.4pt;border-collapse:collapse;mso-yfti-tbllook: 1184;mso-padding-alt:0in 0in 0in 0in'> <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes'> <ROW_HEADER> <td valign=top style='border:solid windowtext 1.0pt; background:#0000FF;padding:0in 5.4pt 0in 5.4pt'> <p class=MsoNormal><span style='color:white'> <REPLACE_HEADER> <o:p></o:p> </span></p> </td> </ROW_HEADER> </tr> <ROW_NUMBER> <tr style='mso-yfti-irow:1'> <ROW_DATA> <td valign=top style='border:solid windowtext 1.0pt; border-top:none;padding:0in 5.4pt 0in 5.4pt'> <p class=MsoNormal><span style='color:black'> <REPLACE_VALUE> <o:p></o:p> </span></p> </td> </ROW_DATA> </tr> </ROW_NUMBER> <ROW_RESULTS> </ROW_RESULTS> </table> <br> <br><br> <p class=MsoNormal> <o:p> </o:p> </p><p class=MsoNormal>For further assistance, please contact the <strong>Nebula webex channel</strong></p> <p class=MsoNormal>With Regards,</p> <p class=MsoNormal>APS - Reliability Engineering Team</p> </div> </body> </html>",
        "contentType": "html",
        "title": "Request Status"
      }
    ]
  }
);
