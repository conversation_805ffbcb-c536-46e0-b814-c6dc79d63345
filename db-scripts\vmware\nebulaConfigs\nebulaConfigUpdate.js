const session = db.getMongo().startSession();

session.startTransaction();

try {
  db.nebulaconfigs.updateOne(
    { shortName: 'linux8&9v3admin' },
    {
      $set: {
        'displayName': 'Linux 8 & 9 V3 Admin',
      },
    },
    { upsert: true },
  );

  print(`Documents updated`);

  session.commitTransaction();
} catch (e) {
  session.abortTransaction();

  print('Transaction aborted due to error', e);
} finally {
  session.endSession();
}
