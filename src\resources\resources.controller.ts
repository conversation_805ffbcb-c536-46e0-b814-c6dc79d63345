import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  HttpStatus,
  NotFoundException,
  Param,
  Patch,
  Post,
  Put,
  Query,
  UseGuards,
  UsePipes,
  ValidationPipe,
  Version,
} from '@nestjs/common';
import { ResourcesService } from './resources.service';
import { ApiBearerAuth, ApiSecurity, ApiTags } from '@nestjs/swagger';
import {
  ResourcesEntity,
  ResourcesRequestDto,
} from '../naas/entities/resources.entity';
import { Anonymous } from '../auth/anonymous.decorator';
import { MultipleRequestTypeResolver } from './utils';
import { PermissionKey } from '../iam/types';
import { UpdateResourceDTO } from './dto/updateResource.request.dto';
import { LoggerService } from '../loggers/logger.service';
import { ResourceQueryDto } from './dto/resource.query.dto';
import { ExtendedResource } from './dto/extendedResource.dto';
import { ResourceUpdateSuccessDto } from './dto/resourceUpdateSuccess.dto';
import {
  PaginatedResponseDto,
  PaginationQueryDto,
} from '../utils/pagination/dto/pagination.dto';
import {
  RequestedVMsDataDto,
  RestartVmResourcesDto,
} from './dto/restartVmResources.dto';
import { ModifyResourceDto, RegenerateUser } from './dto/modifyResource.dto';
import {
  RestoreResourceDto,
  RestoreResourceStatusDto,
} from './dto/restoreStorage.dto';
import { EnvPermission } from '../iam/envPermission.decorator';
import { quotaResourceDTO } from './dto/quotaResource.dto';

@UsePipes(new ValidationPipe({ whitelist: true }))
@ApiTags('Resources')
@ApiSecurity('x-nebula-authorization')
@Controller('resources')
export class ResourcesController {
  constructor(
    private readonly resourcesService: ResourcesService,
    private readonly logger: LoggerService,
  ) {}

  @ApiBearerAuth()
  @Get('')
  async getResourcesList(): Promise<ResourcesEntity[]> {
    try {
      return await this.resourcesService.getResourcesList();
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  @Version('2')
  @ApiBearerAuth()
  @Get('')
  async getPaginatedResourcesList(
    @Query() pageQueryDto: PaginationQueryDto,
  ): Promise<PaginatedResponseDto<ResourcesEntity>> {
    try {
      return await this.resourcesService.getPaginatedResourcesList(
        pageQueryDto,
      );
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  @Patch('/:resourcesName')
  // @Anonymous()
  async updateResourcesSystemUpdate(
    @Param('resourcesName') resourcesName: string,
    @Body() systemUpdate: object,
  ) {
    try {
      return await this.resourcesService.updateResourcesSystemUpdate(
        resourcesName,
        systemUpdate,
      );
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  @Post('/')
  // @Anonymous()
  async createResource(@Body() res: ResourcesRequestDto) {
    return await this.resourcesService.createResource(res).catch((err) => {
      this.logger.debug(`Could not create the resource : ${err?.message}`);
      throw new HttpException(
        {
          message: err.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    });
  }

  @Get('/:resourcesName')
  // @Anonymous()
  async getResources(
    @Param('resourcesName') resourcesName: string,
  ): Promise<ResourcesEntity> {
    return await this.resourcesService.getResource(resourcesName);
  }

  @Get('/latest/resource')
  async getLatestResourceStatus(
    @Query('resourceIds') resourceIds: string,
  ): Promise<ResourcesEntity[]> {
    this.logger.log(`Resource Ids for refresh status are ${resourceIds}`);
    const resourceIdArray = resourceIds?.split(',');
    return await this.resourcesService.getLatestResourceStatus(resourceIdArray);
  }

  @Get('/:resourceId/refresh')
  async getLatestStatus(
    @Param('resourceId') resourceId: string,
  ): Promise<ResourcesEntity> {
    this.logger.log(
      'request received for getLatestStatus for resourceId:',
      resourceId,
    );
    return await this.resourcesService.getLatestStatus(resourceId);
  }

  @Delete()
  @EnvPermission(MultipleRequestTypeResolver, PermissionKey.DELETE)
  async deleteResource(
    @Body() ResourceIdsDTO: { resourceIds: string[]; deeplinkUrl?: string },
  ) {
    const { resourceIds, deeplinkUrl } = ResourceIdsDTO;
    this.logger.debug(
      'Calling resources service for deleting resources with resourceIds',
      resourceIds,
    );
    if (!Array.isArray(resourceIds) || resourceIds.length === 0) {
      this.logger.debug('Invalid resource Ids');
      throw new BadRequestException('Invalid resource Ids');
    }
    return await this.resourcesService.deleteResource(resourceIds, deeplinkUrl);
  }

  @Get('/id/:resourceId')
  // @Anonymous()
  async getResourceByResourceId(
    @Param('resourceId') resourceId: string,
    @Query() resourceQueryDto: ResourceQueryDto = {},
  ): Promise<ResourcesEntity | ExtendedResource> {
    const extendedFetch = resourceQueryDto.extendedFetch ?? false;
    const resource = await this.resourcesService.getResourceByResourceId(
      resourceId,
      extendedFetch,
    );
    if (!resource) {
      throw new NotFoundException(
        `Cannot find resource with id: ${resourceId}`,
      );
    }
    return resource;
  }

  @Patch('/id/:resourceId')
  // @Anonymous()
  async updateResourceByResourceId(
    @Param('resourceId') resourceId: string,
    @Body() data: UpdateResourceDTO,
  ): Promise<ResourcesEntity> {
    return await this.resourcesService.updateResourceByResourceId(
      resourceId,
      data,
    );
  }

  @Patch('/id/:resourceId')
  @Version('2')
  async updateResourceByResourceIdV2(
    @Param('resourceId') resourceId: string,
    @Body() data,
  ): Promise<ResourcesEntity> {
    return await this.resourcesService.updateResourceByResourceId(
      resourceId,
      data,
    );
  }

  @Put('/stop')
  @EnvPermission(MultipleRequestTypeResolver, PermissionKey.UPDATE)
  async stopVmResources(
    @Body() restartVmResourcesDto: RestartVmResourcesDto,
  ): Promise<ResourceUpdateSuccessDto> {
    const resourceIds = restartVmResourcesDto.resourceIds;
    this.logger.debug(`Received stop request for the resources`);
    return await this.resourcesService.stopVmResources(resourceIds);
  }

  @Put('/start')
  @EnvPermission(MultipleRequestTypeResolver, PermissionKey.UPDATE)
  async startVmResources(
    @Body() restartVmResourcesDto: RestartVmResourcesDto,
  ): Promise<ResourceUpdateSuccessDto> {
    const resourceIds = restartVmResourcesDto.resourceIds;
    this.logger.debug(`Received start request for the resources`);
    return await this.resourcesService.startVmResources(resourceIds);
  }

  @Put('/restart')
  @EnvPermission(MultipleRequestTypeResolver, PermissionKey.UPDATE)
  async restartVmResources(
    @Body() restartVmResourcesDto: RestartVmResourcesDto,
  ): Promise<ResourceUpdateSuccessDto> {
    const resourceIds = restartVmResourcesDto.resourceIds;
    this.logger.debug(`Received restart request for the resources`);
    return await this.resourcesService.restartVmResources(resourceIds);
  }
  @Put('/vm/:action')
  @EnvPermission(MultipleRequestTypeResolver, PermissionKey.UPDATE)
  async triggerAction(
    @Param('action') action: string,
    @Body() restartVmResourcesDto: RestartVmResourcesDto,
  ): Promise<ResourceUpdateSuccessDto> {
    const resourceIds = restartVmResourcesDto.resourceIds;
    this.logger.debug(`Received ${action} request for the vCenter resources`);
    return await this.resourcesService.triggerAction(action, resourceIds);
  }

  @Delete('/accessKey/:resourceId')
  // @Anonymous()
  //delete permission resolver is not required for this delete api, This is one time activity from the created user
  async deleteAccessKey(@Param('resourceId') resourceId: string) {
    this.logger.log(
      'Calling resources service for deleting secret accesskey with resourceId',
      resourceId,
    );
    return await this.resourcesService.updateResourceAccessKey(resourceId);
  }

  @Patch('/accessKey/regenerate/:resourceId')
  async regenerateKeys(
    @Param('resourceId') resourceId: string,
    @Body() regenerateUser: RegenerateUser[],
  ) {
    this.logger.log(
      'Calling resources service for regenerating secret accesskey with resourceId and dataCenterName',
      resourceId,

      regenerateUser,
    );

    const result = await this.resourcesService.regenerateKeys(
      resourceId,
      regenerateUser,
    );
    return result;
  }

  @Patch('modify/:resourceId')
  async modifyResource(
    @Param('resourceId') resourceId: string,
    @Body() modifyResource: ModifyResourceDto,
  ) {
    this.logger.log(
      'Calling resources service for modifying with resourceId',
      resourceId,
      modifyResource,
    );

    const result = await this.resourcesService.modifyResource(
      resourceId,
      modifyResource,
    );
    return result;
  }

  @Patch('/storage/restore/:resourceId')
  async restoreStorage(
    @Param('resourceId') resourceId: string,
    @Body() restoreResourceDto: RestoreResourceDto,
  ): Promise<RestoreResourceStatusDto> {
    this.logger.debug(
      `Received restore request for the storage resources ${resourceId}`,
    );
    return await this.resourcesService.restoreStorage(
      resourceId,
      restoreResourceDto,
    );
  }

  @Get('resources-by-id/:serviceRequestId')
  async getResourcesByServiceRequestId(
    @Param('serviceRequestId') serviceRequestId: string,
  ): Promise<RequestedVMsDataDto[]> {
    return await this.resourcesService.getResourcesByServiceRequestId(
      serviceRequestId,
    );
  }

  @Get('resources-count-by-envid/:envId')
  async getResourcesCountByEnvId(@Param('envId') envId: string) {
    return await this.resourcesService.getResourcesCountByEnvId(envId);
  }

  @Get('by-requestId-and-status/:serviceRequestId')
  async getResourcesByServiceRequestIdV2(
    @Param('serviceRequestId') serviceRequestId: string,
    @Query('status') status: string[],
  ): Promise<ResourcesEntity[]> {
    if (!status || status.length === 0) {
      return await this.resourcesService.findByServiceRequestId(
        serviceRequestId,
      );
    }

    return await this.resourcesService.findByServiceRequestIdAndStatus(
      serviceRequestId,
      status,
    );
  }

  @Post('quota-check')
  async quotaChekerForResource(
    @Body() quotaResourceDTO: quotaResourceDTO,
  ): Promise<any> {
    this.logger.log(
      `quota checker service called for service requestID ${quotaResourceDTO.serviceRequestId}`,
    );
    return await this.resourcesService.quotaChekerForResource(quotaResourceDTO);
  }
}
