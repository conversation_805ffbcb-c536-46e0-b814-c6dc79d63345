import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AxiosInstance } from 'axios';
import { LoggerModule } from '../loggers/logger.module';
import { ENVIRONMENT_VARS } from '../utils/constants';
import { ComputeWrapperService } from './compute-wrapper.service';
import { TokenService } from '../auth/token.service';
import { createAxiosInstance } from '../utils/helpers';
import { REQUEST } from '@nestjs/core';
import { AuthModule } from '../auth/auth.module';
import { VmwareAdapterServiceModule } from 'src/vmware-adapter-service/vmware-adapter-service.module';
import { ErrorDetails } from '../vmware-error-loggers/vmware-error-logger.service';

@Module({
  imports: [LoggerModule, AuthModule, VmwareAdapterServiceModule],
  providers: [
    ComputeWrapperService,
    ErrorDetails,
    {
      provide: 'COMPUTE_SERVICE_API',
      inject: [ConfigService, TokenService, REQUEST],
      useFactory: async (
        configService: ConfigService,
        tokenService: TokenService,
        req: Request,
      ): Promise<AxiosInstance> => {
        const baseUrlKey = configService.get(
          ENVIRONMENT_VARS.COMPUTE_API_BASE_URL,
        );
        const nebulaHeader = req.headers['x-nebula-authorization']
          ? (req.headers['x-nebula-authorization'] as string)
          : (req.headers['x-client-jwt'] as string);
        return await createAxiosInstance(
          configService,
          tokenService,
          nebulaHeader,
          baseUrlKey,
        );
      },
    },
  ],

  exports: [ComputeWrapperService],
})
export class ComputeWrapperModule {}
