import { Type } from 'class-transformer';
import {
  IsDefined,
  IsMongoId,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { ZtpRequestDTO } from './ztp.request.dto';

export class PlatformContext {
  @IsNotEmpty()
  @IsMongoId()
  catalogId: string;

  @IsNotEmpty()
  @IsOptional()
  @IsMongoId()
  domainId: string;
}

export class CreateZtpRequestDTO {
  @IsDefined()
  @ValidateNested({ each: true })
  @Type(() => ZtpRequestDTO)
  hosts: ZtpRequestDTO;

  @ValidateNested()
  @IsNotEmpty()
  @Type(() => PlatformContext)
  platformContext: PlatformContext;

  @IsString()
  @IsNotEmpty()
  domainName: string;
}
