import {
  IsString,
  IsBoolean,
  IsA<PERSON>y,
  IsOptional,
  IsNotEmpty,
  IsNumber,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class VcenterDetailsDto {
  @IsString()
  @IsNotEmpty()
  hostname: string;

  @IsNumber()
  @IsNotEmpty()
  port: number;

  @IsString()
  @IsNotEmpty()
  username: string;

  @IsString()
  @IsNotEmpty()
  password: string;
}

export class ClusterDto {
  @IsString()
  clusterId: string;

  @IsString()
  cloudId: string;

  @IsString()
  clusterMor: string;

  @IsString()
  name: string;

  @IsBoolean()
  haEnabled: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  drsEnabled?: boolean | null;

  @IsBoolean()
  @IsOptional()
  disabled: boolean;

  @IsBoolean()
  @IsOptional()
  restricted: boolean;

  @ApiProperty({ type: [String] })
  @IsArray()
  hosts: string[];

  @ApiProperty({ type: [String] })
  @IsArray()
  groups: string[];

  @IsArray()
  @IsOptional()
  projects: [];

  @IsNumber()
  @IsOptional()
  hostCount: number;

  @IsNumber()
  @IsOptional()
  vmCount: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  clusterStatus?: string | null;
}

export class HostDto {
  @IsString()
  hostId: string;

  @IsString()
  cloudId: string;

  @IsString()
  hostMor: string;

  @IsString()
  name: string;

  @IsString()
  @IsOptional()
  connectionState: string;

  @IsString()
  @IsOptional()
  powerState: string;

  @IsBoolean()
  @IsOptional()
  disabled: boolean;

  @IsBoolean()
  @IsOptional()
  restricted: boolean;

  @ApiProperty({ type: [String] })
  @IsArray()
  @IsOptional()
  groups: string[];

  @IsString()
  @IsOptional()
  location: string;

  @IsString()
  @IsOptional()
  vendor: string;

  @IsString()
  @IsOptional()
  model: string;

  @IsNumber()
  @IsOptional()
  cpu: number;

  @IsNumber()
  @IsOptional()
  core: number;

  @IsNumber()
  @IsOptional()
  memory: number;
}

export class DatastoreDto {
  @IsString()
  datastoreId: string;

  @IsString()
  cloudId: string;

  @IsString()
  datastoreMor: string;

  @IsString()
  name: string;

  @IsString()
  type: string;

  @IsBoolean()
  @IsOptional()
  disabled: boolean;

  @IsNumber()
  @IsOptional()
  freeSpace: number;

  @IsNumber()
  @IsOptional()
  capacity: number;

  @ApiProperty({ type: [String] })
  @IsArray()
  @IsOptional()
  groups: string[];
}

export class NetworkDto {
  @IsString()
  networkId: string;

  @IsString()
  cloudId: string;

  @IsString()
  networkMor: string;

  @IsString()
  type: string;

  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  ipv4Subnet?: string;

  @IsOptional()
  @IsString()
  ipv6Subnet?: string;

  @IsBoolean()
  ipv4Enabled: boolean;

  @IsBoolean()
  ipv6Enabled: boolean;

  @IsBoolean()
  dhcp: boolean;

  @IsOptional()
  @IsString()
  dnsDomain?: string;

  @IsOptional()
  @IsString()
  ipv4Gateway?: string;

  @IsOptional()
  @IsString()
  ipv6Gateway?: string;

  @IsOptional()
  @IsString()
  ipv4DnsPrimary?: string;

  @IsOptional()
  @IsString()
  ipv4DnsSecondary?: string;

  @IsOptional()
  @IsString()
  ipv6DnsPrimary?: string;

  @IsOptional()
  @IsString()
  ipv6DnsSecondary?: string;

  @IsOptional()
  @IsString()
  ipv4DhcpServer?: string | null;

  @IsOptional()
  @IsString()
  ipv6DhcpServer?: string | null;

  @IsBoolean()
  disabled: boolean;
}

export class OsLayoutItemDto {
  @IsString()
  osName: string;

  @IsString()
  layoutName: string;

  @IsString()
  layoutId: string;

  @IsString()
  layoutMor: string;

  @IsString()
  @IsOptional()
  shortName: string;

  @IsString()
  @IsOptional()
  imageName: string;

  @IsBoolean()
  @IsOptional()
  disabled: boolean;

  @IsBoolean()
  @IsOptional()
  restricted: boolean;

  @ApiProperty({ type: [String] })
  @IsArray()
  @IsOptional()
  groups: string[];
}

export class OsLayoutsDto {
  @IsString()
  cloudId: string;

  @ApiProperty({ type: [OsLayoutItemDto] })
  @IsArray()
  osLayouts: OsLayoutItemDto[];

  @ApiProperty({ type: [String] })
  @IsArray()
  @IsOptional()
  groups: string[];

  @IsArray()
  @IsOptional()
  projects: [];
}

export class UpdateVmwareCloudDto {
  @IsString()
  cloudId: string;

  @IsString()
  vcenterHost: string;

  @IsString()
  vcenterName: string;

  @IsString()
  vcenterMor: string;

  @IsString()
  cloudName: string;

  @IsString()
  domain: string;

  @IsString()
  datacenter: string;

  @IsString()
  templateFolder: string;

  @IsString()
  vmFolder: string;

  @IsBoolean()
  disabled: boolean;

  @IsArray()
  groups: string[];

  @IsArray()
  projects: [];

  @ValidateNested()
  @Type(() => VcenterDetailsDto)
  vcenterDetails: VcenterDetailsDto;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ClusterDto)
  clusters: ClusterDto[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => HostDto)
  hosts: HostDto[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DatastoreDto)
  datastores: DatastoreDto[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => NetworkDto)
  networks: NetworkDto[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => OsLayoutsDto)
  osLayouts: OsLayoutsDto[];
}
