import {
  BadRequestException,
  Body,
  Controller,
  Post,
  UploadedFile,
  UseInterceptors,
  Version,
} from '@nestjs/common';
import { LoggerService } from 'src/loggers/logger.service';
import { ZtpService } from './ztp.service';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiSecurity,
  ApiTags,
} from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { extname } from 'path';
import { ConfigService } from '@nestjs/config';
import { CreateZtpRequestDTO } from './dto/ztpbulk.request.dto';
import { ztpResponseType } from './types/createztp.response.type';
import { ztpBulkResponseType } from './types/createztpbulk.response.type';

@ApiSecurity('x-nebula-authorization')
@Controller('ztp')
@ApiTags('ZTP')
export class ZtpController {
  private readonly maxZtpRulesFileSize: number;

  constructor(
    private logger: LoggerService,
    private configService: ConfigService,
    private ztpService: ZtpService,
  ) {
    this.maxZtpRulesFileSize = parseInt(
      this.configService.get<string>(
        'MAX_FIREWALL_RULES_FILE_SIZE_LIMIT_IN_MB',
        '1',
      ),
    );
  }

  @Version('1')
  @ApiBearerAuth()
  @Post('fileupload')
  @UseInterceptors(FileInterceptor('file'))
  @ApiBody({
    required: true,
    type: 'multipart/form-data',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @ApiConsumes('multipart/form-data')
  parseFile(@UploadedFile() file: Express.Multer.File): Promise<ztpBulkResponseType> {
    this.logger.log('Uploading file');
    try {
      if (!file || extname(file.originalname) != '.yaml') {
        this.logger.error('Invalid file or incorrect extension provide');
        throw new BadRequestException(
          'Please provide file with correct extension',
        );
      }
      if (file.size > this.maxZtpRulesFileSize * 1024 * 1024) {
        this.logger.debug(
          `File size exceeds the limit of ${this.maxZtpRulesFileSize} MB`,
        );
        throw new BadRequestException(
          `File size exceeds the limit of ${this.maxZtpRulesFileSize} MB`,
        );
      }
      this.logger.log(`Processing file upload: ${file.originalname}`);
      return this.ztpService.ztpBulkImport(file.buffer);
    } catch (error) {
      this.logger.error(error, 'Error occurred during file upload');
      throw new BadRequestException(`${error.message}`);
    }
  }

  @Version('1')
  @ApiBearerAuth()
  @Post('')
  async createZtpRequest(@Body() createZtpRequestDTO: CreateZtpRequestDTO): Promise<ztpResponseType> {
    this.logger.log('creating firewall V2 request', createZtpRequestDTO);
    return await this.ztpService.createZtpRequest(createZtpRequestDTO);
  }
}
