import {
  BadRequestException,
  Body,
  Controller,
  Post,
  UploadedFile,
  UseInterceptors,
  Version,
} from '@nestjs/common';
import { LoggerService } from 'src/loggers/logger.service';
import { ZtpService } from './ztp.service';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiSecurity,
  ApiTags,
} from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { extname } from 'path';
import * as YAML from 'yamljs';
import { ConfigService } from '@nestjs/config';
import { CreateZtpRequestDTO } from './dto/ztpbulk.request.dto';
import { ztpResponseType } from './types/createztp.response.type';
import { ztpBulkResponseType } from './types/createztpbulk.response.type';
import { plainToInstance } from 'class-transformer';
import { validateSync, ValidationError } from 'class-validator';

@ApiSecurity('x-nebula-authorization')
@Controller('ztp')
@ApiTags('ZTP')
export class ZtpController {
  private readonly maxZtpRulesFileSize: number;

  constructor(
    private readonly logger: LoggerService,
    private readonly configService: ConfigService,
    private readonly ztpService: ZtpService,
  ) {
    this.maxZtpRulesFileSize = parseInt(
      this.configService.get<string>(
        'MAX_FIREWALL_RULES_FILE_SIZE_LIMIT_IN_MB',
        '1',
      ),
    );
  }

  @Version('1')
  @ApiBearerAuth()
  @Post('fileupload')
  @UseInterceptors(FileInterceptor('file'))
  @ApiBody({
    required: true,
    type: 'multipart/form-data',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @ApiConsumes('multipart/form-data')
  parseFile(
    @UploadedFile() file: Express.Multer.File,
  ): Promise<ztpBulkResponseType> {
    this.logger.log('Uploading file');
    try {
      if (!file || extname(file.originalname) != '.yaml') {
        this.logger.error('Invalid file or incorrect extension provide');
        throw new BadRequestException(
          'Please provide file with correct extension',
        );
      }
      if (file.size > this.maxZtpRulesFileSize * 1024 * 1024) {
        this.logger.debug(
          `File size exceeds the limit of ${this.maxZtpRulesFileSize} MB`,
        );
        throw new BadRequestException(
          `File size exceeds the limit of ${this.maxZtpRulesFileSize} MB`,
        );
      }
      this.logger.log(`Processing file upload: ${file.originalname}`);
      return this.ztpService.ztpBulkImport(file.buffer);
    } catch (error) {
      this.logger.error(error, 'Error occurred during file upload');
      throw new BadRequestException(`${error.message}`);
    }
  }

  @Version('1')
  @ApiBearerAuth()
  @UseInterceptors(FileInterceptor('file'))
  @ApiBody({
    required: true,
    type: 'multipart/form-data',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
        data: {
          type: 'string',
        },
      },
    },
  })
  @ApiConsumes('multipart/form-data')
  @Post('')
  async createZtpRequest(
    @UploadedFile() file: Express.Multer.File,
    @Body('data') body: any,
  ): Promise<ztpResponseType> {
    this.logger.log('creating ZTP single device request', body);
    let yamlData: any;
    if (file) {
      if (extname(file.originalname) != '.yaml') {
        this.logger.error('Invalid file or incorrect extension provide');
        throw new BadRequestException(
          'Please provide file with correct extension',
        );
      }
      if (file.size > this.maxZtpRulesFileSize * 1024 * 1024) {
        this.logger.debug(
          `File size exceeds the limit of ${this.maxZtpRulesFileSize} MB`,
        );
        throw new BadRequestException(
          `File size exceeds the limit of ${this.maxZtpRulesFileSize} MB`,
        );
      }
      const yamlContent = file.buffer.toString('utf-8');
      try {
        yamlData = YAML.parse(yamlContent);
        this.logger.debug(
          `Parsed Yaml Content: ${JSON.stringify(yamlData, null, 2)}`,
        );
      } catch (error) {
        this.logger.error(error, 'Failed to parse YAML content', error.stack);
        throw new BadRequestException('Invalid Yaml format');
      }
    }

    let createZtpRequestDTO: CreateZtpRequestDTO;
    try {
      const payload = JSON.parse(body);
      createZtpRequestDTO = JSON.parse(payload.data);
      this.logger.log(`Parsed payload data ${createZtpRequestDTO}`);
    } catch (e) {
      this.logger.error('Invalid Json format', e.stack);
      throw new BadRequestException('Invalid Json format');
    }

    if (file && yamlData) {
      createZtpRequestDTO.hosts = { ...createZtpRequestDTO.hosts, ...yamlData };
    }
    const dto: CreateZtpRequestDTO = plainToInstance(
      CreateZtpRequestDTO,
      createZtpRequestDTO,
    );
    const errors: ValidationError[] = validateSync(dto, {
      whitelist: true,
      forbidNonWhitelisted: true,
    });
    if (errors.length > 0) {
      this.logger.error('Validation failed', JSON.stringify(errors, null, 2));
      throw new BadRequestException(errors);
    }

    this.logger.log(
      `Create single device ZTP request with the dto ${createZtpRequestDTO}`,
    );
    return await this.ztpService.createZtpRequest(createZtpRequestDTO, file);
  }
}
