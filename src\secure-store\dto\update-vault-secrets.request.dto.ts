import {
  IsBoolean,
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsPositive,
  IsString,
  Matches,
  MinLength,
} from 'class-validator';
import { RotationType } from '../secretsMetadata/types/secretsMetadata.enum';

export class UpdateVaultSecretRequestDto {
  @IsOptional()
  @IsString()
  description?: string;

  @IsString()
  @IsOptional()
  policyId?: string;

  @IsString()
  @IsNotEmpty()
  vaultKey: string;

  @IsString()
  @IsNotEmpty()
  vaultPassword: string;

  @IsString()
  @IsNotEmpty()
  @IsOptional()
  userNameKey: string;

  @IsString()
  @IsNotEmpty()
  @IsOptional()
  userNamePassword: string;

  @IsOptional()
  @IsNumber()
  @IsPositive()
  secretTTLInHours?: number;

  @IsNotEmpty()
  @IsBoolean()
  notifyBeforeTokenExpiry: boolean = false;

  @IsOptional()
  @IsDateString()
  nextRotationDate?: string;

  @IsOptional()
  @IsEnum(RotationType)
  rotationType?: RotationType;
}
