// secrets-policies.entity.spec.ts
import { Status } from '../types/secretsPolicies.enum';
import { SecretsPoliciesEntity } from './secretsPolicies.entity';

describe('SecretsPoliciesEntity', () => {
  it('should create an instance with required fields', () => {
    const entity = new SecretsPoliciesEntity();
    entity.type = 'VAULT_POLICY';
    entity.status = 'ACTIVE';

    expect(entity.type).toBe('VAULT_POLICY');
    expect(entity.status).toBe(Status.ACTIVE);
  });

  it('should allow optional fields to be undefined', () => {
    const entity = new SecretsPoliciesEntity();
    entity.type = 'VAULT_POLICY';
    entity.status = 'ACTIVE';

    expect(entity.description).toBeUndefined();
    expect(entity.resourceId).toBeUndefined();
    expect(entity.createdBy).toBeUndefined();
    expect(entity.updatedBy).toBeUndefined();
  });

  it('should inherit from BaseEntity', () => {
    const entity = new SecretsPoliciesEntity();
    expect(entity).toBeInstanceOf(Object); // Replace with BaseEntity if it's a class
  });
});

import { PolicyRulesEntity } from './secretsPolicies.entity';

describe('PolicyRulesEntity', () => {
  it('should create an instance with correct values', () => {
    const policy = new PolicyRulesEntity();
    policy.passwordDescription = 'Password must meet complexity requirements';
    policy.acceptedSpecialCharacters = '!@#$%';
    policy.totalCharactersLength = 12;
    policy.specialCharactersCount = 2;
    policy.lowerCaseLettersCount = 3;
    policy.upperCaseLettersCount = 3;
    policy.numericalCharactersCount = 4;

    expect(policy.passwordDescription).toBe(
      'Password must meet complexity requirements',
    );
    expect(policy.acceptedSpecialCharacters).toBe('!@#$%');
    expect(policy.totalCharactersLength).toBe(12);
    expect(policy.specialCharactersCount).toBe(2);
    expect(policy.lowerCaseLettersCount).toBe(3);
    expect(policy.upperCaseLettersCount).toBe(3);
    expect(policy.numericalCharactersCount).toBe(4);
  });
});
