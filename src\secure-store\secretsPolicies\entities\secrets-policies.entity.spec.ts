// secrets-policies.entity.spec.ts
import { SecretsPoliciesEntity } from './secretsPolicies.entity';

describe('SecretsPoliciesEntity', () => {
  it('should create an instance with required fields', () => {
    const entity = new SecretsPoliciesEntity();
    entity.secretId = 'secret123';
    entity.type = 'VAULT_POLICY';
    entity.active = true;
    entity.namespace = 'default';

    expect(entity.secretId).toBe('secret123');
    expect(entity.type).toBe('VAULT_POLICY');
    expect(entity.active).toBe(true);
    expect(entity.namespace).toBe('default');
  });

  it('should allow optional fields to be undefined', () => {
    const entity = new SecretsPoliciesEntity();
    entity.secretId = 'secret123';
    entity.type = 'VAULT_POLICY';
    entity.active = true;
    entity.namespace = 'default';

    expect(entity.description).toBeUndefined();
    expect(entity.parentResourceID).toBeUndefined();
    expect(entity.status).toBeUndefined();
    expect(entity.secretTTLInHours).toBeUndefined();
    expect(entity.createdBy).toBeUndefined();
    expect(entity.updatedBy).toBeUndefined();
    expect(entity.vaultHistoryVersion).toBeUndefined();
    expect(entity.secretVersion).toBeUndefined();
  });

  it('should inherit from BaseEntity', () => {
    const entity = new SecretsPoliciesEntity();
    expect(entity).toBeInstanceOf(Object); // Replace with BaseEntity if it's a class
  });
});
