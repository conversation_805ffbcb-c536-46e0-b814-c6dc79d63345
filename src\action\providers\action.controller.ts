import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { ApiSecurity, ApiTags } from '@nestjs/swagger';
import { LoggerService } from '../../loggers/logger.service';
import { ActionService } from './action.service';
import { Anonymous } from '../../auth/anonymous.decorator';
import { ActionStatus } from '../enums/ActionStatus.enum';

@ApiTags('Actions')
@ApiSecurity('x-nebula-authorization')
@UsePipes(new ValidationPipe({ whitelist: true }))
@Controller('actions')
export class ActionController {
  constructor(
    private readonly logger: LoggerService,
    private readonly actionService: ActionService,
  ) {}

  @Anonymous()
  @Patch(':actionId/status/:status')
  async updateAction(
    @Param('actionId') actionId: string,
    @Param('status') status: ActionStatus,
  ) {
    const updateActionDto = { actionId: actionId, status: status };
    this.logger.log('request received to update action', updateActionDto);
    const updateAction = await this.actionService.update(updateActionDto);
    this.logger.log('action updated using actionId in action controller');
    return updateAction;
  }
}
