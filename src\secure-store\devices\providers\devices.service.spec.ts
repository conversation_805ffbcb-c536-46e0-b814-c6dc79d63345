import { Test, TestingModule } from '@nestjs/testing';
import { DevicesService } from './devices.service';
import { SecretDeviceAssociationRepository } from '../repositories/secretDeviceAssociation.repository';
import {
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import { SecretsMetadataRepository } from '../../secretsMetadata/secretsMetadata.repository';
import { IamService } from '../../../iam/iam.service';

jest.mock('../../../utils/helpers', () => ({
  getUserContext: () => ({ userId: 'mock-user' }),
}));

describe('SecretDevicesService', () => {
  let service: DevicesService;
  let repo: SecretDeviceAssociationRepository;

  const mockRepo = {
    create: jest.fn(),
    findBySecretIdsAndDeviceIds: jest.fn(),
    updateMany: jest.fn(),
    getSecretsMetaData: jest.fn(),
    getDeviceSettingsByEnvIds: jest.fn(),
    bulkUpdate: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DevicesService,
        { provide: SecretsMetadataRepository, useValue: mockRepo },
        { provide: IamService, useValue: mockRepo },
        { provide: SecretDeviceAssociationRepository, useValue: mockRepo },
      ],
    }).compile();

    service = module.get<DevicesService>(DevicesService);
    repo = module.get<SecretDeviceAssociationRepository>(
      SecretDeviceAssociationRepository,
    );
  });

  it('should return success message on valid data', async () => {
    mockRepo.create.mockResolvedValue([{ id: 1 }]);
    const result = await service.createSecretDeviceAssociations([
      { deviceId: 123, secretId: 'abc', sourceSystem: 'test' },
    ]);
    expect(result).toEqual({ message: 'Secret Device Association Succeeded' });
  });

  it('should throw error on failure', async () => {
    mockRepo.create.mockResolvedValue([]);
    await expect(
      service.createSecretDeviceAssociations([
        { deviceId: 123, secretId: 'abc', sourceSystem: 'test' },
      ]),
    ).rejects.toThrow(UnprocessableEntityException);
  });

  it('should return mock secret device association data with rotatable secrets and device ids', async () => {
    const mockSecretsMetaData = [
      {
        secretId: 'suriya-concurrency-test',
        type: 'ROTATABLE-SECRET',
      },
      { secretId: 'TEST-SEC-ID', type: 'ROTATABLE-SECRET' },
    ];

    const mockDeviceSettings = [
      { configration: { id: 0 } },
      { configration: { id: 1 } },
    ];

    const mockData = [
      {
        _id: '6846b20de6c9155f3cf74cc7',
        secretAssociationId: 'NEB-SEC-LINK-1005',
        deviceId: [0],
        secretId: ['suriya-concurrency-test'],
        type: 'DEVICE-VAULT-SECRET-MAPPING',
        active: true,
        createdBy: 'P3271329',
        updatedBy: null,
        createdAt: '2025-06-09T10:06:05.937Z',
        updatedAt: '2025-06-09T10:06:05.937Z',
        __v: 0,
      },
    ];

    mockRepo.getSecretsMetaData.mockResolvedValue(mockSecretsMetaData);
    mockRepo.getDeviceSettingsByEnvIds.mockResolvedValue(mockDeviceSettings);
    mockRepo.findBySecretIdsAndDeviceIds.mockResolvedValue(mockData);

    const result = await service.getAllRotatableSecretDeviceAssociation(
      'nebula-stamp',
      'dev/rc',
      '01966c73-b401-7285-bec1-b122f7118def',
    );

    expect(result).toEqual(mockData);
  });

  it('should return mock secret device association data Not Found', async () => {
    const mockData = [];
    mockRepo.getSecretsMetaData.mockResolvedValue([]);
    mockRepo.getDeviceSettingsByEnvIds.mockResolvedValue([]);
    mockRepo.findBySecretIdsAndDeviceIds.mockResolvedValue(mockData);

    try {
      await service.getAllRotatableSecretDeviceAssociation(
        'nebula-stamp',
        'dev/rc',
        '01966c73-b401-7285-bec1-b122f7118def',
      );
    } catch (err) {
      expect(err).toBeInstanceOf(NotFoundException);
      expect(err.message).toBe(
        `Failed to find device asscoation for path 'dev/rc' in 'nebula-stamp' namespace`,
      );
    }
  });

  it('should successfully process existing and new entries', async () => {
    mockRepo.create.mockResolvedValue([{ id: 1 }]);
    mockRepo.updateMany.mockResolvedValue([{ id: 1 }]);
    const result = await service.updateSecretDeviceAssociations([
      { deviceId: 123, secretId: 'abc', sourceSystem: 'test' },
      {
        secretAssociationId: 'NEB-SEC-LINK-1005',
        deviceId: 1234,
        secretId: 'abc',
        sourceSystem: 'test',
      },
    ]);
    expect(result).toEqual({ message: 'Secret Device Association Succeeded' });
  });

  it('should successfully process existing and new entries', async () => {
    mockRepo.create.mockResolvedValue([{ id: 1 }]);
    mockRepo.updateMany.mockResolvedValue([{ id: 1 }]);
    const result = await service.updateSecretDeviceAssociations([
      { deviceId: 123, secretId: 'abc', sourceSystem: 'test' },
      {
        secretAssociationId: 'NEB-SEC-LINK-1005',
        deviceId: 1234,
        secretId: 'abc',
        sourceSystem: 'test',
      },
    ]);
    expect(result).toEqual({ message: 'Secret Device Association Succeeded' });
  });

  describe('bulkUpdate', function () {
    it('should update the perform bulk update', async function () {
      let mockid = 'NEB-VAULT-ROT-SECRET-1086';
      let mockOps = [
        {
          updateOne: {
            filter: { secretId: mockid },
            update: { $set: { active: false } },
            upsert: true,
          },
        },
      ];
      mockRepo.bulkUpdate.mockResolvedValue(null);
      const result = await mockRepo.bulkUpdate(mockOps);
      expect(result).toEqual(null);
    });
  });
});
