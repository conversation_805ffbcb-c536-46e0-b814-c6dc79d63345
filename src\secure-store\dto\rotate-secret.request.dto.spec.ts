import { validate } from 'class-validator';
import { RotateSecretRequestDto } from './rotate-secret.request.dto';

describe('RotateSecretRequestDto', () => {
  it('should validate a correct DTO', async () => {
    const dto = new RotateSecretRequestDto();
    dto.secretId = 'abc123';
    dto.newPassword = '01e06ea43f0d58778df38400f83773c6';

    const errors = await validate(dto);
    expect(errors.length).toBe(1);
  });

  it('should fail validation if secretId is empty', async () => {
    const dto = new RotateSecretRequestDto();
    dto.secretId = '';
    dto.newPassword = 'newpass';

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors.some((e) => e.property === 'secretId')).toBe(true);
  });

  it('should fail validation if newPassword is empty', async () => {
    const dto = new RotateSecretRequestDto();
    dto.secretId = 'abc123';
    dto.newPassword = '';

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors.some((e) => e.property === 'newPassword')).toBe(true);
  });

  it('should fail validation if both fields are empty', async () => {
    const dto = new RotateSecretRequestDto();
    dto.secretId = '';
    dto.newPassword = '';

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors.some((e) => e.property === 'secretId')).toBe(true);
    expect(errors.some((e) => e.property === 'newPassword')).toBe(true);
  });
});
