import { ApiProperty } from '@nestjs/swagger';
import { ObjectStorageDto } from '../../../objectStorage/object.storage.dto';
import { IsOptional } from 'class-validator';

export class JiraFileUploadRequestDto {
  @ApiProperty()
  issueId: string;
  @ApiProperty()
  issuekey: string;
  @ApiProperty()
  projectKey: string;
  @ApiProperty()
  storageInfo: ObjectStorageDto;
  @ApiProperty()
  @IsOptional()
  fileName?: string;
}
