import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Mongoose, SchemaTypes } from 'mongoose';
import { SecretPolicyTypes, Status } from '../types/secretsPolicies.enum';

export type SecretsPoliciesDocument = SecretsPolicies & Document;

@Schema()
export class PolicyRules {
  @Prop({ type: String })
  passwordDescription: string;

  @Prop({ type: String })
  acceptedSpecialCharacters: string;

  @Prop({ type: Number })
  totalCharactersLength: number;

  @Prop({ type: Number })
  specialCharactersCount: number;

  @Prop({ type: Number })
  lowerCaseLettersCount: number;

  @Prop({ type: Number })
  upperCaseLettersCount: number;

  @Prop({ type: Number })
  numericalCharactersCount: number;
}

@Schema({ timestamps: true })
export class SecretsPolicies {
  @Prop({ type: String })
  policyId?: string;

  @Prop({ type: String })
  policyName?: string;

  @Prop({
    required: true,
    enum: SecretPolicyTypes,
  })
  type: string;

  @Prop({ type: String })
  description?: string;

  @Prop({ type: PolicyRules })
  policyRules: PolicyRules;

  @Prop({ required: true, type: String })
  resourceId: string;

  @Prop({ enum: Status, default: 'ACTIVE' })
  status?: string;

  @Prop({ type: String })
  createdBy?: string;

  @Prop({ type: String })
  updatedBy?: string;
}

export const SecretsPoliciesSchema =
  SchemaFactory.createForClass(SecretsPolicies);
