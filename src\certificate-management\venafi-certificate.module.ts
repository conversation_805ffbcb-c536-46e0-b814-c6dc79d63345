import { Module } from '@nestjs/common';
import { VenafiService } from './venafi-certificate.service';
import { VenafiController } from './venafi-certificate.controller';
import { VenafiWrapperModule } from '../certificate-management-wrapper/venafi-certificate-wrapper-module';
import { ResourcesRepository } from 'src/resources/resources.repository';
import { ActionRepository } from 'src/action/repositories/action.repository';

@Module({
  imports: [VenafiWrapperModule],
  controllers: [VenafiController],
  providers: [VenafiService, ResourcesRepository, ActionRepository],
})
export class VenafiModule {}
