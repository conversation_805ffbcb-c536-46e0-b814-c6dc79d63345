import {
  SecretDTO,
  FolderDTO,
  PathDTO,
  NamespaceResponseDTO,
} from './namespace-child.response.dto';

describe('DTO Structure Tests', () => {
  it('should create a valid SecretDTO', () => {
    const secret: SecretDTO = {
      key: 'secret1',
      type: 'secret',
      path: '/path/to/secret',
      children: [],
    };

    expect(secret.key).toBe('secret1');
    expect(secret.type).toBe('secret');
    expect(secret.path).toBe('/path/to/secret');
    expect(Array.isArray(secret.children)).toBe(true);
  });

  it('should create a valid FolderDTO with nested secrets and folders', () => {
    const folder: FolderDTO = {
      key: 'folder1',
      type: 'folder',
      children: [
        {
          key: 'secret2',
          type: 'secret',
          path: '/path/to/secret2',
          children: [],
        },
        {
          key: 'subfolder1',
          type: 'folder',
          children: [],
        },
      ],
    };

    expect(folder.key).toBe('folder1');
    expect(folder.type).toBe('folder');
    expect(folder.children.length).toBe(2);
  });

  it('should create a valid PathDTO', () => {
    const path: PathDTO = {
      key: 'pathKey',
      value: '/some/path',
    };

    expect(path.key).toBe('pathKey');
    expect(path.value).toBe('/some/path');
  });

  it('should create a valid NamespaceResponseDTO', () => {
    const namespaceResponse: NamespaceResponseDTO = {
      namespace: 'my-namespace',
      children: [
        {
          key: 'secret3',
          type: 'secret',
          path: '/path/to/secret3',
          children: [],
        },
      ],
    };

    expect(namespaceResponse.namespace).toBe('my-namespace');
    expect(namespaceResponse.children.length).toBe(1);
  });
});
