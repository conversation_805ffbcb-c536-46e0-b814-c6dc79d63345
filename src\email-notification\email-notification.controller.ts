import { 
  Controller, 
  Post, 
  Body, 
  UseGuards, 
  UsePipes, 
  ValidationPipe,
  Get,
  Param
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiBearerAuth, 
  ApiSecurity 
} from '@nestjs/swagger';
import { EmailNotificationService } from './email-notification.service';
import { AuthenticationGuard } from '../auth/authentication.guard';
import { LoggerService } from '../loggers/logger.service';
import {
  EmailNotificationRequestDto,
  AwsCherwellEmailRequestDto,
  EmailNotificationResponseDto,
  EmailTemplateDto,
} from './dto/email-notification.dto';

@Controller('email-notification')
@ApiTags('Email Notification')
@ApiBearerAuth()
@ApiSecurity('x-nebula-authorization')
@UseGuards(AuthenticationGuard)
@UsePipes(new ValidationPipe({ whitelist: true, stopAtFirstError: true }))
export class EmailNotificationController {
  constructor(
    private readonly emailNotificationService: EmailNotificationService,
    private readonly logger: LoggerService,
  ) {}

  @Post('send-with-template')
  @ApiOperation({ 
    summary: 'Send email using MongoDB template',
    description: 'Sends email using a template stored in MongoDB with dynamic content replacement'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Email sent successfully',
    type: EmailNotificationResponseDto 
  })
  async sendEmailWithTemplate(
    @Body() request: EmailNotificationRequestDto
  ): Promise<EmailNotificationResponseDto> {
    this.logger.log('Email notification request received', request);

    await this.emailNotificationService.sendEmailWithTemplate(request);

    return {
      message: 'Email sent successfully using template',
      templateId: request.templateId,
      recipientEmail: request.recipientEmail,
      subject: request.subject,
    };
  }

  @Post('send-aws-cherwell-notification')
  @ApiOperation({ 
    summary: 'Send AWS Cherwell approval notification',
    description: 'Sends AWS Cherwell work item approval notification to Srihari using template'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'AWS Cherwell notification sent successfully',
    type: EmailNotificationResponseDto 
  })
  async sendAwsCherwellNotification(
    @Body() request: AwsCherwellEmailRequestDto
  ): Promise<EmailNotificationResponseDto> {
    this.logger.log('AWS Cherwell email notification request received', request);

    await this.emailNotificationService.sendAwsCherwellNotification(
      request.recipientEmail,
      request.workItemNumber,
      request.serviceRequestId,
      request.detectedDevices
    );

    return {
      message: 'AWS Cherwell notification sent successfully',
      templateId: '1014',
      recipientEmail: request.recipientEmail,
      subject: `Approval needed for AWS ${request.workItemNumber}`,
    };
  }

  @Post('test-email')
  @ApiOperation({ 
    summary: 'Send test email to yourself',
    description: 'Sends a test email using the MongoDB template to verify email functionality'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Test email sent successfully',
    type: EmailNotificationResponseDto 
  })
  async sendTestEmail(): Promise<EmailNotificationResponseDto> {
    this.logger.log('Test email request received');

    const testRequest: EmailNotificationRequestDto = {
      templateId: '1014',
      recipientEmail: '<EMAIL>', // Replace with your actual email
      subject: 'Test Email from Nebula - Email Notification Service',
      message: 'This is a test email to verify the email notification service is working correctly.',
      replacementData: {
        'TEST_FIELD': 'Test Value',
        'TIMESTAMP': new Date().toISOString(),
      },
      tableData: [
        {
          'Field': 'Service',
          'Value': 'Email Notification Service',
          'Status': 'Working'
        },
        {
          'Field': 'Template ID',
          'Value': '1014',
          'Status': 'Active'
        },
        {
          'Field': 'Test Time',
          'Value': new Date().toLocaleString(),
          'Status': 'Current'
        }
      ]
    };

    await this.emailNotificationService.sendEmailWithTemplate(testRequest);

    return {
      message: 'Test email sent successfully',
      templateId: testRequest.templateId,
      recipientEmail: testRequest.recipientEmail,
      subject: testRequest.subject,
    };
  }

  @Get('template/:templateId')
  @ApiOperation({ 
    summary: 'Get email template by ID',
    description: 'Retrieves email template from MongoDB by template ID'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Template retrieved successfully',
    type: EmailTemplateDto 
  })
  async getEmailTemplate(
    @Param('templateId') templateId: string
  ): Promise<EmailTemplateDto> {
    this.logger.log('Get email template request', { templateId });

    // This would need to be implemented in the service
    // For now, returning a placeholder response
    return {
      templateId: templateId,
      templateLocation: '',
      notifications: [
        {
          type: 'email',
          address: '',
          message: 'Template content would be here',
          contentType: 'html',
          title: 'Request Status'
        }
      ]
    };
  }
}
