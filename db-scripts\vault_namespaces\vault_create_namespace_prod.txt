db.n8nconfig.insertOne({
  "workflow": "Create-Namespace",
  "baseUrl": "https://nebula.charter.com/",
  "generateUUIDWorkflow": "NbVb60znpXgXlOeM",
  "catalogStepsWorkflow": "j63a8zHvgg5NTRey",
  "eventSourceHostName": "cdptpabb04-caas-mgmt-v3.stage.charter.com",
  "cloudApiUrl": "http://cloud-api-service.nebula.svc.cluster.local:80/nebula-api/",
  "secretsUrl": "http://secrets-management-service.nebula.svc.cluster.local:80/secrets-management-service/",
  "rootNamespace": "nebula-namespaces"
})
