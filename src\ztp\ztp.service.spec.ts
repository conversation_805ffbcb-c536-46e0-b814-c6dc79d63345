import { AssetsService } from '../assets/assets.service';
import { ZtpService } from './ztp.service';
import { RmqService } from '../rmq/rmq.service';
import { Test, TestingModule } from '@nestjs/testing';
import { LoggerService } from '../loggers/logger.service';
import { CreateZtpRequestDTO } from './dto/ztpbulk.request.dto';
import { RequestContext } from 'nestjs-request-context';
import * as YAML from 'yamljs';
import {
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { ObjectStorageService } from '../objectStorage/object.storage.service';
import { StorageRepository } from '../objectStorage/storage.repository';

describe('ZtpService', () => {
  let service: ZtpService;
  let assestsService: AssetsService;
  let rmqService: RmqService;
  let objectStorageService: ObjectStorageService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ZtpService,
        {
          provide: AssetsService,
          useValue: {
            create: jest.fn(),
            queueIfApprovalNotRequired: jest.fn(),
          },
        },
        {
          provide: RmqService,
          useValue: {
            pushMessage: jest.fn(),
          },
        },
        {
          provide: LoggerService,
          useValue: { log: jest.fn(), debug: jest.fn(), error: jest.fn() },
        },
        {
          provide: ObjectStorageService,
          useValue: { upload: jest.fn() },
        },
        {
          provide: StorageRepository,
          useValue: { create: jest.fn() },
        },
      ],
    }).compile();
    jest.mock('yamljs', () => {
      parse: jest.fn();
    });
    service = module.get<ZtpService>(ZtpService);
    assestsService = module.get<AssetsService>(AssetsService);
    rmqService = module.get<RmqService>(RmqService);
    objectStorageService =
      module.get<ObjectStorageService>(ObjectStorageService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should create ZTP request and return response', async () => {
    const mockDto: CreateZtpRequestDTO = {
      hosts: {
        hostname: 'dms01enwdcocd.netops.charter.com',
        ipv6_address: '2001:1998:64a:d::500:500',
        ipv4_address: '************',
        serial_number: 'FDO24281JDX',
        platform: 'nexus',
      },
    } as CreateZtpRequestDTO;
    const mockDbResponse = {
      serviceRequestId: 'req-001',
      id: 'id-123',
    };
    const mockRequestContext = {
      user: {
        userId: 'p3295818',
        email: '<EMAIL>',
      },
    };

    jest.spyOn(RequestContext, 'currentContext', 'get').mockReturnValue({
      req: mockRequestContext,
    } as any);

    (assestsService.create as jest.Mock).mockResolvedValue(mockDbResponse);
    (assestsService.queueIfApprovalNotRequired as jest.Mock).mockResolvedValue(
      true,
    );
    (rmqService.pushMessage as jest.Mock).mockResolvedValue(undefined);
    jest.spyOn(service as any, 'uploadFileToStorage').mockResolvedValue({});

    const result = await service.createZtpRequest(
      mockDto as CreateZtpRequestDTO,
      { mimetype: 'test' } as Express.Multer.File,
    );

    expect(result).toEqual({
      id: mockDbResponse.id,
      serviceRequestId: mockDbResponse.serviceRequestId,
      message: 'Request submitted for processing',
    });
  });

  it('should throw an error if assestsService.create fails', async () => {
    const mockError = new Error('Database unavailable');
    (assestsService.create as jest.Mock).mockRejectedValue(mockError);
    jest.spyOn(service as any, 'uploadFileToStorage').mockResolvedValue({});

    await expect(
      service.createZtpRequest({} as CreateZtpRequestDTO, null),
    ).rejects.toThrow('Database unavailable');

    expect(assestsService.create).toHaveBeenCalledWith(expect.any(Object));
    expect(assestsService.queueIfApprovalNotRequired).not.toHaveBeenCalled();
    expect(rmqService.pushMessage).not.toHaveBeenCalled();
  });

  it('should create a bulk ZTP request and return response', async () => {
    const buffer = Buffer.from('valid: yaml');
    const parsed = { valid: 'data' };
    const dtoInstance = { result: 'data' };

    jest.spyOn(YAML, 'parse').mockReturnValue(parsed);
    jest
      .spyOn(require('class-transformer'), 'plainToInstance')
      .mockReturnValue(dtoInstance);
    jest.spyOn(require('class-validator'), 'validateSync').mockReturnValue([]);

    const result = await service.ztpBulkImport(buffer);

    expect(result.data).toEqual(dtoInstance);
  });

  it('should throw BadrequestException for invalid YAML', async () => {
    const buffer = Buffer.from('valid: yaml');
    const error = new Error('YAML error');
    (YAML.parse as jest.Mock).mockImplementation(() => {
      throw error;
    });

    await expect(service.ztpBulkImport(buffer)).rejects.toThrow(
      BadRequestException,
    );
  });

  describe('uploadFileToStorage', () => {
    beforeEach(() => {
      (process.env.OBJECTSTORAGE_BUCKET = 'test-bucket'),
        (process.env.OBJECTSTORAGE_ZTP_PATH = 'test-path');
    });
    const mockFile = {
      originalname: 'test.yaml',
    };
    it('should upload file successfully', async () => {
      (objectStorageService.upload as jest.Mock).mockResolvedValue({
        status: 'SUCCESS',
      });

      const result = await service['uploadFileToStorage'](
        mockFile as Express.Multer.File,
      );

      expect(objectStorageService.upload).toHaveBeenCalled();
      expect(result).toHaveProperty('bucketName');
    });

    it('should throw internalservererror', async () => {
      (objectStorageService.upload as jest.Mock).mockResolvedValue({
        status: 'Failure',
        errorCode: 'err01',
        errorMessage: 'Failed',
      });

      await expect(
        service['uploadFileToStorage'](mockFile as Express.Multer.File),
      ).rejects.toThrow(InternalServerErrorException);
    });
  });
});
