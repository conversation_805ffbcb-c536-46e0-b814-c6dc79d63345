import {
  Injectable,
  Inject,
  BadRequestException,
  NotFoundException,
  Body,
  HttpStatus,
  InternalServerErrorException,
} from '@nestjs/common';
import { AxiosInstance } from 'axios';
import { withResponseError<PERSON>and<PERSON> } from '../utils/helpers';
import {
  FolderDTO,
  NamespaceResponseDTO,
  PathDTO,
  SecretDTO,
} from './dto/namespace-child.response.dto';
import { CreateNameSpaceRequestDto } from './dto/create_namespace.request.dto';
import { CreateNameSpaceResponse } from './dto/create-namespace.response.dto';
import { AssetsService } from '../assets/assets.service';
import { LoggerService } from '../loggers/logger.service';
import {
  RequestStatus,
  RequestType,
  ServiceCatalogName,
  ServiceCatalogType,
} from '../types';
import { SecretsMetadataRepository } from './secretsMetadata/secretsMetadata.repository';
import { SecretsPoliciesRepository } from './secretsPolicies/secretsPolicies.repository';
import {
  FetchSecretResponseDTO,
  FetchSecretType,
} from './dto/fetch-secrets.response.dto';
import {
  RotationType,
  Secrets,
  SecretType,
  Status,
} from './secretsMetadata/types/secretsMetadata.enum';
import { EncryptionService } from '../encryption/encryption.service';
import { CreateVaultSecretsRequestDto } from './dto/create-vault-secrets.request.dto';
import { CreateSecretMetaDataDto } from './secretsMetadata/dto/secretsMetadata.dto';
import { ResourcesRepository } from '../resources/resources.repository';
import { UpdateVaultSecretRequestDto } from './dto/update-vault-secrets.request.dto';
import { SecretsMetadataService } from './secretsMetadata/secretsMetadata.service';
import { NameSpaceType } from './enums/NamespaceType';
import { RotateSecretRequestDto } from './dto/rotate-secret.request.dto';
import { ENVIRONMENT_VARS } from '../utils/constants';
import { RotateSecretResponseDto } from './dto/rotate-secret.response.dto';
import { ConfigService } from '@nestjs/config';
import { ActionService } from '../action/providers/action.service';
import { ActionStatus } from '../action/enums/ActionStatus.enum';
import { SecretsMetaData } from './secretsMetadata/schemas/secretsMetadata.schema';
import { ActionType } from '../action/enums/ActionType.enum';
import { COLLECTIONS } from '../utils/constants';
import { PaginationQueryDto } from '../utils/pagination/dto/pagination.dto';
import { ResourcesService } from '../resources/resources.service';
import { PasswordPolicies } from './secretsPolicies/schemas/passwordPolicies.schema';
import { PasswordPoliciesResponseDto } from './secretsPolicies/dto/passwordpolicies.response.dto';
import { SecretsPoliciesService } from './secretsPolicies/secretsPolicies.service';
import { DevicesService } from './devices/providers/devices.service';
import { SecretsPolicies } from './secretsPolicies/schemas/secretsPolicies.schema';
import { SecretRotationUpdateRequestDto } from './dto/secret_rotation_update.request.dto';
import { ROTATION_STATUS } from './enums/secret_rotation_request_type';

@Injectable()
export class SecureStoreService {
  constructor(
    @Inject('SECRETS_MANAGEMENT_SERVICE_API')
    private readonly secretsManagmentServiceApi: AxiosInstance,
    @Inject('INTEGRATION_SERVICE_API')
    private readonly integrationApi: AxiosInstance,
    private readonly assetsService: AssetsService,
    private readonly logger: LoggerService,
    private secretsMetadataRepository: SecretsMetadataRepository,
    private secretsPoliciesRepository: SecretsPoliciesRepository,
    private readonly encryptionService: EncryptionService,
    private readonly resourcesRepository: ResourcesRepository,
    private configService: ConfigService,
    private readonly actionService: ActionService,
    private readonly resourcesService: ResourcesService,
    private readonly secretsMetadataService: SecretsMetadataService,
    private readonly secretsPoliciesService: SecretsPoliciesService,
    private readonly secretDevicesService: DevicesService,
  ) {}

  async fetchVaultSecretsPaginated(
    oldParams: {
      path: string;
      namespace: string;
      version?: string;
    },
    paginationOptions: PaginationQueryDto,
    paginate: boolean,
  ): Promise<any> {
    const secretsMetaData =
      await this.secretsMetadataRepository.getSecretsMetaDataPaginated(
        oldParams,
        {
          ...paginationOptions,
          limit: paginate ? paginationOptions.limit : Number.MAX_SAFE_INTEGER,
        },
      );

    const vaultResponses = [];

    for (const sec of secretsMetaData.items) {
      const params = { ...oldParams, path: sec.vaultPath };

      try {
        const response = await withResponseErrorHandler(
          this.secretsManagmentServiceApi.get('secrets', { params }),
        );

        vaultResponses.push({
          path: params.path,
          response,
        });
      } catch (error) {
        throw new InternalServerErrorException(
          'Unable to fetch Secrets from Vault',
        );
      }
    }

    const result = secretsMetaData.items.map((sec) => {
      const value = vaultResponses.find((vault) => vault.path === sec.vaultPath)
        .response?.data?.data[sec.devicePasswordKey];
      const secretData = {
        id: sec?._id,
        secretId: sec?.secretId,
        path: sec.vaultPath,
        key: sec.devicePasswordKey,
        value,
        type:
          SecretType.ROTATABLE_SECRET === sec.type
            ? FetchSecretType.ROTATABLE_SECRET
            : FetchSecretType.NORMAL_SECRET,
        status: sec?.status,
        rotationType: sec?.rotationType,
        secretTTLInHours: sec?.secretTTLInHours,
        nextRotationDate: sec?.nextRotationDate,
        updatedBy: sec?.updatedBy,
        updatedAt: sec?.updatedAt,
      };
      return secretData;
    });

    if (paginate) {
      return { items: result, pageInfo: secretsMetaData.pageInfo };
    } else {
      return result;
    }
  }

  async fetchNamespaceData(
    namespace: string,
    type?: NameSpaceType,
  ): Promise<NamespaceResponseDTO | PathDTO[]> {
    switch (type) {
      case NameSpaceType.FOLDER:
        return await this.fetchChildren(namespace);
      case NameSpaceType.PATH:
        const directories = await this.fetchChildren(namespace);
        return this.extractPathsFromDirectory(directories);
    }
  }

  extractPathsFromDirectory(directories: NamespaceResponseDTO): PathDTO[] {
    const paths = [];
    const loopChildren = (children: FolderDTO['children']) => {
      children.forEach((child) => {
        if ('path' in child) {
          paths.push({ label: child.path, value: child.path });
        } else {
          loopChildren(child.children);
        }
      });
    };

    directories.children.forEach((child) => {
      if ('path' in child) {
        paths.push({ label: child.path, value: child.path });
      } else {
        if ('children' in child) {
          loopChildren(child.children);
        }
      }
    });

    return paths;
  }

  async fetchChildren(
    namespace: string,
    path?: string,
  ): Promise<NamespaceResponseDTO> {
    const result: {
      namespace?: string;
      key?: string;
      type?: string;
      children: any[];
    } = {
      ...(path
        ? { key: path.split('/').filter(Boolean).pop() }
        : { namespace: namespace }),
      type: 'folder',
      children: [],
    };

    const nameSpaceData = await this.fetchMetaData({ namespace, path });

    if (nameSpaceData?.data?.keys) {
      for (let key of nameSpaceData.data.keys) {
        if (!key.includes('/')) {
          result.children.push({
            key: key.replace(/\//g, ''),
            type: 'secret',
            path: `${path ? `/${path}` : '/'}${key.replace(/\//g, '')}`,
            children: [],
          });
        } else {
          const childPath = path ? `${path}${key}` : key;
          const childData = await this.fetchChildren(namespace, childPath);
          result.children.push(childData);
        }
      }
    }

    return result as NamespaceResponseDTO;
  }

  async fetchMetaData(params: { path?: string; namespace: string }) {
    return withResponseErrorHandler(
      this.secretsManagmentServiceApi.get('metadata', { params }),
    );
  }

  async renewBulkTokens(secretIds: string[]) {
    return await withResponseErrorHandler(
      this.secretsManagmentServiceApi.post('tokens/renew-tokens', secretIds),
    );
  }

  async createRotateSecretRequest(
    rotateSecretRequest: RotateSecretRequestDto[],
  ): Promise<RotateSecretResponseDto> {
    const secretIds = rotateSecretRequest.map((data) => data.secretId);
    const secretsMetaData =
      await this.secretsMetadataRepository.getAllSecretsMetaDataBySecretIds(
        secretIds,
      );
    const RotatableSecrets = await Promise.all(
      secretsMetaData.map(async (metaData) => {
        const params = {
          path: metaData.vaultPath,
          namespace: metaData.vaultNamespace,
        };
        const response = await withResponseErrorHandler(
          this.secretsManagmentServiceApi.get('secrets', { params }),
        );

        const secret =
          response?.data?.data?.[metaData.devicePasswordKey] || null;
        return {
          charterId: metaData?.deviceId?.[0],
          deviceName: metaData?.devicePasswordKey,
          oldPassword: secret,
          newPassword: rotateSecretRequest.find(
            (secret) => secret.secretId === metaData.secretId,
          ).newPassword,
          nebulaSecretId: metaData.secretId,
          userName: metaData?.linkedSecret?.devicePasswordKey,
          deviceSource: metaData?.sourceSystem?.[0],
        };
      }),
    );

    await Promise.all(
      RotatableSecrets.map(async (secretsData) => {
        await this.integrationApi.post(`secrets`, secretsData);
        await withResponseErrorHandler(
          this.secretsManagmentServiceApi.post(
            'secrets',
            {
              [secretsData.nebulaSecretId]: secretsData.newPassword,
            },
            {
              params: {
                path: `${this.configService.get(ENVIRONMENT_VARS.ADHOC_LOCATION)}/${secretsData.nebulaSecretId}`,
                namespace: this.configService.get(
                  ENVIRONMENT_VARS.ADHOC_NAMESPACE,
                ),
              },
            },
          ),
        );
      }),
    );

    await this.secretsMetadataRepository.changeActiveStatusByIds(secretIds);
    return { message: 'Secret rotation request raised successfully.' };
  }

  async createNameSpaceRequest(
    createNameSpaceRequestDto: CreateNameSpaceRequestDto,
  ): Promise<CreateNameSpaceResponse> {
    this.logger.log('Vault Request', createNameSpaceRequestDto);

    const metadata = {
      serviceCatalog: {
        catalogName: ServiceCatalogName.CREATE_NAMESPACE,
        catalogType: ServiceCatalogType.IAAS,
      },
    };

    const serviceRequest = {
      metadata,
      payload: {
        ...createNameSpaceRequestDto,
      },
      requestType: RequestType.CREATE_NAMESPACE,
      status: RequestStatus.PENDING_APPROVAL,
    };

    this.logger.debug('serviceRequest: ', { serviceRequest });
    const dbResponse = await this.assetsService.create(serviceRequest);
    this.logger.debug('Response from DB ', dbResponse);
    const queued =
      await this.assetsService.queueIfApprovalNotRequired(dbResponse);
    this.logger.log(
      `Request submitted for ${queued ? 'processing' : 'approval'}`,
    );
    return {
      id: dbResponse.id,
      serviceRequestId: dbResponse.serviceRequestId,
      message: `Request submitted for ${queued ? 'processing' : 'approval'}`,
    };
  }

  async createVaultSecrets(
    namespaceName: string,
    createVaultSecretsRequestDto: CreateVaultSecretsRequestDto,
    path?: string,
  ) {
    const {
      type,
      vaultKey,
      vaultPassword,
      userNameKey,
      userNamePassword,
      rotationType,
      secretTTLInHours,
      description,
      policyId,
      nextRotationDate,
      expiryDate,
      tokenRenewByNebula,
    } = createVaultSecretsRequestDto;

    let secretData = {
      [vaultKey]: vaultPassword,
      [userNameKey]: userNamePassword,
    };

    let VaultTokenSecretId;

    const resources =
      await this.resourcesRepository.getResourcesByNamespace(namespaceName);

    if (createVaultSecretsRequestDto.type !== FetchSecretType.VAULT_TOKEN) {
      const response = await withResponseErrorHandler(
        this.secretsManagmentServiceApi.get('secrets', {
          params: {
            path,
            namespace: namespaceName,
          },
        }),
      );
      if (response?.data?.data.hasOwnProperty(vaultKey)) {
        throw new BadRequestException(
          'Secrets key is already present for the specific path.',
        );
      }
      if (response?.data?.data) {
        secretData = {
          ...response?.data?.data,
          ...secretData,
        };
      }
    } else if (type === FetchSecretType.VAULT_TOKEN) {
      VaultTokenSecretId =
        await this.secretsMetadataRepository.generateSecretId(
          SecretType.VAULT_TOKEN,
        );

      path =
        this.configService.get(ENVIRONMENT_VARS.MASTER_TOKEN_FOLDER) +
        '/' +
        VaultTokenSecretId;
    }

    await withResponseErrorHandler(
      this.secretsManagmentServiceApi.post('secrets', secretData, {
        params: {
          path,
          namespace:
            createVaultSecretsRequestDto.type === FetchSecretType.VAULT_TOKEN
              ? this.configService.get(ENVIRONMENT_VARS.VAULT_MASTER_NAMESPACE)
              : namespaceName,
        },
      }),
    );
    if (type === FetchSecretType.VAULT_TOKEN) {
      const createVaultTokenData: CreateSecretMetaDataDto = {
        secretId: VaultTokenSecretId,
        resourceId: resources?.resourceId,
        status: Status.ACTIVE,
        active: true,
        description,
        type: SecretType.VAULT_TOKEN,
        vaultPath: '',
        expiryDate,
        devicePasswordKey: vaultKey,
        tokenRenewByNebula,
      };
      await this.secretsMetadataRepository.create(createVaultTokenData);
    }
    if (type === FetchSecretType.ROTATABLE_SECRET) {
      const normalSecretId =
        await this.secretsMetadataRepository.generateSecretId(
          SecretType.NORMAL_SECRET,
        );
      const RotatableSecretId =
        await this.secretsMetadataRepository.generateSecretId(
          SecretType.ROTATABLE_SECRET,
        );
      const createNormalSecretData: CreateSecretMetaDataDto = {
        secretId: normalSecretId,
        resourceId: resources?.resourceId,
        status: Status.SUCCESS,
        active: true,
        description,
        policyId,
        type: SecretType.NORMAL_SECRET,
        vaultPath: decodeURIComponent(path),
        devicePasswordKey: userNameKey,
        vaultNamespace: namespaceName,
      };

      const createRotatableSecretData: CreateSecretMetaDataDto = {
        secretId: RotatableSecretId,
        resourceId: resources?.resourceId,
        status: Status.SUCCESS,
        secretTTLInHours,
        active: true,
        description,
        policyId,
        type: SecretType.ROTATABLE_SECRET,
        rotationType,
        nextRotationDate,
        vaultPath: decodeURIComponent(path),
        devicePasswordKey: vaultKey,
        deviceUserNameSecretId: normalSecretId,
        vaultNamespace: namespaceName,
      };

      const action = await this.actionService.create({
        actionTypeId: ActionType.CREATE_NEW_SECRET,
        entityId: RotatableSecretId,
        entityType: COLLECTIONS.SECRETS_METADATA,
        status: ActionStatus.STARTED,
      });

      try {
        await this.secretsMetadataRepository.create(createNormalSecretData);
        await this.secretsMetadataRepository.create(createRotatableSecretData);
        await this.actionService.update({
          actionId: action.actionId,
          status: ActionStatus.COMPLETED,
        });
      } catch (error) {
        await this.actionService.update({
          actionId: action.actionId,
          status: ActionStatus.FAILED,
        });
      }
    }
    if (type === FetchSecretType.NORMAL_SECRET) {
      const normalSecretId =
        await this.secretsMetadataRepository.generateSecretId(
          SecretType.NORMAL_SECRET,
        );
      const createNormalSecretData: CreateSecretMetaDataDto = {
        secretId: normalSecretId,
        resourceId: resources?.resourceId,
        status: Status.SUCCESS,
        active: true,
        description,
        policyId,
        type: SecretType.NORMAL_SECRET,
        vaultPath: decodeURIComponent(path),
        devicePasswordKey: vaultKey,
        vaultNamespace: namespaceName,
      };

      const action = await this.actionService.create({
        actionTypeId: ActionType.CREATE_NEW_SECRET,
        entityId: normalSecretId,
        entityType: COLLECTIONS.SECRETS_METADATA,
        status: ActionStatus.STARTED,
      });

      try {
        await this.secretsMetadataRepository.create(createNormalSecretData);
        await this.actionService.update({
          actionId: action.actionId,
          status: ActionStatus.COMPLETED,
        });
      } catch (error) {
        await this.actionService.update({
          actionId: action.actionId,
          status: ActionStatus.FAILED,
        });
      }
    }
    this.logger.log('Secret is created successfully');
    return { message: 'Secret is created successfully.' };
  }

  async updateVaultSecretByPath(
    secretId: string,
    updateVaultSecretRequest: UpdateVaultSecretRequestDto,
  ): Promise<{ message: string }> {
    const secretMetaData =
      await this.secretsMetadataRepository.getSecretsMetaDataBySecretId(
        secretId,
      );
    if (!secretMetaData) {
      throw new NotFoundException(`Secret with id ${secretId} not found.`);
    }
    let response: {
      data: { data: Secrets };
    } = await withResponseErrorHandler(
      this.secretsManagmentServiceApi.get('secrets', {
        params: {
          path: secretMetaData.vaultPath,
          namespace: secretMetaData.vaultNamespace,
        },
      }),
    );
    const secretResponse = response?.data?.data;
    const { updateMetaData, secretData } =
      await this.secretsMetadataRepository.createQueryForUpdateMetaData({
        secretMetaData,
        secretResponse,
        updateVaultSecretRequest,
        secretId,
      });
    await withResponseErrorHandler(
      this.secretsManagmentServiceApi.post('secrets', secretData, {
        params: {
          path: secretMetaData.vaultPath,
          namespace: secretMetaData.vaultNamespace,
        },
      }),
    );
    await this.secretsMetadataRepository.bulkUpdateSecretsMetaDataBySecretId(
      updateMetaData,
    );
    this.logger.log(
      `Secret with id ${secretId} has been updated successfully.`,
    );
    return { message: 'Secret is updated successfully.' };
  }

  async generatePassword(namespace: string, data: object) {
    const params = namespace ? { params: { namespace } } : {};
    const policyName = { data };
    return withResponseErrorHandler(
      this.secretsManagmentServiceApi.get(
        `vault/${policyName}/password/generate`,
        params,
      ),
    );
  }

  async encryptPassword(password: string): Promise<string> {
    const encryptedVaultPassword = this.encryptionService.encrypt(password);
    return encryptedVaultPassword;
  }

  async decryptPassword(encryptedText: string): Promise<string> {
    const decryptedVaultPassword =
      this.encryptionService.decrypt(encryptedText);
    return decryptedVaultPassword;
  }

  async fetchSecretsDetails(secretId: string) {
    const secretMetaData =
      await this.secretsMetadataRepository.getSecretsMetaDataBySecretId(
        secretId,
      );
    await this.resourcesRepository.findOneByResourceId(
      secretMetaData?.resourceId,
    );
    const params = {
      path: secretMetaData.vaultPath,
      namespace: secretMetaData.vaultNamespace,
    };
    const response = await withResponseErrorHandler(
      this.secretsManagmentServiceApi.get('secrets', { params }),
    );

    const policiesData = await this.secretsPoliciesRepository.getPolicy(
      secretMetaData.policyId,
    );
    const secretsData: {
      vaultKey: string;
      vaultPassword: string;
      userNameKey?: string;
      userNamePassword?: string;
      resourceId: string;
      policyId?: string;
      policyName?: string;
      rotationType?: string;
      expiryDate?: Date;
      notificationEnabled?: boolean;
      tokenTTLInHours?: number;
      nextRotationDate?: Date;
      namespace: string;
      vaultPath: string;
      type: string;
    } = {
      vaultKey: '',
      vaultPassword: '',
      resourceId: secretMetaData.resourceId,
      namespace: secretMetaData.vaultNamespace,
      vaultPath: secretMetaData.vaultPath,
      type:
        SecretType.ROTATABLE_SECRET === secretMetaData.type
          ? FetchSecretType.ROTATABLE_SECRET
          : FetchSecretType.NORMAL_SECRET,
    };

    if (secretMetaData.type === SecretType.ROTATABLE_SECRET) {
      const userNameSecretMetaData =
        await this.secretsMetadataRepository.getSecretsMetaDataBySecretId(
          secretMetaData.deviceUserNameSecretId,
        );

      secretsData.policyId = secretMetaData.policyId;
      secretsData.policyName = policiesData?.policyName;
      secretsData.rotationType = secretMetaData.rotationType;
      secretsData.expiryDate = secretMetaData.expiryDate;
      secretsData.notificationEnabled = secretMetaData.notificationEnabled;
      secretsData.tokenTTLInHours = secretMetaData.tokenTTLInHours;
      secretsData.nextRotationDate = secretMetaData.nextRotationDate;
      Object.entries(response?.data?.data).forEach(
        ([key, value]: [string, string]) => {
          if (key === secretMetaData.devicePasswordKey) {
            secretsData.vaultKey = key;
            secretsData.vaultPassword = value;
          }
          if (key === userNameSecretMetaData.devicePasswordKey) {
            secretsData.userNameKey = key;
            secretsData.userNamePassword = value;
          }
        },
      );
    } else {
      Object.entries(response?.data?.data).forEach(
        ([key, value]: [string, string]) => {
          if (key === secretMetaData.devicePasswordKey) {
            secretsData.vaultKey = key;
            secretsData.vaultPassword = value;
          }
        },
      );
    }

    return secretsData;
  }
  async fetchAuthorizedNamespaces(envId?: string) {
    let filter = {
      requestType: { equals: 'CREATE_NAMESPACE' },
    };

    if (envId) {
      filter['platformContext.envId'] = { equals: envId };
    }

    const resources = await this.resourcesService.getPaginatedResourcesList({
      filter: JSON.stringify(filter),
    });

    const items = resources.items.map((item) => ({
      namespaceName: item.resourcesName,
      resourceId: item.resourceId,
      status: item.status,
      catalogType: item.catalogType,
      catalogLevel03: item.catalogLevel03,
      platformContext: item.platformContext,
      requestType: item.requestType,
      resourcesDetails: item.resourcesDetails,
    }));

    return { items };
  }

  async fetchPasswordPolicies(
    namespace?: string,
  ): Promise<PasswordPoliciesResponseDto[]> {
    const queryParams = namespace
      ? `?namespace=${encodeURIComponent(namespace)}`
      : '';

    const response = await withResponseErrorHandler(
      this.secretsManagmentServiceApi.get(
        `policies/password-policy${queryParams}`,
      ),
    );

    const policyNames: string[] = response.data?.keys ?? [];

    const validPolicyData =
      await this.secretsPoliciesRepository.getPasswordPolicies(policyNames);

    return validPolicyData as PasswordPoliciesResponseDto[];
  }
  private async isPasswordUnique(payload: {
    devicePasswordKey: string;
    newPassword: string;
    namespace: string;
    vaultPath: string;
  }): Promise<Boolean> {
    const {
      namespace,
      vaultPath,
      devicePasswordKey: passwordKey,
      newPassword,
    } = payload;

    const response = await withResponseErrorHandler(
      this.secretsManagmentServiceApi.get('secrets', {
        params: {
          path: vaultPath,
          namespace,
        },
      }),
    );

    if (!response?.data) {
      throw new NotFoundException('Secrets not found.');
    }

    let {
      data: {
        metadata: { version },
      },
    } = response;
    let versionLimit = version > 10 ? version - 10 : 0;
    while (version > versionLimit) {
      const response = await withResponseErrorHandler(
        this.secretsManagmentServiceApi.get('secrets', {
          params: {
            path: vaultPath,
            namespace,
            version,
          },
        }),
      );

      const { data: data } = response;
      version -= 1;
      if (data[passwordKey] === newPassword) return false;
    }
    return true;
  }

  async generateUniquePassword(ids: string): Promise<Object[]> {
    let secretIds = ids.split(',').map((id) => id.trim());
    let secretMetaDataList: SecretsMetaData[] =
      await this.secretsMetadataService.getSecretsMetaDataBySecretId(secretIds);
    if (!secretMetaDataList) {
      throw new NotFoundException('Secrets not found for the provided IDs.');
    }
    const policyIds = [
      ...new Set(
        secretMetaDataList.map((secretMetaData) => secretMetaData.policyId),
      ),
    ];
    let secretsPoliciesList: SecretsPolicies[] =
      await this.secretsPoliciesService.getPolicy(policyIds);
    if (!secretsPoliciesList) {
      throw new NotFoundException(
        'Secrets policies not found for the provided IDs.',
      );
    }
    const policiesMap = secretsPoliciesList.reduce((acc, secretsPolicy) => {
      const policyId = secretsPolicy.policyId;
      acc[policyId] = secretsPolicy.policyName;
      return acc;
    }, {});
    let newPasswordList = [];
    for (const secretMetaData of secretMetaDataList) {
      const {
        vaultNamespace: namespace,
        policyId,
        devicePasswordKey,
        vaultPath,
        secretId,
      } = secretMetaData;
      const policyName = policiesMap[policyId];

      while (true) {
        const {
          data: { password: newPassword },
        } = await withResponseErrorHandler(
          this.secretsManagmentServiceApi.get(
            `vault/${policyName}/password/generate?namespace=${namespace}`,
          ),
        );

        if (
          await this.isPasswordUnique({
            devicePasswordKey,
            newPassword,
            namespace,
            vaultPath,
          })
        ) {
          newPasswordList.push({
            secretId,
            [devicePasswordKey]: newPassword,
          });
          break;
        }
      }
    }
    return newPasswordList;
  }
  async deleteSecretsByIds(ids: string) {
    let secretsIds = ids.split(',').map((si) => si.trim());
    let secretsMetaDataList: SecretsMetaData[] =
      await this.secretsMetadataService.getSecretsMetaDataBySecretId(
        secretsIds,
      );

    if (!secretsMetaDataList) {
      throw new NotFoundException('Secrets not found for the provided IDs.');
    }

    const { deviceUserNameSecretIds, secretIdsRef } =
      secretsMetaDataList.reduce(
        (acc, sm) => {
          if (sm.type === SecretType.ROTATABLE_SECRET) {
            if (!acc?.deviceUserNameSecretIds && !acc?.secretIdsRef) {
              acc.deviceUserNameSecretIds = [];
              acc.secretIdsRef = [];
            }
            acc.deviceUserNameSecretIds.push(sm.deviceUserNameSecretId);
            acc.secretIdsRef.push(sm.secretId);
          }
          return acc;
        },
        { deviceUserNameSecretIds: [], secretIdsRef: [] },
      );
    if (deviceUserNameSecretIds.length) {
      secretsMetaDataList = [
        ...secretsMetaDataList,
        ...(await this.secretsMetadataService.getSecretsMetaDataBySecretId(
          deviceUserNameSecretIds,
        )),
      ];
      secretsIds = [...new Set([...secretsIds, ...deviceUserNameSecretIds])];
      const deactivateSecretDevicesOps = secretIdsRef.map((id) => ({
        updateOne: {
          filter: { secretId: id },
          update: { $set: { active: false } },
          upsert: true,
        },
      }));
      await this.secretDevicesService.bulkUpdate(deactivateSecretDevicesOps);
    }
    const deactivateSecretMetadataOps = secretsIds.map((id) => ({
      updateOne: {
        filter: { secretId: id },
        update: { $set: { active: false, isDeleted: true } },
        upsert: true,
      },
    }));
    await this.secretsMetadataService.bulkUpdateSecretsMetaDataBySecretId(
      deactivateSecretMetadataOps,
    );

    const secretsGroupedByNameSpaceAndPath = secretsMetaDataList.reduce(
      (acc, sm) => {
        const { vaultNamespace, vaultPath, devicePasswordKey } = sm;
        const uniqueGroupName = `${vaultNamespace}_${vaultPath}`;
        if (!acc.hasOwnProperty(uniqueGroupName)) {
          acc[uniqueGroupName] = [];
        }
        acc[uniqueGroupName].push(devicePasswordKey);
        return acc;
      },
      {},
    );

    for (let uniqueGroup in secretsGroupedByNameSpaceAndPath) {
      if (secretsGroupedByNameSpaceAndPath[uniqueGroup].length) {
        const [vaultNamespace, vaultPath] = uniqueGroup
          .split('_')
          .map((ug) => ug.trim());
        const params = {
          namespace: vaultNamespace,
          path: vaultPath,
        };
        const response = await withResponseErrorHandler(
          this.secretsManagmentServiceApi.get('secrets', { params }),
        );

        if (!response?.data?.data) {
          throw new NotFoundException('Secrets not found.');
        }
        const currSecretsObject = response?.data?.data;
        const updatedSecretsObject = { ...currSecretsObject };
        for (const secret of secretsGroupedByNameSpaceAndPath[uniqueGroup]) {
          delete updatedSecretsObject[secret];
        }
        await withResponseErrorHandler(
          this.secretsManagmentServiceApi.post(
            'secrets',
            updatedSecretsObject,
            {
              params,
            },
          ),
        );
      }
    }

    return {
      status: HttpStatus.OK,
      message: 'Secrets deleted successfully.',
    };
  }

  async secretsRotationResult(
    secretRotationUpdateRequest: SecretRotationUpdateRequestDto,
  ) {
    const secretMetaData =
      await this.secretsMetadataRepository.getSecretsMetaDataBySecretId(
        secretRotationUpdateRequest.nebulaSecretId,
      );
    if (!secretMetaData) {
      throw new NotFoundException(
        `Secret with id ${secretRotationUpdateRequest.nebulaSecretId} not found.`,
      );
    }
    const { vaultNamespace, vaultPath } = secretMetaData;

    const params = {
      path: `${this.configService.get(ENVIRONMENT_VARS.ADHOC_LOCATION)}/${secretRotationUpdateRequest.nebulaSecretId}`,
      namespace: this.configService.get(ENVIRONMENT_VARS.ADHOC_NAMESPACE),
    };
    const response = await withResponseErrorHandler(
      this.secretsManagmentServiceApi.get('secrets', { params }),
    );

    const newSecretData =
      response?.data?.data?.[secretRotationUpdateRequest.nebulaSecretId] ||
      null;
    if (!response?.data?.data) {
      throw new NotFoundException('Secrets not found.');
    }

    if (secretRotationUpdateRequest.status === ROTATION_STATUS.COMPLETED) {
      let secretResponse: {
        data: { data: Secrets };
      } = await withResponseErrorHandler(
        this.secretsManagmentServiceApi.get('secrets', {
          params: {
            path: vaultPath,
            namespace: vaultNamespace,
          },
        }),
      );
      const updateSecretData = {
        ...secretResponse?.data?.data,
        [secretMetaData.devicePasswordKey]: newSecretData,
      };

      await withResponseErrorHandler(
        this.secretsManagmentServiceApi.post('secrets', updateSecretData, {
          params: {
            path: vaultPath,
            namespace: vaultNamespace,
          },
        }),
      );
    }

    await this.cleanupTemporarySecrets(
      secretRotationUpdateRequest.nebulaSecretId,
    );

    await this.secretsMetadataRepository.changeActiveStatusByIds(
      [secretRotationUpdateRequest.nebulaSecretId],
      true,
    );
    return {
      status: HttpStatus.OK,
      message: 'Secrets Updated.',
    };
  }

  async cleanupTemporarySecrets(secretId: string) {
    return await withResponseErrorHandler(
      this.secretsManagmentServiceApi.delete('metadata', {
        params: {
          path: `${this.configService.get(ENVIRONMENT_VARS.ADHOC_LOCATION)}/${secretId}`,
          namespace: this.configService.get(ENVIRONMENT_VARS.ADHOC_NAMESPACE),
        },
      }),
    );
  }

  async fetchNamespaceResources() {
    return await this.resourcesService.getNamespaceResources();
  }
}
