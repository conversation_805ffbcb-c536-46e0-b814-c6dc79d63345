import {
  Injectable,
  Inject,
  BadRequestException,
  NotFoundException,
  HttpStatus,
  InternalServerErrorException,
  NotImplementedException,
} from '@nestjs/common';
import { AxiosInstance } from 'axios';
import { getUserContext, with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../utils/helpers';
import {
  FolderDTO,
  NamespaceResponseDTO,
  PathDTO,
} from './dto/namespace-child.response.dto';
import { CreateNameSpaceRequestDto } from './dto/create_namespace.request.dto';
import { CreateNameSpaceResponse } from './dto/create-namespace.response.dto';
import { AssetsService } from '../assets/assets.service';
import { LoggerService } from '../loggers/logger.service';
import {
  RequestStatus,
  RequestType,
  ServiceCatalogName,
  ServiceCatalogType,
} from '../types';
import { SecretsMetadataRepository } from './secretsMetadata/secretsMetadata.repository';
import { SecretsPoliciesRepository } from './secretsPolicies/secretsPolicies.repository';
import { FetchSecretType } from './dto/fetch-secrets.response.dto';
import {
  Secrets,
  SecretType,
  Status,
} from './secretsMetadata/types/secretsMetadata.enum';
import { EncryptionService } from '../encryption/encryption.service';
import { CreateVaultSecretsRequestDto } from './dto/create-vault-secrets.request.dto';
import { CreateSecretMetaDataDto } from './secretsMetadata/dto/secretsMetadata.dto';
import { ResourcesRepository } from '../resources/resources.repository';
import { UpdateVaultSecretRequestDto } from './dto/update-vault-secrets.request.dto';
import { SecretsMetadataService } from './secretsMetadata/secretsMetadata.service';
import { NameSpaceType } from './enums/NamespaceType';
import { RotateSecretRequestDto } from './dto/rotate-secret.request.dto';
import { ENVIRONMENT_VARS } from '../utils/constants';
import { RotateSecretResponseDto } from './dto/rotate-secret.response.dto';
import { ConfigService } from '@nestjs/config';
import { ActionService } from '../action/providers/action.service';
import { ActionStatus } from '../action/enums/ActionStatus.enum';
import { SecretsMetaData } from './secretsMetadata/schemas/secretsMetadata.schema';
import { ActionType } from '../action/enums/ActionType.enum';
import { COLLECTIONS } from '../utils/constants';
import { PaginationQueryDto } from '../utils/pagination/dto/pagination.dto';
import { ResourcesService } from '../resources/resources.service';
import { SecretsPoliciesService } from './secretsPolicies/secretsPolicies.service';
import { DevicesService } from './devices/providers/devices.service';
import { SecretsPolicies } from './secretsPolicies/schemas/secretsPolicies.schema';
import {
  SecretRotationUpdateRequestDto,
  ErrorDto,
} from './dto/secret_rotation_update.request.dto';
import {
  ROTATION_STATUS,
  TOKEN_STATUS,
} from './enums/secret_rotation_request_type';
import * as _ from 'lodash';
import { IntegrationNotificationService } from '../naas/integration.notification.service';
import { createSecretPoliciesDto } from './dto/create_secretPolicy.request.dto';
import { ApiBody } from '@nestjs/swagger';
import { MultiEnvIAMService } from '../multi-env/iam/iam.service';
import { request } from 'http';
import { RequestContext } from 'nestjs-request-context';

@Injectable()
export class SecureStoreService {
  constructor(
    @Inject('SECRETS_MANAGEMENT_SERVICE_API')
    private readonly secretsManagmentServiceApi: AxiosInstance,
    @Inject('WATCHER_SERVICE_API')
    private readonly watcherServiceApi: AxiosInstance,
    @Inject('INTEGRATION_SERVICE_API')
    private readonly integrationApi: AxiosInstance,
    private readonly assetsService: AssetsService,
    private readonly logger: LoggerService,
    private secretsMetadataRepository: SecretsMetadataRepository,
    private secretsPoliciesRepository: SecretsPoliciesRepository,
    private readonly encryptionService: EncryptionService,
    private readonly resourcesRepository: ResourcesRepository,
    private configService: ConfigService,
    private readonly actionService: ActionService,
    private readonly resourcesService: ResourcesService,
    private readonly secretsMetadataService: SecretsMetadataService,
    private readonly secretsPoliciesService: SecretsPoliciesService,
    private readonly integrationNotificationService: IntegrationNotificationService,
    private readonly multiEnvIAMService: MultiEnvIAMService,
    private readonly secretDevicesService: DevicesService,
  ) {}

  async fetchVaultSecretsPaginated(
    oldParams: {
      path: string;
      namespace: string;
      version?: string;
    },
    paginationOptions: PaginationQueryDto,
    paginate: boolean,
  ): Promise<any> {
    const secretsMetaData =
      await this.secretsMetadataRepository.getSecretsMetaDataPaginated(
        oldParams,
        {
          ...paginationOptions,
          limit: paginate ? paginationOptions.limit : Number.MAX_SAFE_INTEGER,
        },
      );

    const promises = secretsMetaData.items.map((sec) => {
      const params = { ...oldParams, path: sec.vaultPath };

      return withResponseErrorHandler(
        this.secretsManagmentServiceApi.get('secrets', { params }),
      ).then((response) => ({
        path: params.path,
        response,
      }));
    });

    let vaultResponses = [];
    try {
      vaultResponses = await Promise.all(promises);
    } catch (error) {
      throw new InternalServerErrorException(
        'Error occured while fetching Secrets from Vault',
      );
    }

    const result = secretsMetaData.items.map((sec) => {
      const value = vaultResponses.find((vault) => vault.path === sec.vaultPath)
        .response?.data?.data[sec.devicePasswordKey];
      const secretData = {
        id: sec?._id,
        secretId: sec?.secretId,
        path: sec.vaultPath,
        key: sec.devicePasswordKey,
        value,
        type:
          SecretType.ROTATABLE_SECRET === sec.type
            ? FetchSecretType.ROTATABLE_SECRET
            : FetchSecretType.NORMAL_SECRET,
        status: sec?.status,
        rotationType: sec?.rotationType,
        secretTTLInHours: sec?.secretTTLInHours,
        policyId: sec?.policyId,
        expiryDate: sec?.expiryDate,
        nextRotationDate: sec?.nextRotationDate,
        updatedBy: sec?.updatedBy,
        updatedAt: sec?.updatedAt,
        lastDeviceSyncStatus: sec?.lastDeviceSyncStatus,
        active: sec?.active,
        notifyBeforeTokenExpiry: sec?.notifyBeforeTokenExpiry,
      };
      return secretData;
    });

    if (paginate) {
      return { items: result, pageInfo: secretsMetaData.pageInfo };
    } else {
      return result;
    }
  }

  async fetchNamespaceData(
    namespace: string,
    type?: NameSpaceType,
  ): Promise<NamespaceResponseDTO | PathDTO[]> {
    switch (type) {
      case NameSpaceType.FOLDER:
        return this.fetchChildren(namespace)
          .then((data) => {
            return data;
          })
          .catch(() => {
            return {
              namespace,
              type,
              children: [],
            };
          });
      case NameSpaceType.PATH:
        return this.fetchChildren(namespace)
          .then((directories) => {
            return this.extractPathsFromDirectory(directories);
          })
          .catch(() => {
            return [];
          });
      case NameSpaceType.SECRET:
        throw new NotImplementedException('This is not implemented yet');
      default:
        throw new BadRequestException(`${type} is not a valid type`);
    }
  }

  extractPathsFromDirectory(directories: NamespaceResponseDTO): PathDTO[] {
    const paths = [];
    const loopChildren = (children: FolderDTO['children']) => {
      children.forEach((child) => {
        if ('path' in child) {
          paths.push({ label: child.path, value: child.path });
        } else {
          loopChildren(child.children);
        }
      });
    };

    directories.children.forEach((child) => {
      if ('path' in child) {
        paths.push({ label: child.path, value: child.path });
      } else {
        if ('children' in child) {
          loopChildren(child.children);
        }
      }
    });

    return paths;
  }

  async fetchChildren(
    namespace: string,
    path?: string,
  ): Promise<NamespaceResponseDTO> {
    const result: {
      namespace?: string;
      key?: string;
      type?: string;
      children: any[];
    } = {
      ...(path
        ? { key: path.split('/').filter(Boolean).pop(), path: '/' + path }
        : { namespace: namespace }),
      type: 'folder',
      children: [],
    };

    const nameSpaceData = await this.fetchMetaData({ namespace, path });

    if (nameSpaceData?.data?.keys) {
      for (const key of nameSpaceData.data.keys) {
        if (!key.includes('/')) {
          result.children.push({
            key: key.replace(/\//g, ''),
            type: 'secret',
            path: `${path ? `/${path}` : '/'}${key.replace(/\//g, '')}`,
            children: [],
          });
        } else {
          const childPath = path ? `${path}${key}` : key;
          const childData = await this.fetchChildren(namespace, childPath);
          result.children.push(childData);
        }
      }
    }

    return result as NamespaceResponseDTO;
  }

  async fetchMetaData(params: { path?: string; namespace: string }) {
    return withResponseErrorHandler(
      this.secretsManagmentServiceApi.get('metadata', { params }),
    );
  }

  async renewBulkTokens(secretIds: string[]) {
    return await withResponseErrorHandler(
      this.secretsManagmentServiceApi.post('tokens/renew-tokens', secretIds),
    );
  }

  async createRotateSecretRequest(
    rotateSecretRequest: RotateSecretRequestDto[],
  ): Promise<RotateSecretResponseDto> {
    const secretIds = rotateSecretRequest.map((data) => data.secretId);
    const secretsMetaData =
      await this.secretsMetadataRepository.getAllSecretsMetaDataBySecretIds(
        secretIds,
      );

    // Validate that all secretIds exist in database before proceeding
    if (secretsMetaData.length !== secretIds.length) {
      const foundSecretIds = secretsMetaData.map((data) => data.secretId);
      const missingSecretIds = secretIds.filter(
        (secretId) => !foundSecretIds.includes(secretId),
      );
      throw new NotFoundException(
        `Secrets not found for the following IDs: ${missingSecretIds.join(
          ', ',
        )}`,
      );
    }

    try {
      var actions = await Promise.all(
        secretsMetaData.map((data) =>
          this.actionService.create({
            actionTypeId: ActionType.SECRET_ROTATION,
            entityId: data.secretId,
            entityType: COLLECTIONS.SECRETS_METADATA,
            status: ActionStatus.STARTED,
          }),
        ),
      );

      const nonRotatableSecrets = secretsMetaData.filter(
        (metaData) =>
          metaData.type !== SecretType.ROTATABLE_SECRET ||
          !metaData.sourceSystem?.length ||
          !metaData.active,
      );
      const nonRotatableSecretIds = nonRotatableSecrets.map(
        ({ secretId }) => secretId,
      );

      if (nonRotatableSecretIds.length) {
        nonRotatableSecrets.forEach((failedSecret) => {
          const {
            secretId,
            type,
            vaultNamespace,
            vaultPath,
            devicePasswordKey,
            resourceId,
          } = failedSecret;

          this.resourcesService
            .getResourceByResourceId(resourceId)
            .then((resource) => {
              this.multiEnvIAMService
                .getProjectAppEnvByEnvId(resource.platformContext.envId)
                .then(async (project) => {
                  const { emailDistribution: emailList, projectName } = project;
                  await this.secretRoatationNotification(
                    secretId,
                    emailList,
                    {
                      project: projectName,
                      namespace: vaultNamespace,
                      path: vaultPath,
                      vaultSecretKey: devicePasswordKey,
                    },
                    TOKEN_STATUS.TOKEN_ROTATION_INITIATION_FAILED,
                    `secretId ${secretId} cannot be rotated because it is inactive or not linked to a device.`,
                  );
                })
                .catch((error) => {
                  this.logger.error('Error in fetching project.');
                  this.logger.error(error);
                });
            })
            .catch((error) => {
              this.logger.error('Error while fetching resources.');
              this.logger.error(error);
            });
        });
        throw new BadRequestException(
          `secretId(s) ${nonRotatableSecretIds.join(', ')} cannot be rotated because they are inactive or not linked to a device.`,
        );
      }

      const RotatableSecrets = await Promise.all(
        secretsMetaData.map(async (metaData) => {
          const params = {
            path: metaData.vaultPath,
            namespace: metaData.vaultNamespace,
          };
          const response = await withResponseErrorHandler(
            this.secretsManagmentServiceApi.get('secrets', { params }),
          );

          const currentPasswordFromVault =
            response?.data?.data?.[metaData.devicePasswordKey] || null;
          const username =
            response?.data?.data?.[metaData?.linkedSecret?.devicePasswordKey] ||
            null;
          return {
            charterId: metaData?.deviceId?.[0],
            deviceName: metaData?.devicePasswordKey, //fix needed
            oldPassword: currentPasswordFromVault,
            newPassword: rotateSecretRequest.find(
              (secret) => secret.secretId === metaData.secretId,
            ).newPassword,
            nebulaSecretId: metaData.secretId,
            userName: username,
            deviceSource: metaData?.sourceSystem?.[0],
            nebulaActionId: actions.find(
              (action) => action.entityId === metaData.secretId,
            ).actionId,
            secretId: metaData.secretId,
            type: metaData.type,
            vaultNamespace: metaData.vaultNamespace,
            vaultPath: metaData.vaultPath,
            devicePasswordKey: metaData.devicePasswordKey,
            resourceId: metaData.resourceId,
          };
        }),
      );

      const successSecretIds = [];
      const successActionIds = [];
      var failureActionIds = [];
      await Promise.all(
        RotatableSecrets.map(async (secretsData) => {
          await this.integrationApi.post(`secrets`, secretsData);
          await withResponseErrorHandler(
            this.secretsManagmentServiceApi.post(
              'secrets',
              {
                [secretsData.nebulaSecretId]: secretsData.newPassword,
              },
              {
                params: {
                  path: `${this.configService.get(ENVIRONMENT_VARS.ADHOC_LOCATION)}/${secretsData.nebulaSecretId}`,
                  namespace: this.configService.get(
                    ENVIRONMENT_VARS.ADHOC_NAMESPACE,
                  ),
                },
              },
            ),
          )
            .then(() => {
              successSecretIds.push(secretsData.nebulaSecretId);
              successActionIds.push(secretsData.nebulaActionId);
              const {
                secretId,
                type,
                vaultNamespace,
                vaultPath,
                devicePasswordKey,
                resourceId,
              } = secretsData;
              this.resourcesService
                .getResourceByResourceId(resourceId)
                .then(async (resource) => {
                  await this.multiEnvIAMService
                    .getProjectAppEnvByEnvId(resource.platformContext.envId)
                    .then(async (project) => {
                      const { emailDistribution: emailList, projectName } =
                        project;

                      await this.secretRoatationNotification(
                        secretId,
                        emailList,
                        {
                          project: projectName,
                          namespace: vaultNamespace,
                          path: vaultPath,
                          vaultSecretKey: devicePasswordKey,
                        },
                        TOKEN_STATUS.TOKEN_ROTATION_INITIATED,
                      );
                    })
                    .catch((error) => {
                      this.logger.error('Error while fetching project.');
                      this.logger.error(error);
                    });
                })
                .catch((error) => {
                  this.logger.error('Error while fetching resources.');
                  this.logger.error(error);
                });
            })
            .catch(() => {
              failureActionIds.push(secretsData.nebulaActionId);
              const {
                secretId,
                type,
                vaultNamespace,
                vaultPath,
                devicePasswordKey,
                resourceId,
              } = secretsData;

              this.resourcesService
                .getResourceByResourceId(resourceId)
                .then((resource) => {
                  this.multiEnvIAMService
                    .getProjectAppEnvByEnvId(resource.platformContext.envId)
                    .then(async (project) => {
                      const { emailDistribution: emailList, projectName } =
                        project;
                      await this.secretRoatationNotification(
                        secretId,
                        emailList,
                        {
                          project: projectName,
                          namespace: vaultNamespace,
                          path: vaultPath,
                          vaultSecretKey: devicePasswordKey,
                        },
                        ROTATION_STATUS.FAILED,
                      );
                    })
                    .catch((error) => {
                      this.logger.error('Error while fetching project.');
                      this.logger.error(error);
                    });
                })
                .catch((error) => {
                  this.logger.error('Error while fetching resources.');
                  this.logger.error(error);
                });
            });
        }),
      );

      this.logger.log(
        'Deactivating the secretIds and updating the respective actionId for secrets',
      );
      const userId = getUserContext(RequestContext)?.userId;

      await this.secretsMetadataRepository.changeActiveStatusByIds(
        successSecretIds.map((secretId) => ({
          actionId: actions.find((action) => action.entityId === secretId)
            .actionId,
          secretId,
          rotationStatus: ActionStatus.STARTED,
          updatedBy: userId,
        })),
      );
      this.logger.log(
        'SecretIds are successfully deactivated and respective actions are updated',
      );

      await Promise.all([
        ...successActionIds.map((action) =>
          this.actionService.update({
            actionId: action.actionId,
            status: ActionStatus.REQUEST_RAISED,
          }),
        ),
        ...failureActionIds.map((action) =>
          this.actionService.update({
            actionId: action.actionId,
            status: ActionStatus.FAILED,
          }),
        ),
      ]);
      return { message: 'Secret rotation request raised successfully.' };
    } catch (error) {
      this.logger.error('error while rotating secret.');
      this.logger.error(error);
      await Promise.all(
        actions.map((action) =>
          this.actionService.update({
            actionId: action.actionId,
            status: ActionStatus.FAILED,
          }),
        ),
      );
      throw error;
    }
  }

  async createNameSpaceRequest(
    createNameSpaceRequestDto: CreateNameSpaceRequestDto,
  ): Promise<CreateNameSpaceResponse> {
    this.logger.log('Vault Request', createNameSpaceRequestDto);
    const existedNamespace = await this.fetchAuthorizedNamespaces();
    const requestedNamespaceName = createNameSpaceRequestDto.namespaceName;

    const isDuplicate = existedNamespace.items.some(
      (item) => item.namespaceName === requestedNamespaceName,
    );

    if (isDuplicate) {
      throw new BadRequestException(
        `Namespace with name "${requestedNamespaceName}" already exists.`,
      );
    }

    const metadata = {
      serviceCatalog: {
        catalogName: ServiceCatalogName.CREATE_NAMESPACE,
        catalogType: ServiceCatalogType.IAAS,
      },
    };

    const serviceRequest = {
      metadata,
      payload: {
        ...createNameSpaceRequestDto,
      },
      requestType: RequestType.CREATE_NAMESPACE,
      status: RequestStatus.PENDING_APPROVAL,
    };

    this.logger.debug('serviceRequest: ', { serviceRequest });
    const dbResponse = await this.assetsService.create(serviceRequest);
    this.logger.debug('Response from DB ', dbResponse);
    const queued =
      await this.assetsService.queueIfApprovalNotRequired(dbResponse);
    this.logger.log(
      `Request submitted for ${queued ? 'processing' : 'approval'}`,
    );
    return {
      id: dbResponse.id,
      serviceRequestId: dbResponse.serviceRequestId,
      message: `Request submitted for ${queued ? 'processing' : 'approval'}`,
    };
  }

  async createVaultSecrets(
    namespaceName: string,
    createVaultSecretsRequestDto: CreateVaultSecretsRequestDto,
    path?: string,
  ) {
    const {
      type,
      vaultKey,
      vaultPassword,
      userNameKey,
      userNamePassword,
      rotationType,
      secretTTLInHours,
      description,
      policyId,
      nextRotationDate,
      expiryDate,
      tokenRenewByNebula,
      notifyBeforeTokenExpiry,
    } = createVaultSecretsRequestDto;

    if (policyId) {
      const policy = await this.secretsPoliciesRepository.getPolicy(policyId);
      if (!policy) {
        throw new NotFoundException(
          `Unable to find secret policy for input policyId`,
        );
      }
    }

    let secretData = {
      [vaultKey]: vaultPassword,
      [userNameKey]: userNamePassword,
    };

    let vaultTokenSecretId;

    const resources =
      await this.resourcesRepository.getResourceByResourcesName(namespaceName);

    if (createVaultSecretsRequestDto.type !== FetchSecretType.VAULT_TOKEN) {
      const response = await withResponseErrorHandler(
        this.secretsManagmentServiceApi.get('secrets', {
          params: {
            path,
            namespace: namespaceName,
          },
        }),
      );
      if (response?.data?.data.hasOwnProperty(vaultKey)) {
        throw new BadRequestException(
          'Secrets key is already present for the specific path.',
        );
      }
      if (response?.data?.data) {
        secretData = {
          ...response?.data?.data,
          ...secretData,
        };
      }
    } else if (type === FetchSecretType.VAULT_TOKEN) {
      vaultTokenSecretId =
        await this.secretsMetadataRepository.generateSecretId(
          SecretType.VAULT_TOKEN,
        );

      path =
        this.configService.get(ENVIRONMENT_VARS.MASTER_TOKEN_FOLDER) +
        '/' +
        vaultTokenSecretId;
    }

    if (type === FetchSecretType.VAULT_TOKEN) {
      const action = await this.actionService.create({
        actionTypeId: ActionType.CREATE_NEW_SECRET,
        entityId: vaultTokenSecretId,
        entityType: COLLECTIONS.SECRETS_METADATA,
        status: ActionStatus.STARTED,
      });

      const createVaultTokenData: CreateSecretMetaDataDto = {
        secretId: vaultTokenSecretId,
        resourceId: resources?.resourceId,
        status: Status.ACTIVE,
        active: true,
        description,
        type: SecretType.VAULT_TOKEN,
        vaultPath: '',
        expiryDate: expiryDate as unknown as string,
        devicePasswordKey: vaultKey,
        tokenRenewByNebula,
        actionId: action.actionId,
      };
      try {
        await this.secretsMetadataRepository.create(createVaultTokenData);
        await withResponseErrorHandler(
          this.secretsManagmentServiceApi.post('secrets', secretData, {
            params: {
              path,
              namespace:
                createVaultSecretsRequestDto.type ===
                FetchSecretType.VAULT_TOKEN
                  ? this.configService.get(
                      ENVIRONMENT_VARS.VAULT_MASTER_NAMESPACE,
                    )
                  : namespaceName,
            },
          }),
        );
        await this.actionService.update({
          actionId: action.actionId,
          status: ActionStatus.COMPLETED,
        });
      } catch (e) {
        this.logger.error(
          e,
          `Error while creating vault token: ${vaultTokenSecretId}`,
        );
        await this.actionService.update({
          actionId: action.actionId,
          status: ActionStatus.FAILED,
        });
      }
    }
    if (type === FetchSecretType.ROTATABLE_SECRET) {
      const rotatableSecrets =
        await this.secretsMetadataRepository.getSecretsMetaData({
          namespace: decodeURIComponent(namespaceName),
          path,
          type: SecretType.ROTATABLE_SECRET,
        });
      if (rotatableSecrets.length) {
        throw new BadRequestException(
          `Rotatable Secret is already present for the namespace: ${decodeURIComponent(namespaceName)} and path: ${decodeURIComponent(path)}`,
        );
      }
      const normalSecretId =
        await this.secretsMetadataRepository.generateSecretId(
          SecretType.NORMAL_SECRET,
        );
      const rotatableSecretId =
        await this.secretsMetadataRepository.generateSecretId(
          SecretType.ROTATABLE_SECRET,
        );

      const [rotateAction, normalAction] = await Promise.all([
        this.actionService.create({
          actionTypeId: ActionType.CREATE_NEW_SECRET,
          entityId: rotatableSecretId,
          entityType: COLLECTIONS.SECRETS_METADATA,
          status: ActionStatus.STARTED,
        }),
        this.actionService.create({
          actionTypeId: ActionType.CREATE_NEW_SECRET,
          entityId: normalSecretId,
          entityType: COLLECTIONS.SECRETS_METADATA,
          status: ActionStatus.STARTED,
        }),
      ]);
      try {
        const secretCreated = await withResponseErrorHandler(
          this.secretsManagmentServiceApi.post('secrets', secretData, {
            params: {
              path,
              namespace:
                createVaultSecretsRequestDto.type ===
                FetchSecretType.VAULT_TOKEN
                  ? this.configService.get(
                      ENVIRONMENT_VARS.VAULT_MASTER_NAMESPACE,
                    )
                  : namespaceName,
            },
          }),
        );
        const createNormalSecretData: CreateSecretMetaDataDto = {
          secretId: normalSecretId,
          resourceId: resources?.resourceId,
          status: Status.SUCCESS,
          active: true,
          description,
          type: SecretType.NORMAL_SECRET,
          vaultPath: decodeURIComponent(path),
          devicePasswordKey: userNameKey,
          vaultNamespace: namespaceName,
          actionId: normalAction.actionId,
          version: secretCreated?.data?.version,
        };

        const createRotatableSecretData: CreateSecretMetaDataDto = {
          secretId: rotatableSecretId,
          resourceId: resources?.resourceId,
          status: Status.SUCCESS,
          secretTTLInHours,
          active: true,
          description,
          policyId,
          type: SecretType.ROTATABLE_SECRET,
          rotationType,
          nextRotationDate,
          expiryDate:
            (expiryDate as unknown as string) || (nextRotationDate as string),
          vaultPath: decodeURIComponent(path),
          devicePasswordKey: vaultKey,
          deviceUserNameSecretId: normalSecretId,
          vaultNamespace: namespaceName,
          actionId: rotateAction.actionId,
          version: secretCreated?.data?.version,
          notifyBeforeTokenExpiry,
        };

        await this.secretsMetadataRepository.create(createNormalSecretData);
        await this.secretsMetadataRepository.create(createRotatableSecretData);
        await Promise.all([
          this.actionService.update({
            actionId: rotateAction.actionId,
            status: ActionStatus.COMPLETED,
          }),
          this.actionService.update({
            actionId: normalAction.actionId,
            status: ActionStatus.COMPLETED,
          }),
        ]);
      } catch (error) {
        this.logger.error(
          error,
          `Error while creating Rotatable SecretId: ${rotatableSecretId}`,
        );
        await Promise.all([
          this.actionService.update({
            actionId: rotateAction.actionId,
            status: ActionStatus.FAILED,
          }),
          this.actionService.update({
            actionId: normalAction.actionId,
            status: ActionStatus.FAILED,
          }),
        ]);
      }
    }
    if (type === FetchSecretType.NORMAL_SECRET) {
      const normalSecretId =
        await this.secretsMetadataRepository.generateSecretId(
          SecretType.NORMAL_SECRET,
        );

      const action = await this.actionService.create({
        actionTypeId: ActionType.CREATE_NEW_SECRET,
        entityId: normalSecretId,
        entityType: COLLECTIONS.SECRETS_METADATA,
        status: ActionStatus.STARTED,
      });
      try {
        const secretCreated = await withResponseErrorHandler(
          this.secretsManagmentServiceApi.post('secrets', secretData, {
            params: {
              path,
              namespace:
                createVaultSecretsRequestDto.type ===
                FetchSecretType.VAULT_TOKEN
                  ? this.configService.get(
                      ENVIRONMENT_VARS.VAULT_MASTER_NAMESPACE,
                    )
                  : namespaceName,
            },
          }),
        );
        const createNormalSecretData: CreateSecretMetaDataDto = {
          secretId: normalSecretId,
          resourceId: resources?.resourceId,
          status: Status.SUCCESS,
          active: true,
          description,
          policyId,
          type: SecretType.NORMAL_SECRET,
          vaultPath: decodeURIComponent(path),
          devicePasswordKey: vaultKey,
          vaultNamespace: namespaceName,
          actionId: action.actionId,
          version: secretCreated?.data?.version,
        };
        this.logger.debug('trying to create normal secret');
        await this.secretsMetadataRepository.create(createNormalSecretData);
        await this.actionService.update({
          actionId: action.actionId,
          status: ActionStatus.COMPLETED,
        });
      } catch (error) {
        this.logger.error(
          error,
          `Error while creating Normal SecretId: ${normalSecretId}`,
        );
        this.logger.debug(`Error occured`);
        await this.actionService.update({
          actionId: action.actionId,
          status: ActionStatus.FAILED,
        });
      }
    }
    this.logger.log('Secret is created successfully');
    return { message: 'Secret is created successfully.' };
  }

  async updateVaultSecretByPath(
    secretId: string,
    updateVaultSecretRequest: UpdateVaultSecretRequestDto,
  ): Promise<{ message: string }> {
    const secretMetaData =
      await this.secretsMetadataRepository.getSecretsMetaDataBySecretId(
        secretId,
      );
    if (!secretMetaData) {
      throw new NotFoundException(`Secret with id ${secretId} not found.`);
    }

    if (!secretMetaData.active) {
      throw new BadRequestException(
        `secretId ${secretId} cannot be updated because it is inactive.`,
      );
    }

    const action = await this.actionService.create({
      actionTypeId: ActionType.UPDATE_SECRET,
      entityId: secretMetaData.secretId,
      entityType: COLLECTIONS.SECRETS_METADATA,
      status: ActionStatus.STARTED,
    });
    try {
      let response: {
        data: {
          metadata: any;
          data: Secrets;
        };
      } = await withResponseErrorHandler(
        this.secretsManagmentServiceApi.get('secrets', {
          params: {
            path: secretMetaData.vaultPath,
            namespace: secretMetaData.vaultNamespace,
          },
        }),
      );
      const secretResponse = response?.data?.data;
      const currentVersion = response?.data?.metadata?.version;
      const userId = getUserContext(RequestContext)?.userId;
      const { updateMetaData, secretData } =
        await this.secretsMetadataRepository.createQueryForUpdateMetaData({
          secretMetaData,
          secretResponse,
          updateVaultSecretRequest,
          secretId,
          actionId: action.actionId,
          currentVersion,
          userId,
        });
      if (Object.keys(secretData).length !== 0) {
        await withResponseErrorHandler(
          this.secretsManagmentServiceApi.post('secrets', secretData, {
            params: {
              path: secretMetaData.vaultPath,
              namespace: secretMetaData.vaultNamespace,
            },
          }),
        );
      }
      await this.secretsMetadataRepository.bulkUpdateSecretsMetaDataBySecretId(
        updateMetaData,
      );
      this.logger.log(
        `Secret with id ${secretId} has been updated successfully.`,
      );
      await this.actionService.update({
        actionId: action.actionId,
        status: ActionStatus.COMPLETED,
      });
      return { message: 'Secret is updated successfully.' };
    } catch (e) {
      this.logger.error(e, `Error while updating SecretId: ${secretId}`);
      await this.actionService.update({
        actionId: action.actionId,
        status: ActionStatus.FAILED,
      });
      throw new InternalServerErrorException(e);
    }
  }

  async generatePassword(namespace: string, name: string) {
    const params = namespace ? { params: { namespace } } : {};
    return withResponseErrorHandler(
      this.secretsManagmentServiceApi.get(
        `vault/${name}/password/generate`,
        params,
      ),
    );
  }

  async encryptPassword(password: string): Promise<string> {
    const encryptedVaultPassword = this.encryptionService.encrypt(password);
    return encryptedVaultPassword;
  }

  async decryptPassword(encryptedText: string): Promise<string> {
    const decryptedVaultPassword =
      this.encryptionService.decrypt(encryptedText);
    return decryptedVaultPassword;
  }

  async fetchSecretsDetails(secretId: string) {
    const secretMetaData =
      await this.secretsMetadataRepository.getSecretsMetaDataBySecretId(
        secretId,
      );
    await this.resourcesRepository.findOneByResourceId(
      secretMetaData?.resourceId,
    );
    const params = {
      path: secretMetaData.vaultPath,
      namespace: secretMetaData.vaultNamespace,
    };
    const response = await withResponseErrorHandler(
      this.secretsManagmentServiceApi.get('secrets', { params }),
    );

    const policiesData = await this.secretsPoliciesRepository.getPolicy(
      secretMetaData.policyId,
    );
    const secretsData: {
      vaultKey: string;
      vaultPassword: string;
      userNameKey?: string;
      userNamePassword?: string;
      resourceId: string;
      policyId?: string;
      policyName?: string;
      rotationType?: string;
      expiryDate?: Date;
      notificationEnabled?: boolean;
      tokenTTLInHours?: number;
      secretTTLInHours?: number;
      nextRotationDate?: Date;
      namespace: string;
      vaultPath: string;
      type: string;
      lastDeviceSyncStatus?: string;
      active?: boolean;
      notifyBeforeTokenExpiry?: boolean;
    } = {
      vaultKey: '',
      vaultPassword: '',
      resourceId: secretMetaData.resourceId,
      namespace: secretMetaData.vaultNamespace,
      vaultPath: secretMetaData.vaultPath,
      type:
        SecretType.ROTATABLE_SECRET === secretMetaData.type
          ? FetchSecretType.ROTATABLE_SECRET
          : FetchSecretType.NORMAL_SECRET,
    };

    if (secretMetaData.type === SecretType.ROTATABLE_SECRET) {
      const userNameSecretMetaData =
        await this.secretsMetadataRepository.getSecretsMetaDataBySecretId(
          secretMetaData.deviceUserNameSecretId,
        );

      secretsData.policyId = secretMetaData.policyId;
      secretsData.policyName = policiesData?.policyName;
      secretsData.rotationType = secretMetaData.rotationType;
      secretsData.expiryDate = secretMetaData.expiryDate;
      secretsData.notificationEnabled = secretMetaData.notificationEnabled;
      secretsData.tokenTTLInHours = secretMetaData.tokenTTLInHours;
      secretsData.secretTTLInHours = secretMetaData.secretTTLInHours;
      secretsData.nextRotationDate = secretMetaData.nextRotationDate;
      secretsData.lastDeviceSyncStatus = secretMetaData.lastDeviceSyncStatus;
      secretsData.active = secretMetaData.active;
      secretsData.notifyBeforeTokenExpiry =
        secretMetaData.notifyBeforeTokenExpiry;
      Object.entries(response?.data?.data).forEach(
        ([key, value]: [string, string]) => {
          if (key === secretMetaData.devicePasswordKey) {
            secretsData.vaultKey = key;
            secretsData.vaultPassword = value;
          }
          if (key === userNameSecretMetaData.devicePasswordKey) {
            secretsData.userNameKey = key;
            secretsData.userNamePassword = value;
          }
        },
      );
    } else {
      secretsData.policyId = secretMetaData.policyId;
      secretsData.active = secretMetaData.active;
      Object.entries(response?.data?.data).forEach(
        ([key, value]: [string, string]) => {
          if (key === secretMetaData.devicePasswordKey) {
            secretsData.vaultKey = key;
            secretsData.vaultPassword = value;
            secretsData.policyId = secretMetaData?.policyId;
            secretsData.policyName = policiesData?.policyName;
          }
        },
      );
    }

    return secretsData;
  }
  async fetchAuthorizedNamespaces(envId?: string) {
    let filter = {
      requestType: { equals: 'CREATE_NAMESPACE' },
    };

    if (envId) {
      filter['platformContext.envId'] = { equals: envId };
    }

    const resources = await this.resourcesService.getPaginatedResourcesList({
      filter: JSON.stringify(filter),
    });

    const items = resources.items.map((item) => ({
      namespaceName: item.resourcesName,
      resourceId: item.resourceId,
      status: item.status,
      catalogType: item.catalogType,
      catalogLevel03: item.catalogLevel03,
      platformContext: item.platformContext,
      requestType: item.requestType,
      resourcesDetails: item.resourcesDetails,
    }));

    return { items };
  }

  private async isPasswordUnique(payload: {
    devicePasswordKey: string;
    newPassword: string;
    namespace: string;
    vaultPath: string;
  }): Promise<Boolean> {
    const {
      namespace,
      vaultPath,
      devicePasswordKey: passwordKey,
      newPassword,
    } = payload;

    const response = await withResponseErrorHandler(
      this.secretsManagmentServiceApi.get('secrets', {
        params: {
          path: vaultPath,
          namespace,
        },
      }),
    );

    if (!response?.data) {
      throw new NotFoundException('Secrets not found.');
    }

    let {
      data: {
        metadata: { version },
      },
    } = response;
    let versionLimit = version > 10 ? version - 10 : 0;
    while (version > versionLimit) {
      const response = await withResponseErrorHandler(
        this.secretsManagmentServiceApi.get('secrets', {
          params: {
            path: vaultPath,
            namespace,
            version,
          },
        }),
      );

      const { data: data } = response;
      version -= 1;
      if (data[passwordKey] === newPassword) return false;
    }
    return true;
  }

  async generateUniquePassword(ids: string): Promise<Object[]> {
    let secretIds = ids.split(',').map((id) => id.trim());
    let secretMetaDataList: SecretsMetaData[] =
      await this.secretsMetadataService.getSecretsMetaDataBySecretId(secretIds);
    if (!secretMetaDataList) {
      throw new NotFoundException('Secrets not found for the provided IDs.');
    }
    const policyIds = [
      ...new Set(
        secretMetaDataList.map((secretMetaData) => secretMetaData.policyId),
      ),
    ];
    let secretsPoliciesList: SecretsPolicies[] =
      await this.secretsPoliciesService.getPolicy(policyIds);
    if (!secretsPoliciesList) {
      throw new NotFoundException(
        'Secrets policies not found for the provided IDs.',
      );
    }
    const policiesMap = secretsPoliciesList.reduce((acc, secretsPolicy) => {
      const policyId = secretsPolicy.policyId;
      acc[policyId] = secretsPolicy.policyName;
      return acc;
    }, {});
    let newPasswordList = [];
    for (const secretMetaData of secretMetaDataList) {
      const {
        vaultNamespace: namespace,
        policyId,
        devicePasswordKey,
        vaultPath,
        secretId,
      } = secretMetaData;
      const policyName = policiesMap[policyId];

      while (true) {
        const {
          data: { password: newPassword },
        } = await withResponseErrorHandler(
          this.secretsManagmentServiceApi.get(
            `vault/${policyName}/password/generate?namespace=${encodeURIComponent(namespace)}`,
          ),
        );

        if (
          await this.isPasswordUnique({
            devicePasswordKey,
            newPassword,
            namespace,
            vaultPath,
          })
        ) {
          newPasswordList.push({
            secretId,
            [devicePasswordKey]: newPassword,
          });
          break;
        }
      }
    }
    return newPasswordList;
  }
  async deleteSecretsByIds(ids: string) {
    try {
      let secretsIds = ids.split(',').map((si) => si.trim());
      let secretsMetaDataList: SecretsMetaData[] =
        await this.secretsMetadataService.getSecretsMetaDataBySecretId(
          secretsIds,
        );

      if (!secretsMetaDataList) {
        throw new NotFoundException('Secrets not found for the provided IDs.');
      }

      var actions = await Promise.all(
        secretsMetaDataList.map((secretMetadata) =>
          this.actionService.create({
            actionTypeId: ActionType.DELETE_SECRET,
            entityId: secretMetadata.secretId,
            entityType: COLLECTIONS.SECRETS_METADATA,
            status: ActionStatus.STARTED,
          }),
        ),
      );

      const { deviceUserNameSecretIds, secretIdsRef } =
        secretsMetaDataList.reduce(
          (acc, sm) => {
            if (sm.type === SecretType.ROTATABLE_SECRET) {
              if (!acc?.deviceUserNameSecretIds && !acc?.secretIdsRef) {
                acc.deviceUserNameSecretIds = [];
                acc.secretIdsRef = [];
              }
              acc.deviceUserNameSecretIds.push(sm.deviceUserNameSecretId);
              acc.secretIdsRef.push(sm.secretId);
            }
            return acc;
          },
          { deviceUserNameSecretIds: [], secretIdsRef: [] },
        );

      if (deviceUserNameSecretIds.length) {
        secretsMetaDataList = [
          ...secretsMetaDataList,
          ...(await this.secretsMetadataService.getSecretsMetaDataBySecretId(
            deviceUserNameSecretIds,
          )),
        ];
        const deviceActions = await Promise.all(
          deviceUserNameSecretIds.map((deviceUserNameSecretId) =>
            this.actionService.create({
              actionTypeId: ActionType.DELETE_SECRET,
              entityId: deviceUserNameSecretId,
              entityType: COLLECTIONS.SECRETS_METADATA,
              status: ActionStatus.STARTED,
            }),
          ),
        );
        actions.push(...deviceActions);
        secretsIds = [...new Set([...secretsIds, ...deviceUserNameSecretIds])];
        const deactivateSecretDevicesOps = secretIdsRef.map((id) => ({
          updateOne: {
            filter: { secretId: id },
            update: {
              $set: {
                active: false,
                actionId: actions.find((action) => action.entityId === id)
                  .actionId,
              },
            },
          },
        }));
        await this.secretDevicesService.bulkUpdate(deactivateSecretDevicesOps);
        const secretDeviceAssociationData =
          await this.secretDevicesService.findBySecretIds(secretIdsRef);
        if (secretDeviceAssociationData.length) {
          const notificationPayload = secretDeviceAssociationData.map(
            (deviceAssocitaion) => ({
              deviceId: deviceAssocitaion.deviceId[0],
              secretId: deviceAssocitaion.secretId[0],
            }),
          );
          await this.secretDevicesService.secretDeviceAssociationNotification(
            notificationPayload,
            RequestType.REMOVE_DEVICE,
          );
        }
      }
      const deactivateSecretMetadataOps = secretsIds.map((id) => ({
        updateOne: {
          filter: { secretId: id },
          update: {
            $set: {
              active: false,
              isDeleted: true,
              actionId: actions.find((action) => action.entityId === id)
                .actionId,
            },
          },
          upsert: true,
        },
      }));
      await this.secretsMetadataService.bulkUpdateSecretsMetaDataBySecretId(
        deactivateSecretMetadataOps,
      );

      const secretsGroupedByNameSpaceAndPath = secretsMetaDataList.reduce(
        (acc, sm) => {
          const { vaultNamespace, vaultPath, devicePasswordKey } = sm;
          const uniqueGroupName = `${vaultNamespace}_${vaultPath}`;
          if (!acc.hasOwnProperty(uniqueGroupName)) {
            acc[uniqueGroupName] = [];
          }
          acc[uniqueGroupName].push(devicePasswordKey);
          return acc;
        },
        {},
      );
      for (const uniqueGroup in secretsGroupedByNameSpaceAndPath) {
        if (secretsGroupedByNameSpaceAndPath[uniqueGroup].length) {
          const [vaultNamespace, vaultPath] = uniqueGroup
            .split('_')
            .map((ug) => ug.trim());
          const params = {
            namespace: vaultNamespace,
            path: vaultPath,
          };
          const response = await withResponseErrorHandler(
            this.secretsManagmentServiceApi.get('secrets', { params }),
          );

          if (!response?.data?.data) {
            throw new NotFoundException('Secrets not found.');
          }
          const currSecretsObject = response?.data?.data;
          const updatedSecretsObject = { ...currSecretsObject };
          for (const secret of secretsGroupedByNameSpaceAndPath[uniqueGroup]) {
            delete updatedSecretsObject[secret];
          }

          await withResponseErrorHandler(
            this.secretsManagmentServiceApi.post(
              'secrets',
              updatedSecretsObject,
              {
                params,
              },
            ),
          );
        }
      }

      await Promise.all(
        actions.map((action) =>
          this.actionService.update({
            actionId: action.actionId,
            status: ActionStatus.COMPLETED,
          }),
        ),
      );

      return {
        status: HttpStatus.OK,
        message: 'Secrets deleted successfully.',
      };
    } catch (error) {
      if(actions){
      await Promise.all(
        actions.map((action) =>
          this.actionService.update({
            actionId: action.actionId,
            status: ActionStatus.FAILED,
          }),
        ),
      );
    }
      throw error;
    }
  }

  async secretsRotationResult(
    secretRotationUpdateRequest: SecretRotationUpdateRequestDto,
  ) {
    try {
      const secretMetaData =
        await this.secretsMetadataRepository.getSecretsMetaDataBySecretId(
          secretRotationUpdateRequest.nebulaSecretId,
        );
      if (!secretMetaData) {
        throw new NotFoundException(
          `Secret with id ${secretRotationUpdateRequest.nebulaSecretId} not found.`,
        );
      }
      const {
        vaultNamespace,
        vaultPath,
        secretId,
        type,
        devicePasswordKey,
        resourceId,
      } = secretMetaData;

      const params = {
        path: `${this.configService.get(ENVIRONMENT_VARS.ADHOC_LOCATION)}/${secretRotationUpdateRequest.nebulaSecretId}`,
        namespace: this.configService.get(ENVIRONMENT_VARS.ADHOC_NAMESPACE),
      };
      const response = await withResponseErrorHandler(
        this.secretsManagmentServiceApi.get('secrets', { params }),
      );
      const newSecretData =
        response?.data?.data?.[secretRotationUpdateRequest.nebulaSecretId] ||
        null;
      if (!response?.data?.data) {
        throw new NotFoundException('Secrets not found.');
      }

      let updatedSecretData;
      const updateFields: {
        active: boolean;
        completedAt: string;
        error: ErrorDto | null;
        version?: number;
        expiryDate?: Date;
        nextRotationDate?: Date;
        lastDeviceSyncStatus: ActionStatus;
      } = {
        active: true,
        completedAt: secretRotationUpdateRequest.completedAt,
        error:
          secretRotationUpdateRequest.status !== ROTATION_STATUS.COMPLETED
            ? secretRotationUpdateRequest.error
            : null,
        lastDeviceSyncStatus:
          secretRotationUpdateRequest.status === ROTATION_STATUS.COMPLETED
            ? ActionStatus.COMPLETED
            : ActionStatus.FAILED,
      };
      if (secretRotationUpdateRequest.status === ROTATION_STATUS.COMPLETED) {
        const secretResponse: {
          data: { data: Secrets };
        } = await withResponseErrorHandler(
          this.secretsManagmentServiceApi.get('secrets', {
            params: {
              path: vaultPath,
              namespace: vaultNamespace,
            },
          }),
        );
        const updateSecretData = {
          ...secretResponse?.data?.data,
          [secretMetaData.devicePasswordKey]: newSecretData,
        };

        updatedSecretData = await withResponseErrorHandler(
          this.secretsManagmentServiceApi.post('secrets', updateSecretData, {
            params: {
              path: vaultPath,
              namespace: vaultNamespace,
            },
          }),
        );

        updateFields.version = updatedSecretData?.data?.version;

        updateFields.expiryDate = new Date(
          Date.now() + secretMetaData.secretTTLInHours * 60 * 60 * 1000,
        );
        updateFields.nextRotationDate = updateFields.expiryDate;
      } else {
        updateFields.expiryDate = new Date(
          Date.now() +
            this.configService.get(
              ENVIRONMENT_VARS.DELAY_FAILED_SECRET_ROTATION_HOURS,
            ) *
              60 *
              60 *
              1000,
        );
        updateFields.nextRotationDate = updateFields.expiryDate;
        this.logger.log(
          `expiryDate is updated to next day for secretId: ${secretRotationUpdateRequest.nebulaSecretId}`,
        );
      }

      await this.cleanupTemporarySecrets(
        secretRotationUpdateRequest.nebulaSecretId,
      );

      await this.secretsMetadataRepository.updateSecretsMetadataById(
        secretRotationUpdateRequest.nebulaSecretId,
        updateFields,
      );

      if (secretRotationUpdateRequest.nebulaActionId) {
        await this.actionService.update({
          actionId: secretRotationUpdateRequest.nebulaActionId,
          status:
            secretRotationUpdateRequest.status === ROTATION_STATUS.COMPLETED
              ? ActionStatus.COMPLETED
              : ActionStatus.FAILED,
        });
      }

      this.resourcesService
        .getResourceByResourceId(resourceId)
        .then((resource) => {
          this.multiEnvIAMService
            .getProjectAppEnvByEnvId(resource.platformContext.envId)
            .then(async (project) => {
              const { emailDistribution: emailList, projectName } = project;
              const { error } = updateFields;
              await this.secretRoatationNotification(
                secretId,
                emailList,
                {
                  project: projectName,
                  namespace: vaultNamespace,
                  path: vaultPath,
                  vaultSecretKey: devicePasswordKey,
                },
                secretRotationUpdateRequest.status,
                error != null && error.message,
              );
              return;
            })
            .catch((error) => {
              this.logger.error('Error in fetching project.');
              this.logger.error(error);
              throw error;
            });
        })
        .catch((error) => {
          this.logger.error('Error while fetching resources');
          this.logger.error(error);
          throw error;
        });

      return {
        status: HttpStatus.OK,
        message: 'Secrets Updated.',
      };
    } catch (error) {
      this.logger.error(
        `Error in updating rotating result: ${secretRotationUpdateRequest.nebulaSecretId}`,
      );
      this.logger.error(error);
      if (secretRotationUpdateRequest.nebulaActionId) {
        await this.actionService.update({
          actionId: secretRotationUpdateRequest.nebulaActionId,
          status: ActionStatus.FAILED,
        });
      }

      const secretMetaData =
        await this.secretsMetadataRepository.getSecretsMetaDataBySecretId(
          secretRotationUpdateRequest.nebulaSecretId,
        );
      if (!secretMetaData) {
        throw new NotFoundException(
          `Secret with id ${secretRotationUpdateRequest.nebulaSecretId} not found.`,
        );
      }

      const {
        secretId,
        type,
        vaultNamespace,
        vaultPath,
        devicePasswordKey,
        resourceId,
      } = secretMetaData;

      this.resourcesService
        .getResourceByResourceId(resourceId)
        .then((resource) => {
          this.multiEnvIAMService
            .getProjectAppEnvByEnvId(resource.platformContext.envId)
            .then(async (project) => {
              const { emailDistribution: emailList, projectName } = project;
              await this.secretRoatationNotification(
                secretId,
                emailList,
                {
                  project: projectName,
                  namespace: vaultNamespace,
                  path: vaultPath,
                  vaultSecretKey: devicePasswordKey,
                },
                ROTATION_STATUS.FAILED,
                secretRotationUpdateRequest?.error?.message != ''
                  ? secretRotationUpdateRequest?.error?.message
                  : null,
              );
            })
            .catch((error) => {
              this.logger.error('Error in fetching project');
              this.logger.error(error);
            });
        })
        .catch((error) => {
          this.logger.error('Error while fetching resources');
          this.logger.error(error);
        });
      throw error;
    }
  }

  async cleanupTemporarySecrets(secretId: string) {
    return await withResponseErrorHandler(
      this.secretsManagmentServiceApi.delete('metadata', {
        params: {
          path: `${this.configService.get(ENVIRONMENT_VARS.ADHOC_LOCATION)}/${secretId}`,
          namespace: this.configService.get(ENVIRONMENT_VARS.ADHOC_NAMESPACE),
        },
      }),
    );
  }

  async fetchNamespaceResources() {
    return await this.resourcesService.getNamespaceResources();
  }

  async diffingOperationOnHistory(secretsHistory: []) {
    let diffArray = [];
    const nonComparedKeys = ['lastUpdatedBy', 'lastUpdatedAt', 'action'];
    const comparator = (curr: any, prev: any) => {
      const newValue: Record<string, any> = {};
      const originalValue: Record<string, any> = {};

      if (!prev) {
        return {
          action: curr.action,
          lastUpdatedAt: curr.lastUpdatedAt,
          lastUpdatedBy: curr.lastUpdatedBy,
          newValue: _.omit(curr, nonComparedKeys),
          originalValue: null,
        };
      }

      for (const key of Object.keys(curr)) {
        if (nonComparedKeys.includes(key)) continue;
        if (!_.isEqual(curr[key], prev[key])) {
          newValue[key] = curr[key];
          originalValue[key] = prev[key];
        }
      }

      if (Object.keys(newValue).length === 0) return null;

      return {
        action: curr.action,
        lastUpdatedBy: curr.lastUpdatedBy,
        lastUpdatedAt: curr.lastUpdatedAt,
        newValue,
        originalValue,
      };
    };

    for (let idx = 0; idx < secretsHistory.length; idx++) {
      let currObj = secretsHistory[idx];
      let prevObj = secretsHistory[idx + 1] || null;
      const diffObj = comparator(currObj, prevObj);
      diffArray.push(diffObj as {});
    }
    return diffArray.filter(Boolean);
  }

  public validateSecretId(secretId: string) {
    const pattern = /^NEB-VAULT-(ROT|NOR|TOKEN)-SECRET-\d+$/;
    if (!pattern.test(secretId))
      throw new BadRequestException('SecretId is not valid.');
  }

  async fetchSecretsHistory(
    secretId: string,
    query: { page: number; paginate: boolean; limit: number; sortBy: string },
  ) {
    this.validateSecretId(secretId);
    let { page, limit, sortBy = 'createdAt', paginate } = query;
    if (!page || typeof page !== 'number') page = 1;
    if (!limit || typeof limit !== 'number') limit = 5;

    let queryString = paginate
      ? `?page=${page}&limit=${limit}&sortBy=${sortBy}&paginate=${paginate}`
      : `?page=1&limit=${Infinity}&sortBy=${sortBy}&paginate=${paginate}`;

    let secretsHistory = await withResponseErrorHandler(
      this.watcherServiceApi.get(
        `v1/history/entityId/${secretId}${queryString}`,
      ),
    );

    if (!secretsHistory?.length) {
      return secretsHistory;
    }
    let currentSecretMetadata;
    if (Number(page) === 1) {
      currentSecretMetadata =
        await this.secretsMetadataService.getSecretsMetaDataBySecretId(
          secretId,
        );
    }
    let versions = secretsHistory.map((history) => history.document.version);
    versions = currentSecretMetadata
      ? [...new Set([...versions, currentSecretMetadata.version])]
      : [...new Set(versions)];
    const vaultSecrets = new Map();
    let secretTrailingSpaces = [];
    let loop = 0;
    const latestVersion = Math.max(...versions);
    const maxVersion = latestVersion - 10;
    for (const version of versions) {
      if (loop % 2 !== 0) secretTrailingSpaces = [];
      loop++;
      const path = secretsHistory[0].document.vaultPath;
      const namespace = secretsHistory[0].document.vaultNamespace;
      const vaultKey = secretsHistory[0].document.devicePasswordKey;

      let secrets;
      if (version > maxVersion) {
        secrets = (
          await withResponseErrorHandler(
            this.secretsManagmentServiceApi.get(
              `secrets?path=${path}&namespace=${namespace}&version=${version}`,
            ),
          )
        )?.data?.data;
      }
      let secret;
      if (secrets) {
        secret = { key: vaultKey, value: secrets[vaultKey] };
      } else {
        const secretNotAvailableText = await this.encryptPassword(
          this.configService.get<string>(
            ENVIRONMENT_VARS.SECRET_UNAVAILABLE_MESSAGE,
            'Secret history not available',
          ) + secretTrailingSpaces.join(''),
        );
        secret = {
          key: vaultKey,
          value: secretNotAvailableText,
        };
        secretTrailingSpaces.push(' ');
      }
      vaultSecrets.set(version, secret);
    }

    if (currentSecretMetadata) {
      const actions = await this.actionService.findAllActionByIds([
        currentSecretMetadata.actionId,
      ]);

      currentSecretMetadata = {
        document: currentSecretMetadata,
        action: actions[0],
      };
      delete currentSecretMetadata.actionEnum;
    }

    for (const history of secretsHistory) {
      history.vaultSecret = vaultSecrets.get(history.document.version);
    }
    if (currentSecretMetadata) {
      secretsHistory.unshift({
        ...currentSecretMetadata,
        vaultSecret: vaultSecrets.get(currentSecretMetadata.document.version),
      });
    }
    let secretDocKeys = [
      'type',
      'secretTTLInHours',
      'rotationType',
      'nextRotationDate',
      'vaultNamespace',
      'vaultPath',
      'devicePasswordKey',
      'policyId',
      'status',
      'active',
      'version',
      'updatedAt',
      'lastDeviceSyncStatus',
      'error',
    ];
    return await this.diffingOperationOnHistory(
      secretsHistory.map((history) => {
        let doc = {};
        for (const key of secretDocKeys) {
          if (key in history['document']) {
            if (key === 'updatedAt') {
              doc['lastUpdatedAt'] = history['document'][key];
              continue;
            }
            doc[key] = history['document'][key];
          }
        }
        doc['secret'] = history['vaultSecret']?.value || '';
        doc['action'] = history['action']?.action || '';
        doc['lastUpdatedBy'] = history['action']?.createdBy || '';
        doc['error'] = history['document']['error']
          ?.map((err) => err.message)
          ?.join('\n');
        return JSON.parse(JSON.stringify(doc));
      }),
    );
  }
  async createPasswordPolicy(body: createSecretPoliciesDto) {
    body.policyname = body?.policyname?.toLowerCase();
    if (
      body.totalchars <
      body.bigAlphabets + body.smallAlphabets + body.noOfSplChars + body.numbers
    ) {
      throw new BadRequestException(
        'Password policy characters combination cannot be greater than the total characters length!',
      );
    }
    const validChars = this.configService.get<string>(
      ENVIRONMENT_VARS.VALID_SPECIAL_CHARS,
    );

    const passwordMinLength = this.configService.get<number>(
      ENVIRONMENT_VARS.SECRET_PASSWORD_MIN_LENGTH,
    );

    const isValid = [...body.splChars].every((char) =>
      validChars.includes(char),
    );

    if (!isValid) {
      throw new BadRequestException(
        `Special characters should be from ${validChars}`,
      );
    }

    if (body.totalchars < passwordMinLength) {
      throw new BadRequestException(
        `Min Password length should be atleast ${passwordMinLength}`,
      );
    }

    const resource = await this.resourcesRepository.getResourcesByNamespace(
      body.namespace,
    );
    if (resource) {
      const response = await this.secretsManagmentServiceApi.put(
        'policies/passsword-policy',
        '',
        {
          params: {
            ...body,
          },
        },
      );
      if (!response?.status) {
        return { success: false, message: 'PasswordPolicy not created.' };
      }
      const Policyresponse = await this.secretsPoliciesRepository.create({
        type: SecretType.VAULT_PASSWORD_POLICY,
        policyName: body.policyname,
        description: body.description,
        policyRules: {
          passwordDescription: body.description,
          acceptedSpecialCharacters: body.splChars,
          totalCharactersLength: body.totalchars,
          specialCharactersCount: body.noOfSplChars,
          lowerCaseLettersCount: body.smallAlphabets,
          upperCaseLettersCount: body.bigAlphabets,
          numericalCharactersCount: body.numbers,
        },
        resourceId: resource.resourceId,
        status: 'ACTIVE',
      });
      return {
        success: true,
        message: 'PasswordPolicy is created successfully.',
      };
    } else {
      return { success: false, message: 'Namespace not found!' };
    }
  }

  async secretRoatationNotification(
    requestId: string,
    email: string,
    vaultDetails: {
      project?: string;
      namespace: string;
      path: string;
      vaultSecretKey: string;
    },
    rotationState: TOKEN_STATUS | ROTATION_STATUS | ActionStatus,
    message?: string,
  ) {
    try {
      await this.integrationNotificationService.notifyTokenExpiryToNameSpaceGroup(
        requestId,
        ActionType.SECRET_ROTATION,
        email,
        vaultDetails,
        rotationState,
        message && message,
      );
    } catch (error) {
      this.logger.error('Error while sending notification.');
      this.logger.error(error);
    }
  }
}
