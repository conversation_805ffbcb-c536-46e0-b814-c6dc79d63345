const session = db.getMongo().startSession();
session.startTransaction();

function runScript() {
  const linuxCorpNetConfig = db.cataloglevel04.findOne({
    shortName: 'linuxcorpnet',
  });
  const windowsCorpNetConfig = db.cataloglevel04.findOne({
    shortName: 'windowscorpnet',
  });
  if (!linuxCorpNetConfig || !windowsCorpNetConfig) {
    print('Catalog document does not exists, aborting the transaction');
    session.abortTransaction();
    return;
  }

  const linuxCorpNetCatalogSteps = {
    catalogLevel04Id: linuxCorpNetConfig._id,
    version: 3,
    active: true,
    activityLogSteps: [
      {
        name: 'Receive VM Request - Nebula',
        eventCode: 'NEB-EVENT-VM-CREATE-1001',
        sequence: 1,
      },
      {
        name: 'Approve Request - Nebula',
        eventCode: 'NEB-EVENT-APPROVAL-1001',
        sequence: 2,
      },
      {
        name: 'Create VM(s) - Morpheus',
        eventCode: 'NEB-EVENT-BLUE-VM-2001',
        sequence: 3,
      },
      {
        name: 'Check VM Status - Morpheus',
        eventCode: 'NEB-EVENT-BLUE-VM-3001',
        sequence: 4,
      },
      {
        name: 'Update Tags -Morpheus',
        eventCode: 'NEB-EVENT-BLUE-VM-4011',
        sequence: 5,
      },
      {
        name: 'Update Resource(s) Metadata - Nebula',
        eventCode: 'NEB-EVENT-BLUE-VM-4001',
        sequence: 6,
      },
      {
        name: 'Update Owner -Morpheus',
        eventCode: 'NEB-EVENT-BLUE-VM-4010',
        sequence: 7,
      },

      {
        name: 'Complete Request Processing - Nebula',
        eventCode: 'NEB-EVENT-BLUE-VM-5001',
        sequence: 8,
      },
    ],
  };

  const windowsCorpNetCatalogSteps = {
    catalogLevel04Id: windowsCorpNetConfig._id,
    version: 3,
    active: true,
    activityLogSteps: [
      {
        name: 'Receive VM Request - Nebula',
        eventCode: 'NEB-EVENT-VM-CREATE-1001',
        sequence: 1,
      },
      {
        name: 'Approve Request - Nebula',
        eventCode: 'NEB-EVENT-APPROVAL-1001',
        sequence: 2,
      },
      {
        name: 'Create VM(s) - Morpheus',
        eventCode: 'NEB-EVENT-BLUE-VM-2001',
        sequence: 3,
      },
      {
        name: 'Check VM Status - Morpheus',
        eventCode: 'NEB-EVENT-BLUE-VM-3001',
        sequence: 4,
      },
      {
        name: 'Update Tags -Morpheus',
        eventCode: 'NEB-EVENT-BLUE-VM-4011',
        sequence: 5,
      },
      {
        name: 'Update Resource(s) Metadata - Nebula',
        eventCode: 'NEB-EVENT-BLUE-VM-4001',
        sequence: 6,
      },
      {
        name: 'Update Owner -Morpheus',
        eventCode: 'NEB-EVENT-BLUE-VM-4010',
        sequence: 7,
      },

      {
        name: 'Complete Request Processing - Nebula',
        eventCode: 'NEB-EVENT-BLUE-VM-5001',
        sequence: 8,
      },
    ],
  };

  db.catalogsteps.updateOne(
    { active: true, catalogLevel04Id: linuxCorpNetConfig._id },
    { $set: { active: false } },
    { upsert: false },
  );
  db.catalogsteps.updateOne(
    { active: true, catalogLevel04Id: windowsCorpNetConfig._id },
    { $set: { active: false } },
    { upsert: false },
  );

  db.catalogsteps.insertOne(linuxCorpNetCatalogSteps);
  db.catalogsteps.insertOne(windowsCorpNetCatalogSteps);
  session.commitTransaction();
  print(`Script ran successfully`);
}

try {
  runScript();
} catch (e) {
  session.abortTransaction();
  print('Transaction aborted due to error', e);
} finally {
  session.endSession();
}
