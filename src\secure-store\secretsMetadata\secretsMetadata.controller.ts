import { Body, Controller, Get, Post, UseGuards } from '@nestjs/common';
import { SecretsMetadataService } from './secretsMetadata.service';
import { CreateSecretMetaDataDto } from './dto/secretsMetadata.dto';
import { ApiSecurity, ApiTags } from '@nestjs/swagger';
import { AuthenticationGuard } from '../../auth/authentication.guard';

@ApiTags('Secrets metadata')
@ApiSecurity('x-nebula-authorization')
@UseGuards(AuthenticationGuard)
@Controller('secrets-metadata')
export class SecretsMetadataController {
  constructor(
    private readonly secretsMetadataService: SecretsMetadataService,
  ) {}

  @Post()
  async create(@Body() createSecretMetaDataDto: CreateSecretMetaDataDto) {
    return await this.secretsMetadataService.create(createSecretMetaDataDto);
  }
}
