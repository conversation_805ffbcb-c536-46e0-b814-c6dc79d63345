import { Injectable, Inject } from '@nestjs/common';
import { LoggerService } from '../loggers/logger.service';
import { IntegrationNotificationService } from '../naas/integration.notification.service';
import { Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';

export interface EmailTemplate {
  _id?: any;
  templateId: string;
  templateLocation: string;
  notifications: Array<{
    type: string;
    address: string;
    message: string;
    contentType: string;
    title: string;
  }>;
}

export interface EmailNotificationRequest {
  templateId: string;
  recipientEmail: string;
  subject: string;
  message: string;
  replacementData?: { [key: string]: string };
  tableData?: Array<{ [key: string]: string }>;
}

@Injectable()
export class EmailNotificationService {
  constructor(
    private readonly logger: LoggerService,
    private readonly integrationNotificationService: IntegrationNotificationService,
    @InjectModel('EmailTemplate') private readonly emailTemplateModel: Model<EmailTemplate>,
  ) {}

  /**
   * Sends email using MongoDB template
   * @param request - Email notification request
   */
  async sendEmailWithTemplate(request: EmailNotificationRequest): Promise<void> {
    try {
      this.logger.log('Sending email with template', { 
        templateId: request.templateId,
        recipientEmail: request.recipientEmail 
      });

      // Get template from MongoDB
      const template = await this.getEmailTemplate(request.templateId);
      if (!template) {
        throw new Error(`Email template with ID ${request.templateId} not found`);
      }

      // Process template with replacement data
      const processedMessage = this.processTemplate(
        template.notifications[0].message,
        request.message,
        request.replacementData || {},
        request.tableData || []
      );

      // Send email using existing integration service
      await this.sendProcessedEmail(
        request.recipientEmail,
        request.subject,
        processedMessage,
        template.notifications[0].title
      );

      this.logger.log('Email sent successfully with template', { templateId: request.templateId });
    } catch (error) {
      this.logger.error('Failed to send email with template', error);
      throw error;
    }
  }

  /**
   * Gets email template from MongoDB
   * @param templateId - Template ID to retrieve
   */
  private async getEmailTemplate(templateId: string): Promise<EmailTemplate | null> {
    try {
      const template = await this.emailTemplateModel.findOne({ templateId }).exec();
      return template;
    } catch (error) {
      this.logger.error(`Failed to get email template ${templateId}`, error);
      throw error;
    }
  }

  /**
   * Processes template with replacement data
   * @param templateMessage - HTML template message
   * @param message - Main message to replace
   * @param replacementData - Key-value pairs for replacement
   * @param tableData - Array of data for table rows
   */
  private processTemplate(
    templateMessage: string,
    message: string,
    replacementData: { [key: string]: string },
    tableData: Array<{ [key: string]: string }>
  ): string {
    let processedMessage = templateMessage;

    // Replace main message
    processedMessage = processedMessage.replace('<REPLACE_MESSAGE>', message);

    // Replace other placeholders
    Object.keys(replacementData).forEach(key => {
      const placeholder = `<${key}>`;
      processedMessage = processedMessage.replace(new RegExp(placeholder, 'g'), replacementData[key]);
    });

    // Process table data if provided
    if (tableData.length > 0) {
      processedMessage = this.processTableData(processedMessage, tableData);
    }

    return processedMessage;
  }

  /**
   * Processes table data in the template
   * @param templateMessage - HTML template message
   * @param tableData - Array of data for table rows
   */
  private processTableData(
    templateMessage: string,
    tableData: Array<{ [key: string]: string }>
  ): string {
    let processedMessage = templateMessage;

    if (tableData.length === 0) {
      return processedMessage;
    }

    // Generate table headers
    const headers = Object.keys(tableData[0]);
    let headerHtml = '';
    headers.forEach(header => {
      headerHtml += `<td valign=top style='border:solid windowtext 1.0pt; background:#0000FF;padding:0in 5.4pt 0in 5.4pt'>
        <p class=MsoNormal><span style='color:white'>${header.toUpperCase()}<o:p></o:p></span></p>
      </td>`;
    });

    // Generate table rows
    let rowsHtml = '';
    tableData.forEach(row => {
      rowsHtml += '<tr style="mso-yfti-irow:1">';
      headers.forEach(header => {
        rowsHtml += `<td valign=top style='border:solid windowtext 1.0pt; border-top:none;padding:0in 5.4pt 0in 5.4pt'>
          <p class=MsoNormal><span style='color:black'>${row[header] || ''}<o:p></o:p></span></p>
        </td>`;
      });
      rowsHtml += '</tr>';
    });

    // Replace placeholders
    processedMessage = processedMessage.replace('<ROW_HEADER>', headerHtml);
    processedMessage = processedMessage.replace('<ROW_NUMBER>', rowsHtml);
    processedMessage = processedMessage.replace('<ROW_RESULTS>', '');
    processedMessage = processedMessage.replace('<REPLACE_HEADER>', '');
    processedMessage = processedMessage.replace('<REPLACE_VALUE>', '');
    processedMessage = processedMessage.replace('<ROW_DATA>', '');

    return processedMessage;
  }

  /**
   * Sends processed email using integration service
   * @param recipientEmail - Recipient email address
   * @param subject - Email subject
   * @param htmlContent - Processed HTML content
   * @param title - Email title
   */
  private async sendProcessedEmail(
    recipientEmail: string,
    subject: string,
    htmlContent: string,
    title: string
  ): Promise<void> {
    try {
      // Create a service request object for the notification service
      const serviceRequestForNotification = {
        serviceRequestId: `EMAIL-${Date.now()}`,
        requestType: 'CUSTOM_EMAIL_NOTIFICATION',
        requesterEmail: recipientEmail,
        status: 'SENT'
      };

      // Use the existing notification service
      await this.integrationNotificationService.notifyApprovalServiceRequestStatusToIntegrationApi(
        serviceRequestForNotification,
        recipientEmail,
        htmlContent
      );

    } catch (error) {
      this.logger.error('Failed to send processed email', error);
      throw error;
    }
  }

  /**
   * Sends simple email notification for AWS Cherwell workflow
   * @param recipientEmail - Recipient email
   * @param workItemNumber - Cherwell work item number
   * @param serviceRequestId - Service request ID
   * @param detectedDevices - List of detected AWS devices
   */
  async sendAwsCherwellNotification(
    recipientEmail: string,
    workItemNumber: string,
    serviceRequestId: string,
    detectedDevices: string[]
  ): Promise<void> {
    try {
      const tableData = [
        {
          'Work Item Number': workItemNumber,
          'Service Request ID': serviceRequestId,
          'Detected AWS Devices': detectedDevices.join(', '),
          'Status': 'Pending Approval',
          'Assignment Group': 'Architecture DFD Team'
        }
      ];

      const emailRequest: EmailNotificationRequest = {
        templateId: '1014', // Your template ID
        recipientEmail: recipientEmail,
        subject: `Approval needed for AWS ${workItemNumber}`,
        message: `Approval needed for AWS related service request from SDIT Architecture Team before implementing.`,
        replacementData: {
          'WORK_ITEM_NUMBER': workItemNumber,
          'SERVICE_REQUEST_ID': serviceRequestId
        },
        tableData: tableData
      };

      await this.sendEmailWithTemplate(emailRequest);
      
      this.logger.log('AWS Cherwell notification sent successfully', { 
        workItemNumber, 
        recipientEmail 
      });
    } catch (error) {
      this.logger.error('Failed to send AWS Cherwell notification', error);
      throw error;
    }
  }
}
