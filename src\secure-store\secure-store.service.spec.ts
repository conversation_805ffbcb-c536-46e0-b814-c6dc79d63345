import { Test } from '@nestjs/testing';
import { SecureStoreService } from './secure-store.service';
import { SecretsMetadataService } from './secretsMetadata/secretsMetadata.service';
import { SecretsMetadataRepository } from './secretsMetadata/secretsMetadata.repository';
import { AssetsService } from '../assets/assets.service';
import { LoggerService } from '../loggers/logger.service';
import { SecretsPoliciesRepository } from './secretsPolicies/secretsPolicies.repository';
import { EncryptionService } from '../encryption/encryption.service';
import { ResourcesRepository } from '../resources/resources.repository';
import { RotateSecretRequestDto } from './dto/rotate-secret.request.dto';
import { ActionService } from '../action/providers/action.service';
import { ResourcesService } from '../resources/resources.service';
import { SecretsPoliciesService } from './secretsPolicies/secretsPolicies.service';
import { DevicesService } from './devices/providers/devices.service';
import {
  BadRequestException,
  HttpStatus,
  NotFoundException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SecretRotationUpdateRequestDto } from './dto/secret_rotation_update.request.dto';
import { ROTATION_STATUS } from './enums/secret_rotation_request_type';
import {
  RotationType,
  SecretType,
} from './secretsMetadata/types/secretsMetadata.enum';
import { FetchSecretType } from './dto/fetch-secrets.response.dto';
import { NameSpaceType } from './enums/NamespaceType';
import { NamespaceResponseDTO } from './dto/namespace-child.response.dto';
import { withResponseErrorHandler } from '../utils/helpers';
import {
  RequestStatus,
  RequestType,
  ServiceCatalogName,
  ServiceCatalogType,
} from '../types';
import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { CreateNameSpaceRequestDto } from './dto/create_namespace.request.dto';

describe('SecureStoreService', () => {
  const secretsMetadataRepositoryRepo = {
    getSecretsMetaDataBySecretId: jest.fn(),
    getSecretsMetaData: jest.fn(),
    getSecretsMetaDataPaginated: jest.fn(),
    getAllSecretsMetaDataBySecretIds: jest.fn(),
    changeActiveStatusByIds: jest.fn(),
    generateSecretId: jest.fn(),
    create: jest.fn(),
  };

  const mockActionService = {
    create: jest.fn(),
    update: jest.fn(),
  };

  const mockResourcesRepository = {
    getResourcesByNamespace: jest.fn(),
  };
  const mockSecretsManagmentServiceApi = {
    get: jest.fn(),
    post: jest.fn(),
  };

  const mockIntegrationServiceApi = {
    get: jest.fn(),
    post: jest.fn(),
  };

  let mockSecretsPoliciesService = {
    getPolicy: jest.fn(),
  };

  let mockSecretsMetadataService = {
    getSecretsMetaDataBySecretId: jest.fn(),
    bulkUpdateSecretsMetaDataBySecretId: jest.fn(),
  };
  let secureStoreService: SecureStoreService;
  let mockResourcesService = {
    getNamespaceResources: jest.fn(),
    getPaginatedResourcesList: jest.fn(),
  };

  let mockConfigService = {
    get: jest.fn(),
  };

  let mockSecretDevicesService = {
    bulkUpdate: jest.fn(),
  };
  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        SecureStoreService,
        { provide: ResourcesService, useValue: mockResourcesService },
        { provide: ConfigService, useValue: mockConfigService },
        {
          provide: SecretsMetadataRepository,
          useValue: secretsMetadataRepositoryRepo,
        },
        {
          provide: 'INTEGRATION_SERVICE_API',
          useValue: {
            get: jest.fn(),
          },
        },
        {
          provide: 'SECRETS_MANAGEMENT_SERVICE_API',
          useValue: mockSecretsManagmentServiceApi,
        },
        {
          provide: 'INTEGRATION_SERVICE_API',
          useValue: mockIntegrationServiceApi,
        },
        {
          provide: AssetsService,
          useValue: {
            queueIfApprovalNotRequired: jest.fn(),
          },
        },
        {
          provide: LoggerService,
          useValue: {
            warn: jest.fn(),
            debug: jest.fn(),
            error: jest.fn(),
            fatal: jest.fn(),
            log: jest.fn(),
          },
        },
        {
          provide: SecretsMetadataRepository,
          useValue: secretsMetadataRepositoryRepo,
        },
        {
          provide: SecretsPoliciesRepository,
          useValue: {
            getPolicy: jest.fn(),
            getPasswordPolicies: jest.fn(),
          },
        },
        {
          provide: EncryptionService,
          useValue: {
            encrypt: jest.fn(),
            decrypt: jest.fn(),
          },
        },
        {
          provide: ResourcesRepository,
          useValue: {
            getResourcesByNamespace: jest.fn(),
            findOneByResourceId: jest.fn(),
          },
        },
        {
          provide: ResourcesService,
          useValue: mockResourcesService,
        },
        {
          provide: SecretsPoliciesService,
          useValue: mockSecretsPoliciesService,
        },
        {
          provide: SecretsMetadataService,
          useValue: mockSecretsMetadataService,
        },
        {
          provide: ActionService,
          useValue: {
            create: jest.fn(),
            update: jest.fn(),
          },
        },
        {
          provide: DevicesService,
          useValue: mockSecretDevicesService,
        },
      ],
    }).compile();

    secureStoreService = module.get(SecureStoreService);
  });

  it('secureStoreService should be defined', () => {
    expect(secureStoreService).toBeDefined();
  });

  describe('deleteSecretsByIds', () => {
    it('should return success status with message on positive response for rotatble secrets', async () => {
      let mockids = 'NEB-VAULT-ROT-SECRET-1086';
      let mockSecretsMetaData = [
        {
          _id: '68523fdf8fab57ab9b3677fe',
          secretId: 'NEB-VAULT-ROT-SECRET-1086',
          type: 'ROTATABLE-SECRET',
          description: 'rotatable secret to test delete',
          resourceId: 'NEB-VAULT-TOKEN-8382',
          secretTTLInHours: 0,
          rotationType: 'Auto',
          nextRotationDate: '2025-06-30T10:14:14.329Z',
          vaultNamespace: 'nebula-stamp',
          vaultPath: 'dev/mock',
          deviceUserNameSecretId: 'NEB-VAULT-NOR-SECRET-1085',
          devicePasswordKey: 'vkeystring',
          policyId: 'NEB-VAULT-PASSWORD-POLICY-12234',
          status: 'SUCCESS',
          active: true,
          error: [],
        },
      ];
      let mockSecretsForDevices = [
        {
          _id: '68523fdf8fab57ab9b3677fc',
          secretId: 'NEB-VAULT-NOR-SECRET-1085',
          type: 'NORMAL-SECRET',
          description: 'rotatable secret to test delete',
          resourceId: 'NEB-VAULT-TOKEN-8382',
          vaultNamespace: 'nebula-stamp',
          vaultPath: 'dev/mock',
          devicePasswordKey: 'unkeystring',
          policyId: 'NEB-VAULT-PASSWORD-POLICY-12234',
          status: 'SUCCESS',
          active: true,
          error: [],
        },
      ];
      mockSecretsMetadataService.getSecretsMetaDataBySecretId
        .mockResolvedValueOnce(mockSecretsMetaData)
        .mockResolvedValueOnce(mockSecretsForDevices);
      mockSecretDevicesService.bulkUpdate.mockResolvedValue(null);
      mockSecretsMetadataService.bulkUpdateSecretsMetaDataBySecretId(null);
      let mockGetApiRes = {
        data: {
          data: {
            data: {
              Rotatable3: 'kkjhj3',
              userName32: 'string2',
            },
          },
        },
      };
      mockSecretsManagmentServiceApi.get.mockResolvedValue(mockGetApiRes);
      mockSecretsManagmentServiceApi.post.mockResolvedValue(true);
      let mockResponse = {
        status: HttpStatus.OK,
        message: 'Secrets deleted successfully.',
      };
      const result = await secureStoreService.deleteSecretsByIds(mockids);
      expect(result).toEqual(mockResponse);
    });
    it('should return success status with message on positive response for normal secrets', async () => {
      let mockIds = 'NEB-VAULT-NOR-SECRET-1006';
      let mockSecretsMetadata = [
        {
          _id: '684810ed9c64fd9fdacd7bb6',
          secretId: 'NEB-VAULT-NOR-SECRET-1006',
          type: 'NORMAL-SECRET',
          description: 'string',
          resourceId: 'NEB-VAULT-TOKEN-8382',
          vaultNamespace: 'nebula-stamp',
          vaultPath: 'dev/test',
          devicePasswordKey: 'userName32',
          policyId: 'string',
          status: 'SUCCESS',
          active: true,
          error: [],
          createdAt: '2025-06-10T11:03:09.044Z',
          updatedAt: '2025-06-10T11:03:09.044Z',
        },
      ];
      mockSecretsMetadataService.getSecretsMetaDataBySecretId.mockResolvedValue(
        mockSecretsMetadata,
      );
      mockSecretsMetadataService.bulkUpdateSecretsMetaDataBySecretId.mockResolvedValue(
        mockIds,
      );
      let mockGetApiRes = {
        data: {
          data: {
            data: {
              Rotatable3: 'kkjhj3',
              userName32: 'string2',
            },
          },
        },
      };
      mockSecretsManagmentServiceApi.get.mockResolvedValue(mockGetApiRes);
      mockSecretsManagmentServiceApi.post.mockResolvedValue(true);
      let mockResponse = {
        status: HttpStatus.OK,
        message: 'Secrets deleted successfully.',
      };
      const result = await secureStoreService.deleteSecretsByIds(mockIds);
      expect(result).toEqual(mockResponse);
    });
  });
  describe('fetchAuthorizedNamespaces', () => {
    it('should fetch all pages and return filtered namespaces', async () => {
      expect(secureStoreService.fetchAuthorizedNamespaces).toBeDefined();
    });
  });

  describe('fetchPasswordPolicies', () => {
    it('should be defined', () => {
      expect(secureStoreService.fetchPasswordPolicies).toBeDefined();
    });
    describe('generateRotatablePassword', () => {
      it('should return result in object array', async () => {
        let mockIds = 'NEB-VAULT-ROT-SECRET-1063,NEB-VAULT-NOR-SECRET-1062';
        let mockSecretsMetadata = [
          {
            _id: '684bfe9757310b4e1e68a0fe',
            secretId: 'NEB-VAULT-ROT-SECRET-1063',
            type: 'ROTATABLE-SECRET',
            description: 'test secrets',
            resourceId: 'NEB-VAULT-TOKEN-8382',
            secretTTLInHours: 30,
            rotationType: 'Auto',
            nextRotationDate: '2025-06-20T18:35:29.421Z',
            vaultNamespace: 'nebula-stamp',
            vaultPath: 'dev/mock',
            deviceUserNameSecretId: 'NEB-VAULT-NOR-SECRET-1062',
            devicePasswordKey: 'mockkey',
            policyId: 'NEB-VAULT-PASSWORD-POLICY-12234',
            status: 'SUCCESS',
            active: true,
            error: [],
            createdAt: '2025-06-13T10:33:59.151Z',
            updatedAt: '2025-06-13T10:33:59.151Z',
            __v: 0,
          },
          {
            _id: '684bfe9757310b4e1e68a0fc',
            secretId: 'NEB-VAULT-NOR-SECRET-1062',
            type: 'NORMAL-SECRET',
            description: 'test secrets',
            resourceId: 'NEB-VAULT-TOKEN-8382',
            vaultNamespace: 'nebula-stamp',
            vaultPath: 'dev/mock',
            devicePasswordKey: 'mockUserKey',
            policyId: 'NEB-VAULT-PASSWORD-POLICY-12234',
            status: 'SUCCESS',
            active: true,
            error: [],
            createdAt: '2025-06-13T10:33:59.123Z',
            updatedAt: '2025-06-13T10:33:59.123Z',
            __v: 0,
          },
        ];

        let mockPolicies = [
          {
            _id: '6849cc71864a08117c6949fd',
            policyId: 'NEB-VAULT-PASSWORD-POLICY-12234',
            type: 'VAULT-PASSWORD-POLICY',
            policyName: 'sample-dev-passwordmay201',
            description: 'test',
            resourceId: 'NEB-RES-VAULT-NAMESPACE-1000',
            status: 'ACTIVE',
            secretTTLInHours: 700,
            active: true,
            createdBy: 'P3226653',
            createdDate: '2025-06-11T18:35:29.421Z',
            updatedBy: 'P3226653',
            updatedDate: '2025-06-11T18:35:29.421Z',
            namespace: 'nebula-stamp',
          },
        ];
        mockSecretsMetadataService.getSecretsMetaDataBySecretId.mockResolvedValue(
          mockSecretsMetadata,
        );
        mockSecretsPoliciesService.getPolicy.mockResolvedValue(mockPolicies);
        let mockNewPassword = {
          data: {
            data: {
              password: '%N@7&9@pGc',
            },
          },
        };
        let secretsMock = {
          data: {
            data: {
              data: {
                mockUserKey: 'mockUserPassword',
                mockkey: 'mockPassword',
              },
              metadata: {
                version: 1,
              },
            },
          },
        };
        mockSecretsManagmentServiceApi.get.mockImplementation((url: string) => {
          if (url.includes('generate')) {
            return Promise.resolve(mockNewPassword);
          } else if (url.includes('secrets')) {
            return Promise.resolve(secretsMock);
          }
        });

        const result = await secureStoreService.generateUniquePassword(mockIds);
        let mockResult = [
          {
            secretId: 'NEB-VAULT-NOR-SECRET-1062',
            mockUserKey: '%N@7&9@pGc',
          },
          {
            secretId: 'NEB-VAULT-ROT-SECRET-1063',
            mockkey: '%N@7&9@pGc',
          },
        ];
        const sortBySecretId = (a, b) => a.secretId.localeCompare(b.secretId);
        expect(result.sort(sortBySecretId)).toEqual(
          mockResult.sort(sortBySecretId),
        );
      });
    });
  });

  describe('fetchNamespaceResources', function () {
    it('should return all namespace resources user have access to', async function () {
      let mockResponse = [
        {
          id: '682b519e3388bf6af95954e1',
          resourcesName: 'test policy vault',
          requestId: 'NEB-SECRET-VAULT-12567',
          resourceId: 'NEB-VAULT-TOKEN-8382',
          status: 'ACTIVE',
          catalogType: 'vault-namespace',
          catalogLevel03: 'SharedServices-Secrets-Vault-Token',
          createdBy: '<EMAIL>',
          resourcesDetails: {
            request_id: '10f1e532-0f7a-39b9-8e78-2a0e4f25e9ba',
            lease_id: '',
            renewable: false,
            lease_duration: 0,
            data: {
              key_info: {
                'nebula-dev/': {
                  id: '1GbQ3',
                  path: 'nebula-stamp/nebula-dev/',
                },
                'nebula-qa/': {
                  id: 'Cn2zs',
                  path: 'nebula-stamp/nebula-qa/',
                },
                'nebula-stamp/': {
                  id: 'XJu3u',
                  path: 'nebula-stamp/nebula-stamp/',
                },
                'nebula-stg/': {
                  id: 'NmkZN',
                  path: 'nebula-stamp/nebula-stg/',
                },
              },
              keys: [
                'nebula-dev/',
                'nebula-qa/',
                'nebula-stamp/',
                'nebula-stg/',
              ],
            },
            wrap_info: null,
            warnings: null,
            auth: null,
          },
          projectId: '66967d8d75564197a15ea214',
          childId: [],
          platformContext: {
            catalogId: '660408f18fd3d3bc24ac58d8',
            domainId: '660408f18fd3d3bc24ac58d8',
            envId: '01966c73-b401-7285-bec1-b122f7118def',
          },
          tokenAccessor: 'string',
          parentID: 'NEB-RES-VAULT-NAMESPACE-1000',
          resourceType: 'Vault-Token',
          vaultTokenRequestId: 'asd',
          expiryDate: '2025-05-19T15:36:20.323Z',
          ttlInHours: 24,
          tokenRenewByNebula: true,
          namespace: 'nebula-stamp',
          createdAt: '2025-05-19T15:43:26.042Z',
          updatedAt: '2025-05-19T15:43:26.042Z',
        },
      ];
      mockResourcesService.getNamespaceResources.mockResolvedValue(
        mockResponse,
      );
      const result = await secureStoreService.fetchNamespaceResources();
      expect(result).toEqual(mockResponse);
    });
  });
  describe('createRotateSecretRequest', () => {
    it('should create a rotation request and return a success message', async () => {
      const mockRotateSecretRequest: RotateSecretRequestDto[] = [
        {
          secretId: 'secret-1',
          newPassword: 'newPassword123',
        },
      ];

      const mockSecretsMetaData = [
        {
          secretId: 'secret-1',
          vaultPath: 'path/to/secret',
          vaultNamespace: 'namespace-1',
          devicePasswordKey: 'device-password-key',
          deviceId: ['charter-1'],
          linkedSecret: { devicePasswordKey: 'username-key' },
          sourceSystem: ['DEVICE_SOURCE'],
        },
      ];

      const mockSecretDataFromVault = {
        data: {
          data: {
            'device-password-key': 'oldPassword123',
          },
        },
      };

      const expectedAdhocSecrets = {
        data: {
          data: {
            'some-key': 'some-value',
          },
        },
      };

      secretsMetadataRepositoryRepo.getAllSecretsMetaDataBySecretIds.mockResolvedValue(
        mockSecretsMetaData,
      );

      mockSecretsManagmentServiceApi.get
        .mockResolvedValueOnce(mockSecretDataFromVault)
        .mockResolvedValueOnce(expectedAdhocSecrets);

      mockIntegrationServiceApi.post.mockResolvedValue({});
      mockSecretsManagmentServiceApi.post.mockResolvedValue({});
      secretsMetadataRepositoryRepo.changeActiveStatusByIds.mockResolvedValue(
        {},
      );

      const result = await secureStoreService.createRotateSecretRequest(
        mockRotateSecretRequest,
      );

      expect(result).toEqual({
        message: 'Secret rotation request raised successfully.',
      });

      expect(
        secretsMetadataRepositoryRepo.changeActiveStatusByIds,
      ).toHaveBeenCalledWith(['secret-1']);
    });
  });

  describe('secretsRotationResult', () => {
    it('should throw NotFoundException if adhoc secrets are missing', async () => {
      const mockRequest: SecretRotationUpdateRequestDto = {
        nebulaSecretId: 'secret-1',
        status: ROTATION_STATUS.COMPLETED,
      };

      const mockSecretMetaData = {
        vaultNamespace: 'namespace-1',
        vaultPath: 'path/to/secret',
        devicePasswordKey: 'device-password-key',
      };

      const mockAdhocResponse = {
        data: {
          data: null,
        },
      };

      secretsMetadataRepositoryRepo.getSecretsMetaDataBySecretId.mockResolvedValue(
        mockSecretMetaData,
      );

      mockConfigService.get.mockReturnValue('adhoc-value');

      mockSecretsManagmentServiceApi.get.mockResolvedValueOnce(
        mockAdhocResponse,
      );

      await expect(
        secureStoreService.secretsRotationResult(mockRequest),
      ).rejects.toThrowError(new NotFoundException('Secrets not found.'));

      expect(
        secretsMetadataRepositoryRepo.getSecretsMetaDataBySecretId,
      ).toHaveBeenCalledWith('secret-1');

      expect(mockSecretsManagmentServiceApi.get).toHaveBeenCalledWith(
        'secrets',
        {
          params: {
            path: 'adhoc-value/secret-1',
            namespace: 'adhoc-value',
          },
        },
      );
    });
  });

  describe('fetchVaultSecrets', () => {
    it('should throw NotFoundException when no secrets are found', async () => {
      const mockParams = {
        path: 'some/path',
        namespace: 'default',
      };

      mockSecretsManagmentServiceApi.get.mockResolvedValue({
        data: { data: null },
      });

      await expect(
        secureStoreService.fetchVaultSecretsPaginated(mockParams, {}, false),
      ).rejects.toThrow(TypeError);
    });
    it('should return empty array if no metadata is found', async () => {
      const oldParams = { path: 'base/path', namespace: 'default' };

      jest
        .spyOn(
          secureStoreService['secretsMetadataRepository'],
          'getSecretsMetaDataPaginated',
        )
        .mockResolvedValueOnce({ items: [], pageInfo: { totalItems: 0 } });

      const result = await secureStoreService.fetchVaultSecretsPaginated(
        oldParams,
        {},
        false,
      );

      expect(result).toBeInstanceOf(Array);
    });
  });

  describe('fetchNamespaceData', () => {
    it('should return children when type is FOLDER', async () => {
      const namespace = 'test/folder';
      const mockChildren = [{ name: 'child1' }, { name: 'child2' }];

      // Mock fetchChildren to return mockChildren
      secureStoreService.fetchChildren = jest
        .fn()
        .mockResolvedValue(mockChildren);

      const result = await secureStoreService.fetchNamespaceData(
        namespace,
        NameSpaceType.FOLDER,
      );

      expect(secureStoreService.fetchChildren).toHaveBeenCalledWith(namespace);
      expect(result).toEqual(mockChildren);
    });

    it('should return extracted paths when type is PATH', async () => {
      const namespace = 'test/path';
      const mockDirectories = [{ name: 'dir1' }, { name: 'dir2' }];
      const mockExtractedPaths = ['path1', 'path2'];

      // Mock fetchChildren and extractPathsFromDirectory
      secureStoreService.fetchChildren = jest
        .fn()
        .mockResolvedValue(mockDirectories);
      secureStoreService.extractPathsFromDirectory = jest
        .fn()
        .mockReturnValue(mockExtractedPaths);

      const result = await secureStoreService.fetchNamespaceData(
        namespace,
        NameSpaceType.PATH,
      );

      expect(secureStoreService.fetchChildren).toHaveBeenCalledWith(namespace);
      expect(secureStoreService.extractPathsFromDirectory).toHaveBeenCalledWith(
        mockDirectories,
      );
      expect(result).toEqual(mockExtractedPaths);
    });

    it('should return undefined when type is not provided', async () => {
      const namespace = 'test/undefined';

      const result = await secureStoreService.fetchNamespaceData(namespace);

      expect(result).toBeUndefined();
    });
  });

  describe('extractPathsFromDirectory', () => {
    it('should extract paths from top-level SecretDTO children', () => {
      const directories: NamespaceResponseDTO = {
        namespace: 'test',
        children: [
          { key: 's1', type: 'secret', path: 'secret/path1', children: [] },
          { key: 's2', type: 'secret', path: 'secret/path2', children: [] },
        ],
      };

      const result = secureStoreService.extractPathsFromDirectory(directories);

      expect(result).toEqual([
        { label: 'secret/path1', value: 'secret/path1' },
        { label: 'secret/path2', value: 'secret/path2' },
      ]);
    });

    it('should extract paths from nested SecretDTO inside FolderDTO', () => {
      const directories: NamespaceResponseDTO = {
        namespace: 'test',
        children: [
          {
            key: 'f1',
            type: 'folder',
            children: [
              {
                key: 's1',
                type: 'secret',
                path: 'nested/secret1',
                children: [],
              },
              {
                key: 'f2',
                type: 'folder',
                children: [
                  {
                    key: 's2',
                    type: 'secret',
                    path: 'deep/nested/secret2',
                    children: [],
                  },
                ],
              },
            ],
          },
        ],
      };

      const result = secureStoreService.extractPathsFromDirectory(directories);

      expect(result).toEqual([
        { label: 'nested/secret1', value: 'nested/secret1' },
        { label: 'deep/nested/secret2', value: 'deep/nested/secret2' },
      ]);
    });

    it('should return an empty array if no secrets are present', () => {
      const directories: NamespaceResponseDTO = {
        namespace: 'test',
        children: [
          {
            key: 'f1',
            type: 'folder',
            children: [],
          },
        ],
      };

      const result = secureStoreService.extractPathsFromDirectory(directories);

      expect(result).toEqual([]);
    });

    it('should handle mixed FolderDTO and SecretDTO at various levels', () => {
      const directories: NamespaceResponseDTO = {
        namespace: 'test',
        children: [
          {
            key: 'f1',
            type: 'folder',
            children: [
              {
                key: 's1',
                type: 'secret',
                path: 'folder1/secret1',
                children: [],
              },
              {
                key: 'f2',
                type: 'folder',
                children: [
                  {
                    key: 's2',
                    type: 'secret',
                    path: 'folder2/secret2',
                    children: [],
                  },
                ],
              },
            ],
          },
          {
            key: 's3',
            type: 'secret',
            path: 'root/secret3',
            children: [],
          },
        ],
      };

      const result = secureStoreService.extractPathsFromDirectory(directories);

      expect(result).toEqual([
        { label: 'folder1/secret1', value: 'folder1/secret1' },
        { label: 'folder2/secret2', value: 'folder2/secret2' },
        { label: 'root/secret3', value: 'root/secret3' },
      ]);
    });
  });

  describe('fetchChildren', () => {
    it('should return FolderDTO with nested secrets and folders', async () => {
      const namespace = 'test-namespace';

      const mockMetaDataRoot = {
        data: {
          keys: ['secret1', 'folder1/'],
        },
      };

      const mockMetaDataFolder1 = {
        data: {
          keys: ['nestedSecret'],
        },
      };

      secureStoreService.fetchMetaData = jest
        .fn()
        .mockResolvedValueOnce(mockMetaDataRoot)
        .mockResolvedValueOnce(mockMetaDataFolder1);

      const result = await secureStoreService.fetchChildren(namespace);

      expect(result).toEqual({
        namespace: namespace,
        type: 'folder',
        children: [
          {
            key: 'secret1',
            type: 'secret',
            path: '/secret1',
            children: [],
          },
          {
            key: 'folder1',
            type: 'folder',
            children: [
              {
                key: 'nestedSecret',
                type: 'secret',
                path: '/folder1/nestedSecret',
                children: [],
              },
            ],
          },
        ],
      });
    });

    it('should return empty children if no keys are present', async () => {
      secureStoreService.fetchMetaData = jest
        .fn()
        .mockResolvedValue({ data: { keys: [] } });

      const result = await secureStoreService.fetchChildren('empty-namespace');

      expect(result.children).toEqual([]);
    });
  });

  describe('CreateNameSpaceRequestDto', () => {
    it('should validate a correct DTO instance', async () => {
      const dto = {
        namespaceName: 'test-namespace',
        platformContext: {
          platform: 'aws',
          region: 'us-east-1',
        },
        vaultPolicies: [
          {
            enable: true,
            policyName: 'policy1',
            role: 'admin',
          },
        ],
        vaultAppRoles: [
          {
            policyName: 'policy1',
            appRoleName: 'app-role-1',
          },
        ],
        tokenTTL: 3600,
        autoRenewTokenOnExpiry: true,
        notifyBeforeTokenExpiry: false,
        disks: ['disk1', 'disk2'],
        deeplinkUrl: 'https://example.com',
      };

      const instance = plainToInstance(CreateNameSpaceRequestDto, dto, {
        enableImplicitConversion: true,
      });

      const errors = await validate(instance, {
        whitelist: true,
        forbidNonWhitelisted: true,
      });

      expect(errors.length).toBe(1);
    });

    it('should fail validation if required fields are missing', async () => {
      const dto = {
        vaultPolicies: [],
        tokenTTL: null,
        autoRenewTokenOnExpiry: true,
        notifyBeforeTokenExpiry: false,
        deeplinkUrl: '',
      };

      const instance = plainToInstance(CreateNameSpaceRequestDto, dto);
      const errors = await validate(instance);

      expect(errors.length).toBeGreaterThan(0);
      const errorProps = errors.map((e) => e.property);
      expect(errorProps).toContain('namespaceName');
      expect(errorProps).toContain('platformContext');
      expect(errorProps).toContain('tokenTTL');
      expect(errorProps).toContain('deeplinkUrl');
    });
  });

  it('should call renewBulkTokens and handle response', async () => {
    const secretIds = ['id1', 'id2'];
    const mockApiResponse = { success: true };

    mockSecretsManagmentServiceApi.post.mockReturnValue(
      Promise.resolve(mockApiResponse),
    );

    const result = await secureStoreService.renewBulkTokens(secretIds);

    expect(mockSecretsManagmentServiceApi.post).toHaveBeenCalledWith(
      'tokens/renew-tokens',
      secretIds,
    );
  });

  it('should create a vault token secret', async () => {
    const dto = {
      type: FetchSecretType.VAULT_TOKEN,
      vaultKey: 'tokenKey',
      vaultPassword: 'tokenPass',
      userNameKey: '',
      userNamePassword: '',
      description: 'Vault token',
      expiryDate: new Date(),
      tokenRenewByNebula: true,
      notifyBeforeTokenExpiry: true,
      nextRotationDate: '2025-06-20',
    };

    mockResourcesRepository.getResourcesByNamespace.mockResolvedValue({
      resourceId: 'res1',
    });
    secretsMetadataRepositoryRepo.generateSecretId.mockResolvedValue(
      'token123',
    );
    mockConfigService.get.mockReturnValue('master/folder');

    await secureStoreService.createVaultSecrets('namespace1', dto);

    expect(secretsMetadataRepositoryRepo.create).toHaveBeenCalledWith(
      expect.objectContaining({
        secretId: 'token123',
        type: SecretType.VAULT_TOKEN,
      }),
    );
  });
});
