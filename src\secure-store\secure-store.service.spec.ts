import { Test } from '@nestjs/testing';
import { SecureStoreService } from './secure-store.service';
import { SecretsMetadataService } from './secretsMetadata/secretsMetadata.service';
import { SecretsMetadataRepository } from './secretsMetadata/secretsMetadata.repository';
import { AssetsService } from '../assets/assets.service';
import { LoggerService } from '../loggers/logger.service';
import { SecretsPoliciesRepository } from './secretsPolicies/secretsPolicies.repository';
import { EncryptionService } from '../encryption/encryption.service';
import { ResourcesRepository } from '../resources/resources.repository';
import { RotateSecretRequestDto } from './dto/rotate-secret.request.dto';
import { RotateSecretResponseDto } from './dto/rotate-secret.response.dto';
import { ActionService } from '../action/providers/action.service';
import { ResourcesService } from '../resources/resources.service';
import { SecretsPoliciesService } from './secretsPolicies/secretsPolicies.service';
import { DevicesService } from './devices/providers/devices.service';
import {
  BadRequestException,
  HttpStatus,
  NotFoundException,
  InternalServerErrorException,
  NotImplementedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SecretRotationUpdateRequestDto } from './dto/secret_rotation_update.request.dto';
import { ROTATION_STATUS } from './enums/secret_rotation_request_type';
import {
  RotationType,
  SecretType,
} from './secretsMetadata/types/secretsMetadata.enum';
import { FetchSecretType } from './dto/fetch-secrets.response.dto';
import { NameSpaceType } from './enums/NamespaceType';
import { NamespaceResponseDTO } from './dto/namespace-child.response.dto';
import {
  RequestStatus,
  RequestType,
  ServiceCatalogName,
  ServiceCatalogType,
} from '../types';
import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { CreateNameSpaceRequestDto } from './dto/create_namespace.request.dto';
import { UpdateVaultSecretRequestDto } from './dto/update-vault-secrets.request.dto';
import { IntegrationNotificationService } from 'src/naas/integration.notification.service';
import { MultiEnvIAMService } from '../multi-env/iam/iam.service';
import * as helpers from '../utils/helpers';

jest.mock('../utils/helpers', () => ({
  ...jest.requireActual('../utils/helpers'),
  getUserContext: jest.fn(),
}));

describe('SecureStoreService', () => {
  const secretsMetadataRepositoryRepo = {
    getSecretsMetaDataBySecretId: jest.fn(),
    getSecretsMetaData: jest.fn(),
    getSecretsMetaDataPaginated: jest.fn(),
    getAllSecretsMetaDataBySecretIds: jest.fn(),
    changeActiveStatusByIds: jest.fn(),
    generateSecretId: jest.fn(),
    create: jest.fn(),
    createQueryForUpdateMetaData: jest.fn(),
    bulkUpdateSecretsMetaDataBySecretId: jest.fn(),
    updateSecretsMetadataById: jest.fn(),
    fetchVaultSecretsPaginated: jest.fn(),
  };

  const mockActionService = {
    create: jest.fn().mockResolvedValue({
      actionId: 'NEB-ACTION-1',
      entityId: 'NEB-VAULT-ROT-SECRET-1006',
    }),
    update: jest.fn(),
    findAllActionByIds: jest.fn(),
  };

  const mockResourcesRepository = {
    getResourcesByNamespace: jest.fn(),
    findOneByResourceId: jest.fn(),
    getResourceByResourcesName: jest.fn(),
  };
  const mockSecretsManagmentServiceApi = {
    get: jest.fn(),
    post: jest.fn(),
    delete: jest.fn(),
    put: jest.fn(),
  };

  const mockIntegrationServiceApi = {
    get: jest.fn(),
    post: jest.fn(),
  };

  let mockSecretsPoliciesService = {
    getPolicy: jest.fn(),
    create: jest.fn(),
  };

  let mockSecretsMetadataService = {
    getSecretsMetaDataBySecretId: jest.fn(),
    bulkUpdateSecretsMetaDataBySecretId: jest.fn(),
  };
  let secureStoreService: SecureStoreService;
  let actionService: ActionService;

  let mockResourcesService = {
    getNamespaceResources: jest.fn(),
    getPaginatedResourcesList: jest.fn(),
    getResourceByResourceId: jest.fn(),
    getResourcesByNamespace: jest.fn(),
  };
  let mockEncryptionService: jest.Mock<EncryptionService> = {
    encrypt: jest.fn(),
    decrypt: jest.fn(),
  } as any;

  const mockAssetsService: Partial<AssetsService> = {
    create: jest.fn(),
    queueIfApprovalNotRequired: jest.fn(),
  };

  const mockWatchServiceApi = {
    get: jest.fn(),
  };

  let mockConfigService = {
    get: jest.fn(),
  };

  let mockSecretDevicesService = {
    bulkUpdate: jest.fn(),
    findBySecretIds: jest.fn(),
    secretDeviceAssociationNotification: jest.fn(),
  };

  let mockNotificationIntegrationAPI = {
    notifyTokenExpiryToNameSpaceGroup: jest.fn(),
    notifySecretDeviceAssociationChangesToNameSpaceGroup: jest.fn(),
  };

  const mockSecretsPoliciesRepository: Partial<SecretsPoliciesService> = {
    getPolicy: jest.fn(),
    create: jest.fn(),
  } as any;

  const mockIamService = {
    getProjectAppEnvByEnvId: jest.fn(),
  };

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        SecureStoreService,
        { provide: ActionService, useValue: mockActionService },
        { provide: ResourcesService, useValue: mockResourcesService },
        { provide: ConfigService, useValue: mockConfigService },
        { provide: EncryptionService, useValue: mockEncryptionService },
        { provide: 'WATCHER_SERVICE_API', useValue: mockWatchServiceApi },
        {
          provide: SecretsMetadataRepository,
          useValue: secretsMetadataRepositoryRepo,
        },
        {
          provide: 'INTEGRATION_SERVICE_API',
          useValue: {
            get: jest.fn(),
          },
        },
        {
          provide: 'SECRETS_MANAGEMENT_SERVICE_API',
          useValue: mockSecretsManagmentServiceApi,
        },
        {
          provide: 'INTEGRATION_SERVICE_API',
          useValue: mockIntegrationServiceApi,
        },
        { provide: AssetsService, useValue: mockAssetsService },
        {
          provide: LoggerService,
          useValue: {
            warn: jest.fn(),
            debug: jest.fn(),
            error: jest.fn(),
            fatal: jest.fn(),
            log: jest.fn(),
          },
        },
        {
          provide: SecretsMetadataRepository,
          useValue: secretsMetadataRepositoryRepo,
        },
        {
          provide: SecretsPoliciesRepository,
          useValue: mockSecretsPoliciesRepository,
        },
        {
          provide: EncryptionService,
          useValue: {
            encrypt: jest.fn(),
            decrypt: jest.fn(),
          },
        },
        {
          provide: ResourcesRepository,
          useValue: mockResourcesRepository
        },
        {
          provide: ResourcesService,
          useValue: mockResourcesService,
        },
        {
          provide: SecretsPoliciesService,
          useValue: mockSecretsPoliciesService,
        },
        {
          provide: SecretsMetadataService,
          useValue: mockSecretsMetadataService,
        },
        {
          provide: DevicesService,
          useValue: mockSecretDevicesService,
        },
        {
          provide: IntegrationNotificationService,
          useValue: mockNotificationIntegrationAPI,
        },
        {
          provide: MultiEnvIAMService,
          useValue: mockIamService,
        },
      ],
    }).compile();

    secureStoreService = module.get(SecureStoreService);
    actionService = module.get(ActionService);
    (secureStoreService as any).encryptionService = mockEncryptionService;
    (secureStoreService as any).secretsManagmentServiceApi =
      mockSecretsManagmentServiceApi;
    (secureStoreService as any).actionService = mockActionService;
    (secureStoreService as any).secretsPoliciesService =
      mockSecretsPoliciesService;
  });

  it('secureStoreService should be defined', () => {
    expect(secureStoreService).toBeDefined();
  });

  describe('deleteSecretsByIds', () => {
    it('should return success status with message on positive response for rotatble secrets', async () => {
      let mockids = 'NEB-VAULT-ROT-SECRET-1006';
      let mockSecretsMetaData = [
        {
          _id: '68523fdf8fab57ab9b3677fe',
          secretId: 'NEB-VAULT-ROT-SECRET-1006',
          type: 'ROTATABLE-SECRET',
          description: 'rotatable secret to test delete',
          resourceId: 'NEB-VAULT-TOKEN-8382',
          secretTTLInHours: 0,
          rotationType: 'Auto',
          nextRotationDate: '2025-06-30T10:14:14.329Z',
          vaultNamespace: 'nebula-stamp',
          vaultPath: 'dev/mock',
          deviceUserNameSecretId: 'NEB-VAULT-ROT-SECRET-1006',
          devicePasswordKey: 'vkeystring',
          policyId: 'NEB-VAULT-PASSWORD-POLICY-12234',
          status: 'SUCCESS',
          active: true,
          error: [],
        },
      ];
      let mockSecretsForDevices = [
        {
          _id: '68523fdf8fab57ab9b3677fc',
          secretId: 'NEB-VAULT-NOR-SECRET-1085',
          type: 'NORMAL-SECRET',
          description: 'rotatable secret to test delete',
          resourceId: 'NEB-VAULT-TOKEN-8382',
          vaultNamespace: 'nebula-stamp',
          vaultPath: 'dev/mock',
          devicePasswordKey: 'unkeystring',
          policyId: 'NEB-VAULT-PASSWORD-POLICY-12234',
          status: 'SUCCESS',
          active: true,
          error: [],
        },
      ];
      mockSecretsMetadataService.getSecretsMetaDataBySecretId
        .mockResolvedValueOnce(mockSecretsMetaData)
        .mockResolvedValueOnce(mockSecretsForDevices);
      mockSecretDevicesService.bulkUpdate.mockResolvedValue(null);
      mockSecretsMetadataService.bulkUpdateSecretsMetaDataBySecretId(null);
      let mockGetApiRes = {
        data: {
          data: {
            data: {
              Rotatable3: 'kkjhj3',
              userName32: 'string2',
            },
          },
        },
      };
      mockSecretsManagmentServiceApi.get.mockResolvedValue(mockGetApiRes);
      mockSecretsManagmentServiceApi.post.mockResolvedValue(true);
      mockActionService.create.mockResolvedValueOnce({
        actionId: '123',
        entityId: 'NEB-VAULT-NOR-SECRET-1006',
      });
      mockSecretDevicesService.findBySecretIds.mockResolvedValue([{
        deviceId: [123],
        secretId: ['abc']
      }])

      let mockResponse = {
        status: HttpStatus.OK,
        message: 'Secrets deleted successfully.',
      };
      const result = await secureStoreService.deleteSecretsByIds(mockids);
      expect(result).toEqual(mockResponse);
    });
    it('should return success status with message on positive response for normal secrets', async () => {
      let mockIds = 'NEB-VAULT-NOR-SECRET-1006';
      let mockSecretsMetadata = [
        {
          _id: '684810ed9c64fd9fdacd7bb6',
          secretId: 'NEB-VAULT-NOR-SECRET-1006',
          type: 'NORMAL-SECRET',
          description: 'string',
          resourceId: 'NEB-VAULT-TOKEN-8382',
          vaultNamespace: 'nebula-stamp',
          vaultPath: 'dev/test',
          devicePasswordKey: 'userName32',
          policyId: 'string',
          status: 'SUCCESS',
          active: true,
          error: [],
          createdAt: '2025-06-10T11:03:09.044Z',
          updatedAt: '2025-06-10T11:03:09.044Z',
        },
      ];
      mockSecretsMetadataService.getSecretsMetaDataBySecretId.mockResolvedValue(
        mockSecretsMetadata,
      );
      mockSecretsMetadataService.bulkUpdateSecretsMetaDataBySecretId.mockResolvedValue(
        mockIds,
      );
      let mockGetApiRes = {
        data: {
          data: {
            data: {
              Rotatable3: 'kkjhj3',
              userName32: 'string2',
            },
          },
        },
      };
      mockActionService.create.mockResolvedValue({
        actionId: '123',
        entityId: 'NEB-VAULT-NOR-SECRET-1006',
      });
      mockSecretsManagmentServiceApi.get.mockResolvedValue(mockGetApiRes);
      mockSecretsManagmentServiceApi.post.mockResolvedValue(true);
      let mockResponse = {
        status: HttpStatus.OK,
        message: 'Secrets deleted successfully.',
      };
      const result = await secureStoreService.deleteSecretsByIds(mockIds);
      expect(result).toEqual(mockResponse);
    });

    it('should throw NotFoundException when secret not found', async () => {
      let mockids = 'NEB-VAULT-ROT-SECRET-1006';
      
      mockSecretsMetadataService.getSecretsMetaDataBySecretId
        .mockResolvedValueOnce(undefined);

      await expect(
      secureStoreService.deleteSecretsByIds(mockids)
    ).rejects.toThrow(
      new NotFoundException(
        'Secrets not found for the provided IDs.',
      ),
    );
    });
  });

  describe('fetchAuthorizedNamespaces', () => {
    it('should fetch all pages and return filtered namespaces', async () => {
      expect(secureStoreService.fetchAuthorizedNamespaces).toBeDefined();
    });
  });

  describe('fetchNamespaceResources', function () {
    it('should return all namespace resources user have access to', async function () {
      let mockResponse = [
        {
          id: '682b519e3388bf6af95954e1',
          resourcesName: 'test policy vault',
          requestId: 'NEB-SECRET-VAULT-12567',
          resourceId: 'NEB-VAULT-TOKEN-8382',
          status: 'ACTIVE',
          catalogType: 'vault-namespace',
          catalogLevel03: 'SharedServices-Secrets-Vault-Token',
          createdBy: '<EMAIL>',
          resourcesDetails: {
            request_id: '10f1e532-0f7a-39b9-8e78-2a0e4f25e9ba',
            lease_id: '',
            renewable: false,
            lease_duration: 0,
            data: {
              key_info: {
                'nebula-dev/': {
                  id: '1GbQ3',
                  path: 'nebula-stamp/nebula-dev/',
                },
                'nebula-qa/': {
                  id: 'Cn2zs',
                  path: 'nebula-stamp/nebula-qa/',
                },
                'nebula-stamp/': {
                  id: 'XJu3u',
                  path: 'nebula-stamp/nebula-stamp/',
                },
                'nebula-stg/': {
                  id: 'NmkZN',
                  path: 'nebula-stamp/nebula-stg/',
                },
              },
              keys: [
                'nebula-dev/',
                'nebula-qa/',
                'nebula-stamp/',
                'nebula-stg/',
              ],
            },
            wrap_info: null,
            warnings: null,
            auth: null,
          },
          projectId: '66967d8d75564197a15ea214',
          childId: [],
          platformContext: {
            catalogId: '660408f18fd3d3bc24ac58d8',
            domainId: '660408f18fd3d3bc24ac58d8',
            envId: '01966c73-b401-7285-bec1-b122f7118def',
          },
          tokenAccessor: 'string',
          parentID: 'NEB-RES-VAULT-NAMESPACE-1000',
          resourceType: 'Vault-Token',
          vaultTokenRequestId: 'asd',
          expiryDate: '2025-05-19T15:36:20.323Z',
          ttlInHours: 24,
          tokenRenewByNebula: true,
          namespace: 'nebula-stamp',
          createdAt: '2025-05-19T15:43:26.042Z',
          updatedAt: '2025-05-19T15:43:26.042Z',
        },
      ];
      mockResourcesService.getNamespaceResources.mockResolvedValue(
        mockResponse,
      );
      const result = await secureStoreService.fetchNamespaceResources();
      expect(result).toEqual(mockResponse);
    });
  });

  describe('secretsRotationResult', () => {
    it('should throw NotFoundException if adhoc secrets are missing', async () => {
      const mockRequest: SecretRotationUpdateRequestDto = {
        nebulaSecretId: 'secret-1',
        status: ROTATION_STATUS.COMPLETED,
      };

      const mockSecretMetaData = {
        vaultNamespace: 'namespace-1',
        vaultPath: 'path/to/secret',
        devicePasswordKey: 'device-password-key',
        resourceId: 'resource-id',
        secretId: 'secret-1',
        type: 'type',
      };

      const mockResource = {
        platformContext: {
          envId: 'env-id',
        },
      };

      const mockAdhocResponse = {
        data: {
          data: null,
        },
      };

      secretsMetadataRepositoryRepo.getSecretsMetaDataBySecretId.mockResolvedValue(
        mockSecretMetaData,
      );

      mockResourcesService.getResourceByResourceId.mockResolvedValue(
        mockResource,
      );

      mockConfigService.get.mockReturnValue('adhoc-value');

      mockSecretsManagmentServiceApi.get.mockResolvedValueOnce(
        mockAdhocResponse,
      );

      await expect(
        secureStoreService.secretsRotationResult(mockRequest),
      ).rejects.toThrow(new NotFoundException('Secrets not found.'));

      expect(
        secretsMetadataRepositoryRepo.getSecretsMetaDataBySecretId,
      ).toHaveBeenCalledWith('secret-1');

      expect(mockSecretsManagmentServiceApi.get).toHaveBeenCalledWith(
        'secrets',
        {
          params: {
            path: 'adhoc-value/secret-1',
            namespace: 'adhoc-value',
          },
        },
      );
    });

    it('should throw NotFoundException if secretId is mising', async () => {
      const mockRequest: SecretRotationUpdateRequestDto = {
        nebulaSecretId: 'secret-1',
        status: ROTATION_STATUS.COMPLETED,
      };

      const mockSecretMetaData = null;

      secretsMetadataRepositoryRepo.getSecretsMetaDataBySecretId.mockResolvedValue(
        mockSecretMetaData,
      );

      mockConfigService.get.mockReturnValue('adhoc-value');

      await expect(
        secureStoreService.secretsRotationResult(mockRequest),
      ).rejects.toThrow(
        new NotFoundException(
          `Secret with id ${mockRequest.nebulaSecretId} not found.`,
        ),
      );
    });
  });

  describe('fetchVaultSecrets', () => { });

  describe('fetchNamespaceData', () => {
    it('should return children when type is FOLDER', async () => {
      const namespace = 'test/folder';
      const mockChildren = [{ name: 'child1' }, { name: 'child2' }];

      // Mock fetchChildren to return mockChildren
      secureStoreService.fetchChildren = jest
        .fn()
        .mockResolvedValue(mockChildren);

      const result = await secureStoreService.fetchNamespaceData(
        namespace,
        NameSpaceType.FOLDER,
      );

      expect(secureStoreService.fetchChildren).toHaveBeenCalledWith(namespace);
      expect(result).toEqual(mockChildren);
    });

    it('should return extracted paths when type is PATH', async () => {
      const namespace = 'test/path';
      const mockDirectories = [{ name: 'dir1' }, { name: 'dir2' }];
      const mockExtractedPaths = ['path1', 'path2'];

      // Mock fetchChildren and extractPathsFromDirectory
      secureStoreService.fetchChildren = jest
        .fn()
        .mockResolvedValue(mockDirectories);
      secureStoreService.extractPathsFromDirectory = jest
        .fn()
        .mockReturnValue(mockExtractedPaths);

      const result = await secureStoreService.fetchNamespaceData(
        namespace,
        NameSpaceType.PATH,
      );

      expect(secureStoreService.fetchChildren).toHaveBeenCalledWith(namespace);
      expect(secureStoreService.extractPathsFromDirectory).toHaveBeenCalledWith(
        mockDirectories,
      );
      expect(result).toEqual(mockExtractedPaths);
    });

    it('should throw NotImplementedException when type is SECRET', async () => {
      const namespace = 'test/path';
      const mockDirectories = [{ name: 'dir1' }, { name: 'dir2' }];
      const mockExtractedPaths = ['path1', 'path2'];

      // Mock fetchChildren and extractPathsFromDirectory
      secureStoreService.fetchChildren = jest
        .fn()
        .mockResolvedValue(mockDirectories);
      secureStoreService.extractPathsFromDirectory = jest
        .fn()
        .mockReturnValue(mockExtractedPaths);

      await expect(
        secureStoreService.fetchNamespaceData(
        namespace,
        NameSpaceType.SECRET,
      )
      ).rejects.toThrow(
        new NotImplementedException('This is not implemented yet')
      );
    });

  });


  describe('fetchVaultSecretsPaginated', () => {
    it('should return secrets with paginations', async () => {
      const mockSecretMetaData = {
        items: [{
          vaultNamespace: 'namespace-1',
          vaultPath: 'path/to/secret',
          devicePasswordKey: 'device-password-key',
          resourceId: 'resource-id',
          secretId: 'secret-1',
          type: 'type',
        }]
      };

      let mockResponse = {
        items:
          [
            {
              active: undefined,
              expiryDate: undefined,
              id: undefined,
              key: "device-password-key",
              lastDeviceSyncStatus: undefined,
              nextRotationDate: undefined,
              notifyBeforeTokenExpiry: undefined,
              path: "path/to/secret",
              policyId: undefined,
              rotationType: undefined,
              secretId: "secret-1",
              secretTTLInHours: undefined,
              "status": undefined,
              type: "Normal",
              updatedAt: undefined,
              updatedBy: undefined,
              value: undefined
            }],
        pageInfo: undefined
      }
      

      secretsMetadataRepositoryRepo.getSecretsMetaDataPaginated.mockResolvedValue(mockSecretMetaData);
      mockSecretsManagmentServiceApi.get.mockResolvedValue([mockSecretMetaData]);
      const resultWithPagination = await secureStoreService.fetchVaultSecretsPaginated({ path: "path", namespace: 'namespace1' }, {}, true);
      expect(resultWithPagination).toEqual(mockResponse);
      mockSecretsManagmentServiceApi.get.mockResolvedValue(mockSecretMetaData.items);
      const resultWithoutPagination = await secureStoreService.fetchVaultSecretsPaginated({ path: "path", namespace: 'namespace1' }, {}, null);
      expect(resultWithoutPagination).toEqual(mockResponse.items);
    });

    it('should throw internalservererror', async () => {
      const mockSecretMetaData = {
        items: [{
          vaultNamespace: 'namespace-1',
          vaultPath: 'path/to/secret',
          devicePasswordKey: 'device-password-key',
          resourceId: 'resource-id',
          secretId: 'secret-1',
          type: 'type',
        }]
      };

      secretsMetadataRepositoryRepo.getSecretsMetaDataPaginated.mockResolvedValue(mockSecretMetaData);
      mockSecretsManagmentServiceApi.get.mockRejectedValue(null);
      await expect(secureStoreService.fetchVaultSecretsPaginated({ path: "path", namespace: 'namespace1' }, {}, true))
      .rejects.toThrow(
        new InternalServerErrorException(
          'Error occured while fetching Secrets from Vault',
        ),
      );
    });

  });

  describe('createRotateSecretRequest', () => {
    it('rotateSecrets should be successful', async () => {
      const rotateRequest: RotateSecretRequestDto[] = [
        {
          secretId: 'NEB-VAULT-ROT-SECRET-001',
          newPassword: 'TestnewPassword',
        } as RotateSecretRequestDto,
      ];

      const responseMock: RotateSecretResponseDto = {
        message: 'Secret rotation request raised successfully.',
      };

      const secretMetaMock = [{
        id: "6874e0909a0158ca0752c916",
        secretId: "NEB-VAULT-ROT-SECRET-001",
        path: "CO/CISCO/123",
        key: "sss",
        value: "de91c60ae8861388529a79ac92bb9feb2313c980b9eaf9bc9dd0cc776dd0f9bb",
        type: SecretType.ROTATABLE_SECRET,
        status: "SUCCESS",
        rotationType: "Manual",
        secretTTLInHours: 720,
        policyId: "NEB-VAULT-PASSWORD-POLICY-12",
        expiryDate: '2025-07-14T11:31:11.713Z',
        nextRotationDate: '2025-07-14T18:30:00.000Z',
        updatedAt: '2025-07-14T10:48:48.334Z',
        lastDeviceSyncStatus: "NOT-STARTED",
        active: true,
        notifyBeforeTokenExpiry: true,
        sourceSystem: ['keyguard'],
        devicePasswordKey: 'sss',
        linkedSecret: { devicePasswordKey: 'userName32' }
      }]

      let mockGetApiRes = {
        data: {
          data: {
            data: {
              sss: 'kkjhj3',
              userName32: 'string2',

            },
          },
        },
      };

      secretsMetadataRepositoryRepo.getAllSecretsMetaDataBySecretIds.mockResolvedValue(secretMetaMock);
      mockSecretsManagmentServiceApi.get.mockResolvedValue(mockGetApiRes);
      (mockActionService as any).create.mockResolvedValue({
        actionId: 'token123',
        entityId: 'NEB-VAULT-ROT-SECRET-001',
      });
      const result = await secureStoreService.createRotateSecretRequest(rotateRequest)
      expect(result).toEqual(responseMock);
    });
    it('rotateSecrets should throw if secrets is not available in database', async () => {
      const rotateRequest: RotateSecretRequestDto[] = [
        {
          secretId: 'NEB-VAULT-ROT-SECRET-001',
          newPassword: 'TestnewPassword',
        } as RotateSecretRequestDto,
      ];

      const responseMock: RotateSecretResponseDto = {
        message: 'Secret rotation request raised successfully.',
      };

      const secretMetaMock = [{
        id: "6874e0909a0158ca0752c916",
        secretId: "NEB-VAULT-ROT-SECRET-001",
        path: "CO/CISCO/123",
        key: "sss",
        value: "de91c60ae8861388529a79ac92bb9feb2313c980b9eaf9bc9dd0cc776dd0f9bb",
        type: SecretType.ROTATABLE_SECRET,
        status: "SUCCESS",
        rotationType: "Manual",
        secretTTLInHours: 720,
        policyId: "NEB-VAULT-PASSWORD-POLICY-12",
        expiryDate: '2025-07-14T11:31:11.713Z',
        nextRotationDate: '2025-07-14T18:30:00.000Z',
        updatedAt: '2025-07-14T10:48:48.334Z',
        lastDeviceSyncStatus: "NOT-STARTED",
        active: true,
        notifyBeforeTokenExpiry: true,
        sourceSystem: ['keyguard'],
        devicePasswordKey: 'sss',
        linkedSecret: { devicePasswordKey: 'userName32' }
      }]

      let mockGetApiRes = {
        data: {
          data: {
            data: {
              sss: 'kkjhj3',
              userName32: 'string2',

            },
          },
        },
      };

      secretsMetadataRepositoryRepo.getAllSecretsMetaDataBySecretIds.mockResolvedValue([]);
      // mockSecretsManagmentServiceApi.get.mockResolvedValue(mockGetApiRes);
      //   (mockActionService as any).create.mockResolvedValue({
      //   actionId: 'token123',
      //   entityId: 'NEB-VAULT-ROT-SECRET-001',
      // });
      // const result = await secureStoreService.createRotateSecretRequest(rotateRequest)
      // expect(result).toEqual(responseMock);
      await expect(secureStoreService.createRotateSecretRequest(rotateRequest)).rejects.toThrow(
        new NotFoundException(
          `Secrets not found for the following IDs: NEB-VAULT-ROT-SECRET-001`,
        )
      );
    });

    it('rotateSecrets throw error if secrets are inactive', async () => {
      const rotateRequest: RotateSecretRequestDto[] = [
        {
          secretId: 'NEB-VAULT-ROT-SECRET-001',
          newPassword: 'TestnewPassword',
        } as RotateSecretRequestDto,
      ];

      const responseMock: RotateSecretResponseDto = {
        message: 'Secrets rotated successfully',
      };

      const secretMetaMock = [{
        id: "6874e0909a0158ca0752c916",
        secretId: "NEB-VAULT-ROT-SECRET-001",
        path: "CO/CISCO/123",
        key: "sss",
        value: "de91c60ae8861388529a79ac92bb9feb2313c980b9eaf9bc9dd0cc776dd0f9bb",
        type: SecretType.ROTATABLE_SECRET,
        status: "SUCCESS",
        rotationType: "Manual",
        secretTTLInHours: 720,
        policyId: "NEB-VAULT-PASSWORD-POLICY-12",
        expiryDate: '2025-07-14T11:31:11.713Z',
        nextRotationDate: '2025-07-14T18:30:00.000Z',
        updatedAt: '2025-07-14T10:48:48.334Z',
        lastDeviceSyncStatus: "NOT-STARTED",
        active: false,
        notifyBeforeTokenExpiry: true,
      }]

      secretsMetadataRepositoryRepo.getAllSecretsMetaDataBySecretIds.mockResolvedValue(secretMetaMock)
      await expect(secureStoreService.createRotateSecretRequest(rotateRequest)).rejects.toThrow(
        new BadRequestException(
          `secretId(s) NEB-VAULT-ROT-SECRET-001 cannot be rotated because they are inactive or not linked to a device.`,
        ),
      );
    });

  });

  describe('extractPathsFromDirectory', () => {
    it('should extract paths from top-level SecretDTO children', () => {
      const directories: NamespaceResponseDTO = {
        namespace: 'test',
        children: [
          { key: 's1', type: 'secret', path: 'secret/path1', children: [] },
          { key: 's2', type: 'secret', path: 'secret/path2', children: [] },
        ],
      };

      const result = secureStoreService.extractPathsFromDirectory(directories);

      expect(result).toEqual([
        { label: 'secret/path1', value: 'secret/path1' },
        { label: 'secret/path2', value: 'secret/path2' },
      ]);
    });

    it('should extract paths from nested SecretDTO inside FolderDTO', () => {
      const directories: NamespaceResponseDTO = {
        namespace: 'test',
        children: [
          {
            key: 'f1',
            type: 'folder',
            children: [
              {
                key: 's1',
                type: 'secret',
                path: 'nested/secret1',
                children: [],
              },
              {
                key: 'f2',
                type: 'folder',
                children: [
                  {
                    key: 's2',
                    type: 'secret',
                    path: 'deep/nested/secret2',
                    children: [],
                  },
                ],
              },
            ],
          },
        ],
      };

      const result = secureStoreService.extractPathsFromDirectory(directories);

      expect(result).toEqual([
        { label: 'nested/secret1', value: 'nested/secret1' },
        { label: 'deep/nested/secret2', value: 'deep/nested/secret2' },
      ]);
    });

    it('should return an empty array if no secrets are present', () => {
      const directories: NamespaceResponseDTO = {
        namespace: 'test',
        children: [
          {
            key: 'f1',
            type: 'folder',
            children: [],
          },
        ],
      };

      const result = secureStoreService.extractPathsFromDirectory(directories);

      expect(result).toEqual([]);
    });

    it('should handle mixed FolderDTO and SecretDTO at various levels', () => {
      const directories: NamespaceResponseDTO = {
        namespace: 'test',
        children: [
          {
            key: 'f1',
            type: 'folder',
            children: [
              {
                key: 's1',
                type: 'secret',
                path: 'folder1/secret1',
                children: [],
              },
              {
                key: 'f2',
                type: 'folder',
                children: [
                  {
                    key: 's2',
                    type: 'secret',
                    path: 'folder2/secret2',
                    children: [],
                  },
                ],
              },
            ],
          },
          {
            key: 's3',
            type: 'secret',
            path: 'root/secret3',
            children: [],
          },
        ],
      };

      const result = secureStoreService.extractPathsFromDirectory(directories);

      expect(result).toEqual([
        { label: 'folder1/secret1', value: 'folder1/secret1' },
        { label: 'folder2/secret2', value: 'folder2/secret2' },
        { label: 'root/secret3', value: 'root/secret3' },
      ]);
    });
  });

  describe('fetchChildren', () => {
    it('should return FolderDTO with nested secrets and folders', async () => {
      const namespace = 'test-namespace';

      const mockMetaDataRoot = {
        data: {
          keys: ['secret1', 'folder1/'],
        },
      };

      const mockMetaDataFolder1 = {
        data: {
          keys: ['nestedSecret'],
        },
      };

      secureStoreService.fetchMetaData = jest
        .fn()
        .mockResolvedValueOnce(mockMetaDataRoot)
        .mockResolvedValueOnce(mockMetaDataFolder1);

      const result = await secureStoreService.fetchChildren(namespace);

      expect(result).toEqual({
        namespace: namespace,
        type: 'folder',
        children: [
          {
            key: 'secret1',
            type: 'secret',
            path: '/secret1',
            children: [],
          },
          {
            key: 'folder1',
            type: 'folder',
            path: '/folder1/',
            children: [
              {
                key: 'nestedSecret',
                type: 'secret',
                path: '/folder1/nestedSecret',
                children: [],
              },
            ],
          },
        ],
      });
    });

    it('should return empty children if no keys are present', async () => {
      let mockApiResponse = {
        data: {
          data: { keys: [] },
        },
      };

      mockSecretsManagmentServiceApi.get.mockReturnValue(mockApiResponse);
      const result = await secureStoreService.fetchChildren('empty-namespace');

      expect(result.children).toEqual([]);
    });
  });

  describe('CreateNameSpaceRequest', () => {
    it('should validate a correct DTO instance', async () => {
      const dto = {
        namespaceName: 'test-namespace',
        platformContext: {
          platform: 'aws',
          region: 'us-east-1',
        },
        vaultPolicies: [
          {
            enable: true,
            policyName: 'policy1',
            role: 'admin',
          },
        ],
        vaultAppRoles: [
          {
            policyName: 'policy1',
            appRoleName: 'app-role-1',
          },
        ],
        tokenTTL: 3600,
        autoRenewTokenOnExpiry: true,
        notifyBeforeTokenExpiry: false,
        disks: ['disk1', 'disk2'],
        deeplinkUrl: 'https://example.com',
      };

      const instance = plainToInstance(CreateNameSpaceRequestDto, dto, {
        enableImplicitConversion: true,
      });

      const errors = await validate(instance, {
        whitelist: true,
        forbidNonWhitelisted: true,
      });
      //Validation updated
      expect(errors.length).toBe(2);
    });

    it('should fail validation if required fields are missing', async () => {
      const dto = {
        vaultPolicies: [],
        tokenTTL: null,
        autoRenewTokenOnExpiry: true,
        notifyBeforeTokenExpiry: false,
        deeplinkUrl: '',
      };

      const instance = plainToInstance(CreateNameSpaceRequestDto, dto);
      const errors = await validate(instance);

      expect(errors.length).toBeGreaterThan(0);
      const errorProps = errors.map((e) => e.property);
      expect(errorProps).toContain('namespaceName');
      expect(errorProps).toContain('platformContext');
      expect(errorProps).toContain('tokenTTL');
      expect(errorProps).toContain('deeplinkUrl');
    });
  });

  it('should call renewBulkTokens and handle response', async () => {
    const secretIds = ['id1', 'id2'];
    const mockApiResponse = { success: true };

    mockSecretsManagmentServiceApi.post.mockReturnValue(
      Promise.resolve(mockApiResponse),
    );

    const result = await secureStoreService.renewBulkTokens(secretIds);

    expect(mockSecretsManagmentServiceApi.post).toHaveBeenCalledWith(
      'tokens/renew-tokens',
      secretIds,
    );
  });

  it('should create a vault token secret', async () => {
    const dto = {
      type: FetchSecretType.VAULT_TOKEN,
      vaultKey: 'tokenKey',
      vaultPassword: 'tokenPass',
      userNameKey: '',
      userNamePassword: '',
      description: 'Vault token',
      expiryDate: '2025-06-20',
      tokenRenewByNebula: true,
      notifyBeforeTokenExpiry: true,
      nextRotationDate: '2025-06-20',
    };

    mockResourcesRepository.getResourcesByNamespace.mockResolvedValue({
      resourceId: 'res1',
    });
    mockResourcesRepository.getResourceByResourcesName.mockResolvedValue({
      resourceId: 'res1',
    });
    secretsMetadataRepositoryRepo.generateSecretId.mockResolvedValue(
      'token123',
    );
    mockConfigService.get.mockReturnValue('master/folder');

    await secureStoreService.createVaultSecrets('namespace1', dto);

    expect(secretsMetadataRepositoryRepo.create).toHaveBeenCalledWith(
      expect.objectContaining({
        secretId: 'token123',
        type: SecretType.VAULT_TOKEN,
      }),
    );
  });

  it('should create a ROTATABLE SECRET', async () => {
    const dto = {
      type: FetchSecretType.ROTATABLE_SECRET,
      vaultKey: 'tokenKey1',
      vaultPassword: 'tokenPass',
      userNameKey: '',
      userNamePassword: '',
      description: 'Vault token',
      expiryDate: '2025-06-20',
      tokenRenewByNebula: true,
      notifyBeforeTokenExpiry: true,
      nextRotationDate: '2025-06-20',
    };

    mockResourcesRepository.getResourcesByNamespace.mockResolvedValue({
      resourceId: 'res1',
    });
    mockResourcesRepository.getResourceByResourcesName.mockResolvedValue({
      resourceId: 'res1',
    });
    secretsMetadataRepositoryRepo.generateSecretId.mockResolvedValue(
      'token123',
    );

    (mockActionService as any).create.mockResolvedValue({
      actionId: 'token123',
    });
    (mockActionService as any).update.mockResolvedValue();
    mockConfigService.get.mockReturnValue('master/folder');

    let mockApiResponse = {
      data: {
        data: {
          data: {
            tokenKey: 'kkjhj3',
            userName32: 'string2',
          },
        },
      },
    };

    secretsMetadataRepositoryRepo.getSecretsMetaData.mockResolvedValue([]);
    mockSecretsManagmentServiceApi.get.mockReturnValue(mockApiResponse);
    mockSecretsManagmentServiceApi.post.mockReturnValue(mockApiResponse);
    const result = await secureStoreService.createVaultSecrets(
      'dummyNameSpace',
      dto,
      'dev/test'
    );

    expect(result.message).toBe('Secret is created successfully.');
  });

  it('should create a NORMAL SECRET', async () => {
    const dto = {
      type: FetchSecretType.NORMAL_SECRET,
      vaultKey: 'tokenKey1',
      vaultPassword: 'tokenPass',
      userNameKey: '',
      userNamePassword: '',
      description: 'Vault token',
      expiryDate: '2025-06-20',
      tokenRenewByNebula: true,
      notifyBeforeTokenExpiry: true,
      nextRotationDate: '2025-06-20',
    };

    mockResourcesRepository.getResourcesByNamespace.mockResolvedValue({
      resourceId: 'res1',
    });
    secretsMetadataRepositoryRepo.generateSecretId.mockResolvedValue(
      'token123',
    );

    (mockActionService as any).create.mockResolvedValue({
      actionId: 'token123',
    });
    (mockActionService as any).update.mockResolvedValue();
    mockConfigService.get.mockReturnValue('master/folder');

    let mockApiResponse = {
      data: {
        data: {
          data: {
            tokenKey: 'kkjhj3',
            userName32: 'string2',
          },
        },
      },
    };

    secretsMetadataRepositoryRepo.getSecretsMetaData.mockResolvedValue([]);
    mockSecretsManagmentServiceApi.get.mockReturnValue(mockApiResponse);
    mockSecretsManagmentServiceApi.post.mockReturnValue(mockApiResponse);
    const result = await secureStoreService.createVaultSecrets(
      'namespace1',
      dto,
    );

    expect(result.message).toBe('Secret is created successfully.');
  });

  it('Error for duplicate vaultKey', async () => {
    const dto = {
      type: FetchSecretType.NORMAL_SECRET,
      vaultKey: 'tokenKey1',
      vaultPassword: 'tokenPass',
      userNameKey: '',
      userNamePassword: '',
      description: 'Vault token',
      expiryDate: '2025-06-20',
      tokenRenewByNebula: true,
      notifyBeforeTokenExpiry: true,
      nextRotationDate: '2025-06-20',
    };

    mockResourcesRepository.getResourcesByNamespace.mockResolvedValue({
      resourceId: 'res1',
    });
    secretsMetadataRepositoryRepo.generateSecretId.mockResolvedValue(
      'token123',
    );

    mockConfigService.get.mockReturnValue('master/folder');

    let mockApiResponse = {
      data: {
        data: {
          data: {
            tokenKey1: 'kkjhj3',
            userName32: 'string2',
          },
        },
      },
    };

    let mockSecretsMetadata = [
      {
        _id: '684810ed9c64fd9fdacd7bb6',
        secretId: 'NEB-VAULT-NOR-SECRET-1006',
        type: 'NORMAL-SECRET',
        description: 'string',
        resourceId: 'NEB-VAULT-TOKEN-8382',
        vaultNamespace: 'nebula-stamp',
        vaultPath: 'dev/test',
        devicePasswordKey: 'userName32',
        policyId: 'string',
        status: 'SUCCESS',
        active: true,
        error: [],
        createdAt: '2025-06-10T11:03:09.044Z',
        updatedAt: '2025-06-10T11:03:09.044Z',
      },
    ];

    mockSecretsManagmentServiceApi.get.mockReturnValue(mockApiResponse);
    secretsMetadataRepositoryRepo.getSecretsMetaData.mockResolvedValue(mockSecretsMetadata);

    await expect(
      secureStoreService.createVaultSecrets('namespace1', dto),
    ).rejects.toThrow(
      new BadRequestException(
        'Secrets key is already present for the specific path.',
      ),
    );

  });

  it('Error for no policy found by policyId', async () => {
    const dto = {
      type: FetchSecretType.NORMAL_SECRET,
      vaultKey: 'tokenKey1',
      policyId: '123',
      vaultPassword: 'tokenPass',
      userNameKey: '',
      userNamePassword: '',
      description: 'Vault token',
      expiryDate: '2025-06-20',
      tokenRenewByNebula: true,
      notifyBeforeTokenExpiry: true,
      nextRotationDate: '2025-06-20',
    };

    mockResourcesRepository.getResourcesByNamespace.mockResolvedValue({
      resourceId: 'res1',
    });
    secretsMetadataRepositoryRepo.generateSecretId.mockResolvedValue(
      'token123',
    );

    mockConfigService.get.mockReturnValue('master/folder');
    mockSecretsPoliciesService.getPolicy.mockRejectedValue(null);
    let mockApiResponse = {
      data: {
        data: {
          data: {
            tokenKey1: 'kkjhj3',
            userName32: 'string2',
          },
        },
      },
    };

    let mockSecretsMetadata = [
      {
        _id: '684810ed9c64fd9fdacd7bb6',
        secretId: 'NEB-VAULT-NOR-SECRET-1006',
        type: 'NORMAL-SECRET',
        description: 'string',
        resourceId: 'NEB-VAULT-TOKEN-8382',
        vaultNamespace: 'nebula-stamp',
        vaultPath: 'dev/test',
        devicePasswordKey: 'userName32',
        policyId: 'string',
        status: 'SUCCESS',
        active: true,
        error: [],
        createdAt: '2025-06-10T11:03:09.044Z',
        updatedAt: '2025-06-10T11:03:09.044Z',
      },
    ];

    mockSecretsManagmentServiceApi.get.mockReturnValue(mockApiResponse);
    secretsMetadataRepositoryRepo.getSecretsMetaData.mockResolvedValue(mockSecretsMetadata);

    await expect(
      secureStoreService.createVaultSecrets('namespace1', dto),
    ).rejects.toThrow(
      new NotFoundException(
        `Unable to find secret policy for input policyId`,
      )
    );
  });

  describe('cleanupTemporarySecrets', () => {
    it('should call secretsManagmentServiceApi.delete with correct parameters and return expected result', async () => {
      const mockSecretId = 'TEMP-SECRET-1234';
      const mockNamespace = 'test-namespace';
      const mockPath = 'test-path';

      // Mock config values
      mockConfigService.get = jest.fn((key) => {
        if (key === 'ADHOC_LOCATION') return mockPath;
        if (key === 'ADHOC_NAMESPACE') return mockNamespace;
        return undefined; // Add a default return for unhandled keys
      });

      // The API *returns* an object with a 'data' property
      const mockApiResponse = { data: 'deleted' };
      mockSecretsManagmentServiceApi.delete.mockResolvedValue(mockApiResponse);

      const result =
        await secureStoreService.cleanupTemporarySecrets(mockSecretId);

      expect(mockSecretsManagmentServiceApi.delete).toHaveBeenCalledWith(
        'metadata',
        {
          params: {
            path: `${mockPath}/${mockSecretId}`,
            namespace: mockNamespace,
          },
        },
      );

      // This is the key change: expect the result to be the raw data from the API response,
      // assuming withResponseErrorHandler extracts it.
      expect(result).toEqual(mockApiResponse.data); // Expect "deleted"
    });

    it('should handle errors gracefully via withResponseErrorHandler', async () => {
      const mockSecretId = 'TEMP-SECRET-ERROR';
      const mockNamespace = 'error-namespace';
      const mockPath = 'error-path';

      mockConfigService.get = jest.fn((key) => {
        if (key === 'ADHOC_LOCATION') return mockPath;
        if (key === 'ADHOC_NAMESPACE') return mockNamespace;
        return undefined; // Add a default return for unhandled keys
      });

      const error = new Error('Delete failed');
      mockSecretsManagmentServiceApi.delete.mockRejectedValue(error);

      await expect(
        secureStoreService.cleanupTemporarySecrets(mockSecretId),
      ).rejects.toThrow('Delete failed');
    });
  });
  describe('updateVaultSecretByPath', () => {
    const mockSecretId = 'SECRET-1234';
    const mockUpdateVaultSecretRequest: UpdateVaultSecretRequestDto = {
      vaultKey: 'dbPassword',
      vaultPassword: 'superSecret123!',
      userNameKey: 'dbUser',
      userNamePassword: 'admin123!',
      notifyBeforeTokenExpiry: true,
      description: 'Test update',
      policyId: 'POLICY-5678',
      secretTTLInHours: 24,
      nextRotationDate: '2025-07-01T10:00:00Z',
      rotationType: RotationType.AUTO,
    };

    const mockSecretMetaData = {
      vaultPath: 'dev/test',
      vaultNamespace: 'namespace-1',
      active: true,
    };

    const mockSecretDataResponse = {
      data: {
        data: {
          key1: 'value1',
        },
      },
    };

    const mockUpdateMetaData = [
      { secretId: mockSecretId, updated: true, active: true },
    ];

    const mockSecretData = {
      key: 'value',
    };

    beforeEach(() => {
      secretsMetadataRepositoryRepo.getSecretsMetaDataBySecretId.mockReset();
      mockSecretsManagmentServiceApi.get.mockReset();
      mockSecretsManagmentServiceApi.post.mockReset();
      secretsMetadataRepositoryRepo.createQueryForUpdateMetaData = jest.fn();
      secretsMetadataRepositoryRepo.bulkUpdateSecretsMetaDataBySecretId =
        jest.fn();
    });

    it('should update a secret successfully', async () => {
      secretsMetadataRepositoryRepo.getSecretsMetaDataBySecretId.mockResolvedValue(
        mockSecretMetaData,
      );
      mockSecretsManagmentServiceApi.get.mockResolvedValue(
        mockSecretDataResponse,
      );
      secretsMetadataRepositoryRepo.createQueryForUpdateMetaData.mockResolvedValue(
        {
          updateMetaData: mockUpdateMetaData,
          secretData: mockSecretData,
          active: true,
        },
      );

      const mockedGetUserContext = helpers.getUserContext as jest.Mock;

      mockedGetUserContext.mockReturnValue({ userId: 'mock-user' });

      mockSecretsManagmentServiceApi.post.mockResolvedValue({});

      const result = await secureStoreService.updateVaultSecretByPath(
        mockSecretId,
        mockUpdateVaultSecretRequest,
      );

      expect(result).toEqual({ message: 'Secret is updated successfully.' });
      expect(
        secretsMetadataRepositoryRepo.getSecretsMetaDataBySecretId,
      ).toHaveBeenCalledWith(mockSecretId);
      expect(mockSecretsManagmentServiceApi.get).toHaveBeenCalled();
      expect(mockSecretsManagmentServiceApi.post).toHaveBeenCalled();
      expect(
        secretsMetadataRepositoryRepo.bulkUpdateSecretsMetaDataBySecretId,
      ).toHaveBeenCalledWith(mockUpdateMetaData);
    });

    it('should throw NotFoundException if secret metadata is not found', async () => {
      secretsMetadataRepositoryRepo.getSecretsMetaDataBySecretId.mockResolvedValue(
        null,
      );

      await expect(
        secureStoreService.updateVaultSecretByPath(
          mockSecretId,
          mockUpdateVaultSecretRequest,
        ),
      ).rejects.toThrow(NotFoundException);
    });

    it('should handle error during secretsManagmentServiceApi.get call', async () => {
      secretsMetadataRepositoryRepo.getSecretsMetaDataBySecretId.mockResolvedValue(
        mockSecretMetaData,
      );
      mockSecretsManagmentServiceApi.get.mockRejectedValue(
        new Error(
          'secretId SECRET-1234 cannot be updated because it is inactive.',
        ),
      );

      await expect(
        secureStoreService.updateVaultSecretByPath(
          mockSecretId,
          mockUpdateVaultSecretRequest,
        ),
      ).rejects.toThrow(
        'secretId SECRET-1234 cannot be updated because it is inactive.',
      );
    });

    it('should handle error during secretsManagmentServiceApi.post call', async () => {
      secretsMetadataRepositoryRepo.getSecretsMetaDataBySecretId.mockResolvedValue(
        mockSecretMetaData,
      );
      mockSecretsManagmentServiceApi.get.mockResolvedValue(
        mockSecretDataResponse,
      );
      secretsMetadataRepositoryRepo.createQueryForUpdateMetaData.mockResolvedValue(
        {
          updateMetaData: mockUpdateMetaData,
          secretData: mockSecretData,
        },
      );
      mockSecretsManagmentServiceApi.post.mockRejectedValue(
        new Error(
          'secretId SECRET-1234 cannot be updated because it is inactive.',
        ),
      );

      await expect(
        secureStoreService.updateVaultSecretByPath(
          mockSecretId,
          mockUpdateVaultSecretRequest,
        ),
      ).rejects.toThrow(
        'secretId SECRET-1234 cannot be updated because it is inactive.',
      );
    });
  });
  describe('generatePassword', () => {
    it('should call secretsManagmentServiceApi.get with correct URL and params', async () => {
      const namespace = 'test-namespace';
      const data = { length: 16 };
      const mockResponse = { password: 'abc@123XYZ' }; // the .data part of the response
      mockSecretsManagmentServiceApi.get.mockResolvedValue({
        data: mockResponse,
      });

      const result = await secureStoreService.generatePassword(
        namespace,
        'test',
      );

      expect(mockSecretsManagmentServiceApi.get).toHaveBeenCalledWith(
        'vault/test/password/generate',
        { params: { namespace } },
      );
      expect(result).toEqual(mockResponse);
    });
  });

  describe('encryptPassword', () => {
    it('should return encrypted password', async () => {
      const password = 'plainPassword';
      const encrypted = '5021abeb40eda90081b396f6c189e526';

      (mockEncryptionService as any).encrypt.mockResolvedValue(encrypted);
      const result = await secureStoreService.encryptPassword(password);

      expect(result).toBe(encrypted);
      expect((mockEncryptionService as any).encrypt).toHaveBeenCalledWith(
        password,
      );
    });
  });

  describe('decryptPassword', () => {
    it('should return decrypted password', async () => {
      const encryptedText = 'encryptedPassword';
      const decrypted = 'plainPassword';

      (mockEncryptionService as any).decrypt.mockReturnValue(decrypted);

      const result = await secureStoreService.decryptPassword(encryptedText);
      expect(result).toBe(decrypted);
      expect((mockEncryptionService as any).decrypt).toHaveBeenCalledWith(
        encryptedText,
      );
    });
  });

  describe('fetchSecretsDetails', () => {
    it('should return secret details for NORMAL-SECRET type', async () => {
      const secretId = 'mock-id';
      const mockMetaData = {
        secretId,
        vaultPath: 'path/to/secret',
        vaultNamespace: 'test-namespace',
        devicePasswordKey: 'device-password-key',
        resourceId: 'resource-id',
        type: SecretType.NORMAL_SECRET,
      };
      const mockSecretData = {
        data: {
          data: {
            data: {
              'device-password-key': 'secret-value',
            },
          },
        },
      };
      secretsMetadataRepositoryRepo.getSecretsMetaDataBySecretId.mockResolvedValue(
        Promise.resolve(mockMetaData),
      );
      mockResourcesRepository.findOneByResourceId = jest.fn();
      mockSecretsManagmentServiceApi.get.mockResolvedValue(mockSecretData);

      const result = await secureStoreService.fetchSecretsDetails(secretId);
      expect(result.vaultKey).toEqual('device-password-key');
      expect(result.vaultPassword).toEqual('secret-value');
      expect(result.resourceId).toEqual(mockMetaData.resourceId);
    });

    it('should return secret details for ROTATABLE-SECRET type', async () => {
      const secretId = 'rot-secret';
      const mockMetaData = {
        secretId,
        vaultPath: 'rotatable/path',
        vaultNamespace: 'ns-1',
        devicePasswordKey: 'vaultKey',
        resourceId: 'resource-id',
        deviceUserNameSecretId: 'username-id',
        rotationType: RotationType.AUTO,
        type: SecretType.ROTATABLE_SECRET,
        expiryDate: new Date(),
        notificationEnabled: true,
        tokenTTLInHours: 24,
        nextRotationDate: new Date(),
        policyId: 'policy-id',
      };
      const mockUserSecretMeta = {
        devicePasswordKey: 'userNameKey',
      };
      const mockSecretData = {
        data: {
          data: {
            data: {
              vaultKey: 'vault-password',
              userNameKey: 'username-password',
            },
          },
        },
      };
      const mockPolicy = {
        policyName: 'test-policy',
      };
      secretsMetadataRepositoryRepo.getSecretsMetaDataBySecretId
        .mockResolvedValueOnce(mockMetaData)
        .mockResolvedValueOnce(mockUserSecretMeta);
      mockResourcesRepository.findOneByResourceId = jest.fn();
      mockSecretsManagmentServiceApi.get.mockResolvedValue(mockSecretData);
      mockSecretsPoliciesService.getPolicy.mockResolvedValue(mockPolicy);

      const result = await secureStoreService.fetchSecretsDetails(secretId);

      expect(result.vaultKey).toBe('vaultKey');
      expect(result.vaultPassword).toBe('vault-password');
      expect(result.userNameKey).toBe('userNameKey');
      expect(result.userNamePassword).toBe('username-password');
      // expect(result.policyName).toBe('test-policy');
    });
  });

  it('should create a namespace request and return processing message', async () => {
    const mockCreateNamespaceDto: CreateNameSpaceRequestDto = {
      namespaceName: 'test-namespace',
      adminGroup: ['string'],
      platformContext: {
        catalogId: '',
        domainId: '',
        envId: '',
      },
      vaultPolicies: [
        {
          enable: true,
          policyName: 'policy-1',
          role: 'admin',
        },
      ],
      vaultAppRoles: [
        {
          policyName: 'policy-1',
          appRoleName: 'approle-1',
        },
      ],
      tokenTTL: 3600,
      autoRenewTokenOnExpiry: true,
      notifyBeforeTokenExpiry: false,
      disks: ['disk1', 'disk2'],
      deeplinkUrl: 'https://example.com/deeplink',
    };

    const dbResponse = {
      id: '123',
      serviceRequestId: 'req-456',
    };

    secureStoreService.fetchAuthorizedNamespaces = jest.fn().mockResolvedValue({
      items: [], // No existing namespaces
    });

    (mockAssetsService.create as jest.Mock).mockResolvedValue(dbResponse);
    (
      mockAssetsService.queueIfApprovalNotRequired as jest.Mock
    ).mockResolvedValue(true);

    const result = await secureStoreService.createNameSpaceRequest(
      mockCreateNamespaceDto,
    );

    expect(result).toEqual({
      id: '123',
      serviceRequestId: 'req-456',
      message: 'Request submitted for processing',
    });
  });

  it('should return approval message if not queued', async () => {
    const dto: CreateNameSpaceRequestDto = {
      namespaceName: 'test-namespace',
      adminGroup: ['string'],
      platformContext: {
        catalogId: '',
        domainId: '',
        envId: '',
      },
      vaultPolicies: [
        {
          enable: true,
          policyName: 'policy-1',
          role: 'admin',
        },
      ],
      vaultAppRoles: [
        {
          policyName: 'policy-1',
          appRoleName: 'approle-1',
        },
      ],
      tokenTTL: 3600,
      autoRenewTokenOnExpiry: true,
      notifyBeforeTokenExpiry: false,
      disks: ['disk1', 'disk2'],
      deeplinkUrl: 'https://example.com/deeplink',
    };

    const dbResponse = {
      id: '789',
      serviceRequestId: 'req-101',
    };

    (mockAssetsService.create as jest.Mock).mockResolvedValue(dbResponse);
    secureStoreService.fetchAuthorizedNamespaces = jest.fn().mockResolvedValue({
      items: [], // No existing namespaces
    });
    (
      mockAssetsService.queueIfApprovalNotRequired as jest.Mock
    ).mockResolvedValue(false);

    const result = await secureStoreService.createNameSpaceRequest(dto);

    expect(result.message).toBe('Request submitted for approval');
  });

  it('should throw error if requested for duplicate namespace', async () => {
    const dto: CreateNameSpaceRequestDto = {
      namespaceName: 'test-namespace',
      adminGroup: ['string'],
      platformContext: {
        catalogId: '',
        domainId: '',
        envId: '',
      },
      vaultPolicies: [
        {
          enable: true,
          policyName: 'policy-1',
          role: 'admin',
        },
      ],
      vaultAppRoles: [
        {
          policyName: 'policy-1',
          appRoleName: 'approle-1',
        },
      ],
      tokenTTL: 3600,
      autoRenewTokenOnExpiry: true,
      notifyBeforeTokenExpiry: false,
      disks: ['disk1', 'disk2'],
      deeplinkUrl: 'https://example.com/deeplink',
    };

    const dbResponse = {
      id: '789',
      serviceRequestId: 'req-101',
    };

    (mockAssetsService.create as jest.Mock).mockResolvedValue(dbResponse);
    secureStoreService.fetchAuthorizedNamespaces = jest.fn().mockResolvedValue({
      items: [{ namespaceName: 'test-namespace' }], // duplicate namespaces
    });

    await expect(secureStoreService.createNameSpaceRequest(dto)).rejects.toThrow(
      new BadRequestException(
        `Namespace with name "${dto.namespaceName}" already exists.`, // same name as dto
      ),
    );

  });

  it('should fetch authorized namespaces without envId', async () => {
    const mockResponse = {
      items: [
        {
          resourcesName: 'namespace-1',
          resourceId: 'res-123',
          status: 'ACTIVE',
          catalogType: 'IAAS',
          catalogLevel03: 'level-3',
          platformContext: { envId: 'env-1' },
          requestType: 'CREATE_NAMESPACE',
          resourcesDetails: { detail: 'info' },
        },
      ],
    };

    mockResourcesService.getPaginatedResourcesList.mockResolvedValue(
      mockResponse,
    );

    const result = await secureStoreService.fetchAuthorizedNamespaces();

    expect(mockResourcesService.getPaginatedResourcesList).toHaveBeenCalledWith(
      {
        filter: JSON.stringify({
          requestType: { equals: 'CREATE_NAMESPACE' },
        }),
      },
    );

    expect(result.items).toEqual([
      {
        namespaceName: 'namespace-1',
        resourceId: 'res-123',
        status: 'ACTIVE',
        catalogType: 'IAAS',
        catalogLevel03: 'level-3',
        platformContext: { envId: 'env-1' },
        requestType: 'CREATE_NAMESPACE',
        resourcesDetails: { detail: 'info' },
      },
    ]);
  });

  it('should include envId in filter if provided', async () => {
    const envId = 'env-123';
    const mockResponse = { items: [] };

    mockResourcesService.getPaginatedResourcesList.mockResolvedValue(
      mockResponse,
    );

    const result = await secureStoreService.fetchAuthorizedNamespaces(envId);

    expect(mockResourcesService.getPaginatedResourcesList).toHaveBeenCalledWith(
      {
        filter: JSON.stringify({
          requestType: { equals: 'CREATE_NAMESPACE' },
          'platformContext.envId': { equals: envId },
        }),
      },
    );

    expect(result.items).toEqual([]);
  });

  describe('fetchSecretsHistory', () => {
    it('should fetch history for secrets with the provided secretId', async () => {
      let mockWatchServiceGetResponse = [
        {
          document: {
            _id: '685b87b4079e5bd190d8ae2e',
            secretId: 'NEB-VAULT-ROT-SECRET-1267',
            type: 'ROTATABLE-SECRET',
            secretTTLInHours: 10,
            rotationType: 'Manual',
            nextRotationDate: '2025-06-30T00:00:00.000Z',
            vaultNamespace: 'nebula-stamp/suriyavault',
            vaultPath: 'test',
            deviceUserNameSecretId: 'NEB-VAULT-NOR-SECRET-1266',
            devicePasswordKey: 'test',
            policyId: '647484',
            status: 'SUCCESS',
            active: true,
            version: 2,
            actionId: 'NEB-ACTION-172',
            error: [],
            createdAt: '2025-06-25T05:23:00.361Z',
            updatedAt: '2025-06-25T05:25:24.979Z',
            __v: 0,
          },
          action: {
            _id: '685b8844079e5bd190d8ae69',
            actionId: 'NEB-ACTION-172',
            entityId: 'NEB-VAULT-ROT-SECRET-1267',
            entityType: 'secretsmetadatas',
            actionTypeId: 'UPDATE_SECRET',
            status: 'COMPLETED',
            createdBy: 'P3271329',
            createdAt: '2025-06-25T05:25:24.722Z',
            updatedAt: '2025-06-25T05:25:25.001Z',
            __v: 0,
            action: 'Update Secret',
          },
        },
        {
          document: {
            _id: '685b87b4079e5bd190d8ae2e',
            secretId: 'NEB-VAULT-ROT-SECRET-1267',
            type: 'ROTATABLE-SECRET',
            secretTTLInHours: 3,
            rotationType: 'Manual',
            nextRotationDate: '2025-06-30T00:00:00.000Z',
            vaultNamespace: 'nebula-stamp/suriyavault',
            vaultPath: 'test',
            deviceUserNameSecretId: 'NEB-VAULT-NOR-SECRET-1266',
            devicePasswordKey: 'test',
            policyId: '647484',
            status: 'SUCCESS',
            active: true,
            version: 1,
            actionId: 'NEB-ACTION-171',
            error: [],
            createdAt: '2025-06-25T05:23:00.361Z',
            updatedAt: '2025-06-25T05:24:56.260Z',
            __v: 0,
          },
          action: {
            _id: '685b8828079e5bd190d8ae5c',
            actionId: 'NEB-ACTION-171',
            entityId: 'NEB-VAULT-ROT-SECRET-1267',
            entityType: 'secretsmetadatas',
            actionTypeId: 'UPDATE_SECRET',
            status: 'COMPLETED',
            createdBy: 'P3271329',
            createdAt: '2025-06-25T05:24:56.067Z',
            updatedAt: '2025-06-25T05:24:56.282Z',
            __v: 0,
            action: 'Update Secret',
          },
        },
        {
          document: {
            _id: '685b87b4079e5bd190d8ae2e',
            secretId: 'NEB-VAULT-ROT-SECRET-1267',
            type: 'ROTATABLE-SECRET',
            secretTTLInHours: 3,
            rotationType: 'Manual',
            nextRotationDate: '2025-06-26T00:00:00.000Z',
            vaultNamespace: 'nebula-stamp/suriyavault',
            vaultPath: 'test',
            deviceUserNameSecretId: 'NEB-VAULT-NOR-SECRET-1266',
            devicePasswordKey: 'test',
            policyId: '647484',
            status: 'SUCCESS',
            active: true,
            version: 1,
            actionId: 'NEB-ACTION-170',
            error: [],
            createdAt: '2025-06-25T05:23:00.361Z',
            updatedAt: '2025-06-25T05:24:37.609Z',
            __v: 0,
          },
          action: {
            _id: '685b8815079e5bd190d8ae4f',
            actionId: 'NEB-ACTION-170',
            entityId: 'NEB-VAULT-ROT-SECRET-1267',
            entityType: 'secretsmetadatas',
            actionTypeId: 'UPDATE_SECRET',
            status: 'COMPLETED',
            createdBy: 'P3271329',
            createdAt: '2025-06-25T05:24:37.421Z',
            updatedAt: '2025-06-25T05:24:37.631Z',
            __v: 0,
            action: 'Update Secret',
          },
        },
        {
          document: {
            _id: '685b87b4079e5bd190d8ae2e',
            secretId: 'NEB-VAULT-ROT-SECRET-1267',
            type: 'ROTATABLE-SECRET',
            secretTTLInHours: 5,
            rotationType: 'Manual',
            nextRotationDate: '2025-06-26T00:00:00.000Z',
            vaultNamespace: 'nebula-stamp/suriyavault',
            vaultPath: 'test',
            deviceUserNameSecretId: 'NEB-VAULT-NOR-SECRET-1266',
            devicePasswordKey: 'test',
            policyId: '647484',
            status: 'SUCCESS',
            active: true,
            version: 1,
            actionId: 'NEB-ACTION-169',
            error: [],
            createdAt: '2025-06-25T05:23:00.361Z',
            updatedAt: '2025-06-25T05:23:49.774Z',
            __v: 0,
          },
          action: {
            _id: '685b87e5079e5bd190d8ae42',
            actionId: 'NEB-ACTION-169',
            entityId: 'NEB-VAULT-ROT-SECRET-1267',
            entityType: 'secretsmetadatas',
            actionTypeId: 'UPDATE_SECRET',
            status: 'COMPLETED',
            createdBy: 'P3271329',
            createdAt: '2025-06-25T05:23:49.569Z',
            updatedAt: '2025-06-25T05:23:49.800Z',
            __v: 0,
            action: 'Update Secret',
          },
        },
        {
          document: {
            _id: '685b87b4079e5bd190d8ae2e',
            secretId: 'NEB-VAULT-ROT-SECRET-1267',
            type: 'ROTATABLE-SECRET',
            secretTTLInHours: 1,
            rotationType: 'Manual',
            nextRotationDate: '2025-06-26T00:00:00.000Z',
            vaultNamespace: 'nebula-stamp/suriyavault',
            vaultPath: 'test',
            deviceUserNameSecretId: 'NEB-VAULT-NOR-SECRET-1266',
            devicePasswordKey: 'test',
            policyId: 'Policy123',
            status: 'SUCCESS',
            active: true,
            version: 1,
            actionId: 'NEB-ACTION-168',
            error: [],
            createdAt: '2025-06-25T05:23:00.361Z',
            updatedAt: '2025-06-25T05:23:00.361Z',
            __v: 0,
          },
          action: {
            _id: '685b87b4079e5bd190d8ae2a',
            actionId: 'NEB-ACTION-168',
            entityId: 'NEB-VAULT-ROT-SECRET-1267',
            entityType: 'secretsmetadatas',
            actionTypeId: 'CREATE_NEW_SECRET',
            status: 'COMPLETED',
            createdBy: 'P3271329',
            createdAt: '2025-06-25T05:23:00.316Z',
            updatedAt: '2025-06-25T05:23:00.382Z',
            __v: 0,
            action: 'Create Secret',
          },
        },
      ];
      mockWatchServiceApi.get.mockResolvedValueOnce({
        data: mockWatchServiceGetResponse,
      });
      const apiRes = [
        {
          data: {
            data: {
              data: {
                test: 'password-CHANGED-AGAIN',
                usernametest: 'username',
              },
            },
          },
        },
        {
          data: {
            data: {
              data: {
                test: 'password-CHANGED',
                usernametest: 'username',
              },
            },
          },
        },
        {
          data: {
            data: {
              data: {
                test: 'password',
                usernametest: 'username',
              },
            },
          },
        },
      ];
      mockSecretsManagmentServiceApi.get
        .mockResolvedValueOnce(apiRes[0])
        .mockResolvedValueOnce(apiRes[1])
        .mockResolvedValueOnce(apiRes[2]);
      mockActionService.findAllActionByIds.mockResolvedValueOnce([
        {
          _id: '685b8888079e5bd190d8ae76',
          actionId: 'NEB-ACTION-173',
          entityId: 'NEB-VAULT-ROT-SECRET-1267',
          entityType: 'secretsmetadatas',
          actionTypeId: 'UPDATE_SECRET',
          status: 'COMPLETED',
          createdBy: 'P3271329',
          createdAt: '2025-06-25T05:26:32.124Z',
          updatedAt: '2025-06-25T05:26:32.411Z',
          __v: 0,
          actionEnum: {
            _id: '6859fe6fa99f652cec0aaa47',
            actionTypeId: 'UPDATE_SECRET',
            action: 'Update Secret',
          },
          action: 'Update Secret',
        },
      ]);
      mockSecretsMetadataService.getSecretsMetaDataBySecretId.mockResolvedValueOnce(
        {
          _id: '685b87b4079e5bd190d8ae2e',
          secretId: 'NEB-VAULT-ROT-SECRET-1267',
          type: 'ROTATABLE-SECRET',
          secretTTLInHours: 10,
          rotationType: 'Manual',
          nextRotationDate: '2025-06-30T00:00:00.000Z',
          vaultNamespace: 'nebula-stamp/suriyavault',
          vaultPath: 'test',
          deviceUserNameSecretId: 'NEB-VAULT-NOR-SECRET-1266',
          devicePasswordKey: 'test',
          policyId: '647484',
          status: 'SUCCESS',
          active: true,
          version: 3,
          actionId: 'NEB-ACTION-173',
          error: [],
          createdAt: '2025-06-25T05:23:00.361Z',
          updatedAt: '2025-06-25T05:26:32.385Z',
          __v: 0,
        },
      );
      let mockSecretId = 'NEB-VAULT-ROT-SECRET-1267';
      const result = await secureStoreService.fetchSecretsHistory(
        mockSecretId,
        { page: 1, limit: 5, paginate: false, sortBy: '' },
      );
      let mockRes = [
        {
          action: 'Update Secret',
          lastUpdatedBy: 'P3271329',
          lastUpdatedAt: '2025-06-25T05:26:32.385Z',
          newValue: {
            version: 3,
            secret: 'password-CHANGED-AGAIN',
          },
          originalValue: {
            version: 2,
            secret: 'password-CHANGED',
          },
        },
        {
          action: 'Update Secret',
          lastUpdatedBy: 'P3271329',
          lastUpdatedAt: '2025-06-25T05:25:24.979Z',
          newValue: {
            secretTTLInHours: 10,
            version: 2,
            secret: 'password-CHANGED',
          },
          originalValue: {
            secretTTLInHours: 3,
            version: 1,
            secret: 'password',
          },
        },
        {
          action: 'Update Secret',
          lastUpdatedBy: 'P3271329',
          lastUpdatedAt: '2025-06-25T05:24:56.260Z',
          newValue: {
            nextRotationDate: '2025-06-30T00:00:00.000Z',
          },
          originalValue: {
            nextRotationDate: '2025-06-26T00:00:00.000Z',
          },
        },
        {
          action: 'Update Secret',
          lastUpdatedBy: 'P3271329',
          lastUpdatedAt: '2025-06-25T05:24:37.609Z',
          newValue: {
            secretTTLInHours: 3,
          },
          originalValue: {
            secretTTLInHours: 5,
          },
        },
        {
          action: 'Update Secret',
          lastUpdatedBy: 'P3271329',
          lastUpdatedAt: '2025-06-25T05:23:49.774Z',
          newValue: {
            secretTTLInHours: 5,
            policyId: '647484',
          },
          originalValue: {
            secretTTLInHours: 1,
            policyId: 'Policy123',
          },
        },
        {
          action: 'Create Secret',
          lastUpdatedAt: '2025-06-25T05:23:00.361Z',
          lastUpdatedBy: 'P3271329',
          newValue: {
            type: 'ROTATABLE-SECRET',
            secretTTLInHours: 1,
            rotationType: 'Manual',
            nextRotationDate: '2025-06-26T00:00:00.000Z',
            vaultNamespace: 'nebula-stamp/suriyavault',
            vaultPath: 'test',
            devicePasswordKey: 'test',
            policyId: 'Policy123',
            status: 'SUCCESS',
            active: true,
            version: 1,
            secret: 'password',
          },
          originalValue: null,
        },
      ];
      expect(result).toHaveLength(6);
    });
  });

  describe('validateSecretId', () => {
    it('should throw BasRequestException for invalid secretId', async () => {
      const mockSecretId = 'NEB-VAULT-ROTE-SECRET-1586';
      expect(() => secureStoreService.validateSecretId(mockSecretId)).toThrow(
        BadRequestException,
      );
      expect(() => secureStoreService.validateSecretId(mockSecretId)).toThrow(
        'SecretId is not valid.',
      );
    });
    it('should not throw BasRequestException for valid secretId', async () => {
      const mockSecretId = 'NEB-VAULT-ROT-SECRET-1586';
      expect(() =>
        secureStoreService.validateSecretId(mockSecretId),
      ).not.toThrow();
    });
  });
  describe('createpasswordPolicy', () => {
    it('should create passwordPolicy', async () => {
      const dto = {
        policyname: "newPolicy",
        totalchars: 12,
        smallAlphabets: 4,
        bigAlphabets: 4,
        numbers: 2,
        noOfSplChars: 2,
        splChars: '$#',
        description: "stri2ng",
        namespace: "namspace1"
      };

      const response = {
        "success": true,
        "message": "PasswordPolicy is created successfully."
      }
      mockConfigService.get.mockReturnValue('$#%&*@');
      // mockResourcesRepository.getResourcesByNamespace.mockResolvedValue({ resourceId: 'res1' });
      // mockResourcesService.getResourceByResourceId
      mockResourcesService.getResourcesByNamespace.mockResolvedValueOnce({ resourceId: 'res1' });
      mockSecretsManagmentServiceApi.put.mockResolvedValue({ status: 201, message: 'created' })
      mockSecretsPoliciesService.create.mockResolvedValue({ status: 201 })
      const result = await secureStoreService.createPasswordPolicy(
        dto
      );

      expect(result).toEqual(response);

    });

    it('should throw error for invalid splChars', async () => {
      const dto = {
        policyname: "newPolicy",
        totalchars: 12,
        smallAlphabets: 4,
        bigAlphabets: 4,
        numbers: 2,
        noOfSplChars: 2,
        splChars: '$/',
        description: "stri2ng",
        namespace: "namspace1"
      };

      const response = {
        "success": true,
        "message": "PasswordPolicy is created successfully."
      }
      const validSpecilchars = '$#%&*@';
      mockConfigService.get.mockReturnValue(validSpecilchars);
      await expect(
        secureStoreService.createPasswordPolicy(dto),
      ).rejects.toThrow(
        new NotFoundException(
          `Special characters should be from ${validSpecilchars}`,
        )
      );

    });

    it('should throw error on totalChars validation passwordPolicy', async () => {
      const dto = {
        policyname: "newPolicy",
        totalchars: 12,
        smallAlphabets: 6,
        bigAlphabets: 4,
        numbers: 2,
        noOfSplChars: 2,
        splChars: '$#',
        description: "stri2ng",
        namespace: "namspace1"
      };

      await expect(
        secureStoreService.createPasswordPolicy(dto),
      ).rejects.toThrow(
        new NotFoundException(
          'Password policy characters combination cannot be greater than the total characters length!',
        )
      );
    });
  });
});
