import { Injectable } from '@nestjs/common';
import { LoggerService } from '../loggers/logger.service';
import { VenafiWrapperService } from 'src/certificate-management-wrapper/venafi-certificate-wrapper.service';
import {
  VenafiCertificateDownloadResponseDto,
  VenafiCertificateRenewRequestDto,
  VenafiCertificateRenewResponseDto,
} from './dto/venafi-certificate.dto';
import { ResourcesRepository } from 'src/resources/resources.repository';
import { VenafiCertificateTransformedDetailsDto } from './dto/venafi-ui-transform.dto';
import { ActionRepository } from 'src/action/repositories/action.repository';
import { ActionType } from 'src/action/enums/ActionType.enum';
import { COLLECTIONS } from 'src/utils/constants';
import { ActionStatus } from 'src/action/enums/ActionStatus.enum';
import { getUserContext } from 'src/utils/helpers';
import { RequestContext } from 'nestjs-request-context';

@Injectable()
export class VenafiService {
  constructor(
    private readonly venafiWrapperService: VenafiWrapperService,
    private readonly resourcesRepository: ResourcesRepository,
    private readonly logger: LoggerService,
    private readonly actionRepository: ActionRepository,
  ) {}

  async fetchCertificateDetails(
    resourceId: string,
  ): Promise<VenafiCertificateTransformedDetailsDto> {
    this.logger.log(
      `Calling the venafi wrapper service to fetch Venafi certificate details using resourceId: ${resourceId}`,
    );
    const resource: any =
      await this.resourcesRepository.findOneByResourceId(resourceId);
    return await this.venafiWrapperService.getVenafiCertificateDetails(
      resource.resourcesDetails.guid,
    );
  }

  async renewCertificate(
    resourceId: string,
  ): Promise<VenafiCertificateRenewResponseDto> {
    this.logger.log(
      `Calling the venafi wrapper service to renew certificate using resourceId: ${resourceId}`,
    );
    const resource: any =
      await this.resourcesRepository.findOneByResourceId(resourceId);
    const action = await this.actionRepository.create({
      createdBy:getUserContext(RequestContext)?.userId,
      actionTypeId: ActionType.RENEW_CERTIFICATE,
      entityId: resourceId,
      entityType: COLLECTIONS.RESOURCES,
      status: ActionStatus.STARTED,
    });
    this.logger.log(`Action ${action.actionId} created with status: ${action.status} for resourceId: ${resourceId}`);
    try {
      const renewPayload: VenafiCertificateRenewRequestDto = {
        certificateDN: resource.resourcesDetails.certificateDn,
      };
      const response =
        await this.venafiWrapperService.renewVenafiCertificate(renewPayload);
      await this.actionRepository.update({
        actionId: action.actionId,
        status: ActionStatus.COMPLETED,
      });
      return response;
    } catch (error) {
      this.logger.error(
        `Error while renewing the Venafi certificate using resourceId: ${resourceId}`,
      );
      await this.actionRepository.update({
        actionId: action.actionId,
        status: ActionStatus.FAILED,
      });
    }
  }

  async downloadCertificate(
    resourceId: string,
  ): Promise<VenafiCertificateDownloadResponseDto> {
    this.logger.log(
      `Downloading the Venafi certificate using resourceId: ${resourceId}`,
    );
    const resource: any =
      await this.resourcesRepository.findOneByResourceId(resourceId);
    const action = await this.actionRepository.create({
      createdBy:getUserContext(RequestContext)?.userId,
      actionTypeId: ActionType.DOWNLOAD_CERTIFICATE,
      entityId: resourceId,
      entityType: COLLECTIONS.RESOURCES,
      status: ActionStatus.STARTED,
    });
    this.logger.log(`Action ${action.actionId} created with status: ${action.status} for resourceId: ${resourceId}`);
    try {
      const response =
        await this.venafiWrapperService.downloadVenafiCertificate(
          resource.resourcesDetails.certificateDn,
          resource.resourcesDetails.format,
        );
      await this.actionRepository.update({
        actionId: action.actionId,
        status: ActionStatus.COMPLETED,
      });
      return response;
    } catch (error) {
      this.logger.error(
        `Error while downloading the Venafi certificate using resourceId: ${resourceId}`,
      );
      await this.actionRepository.update({
        actionId: action.actionId,
        status: ActionStatus.FAILED,
      });
    }
  }
}
