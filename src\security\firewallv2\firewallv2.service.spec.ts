import { Test, TestingModule } from '@nestjs/testing';
import { FirewallV2Service } from './firewallv2.service';
import { FirewallRequestDetailsRepository } from '../repository/firewallRequestDetails-repository';
import { ObjectStorageService } from '../../objectStorage/object.storage.service';
import { StorageRepository } from '../../objectStorage/storage.repository';
import { AssetsService } from '../../assets/assets.service';
import { LoggerService } from '../../loggers/logger.service';
import { SubTypeApprovalsService } from '../../approvals/sub-type-approval/sub-type-approvals.service';
import { RestService } from '../../rest/rest.service';
import { CatalogStepsService } from '../../catalog-steps/catalog-steps.service';
import { RmqService } from '../../rmq/rmq.service';
import { ActivityLoggerWrapperService } from '../../activity-logs-wrapper/activity-logger-wrapper.service';
import { FirewallRequestEntity } from '../entity/firewallRequest.entity';
import { ApprovalStatus, RequestStatus, RequestType } from '../../types';
import { TufinDeviceRepository } from '../repository/tufin.path-analysis.repository';

describe('Firewallv2 Service', () => {
  let service: FirewallV2Service;
  let firewallRequestRepository: FirewallRequestDetailsRepository;
  let tufinDeviceRepository: TufinDeviceRepository;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FirewallV2Service,
        {
          provide: FirewallRequestDetailsRepository,
          useValue: {
            updateRiskAnalysis: jest.fn(),
            updateTufinTask: jest.fn(),
            getFirewallTicketDetails: jest.fn(),
            createRequest: jest.fn(),
            findByServiceRequestIdAndSubRequestId: jest.fn(),
          },
        },
        {
          provide: TufinDeviceRepository,
          useValue: {
            getTufinDevicesByRuleIds: jest.fn(),
          },
        },
        {
          provide: ObjectStorageService,
          useValue: {},
        },
        {
          provide: StorageRepository,
          useValue: {},
        },
        {
          provide: AssetsService,
          useValue: {},
        },
        {
          provide: SubTypeApprovalsService,
          useValue: {},
        },
        {
          provide: RestService,
          useValue: {},
        },
        {
          provide: CatalogStepsService,
          useValue: {},
        },
        {
          provide: 'JIRA_SERVICE_API',
          useValue: {},
        },
        {
          provide: 'TUFIN_SERVICE_API',
          useValue: {},
        },
        {
          provide: RmqService,
          useValue: {},
        },
        {
          provide: ActivityLoggerWrapperService,
          useValue: {},
        },
        {
          provide: LoggerService,
          useValue: {
            log: jest.fn(),
            error: jest.fn(),
            debug: jest.fn(),
            info: jest.fn(),
            fatal: jest.fn(),
            warn: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<FirewallV2Service>(FirewallV2Service);
    firewallRequestRepository = module.get<FirewallRequestDetailsRepository>(
      FirewallRequestDetailsRepository,
    );
    tufinDeviceRepository = module.get<TufinDeviceRepository>(
      TufinDeviceRepository,
    );
  });

  it('service is defined', () => {
    expect(service).toBeDefined();
  });

  describe('getFirewallTicketDetailsN8N', () => {
    it('fetch all subrequest using service request id', async () => {
      const mockResponse = {
        status: 'SUCCESS',
        data: [
          {
            createdAt: '2024-10-08T09:28:42.890Z',
            updatedAt: '2024-10-08T09:29:57.117Z',
            serviceRequestId: 'NEB-IAAS-FW-V2-1',
            subRequestId: 'NEB-FW-SUB-REQ-1',
            status: 'PROCESSING',
            ipType: 'IPV4',
            approvalStatus: 'NA',
            organizationName: 'red_aps',
            ticketDetails: {
              jira: {
                ticketId: '',
                ticketUrl: '',
                status: 'FAILED',
                error: [
                  {
                    errorCode: '504',
                    errorMessage:
                      '504 - <html>rn<head><title>504 Gateway Time-out</title></head>rn<body>rn<center><h1>504 Gateway Time-out</h1></center>rn<hr><center>nginx</center>rn</body>rn</html>rn',
                  },
                ],
              },
              tufin: {
                ticketId: '1',
                ticketUrl: 'localhost:3000/tufin',
                status: 'CREATED',
                error: [],
              },
            },
          },
        ],
      };
      const mockRepoReposne: Partial<FirewallRequestEntity[]> = [
        {
          createdAt: new Date(),
          updatedAt: new Date(),
          requestId: 'NEB-IAAS-FW-V2-1',
          status: RequestStatus.PROCESSING,
          ipType: 'IPV4',
          approvalStatus: ApprovalStatus.NA,
          organizationName: 'red_aps',
          ticketDetails: {
            jira: {
              ticketId: '',
              ticketUrl: '',
              status: 'FAILED',
            },
            tufin: {
              ticketId: '1',
              ticketUrl: 'localhost:3000/tufin',
              status: 'CREATED',
            },
            cherwell: {},
            remedy: {},
          },
          requestType: RequestType.FIREWALL_V2,
          id: '1',
        },
      ];

      jest
        .spyOn(firewallRequestRepository, 'getFirewallTicketDetails')
        .mockResolvedValue(mockRepoReposne);

      await service.getFirewallTicketDetailsN8N('Iaas-FW2-1');
      expect(
        firewallRequestRepository.getFirewallTicketDetails,
      ).toHaveBeenCalled();
    });
  });

  describe('createNewFirewallRequestResult', () => {
    it('should be able to create new subrequest', async () => {
      const payload : any= {
        requestType: 'FIREWALL_V2',
        requestId: 'NEB-IAAS-FW-V2-1',
        status: 'PROCESSING',
        startedAt: '2024-10-08T08:28:36.205Z',
        ipType: 'IPV4',
        approvalStatus: 'NA',
        organizationName: 'red_aps',
        ticketDetails: {
          tufin: {
            ticketId: '7592',
            ticketUrl: 'localhost:4000',
            status: 'CREATED',
            error: [],
          },
        },
      };

      const mockResponse: any = {
        id: '6704f31d06a1e2670f617a34',
        requestType: 'FIREWALL_V2',
        status: 'PROCESSING',
        approvalStatus: 'NA',
        organizationName: 'red_aps',
        requestId: 'NEB-IAAS-FW-V2-1',
        ipType: 'IPV4',
        subRequestId: 'NEB-FW-SUB-REQ-1',
        startedAt: '2024-10-08T08:28:36.205Z',
        completedAt: null,
        updatedAt: '2024-10-08T08:53:49.567Z',
        ticketDetails: {
          tufin: {
            ticketId: '7592',
            ticketUrl:
              'localhost:4000',
            status: 'CREATED',
            error: [],
          },
        },
        createdAt: '2024-10-08T08:53:49.567Z',
      };

      jest
        .spyOn(firewallRequestRepository, 'createRequest')
        .mockResolvedValue(mockResponse);

        await service.createNewFirewallRequestResult(payload);
        expect(
          firewallRequestRepository.createRequest,
        ).toHaveBeenCalled();
    });
  });

  describe('getFirewallImpactedDevices', () => {
    const mockTufinRes = [
      {
        target: [
          {
            name: 'test name',
            type: 'test type',
            vendor: 'test vendor',
            isImpacted: false,
            owner: 'test owner',
            organization: 'test organization',
          },
        ],
        source: 'test source',
        destination: 'test destination',
        ruleId: 4,
      },
    ];
    const mockFirewallRes = {
      id: 'test id',
      requestType: RequestType.FIREWALL_V2,
      approvalStatus: ApprovalStatus.NA,
      status: RequestStatus.PROCESSING,
      organizationName: 'test org',
      requestId: 'test id',
      ipType: 'test ip',
      ruleIds: [4],
      subRequestId: 'test id',
      startedAt: new Date(),
      completedAt: null,
      updatedAt: new Date(),
      isSubRequestInValidated: true,
      createdAt: new Date(),
      ticketDetails: {
        tufin: {
          ticketId: 'test id',
        },
        jira: {},
        cherwell: {},
        remedy: {},
      },
    };
    const req = {
      serviceRequestId: 'NEB-IAAS-FW-V2-25437',
      subRequestId: 'NEB-FW-SUB-REQ-3677',
    };

    it('should get impacted devices from DB', async () => {
      jest
        .spyOn(
          firewallRequestRepository,
          'findByServiceRequestIdAndSubRequestId',
        )
        .mockResolvedValue(mockFirewallRes);
      jest
        .spyOn(tufinDeviceRepository, 'getTufinDevicesByRuleIds')
        .mockResolvedValue(mockTufinRes);
      await service.getFirewallImpactedDevices(req);
      expect(
        firewallRequestRepository.findByServiceRequestIdAndSubRequestId,
      ).toHaveBeenCalled();
    });
  });
});
