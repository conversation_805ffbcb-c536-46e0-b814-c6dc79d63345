import { Injectable } from '@nestjs/common';
import { LoggerService } from '../loggers/logger.service';
import { TicketManagementWrapperService } from '../ticket-management-service-wrapper/ticket-management-wrapper-service';
import {
  CrqValidationStatus,
  BooleanString,
  SUCCESS_RESPONSE_TEXT,
} from '../utils/constants';
import {
  ValidateCrqTicketRequestDto,
  ValidateCrqTicketResponseDto,
  ValidateCrqDto,
  GetMopIdFromCrqResponseDto,
  AddCommentRequestDTO,
  AddCommentResponsetDTO,
} from './dto/ticket-management.dto';

@Injectable()
export class TicketManagementService {
  constructor(
    private readonly ticketManagementWrapperService: TicketManagementWrapperService,
    private readonly logger: LoggerService,
  ) {}

  async validateCrqTicket(
    validateCrqTicketDto: ValidateCrqTicketRequestDto,
  ): Promise<ValidateCrqTicketResponseDto> {
    try {
      const validateCrqResponse: ValidateCrqDto =
        await this.ticketManagementWrapperService.validateCrqTicket(
          validateCrqTicketDto,
        );

      const isValid =
        validateCrqResponse?.crq_results[0]?.status == SUCCESS_RESPONSE_TEXT
          ? BooleanString.true
          : BooleanString.false;

      return {
        status: 'Success',
        message: 'Validated given crq ticket',
        crqTicketStatus: CrqValidationStatus[isValid],
      };
    } catch (error) {
      this.logger.error(
        error.stack,
        `Exception while validating crq ticket ${validateCrqTicketDto.crqTicketId}. Err: ${error.message}`,
      );
      throw error;
    }
  }

  async getMopIdFromCrqTicket(
    crqTicketDto: ValidateCrqTicketRequestDto,
  ): Promise<GetMopIdFromCrqResponseDto> {
    return await this.ticketManagementWrapperService.getMopIdFromCrqTicket(
      crqTicketDto,
    );
  }

  async addCommentInCrqTicket(
    addCrqCommentDto: AddCommentRequestDTO,
  ): Promise<AddCommentResponsetDTO> {
    return await this.ticketManagementWrapperService.addCommentsInCrqTicket(
      addCrqCommentDto,
    );
  }
}
