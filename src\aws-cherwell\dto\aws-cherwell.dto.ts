import { IsString, <PERSON><PERSON>otEmpty, <PERSON><PERSON><PERSON>al, IsA<PERSON>y, IsBoolean } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class AwsDeviceDetectionRequestDto {
  @ApiProperty({ description: 'Service request ID' })
  @IsString()
  @IsNotEmpty()
  serviceRequestId: string;

  @ApiPropertyOptional({ description: 'Sub request ID' })
  @IsString()
  @IsOptional()
  subRequestId?: string;

  @ApiProperty({ description: 'Path analysis data containing device information' })
  @IsArray()
  pathAnalysisData: any[];
}

export class AwsDeviceDetectionResponseDto {
  @ApiProperty({ description: 'Whether AWS devices were detected' })
  @IsBoolean()
  hasAwsDevices: boolean;

  @ApiProperty({ description: 'List of detected AWS device names' })
  @IsArray()
  detectedDevices: string[];

  @ApiProperty({ description: 'Service request ID' })
  @IsString()
  serviceRequestId: string;

  @ApiPropertyOptional({ description: 'Sub request ID' })
  @IsString()
  @IsOptional()
  subRequestId?: string;
}

export class CherwellWorkItemRequestDto {
  @ApiProperty({ description: 'Work item subject' })
  @IsString()
  @IsNotEmpty()
  subject: string;

  @ApiProperty({ description: 'Work item description' })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({ description: 'Assignment group for the work item' })
  @IsString()
  @IsNotEmpty()
  assignmentGroup: string;

  @ApiProperty({ description: 'Service request ID' })
  @IsString()
  @IsNotEmpty()
  serviceRequestId: string;

  @ApiPropertyOptional({ description: 'Sub request ID' })
  @IsString()
  @IsOptional()
  subRequestId?: string;
}

export class CherwellWorkItemResponseDto {
  @ApiProperty({ description: 'Cherwell work item ID' })
  @IsString()
  workItemId: string;

  @ApiProperty({ description: 'Cherwell work item number' })
  @IsString()
  workItemNumber: string;

  @ApiProperty({ description: 'Work item status' })
  @IsString()
  status: string;

  @ApiPropertyOptional({ description: 'Work item URL' })
  @IsString()
  @IsOptional()
  ticketUrl?: string;
}

export class AwsApprovalEmailRequestDto {
  @ApiProperty({ description: 'Cherwell work item number' })
  @IsString()
  @IsNotEmpty()
  workItemNumber: string;

  @ApiProperty({ description: 'Service request ID' })
  @IsString()
  @IsNotEmpty()
  serviceRequestId: string;
}

export class AwsDeviceHandlingResponseDto {
  @ApiProperty({ description: 'Whether AWS devices were detected and handled' })
  @IsBoolean()
  awsDevicesDetected: boolean;

  @ApiProperty({ description: 'List of detected AWS device names' })
  @IsArray()
  detectedDevices: string[];

  @ApiPropertyOptional({ description: 'Cherwell work item details if created' })
  @IsOptional()
  cherwellWorkItem?: CherwellWorkItemResponseDto;

  @ApiProperty({ description: 'Whether email notification was sent' })
  @IsBoolean()
  emailSent: boolean;

  @ApiProperty({ description: 'Processing message' })
  @IsString()
  message: string;
}

export class CherwellWorkItemStatusUpdateDto {
  @ApiProperty({ description: 'Cherwell work item ID' })
  @IsString()
  @IsNotEmpty()
  workItemId: string;

  @ApiProperty({ description: 'Work item status', enum: ['approved', 'rejected'] })
  @IsString()
  @IsNotEmpty()
  status: 'approved' | 'rejected';

  @ApiProperty({ description: 'Service request ID' })
  @IsString()
  @IsNotEmpty()
  serviceRequestId: string;

  @ApiPropertyOptional({ description: 'Sub request ID' })
  @IsString()
  @IsOptional()
  subRequestId?: string;
}
