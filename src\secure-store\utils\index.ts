import { BadRequestException } from '@nestjs/common';
import { SecretsMetadataRepository } from '../secretsMetadata/secretsMetadata.repository';
import { ResourcesRepository } from '../../resources/resources.repository';
import { LoggerService } from '../../loggers/logger.service';

export const SecureStoreRequestResolver = async (req) => {
  const secretId = req.params?.secretId || '';
  let resourceId;
  if (!secretId) {
    const namespace = req.params?.namespaceName || '';
    const resourceRepo = new ResourcesRepository(new LoggerService());

    const resource = await resourceRepo.getResourceByResourcesName(namespace);
    resourceId = resource?.resourceId;
  } else {
    const secretsMetadataRepo = new SecretsMetadataRepository();
    const secretsMetadata =
      await secretsMetadataRepo.getSecretsMetaDataBySecretId(secretId);
    resourceId = secretsMetadata?.resourceId;
  }
  if (!resourceId) {
    throw new BadRequestException(`Resource Id is invalid ${resourceId}`);
  }
  return resourceId;
};
