import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import {
  SecretsPolicies,
  SecretsPoliciesSchema,
} from './schemas/secretsPolicies.schema';
import { deepSanitize, transformDocumentToEntity } from '../../utils/helpers';
import { SecretsPoliciesEntity } from './entities/secretsPolicies.entity';
import { CounterTypes, GenericResourceTypes } from '../../types';

import { Counters, CountersSchema } from '../../naas/schemas/counter.schema';
import mongoose, { Mongoose, connect, Model } from 'mongoose';

const transformSecretsPoliciesDocumentToEntity = transformDocumentToEntity<
  SecretsPolicies,
  SecretsPoliciesEntity
>;

@Injectable()
export class SecretsPoliciesRepository {
  private async connectToDB(): Promise<Mongoose> {
    return await connect(process.env.MONGODB_URI);
  }

  private async getSecretsPoliciesModel(): Promise<
    Model<SecretsPolicies & { createdAt: Date; updatedAt: Date; __v: number }>
  > {
    const conn = await this.connectToDB();
    return conn.model<
      SecretsPolicies & { createdAt: Date; updatedAt: Date; __v: number }
    >(SecretsPolicies.name, SecretsPoliciesSchema);
  }

  async create(payload: SecretsPolicies): Promise<SecretsPoliciesEntity> {
    const model = await this.getSecretsPoliciesModel();
    payload.policyId = await this.getSecretPolicyId();
    const secretPolicy = await model.create(deepSanitize(payload));
    return transformSecretsPoliciesDocumentToEntity(secretPolicy);
  }

  async getPolicy(policyId: string | string[]) {
    const model = await this.getSecretsPoliciesModel();
    let secretPolicy;
    if (Array.isArray(policyId)) {
      secretPolicy = await model.find({
        policyId: { $in: policyId },
        status: "ACTIVE",
      });
    } else {
      secretPolicy = await model.findOne({
        policyId,
      });
    }
    return secretPolicy;
  }

  async getSecretPoliciesWithResourceId(resourceId: string) {
    const model = await this.getSecretsPoliciesModel();
    const secretPolicies = await model.find({ resourceId: resourceId });
    return secretPolicies;
  }

  async getSecretPoliciesWithResourceIdAndPolicy(policyName: string, resourceId: string) {
    const model = await this.getSecretsPoliciesModel();
    const secretPolicies = await model.find({ policyName, resourceId: resourceId });
    return secretPolicies;
  }

  private async getCount(conn: typeof mongoose) {
    const Counter = conn.model<Counters>(Counters.name, CountersSchema);
    const type = CounterTypes.SECRET_POLICY;
    const doc = await Counter.findOneAndUpdate(
      { type },
      { $inc: { counter: 1 } },
      { new: true, upsert: true },
    );
    return doc.counter;
  }

  async getSecretPolicyId(): Promise<string> {
    const conn = await this.connectToDB();
    const count = await this.getCount(conn);
    const prefix = GenericResourceTypes.SecretPolicy;
    return `${prefix}-${count}`;
  }
}
