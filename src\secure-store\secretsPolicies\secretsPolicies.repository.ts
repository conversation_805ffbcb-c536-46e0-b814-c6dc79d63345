import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import {
  SecretsPolicies,
  SecretsPoliciesSchema,
} from './schemas/secretsPolicies.schema';
import { deepSanitize, transformDocumentToEntity } from '../../utils/helpers';
import { SecretsPoliciesEntity } from './entities/secretsPolicies.entity';
import { Mongoose, connect, Model } from 'mongoose';
import {
  PasswordPolicies,
  PasswordPoliciesSchema,
} from './schemas/passwordPolicies.schema';
import { PasswordPoliciesResponseDto } from './dto/passwordpolicies.response.dto';

const transformSecretsPoliciesDocumentToEntity = transformDocumentToEntity<
  SecretsPolicies,
  SecretsPoliciesEntity
>;

@Injectable()
export class SecretsPoliciesRepository {
  private async connectToDB(): Promise<Mongoose> {
    return await connect(process.env.MONGODB_URI);
  }

  private async getSecretsPoliciesModel(): Promise<
    Model<SecretsPolicies & { createdAt: Date; updatedAt: Date; __v: number }>
  > {
    const conn = await this.connectToDB();
    return conn.model<
      SecretsPolicies & { createdAt: Date; updatedAt: Date; __v: number }
    >(SecretsPolicies.name, SecretsPoliciesSchema);
  }

  async create(payload: SecretsPolicies): Promise<SecretsPoliciesEntity> {
    const model = await this.getSecretsPoliciesModel();
    const secretPolicy = await model.create(deepSanitize(payload));
    return transformSecretsPoliciesDocumentToEntity(secretPolicy);
  }

  async getPolicy(policyId: string | string[]) {
    const model = await this.getSecretsPoliciesModel();
    let secretPolicy;
    if (Array.isArray(policyId)) {
      secretPolicy = await model.find({
        policyId: { $in: policyId },
        active: true,
      });
    } else {
      secretPolicy = await model.findOne({
        policyId,
      });
    }
    return secretPolicy;
  }

  async getPasswordPolicies(
    policyNames: string[],
  ): Promise<PasswordPolicies[]> {
    const model = await this.getSecretsPoliciesModel();
    const passwordPolicies = await model.find(
      {
        policyName: { $in: policyNames },
      },
      { _id: 0, policyId: 1, policyName: 1, description: 1 },
    );
    return passwordPolicies;
  }
}
