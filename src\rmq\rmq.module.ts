import { Module } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ConfigService } from '@nestjs/config';
import { RmqService } from './rmq.service';
import { ENVIRONMENT_VARS } from '../utils/constants';
import { LoggerModule } from '../loggers/logger.module';

@Module({
  imports: [
    LoggerModule,
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'IPAM_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(ENVIRONMENT_VARS.RMQ_IPAM_QUEUE),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_IPAM_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'MON<PERSON>ORING_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_MONITORING_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_MONITORING_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'DNP_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(ENVIRONMENT_VARS.RMQ_DNP_QUEUE),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_DNP_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'COMPUTE_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_COMPUTE_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_COMPUTE_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'BLUE_COMPUTE_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_BLUE_COMPUTE_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_BLUE_COMPUTE_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'CHARTER_LAB_COMPUTE_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_CHARTER_LAB_COMPUTE_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_CHARTER_LAB_COMPUTE_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'VMWARE_COMPUTE_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_VMWARE_COMPUTE_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_VMWARE_COMPUTE_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'VMWARE_LAB_COMPUTE_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_VMWARE_LAB_COMPUTE_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_VMWARE_LAB_COMPUTE_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'FIREWALL_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_FIREWALL_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_FIREWALL_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'DELETE_VM_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_DELETE_VM_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_DELETE_VM_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'DELETE_PACE_VM_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_DELETE_PACE_VM_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_DELETE_PACE_VM_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'DELETE_BLUE_VM_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_DELETE_BLUE_VM_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_DELETE_BLUE_VM_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'DELETE_VM_VMWARE_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_DELETE_VM_VMWARE_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_DELETE_VM_VMWARE_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'START_STOP_RESTART_VM_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_START_STOP_RESTART_VM_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_START_STOP_RESTART_VM_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'CREATE_DB_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_CREATE_DB_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_CREATE_DB_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'COMMON_FIREWALL_POLICY_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_COMMON_FIREWALL_POLICY_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_COMMON_FIREWALL_POLICY_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'FIREWALL_V2_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_FIREWALL_V2_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_FIREWALL_V2_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'STORAGE_S3_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_STORAGE_S3_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_STORAGE_S3_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'STORAGE_MODIFY_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_MODIFY_STORAGE_S3_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_STORAGE_S3_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'STORAGE_NFS_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_STORAGE_NFS_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_STORAGE_NFS_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'AWS_CREATE_SUBACCOUNT_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_AWS_CREATE_SUBACCOUNT_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_AWS_CREATE_SUBACCOUNT_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'AWS_CREATE_SUBACCOUNT_GROUP_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_AWS_CREATE_SUBACCOUNT_GROUP_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_AWS_CREATE_SUBACCOUNT_GROUP_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'AWS_CREATE_USER_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_AWS_CREATE_USER_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_AWS_CREATE_USER_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'REGENERATE_KEYS_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_REGENERATE_KEYS_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_REGENERATE_KEYS_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'MODIFY_NFS_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_MODIFY_NFS_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_MODIFY_NFS_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'DELETE_S3_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_DELETE_S3_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_DELETE_S3_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'DELETE_NFS_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_DELETE_NFS_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_DELETE_NFS_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'F5_LOAD_BALANCE_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_F5_LOAD_BALANCE_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_F5_LOAD_BALANCE_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'INTERNAL_CERTIFICATE_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_INTERNAL_CERTIFICATE_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_INTERNAL_CERTIFICATE_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'MANAGE_SUB_ACCOUNT_GROUP_PERMISSION_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_MANAGE_SUB_ACCOUNT_GROUP_PERMISSION_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_MANAGE_SUB_ACCOUNT_GROUP_PERMISSION_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'MANAGE_PERMISSION_SET_CREATE_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_MANAGE_PERMISSION_SET_CREATE_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_MANAGE_PERMISSION_SET_CREATE_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'MANAGE_PERMISSION_SET_REMOVE_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_MANAGE_PERMISSION_SET_REMOVE_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_MANAGE_PERMISSION_SET_REMOVE_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'WORKFLOW_RETRY_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_WORKFLOW_RETRY_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_WORKFLOW_RETRY_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'WORKFLOW_RETRY_VMWARE_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_WORKFLOW_RETRY_VMWARE_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_WORKFLOW_RETRY_VMWARE_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'FIREWALL_MIGRATION_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_FIREWALL_MIGRATION_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_FIREWALL_MIGRATION_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'DELETE_DB_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_DELETE_DB_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_DELETE_DB_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'RMQ_BULK_VM_IMPORT_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_BULK_VM_IMPORT_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_BULK_VM_IMPORT_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'RMQ_ONBOARD_PROJECT_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_ONBOARD_PROJECT_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_ONBOARD_PROJECT_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'RMQ_CATALOG_ACCESS_REQUEST_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_CATALOG_ACCESS_REQUEST_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_CATALOG_ACCESS_REQUEST_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'RMQ_RESUBMIT_FIREWALL_V2_REQUEST_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_RESUBMIT_FIREWALL_V2_REQUEST_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_RESUBMIT_FIREWALL_V2_REQUEST_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'RMQ_ONBOARD_GROUP_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_ONBOARD_GROUP_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_ONBOARD_GROUP_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'RMQ_SPEC_FLOW_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_SPEC_FLOW_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_SPEC_FLOW_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'RMQ_CREATE_PUBLIC_CLOUD_VPC_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_CREATE_PUBLIC_CLOUD_VPC_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_CREATE_PUBLIC_CLOUD_VPC_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'RMQ_DELETE_PUBLIC_CLOUD_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_DELETE_PUBLIC_CLOUD_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_DELETE_PUBLIC_CLOUD_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'RMQ_CREATE_NAMESPACE_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_CREATE_NAMESPACE_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_CREATE_NAMESPACE_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'RECONFIGURE_VM_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_RECONFIGURE_VM_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_RECONFIGURE_VM_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'RMQ_CREATE_ZTP_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(ENVIRONMENT_VARS.RMQ_ZTP_QUEUE),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_ZTP_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'PACE_RECONFIGURE_VM_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_PACE_RECONFIGURE_VM_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_RECONFIGURE_PACE_VM_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'EDIT_VM_VMWARE_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_EDIT_VM_VMWARE_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_EDIT_VM_VMWARE_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'EDIT_PACE_VM_VMWARE_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_EDIT_PACE_VM_VMWARE_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_EDIT_PACE_VM_VMWARE_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'RMQ_CREATE_SECRET_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_CREATE_SECRET_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_CREATE_SECRET_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
    ClientsModule.registerAsync({
      clients: [
        {
          name: 'RMQ_UPDATE_SECRET_QUEUE',
          useFactory: (configService: ConfigService) => ({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get<string>(ENVIRONMENT_VARS.RMQ_URI)],
              queue: configService.get<string>(
                ENVIRONMENT_VARS.RMQ_UPDATE_SECRET_QUEUE,
              ),
              queueOptions: {
                durable: configService.get<boolean>(
                  ENVIRONMENT_VARS.RMQ_UPDATE_SECRET_QUEUE_DURABLE,
                ),
              },
            },
          }),
          inject: [ConfigService],
        },
      ],
    }),
  ],
  providers: [RmqService],
  exports: [RmqService],
})
export class RmqModule {}
