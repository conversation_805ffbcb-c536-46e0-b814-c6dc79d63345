import * as express from 'express';

declare global {
  namespace Express {
    namespace Multer {
      interface File {
        /** Field name specified in the form */
        fieldname: string;

        /** Name of the file on the user's computer */
        originalname: string;

        /** Encoding type of the file */
        encoding: string;

        /** Mime type of the file */
        mimetype: string;

        /** File contents */
        buffer: Buffer;

        /** Size of the file in bytes */
        size: number;
      }
    }
  }
}
