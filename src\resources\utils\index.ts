import { BadRequestException } from '@nestjs/common';
import { RequestType } from '../../types';

//In future we may need to add other catalogs like mongodb for other resources
export const deleteTypeRequestTypes = {
  'Linux 8 & 9': RequestType.CREATE_VM_LINUX89,
  'Linux 7': RequestType.CREATE_VM_LINUX7,
  Ubuntu: RequestType.CREATE_VM_UBUNTU,
  Windows: RequestType.CREATE_VM_WINDOWS,
};

export const deleteRequestTypeResolver = (req) => {
  const resourceId = req.params?.resourceId;
  if (!resourceId) {
    console.log(`Resource Id is invalid ${resourceId}`);
    throw new BadRequestException(`Resource Id is invalid ${resourceId}`);
  }
  return resourceId;
};

export const MultipleRequestTypeResolver = (req) => {
  const resourceIds = req.body?.resourceIds;
  console.log(resourceIds, '::Recieved resourceIDs in resolver');
  if (!Array.isArray(resourceIds) || resourceIds.length === 0) {
    console.log(`Invalid resource Ids`, resourceIds);
    throw new BadRequestException(`Resource Ids are invalid`);
  }
  return resourceIds;
};

export const CatalogTypes = {
  LINUX_8_9: 'linux 8 & 9',
  WINDOWS: 'windows',
  UBUNTU: 'ubuntu',
  LINUX_CORPNET: 'linux corpnet',
  WINDOWS_CORPNET: 'windows corpnet',
  S3: 's3',
  NFS: 'nfs',
  MONGODB_6: 'mongodb 6',
  MONGODB_7: 'mongodb 7',
  POSTGRESDB_15: 'postgresdb 15',
  POSTGRESDB_16: 'postgresdb 16',
  REDISDB_6: 'redisdb 6',
  REDISDB_7: 'redisdb 7',
  ORACLEDB_19C: 'oracledb 19c',
  ORACLEDB_21C: 'oracledb 21c',
  SPECFLOW: 'specflow',
  LINUXV3: 'linux 8 & 9 v3',
  LINUXV3ADMIN: 'linux 8 & 9 v3 admin',
  UBUNTUV3: 'ubuntu v3',
  UBUNTUV3ADMIN: 'ubuntu v3 admin',
  WINDOWSV3: 'windows v3',
  WINDOWSV3ADMIN: 'windows v3 admin',
  NAMESPACE: 'Create-Namespace',
};

export const StorageStatus = {
  ACTIVE: 'ACTIVE',
  FAILED: 'FAILED',
};
