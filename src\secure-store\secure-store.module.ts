import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';
import { SecureStoreService } from './secure-store.service';
import { TokenService } from '../auth/token.service';
import { ENVIRONMENT_VARS } from '../utils/constants';
import { createAxiosInstance } from '../utils/helpers';
import { REQUEST } from '@nestjs/core';
import { AuthModule } from '../auth/auth.module';
import { SecureStoreController } from './secure-store.controller';
import { SecretsPoliciesModule } from './secretsPolicies/secretsPolicies.module';
import { SecretsMetadataModule } from './secretsMetadata/secretsMetadata.module';
import { SecretsMetadataRepository } from './secretsMetadata/secretsMetadata.repository';
import { EncryptionService } from '../encryption/encryption.service';
import { ResourcesRepository } from 'src/resources/resources.repository';
import { DevicesModule } from './devices/devices.module';
import { SecretsPoliciesRepository } from './secretsPolicies/secretsPolicies.repository';
import { ResourcesModule } from 'src/resources/resources.module';
import { SecretsPoliciesService } from './secretsPolicies/secretsPolicies.service';
import { ResourcesService } from '../resources/resources.service';
import { IntegrationNotificationModule } from '../naas/integration.notification.module';
import { IsEncryptedConstraint } from './validators/is-encrypted.decorator';
import { MultiEnvIAMModule } from '../multi-env/iam/iam.module';
import { RmqModule } from 'src/rmq/rmq.module';

@Module({
  imports: [
    AuthModule,
    SecretsPoliciesModule,
    SecretsMetadataModule,
    DevicesModule,
    ResourcesModule,
    IntegrationNotificationModule,
    MultiEnvIAMModule,
    RmqModule,
  ],
  controllers: [SecureStoreController],
  providers: [
    SecureStoreService,
    SecretsMetadataRepository,
    SecretsPoliciesRepository,
    SecretsPoliciesService,
    EncryptionService,
    IsEncryptedConstraint,
    {
      provide: 'SECRETS_MANAGEMENT_SERVICE_API',
      inject: [ConfigService, TokenService, REQUEST],
      useFactory: async (
        configService: ConfigService,
        tokenService: TokenService,
        req: Request,
      ): Promise<AxiosInstance> => {
        const baseUrlKey = configService.get(
          ENVIRONMENT_VARS.SECRETS_MANAGEMENT_SERVICE_BASE_URL,
        );
        const nebulaHeader = req.headers['x-nebula-authorization']
          ? (req.headers['x-nebula-authorization'] as string)
          : (req.headers['x-client-jwt'] as string);
        return await createAxiosInstance(
          configService,
          tokenService,
          nebulaHeader,
          baseUrlKey,
        );
      },
    },
    {
      provide: 'INTEGRATION_SERVICE_API',
      inject: [ConfigService, REQUEST],
      useFactory: async (
        configService: ConfigService,
        req: Request,
      ): Promise<AxiosInstance> => {
        return axios.create({
          baseURL: configService.get(
            ENVIRONMENT_VARS.INTEGRATION_API_SERVICE_BASE_URL,
          ),
          headers: {
            'x-nebula-authorization': req.headers['x-nebula-authorization'],
          },
        });
      },
    },
    {
      provide: 'WATCHER_SERVICE_API',
      inject: [ConfigService, TokenService, REQUEST],
      useFactory: async (
        configService: ConfigService,
        tokenService: TokenService,
        req: Request,
      ): Promise<AxiosInstance> => {
        const baseUrlKey = configService.get(
          ENVIRONMENT_VARS.WATCHER_SERVICE_BASE_URL,
        );
        const nebulaHeader = req.headers['x-nebula-authorization']
          ? (req.headers['x-nebula-authorization'] as string)
          : (req.headers['x-client-jwt'] as string);
        return await createAxiosInstance(
          configService,
          tokenService,
          nebulaHeader,
          baseUrlKey,
        );
      },
    },
  ],
  exports: [SecureStoreService, IsEncryptedConstraint],
})
export class SecureStoreModule {}
