import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { LoggerService } from 'src/loggers/logger.service';
import { ENVIRONMENT_VARS } from 'src/utils/constants';
import { v4 as uuidv4 } from 'uuid';

interface ErrorLog {
  timestamp: string;
  environment: string;
  servicename: string;
  details: {
    code: string;
    traceid: string;
    origin: string;
    message: string;
    detail: string;
  };
}

@Injectable()
export class ErrorDetails {
  constructor(
    private readonly logger: LoggerService,
    private readonly configService: ConfigService,
  ) {}

  logError(err: any, origin: string, errorCode: string, errorMessage: string, servicerequestId?: string) {
    const traceId =  servicerequestId ? servicerequestId : uuidv4();
    const errorLog: ErrorLog = {
      timestamp: new Date().toISOString(),
      environment: this.configService.get<string>(ENVIRONMENT_VARS.ENVIRONMENT),
      servicename: 'cloud-api-service',
      details: {
        code: errorCode,
        traceid: traceId,
        origin,
        message: errorMessage,
        detail: err?.message,
      },
    };
    this.logger.error(JSON.stringify(errorLog, null, 2));
  }
}
