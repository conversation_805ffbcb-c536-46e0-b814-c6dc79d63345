import {
  IsString,
  IsBoolean,
  IsArray,
  IsOptional,
  IsNotEmpty,
  IsNumber,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

class VcenterDetailsDto {
  @IsString()
  @IsNotEmpty()
  hostname: string;

  @IsNumber()
  @IsNotEmpty()
  port: number;

  @IsString()
  @IsNotEmpty()
  username: string;

  @IsString()
  @IsNotEmpty()
  password: string;
}

export class GetAllVcenterResponseDto {
  @IsString()
  @IsOptional()
  id: string;

  @IsString()
  @IsNotEmpty()
  cloudId: string;

  @IsString()
  @IsNotEmpty()
  cloudName: string;

  @IsString()
  @IsNotEmpty()
  vcenterHost: string;

  @IsString()
  @IsNotEmpty()
  vcenterMor: string;

  @IsString()
  @IsOptional()
  templateFolder: string;

  @IsString()
  @IsOptional()
  vmFolder: string;

  @IsBoolean()
  @IsOptional()
  disabled: boolean;

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  groups: string[];

  @IsArray()
  @IsOptional()
  projects: [];

  @ValidateNested()
  @Type(() => VcenterDetailsDto)
  vcenterDetails: VcenterDetailsDto;

  @IsString()
  @IsOptional()
  createdDate: string;

  @IsString()
  @IsOptional()
  dataCenterName: string;

  @IsString()
  @IsOptional()
  domainName: string;
}
