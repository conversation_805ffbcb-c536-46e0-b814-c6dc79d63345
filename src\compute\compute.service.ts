import {
  BadRequestException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import {
  NebulaConfigMap,
  RequestStatus,
  RequestType,
  ServiceCatalogName,
  ServiceCatalogType,
  VMWARE_TYPE,
} from '../types';
import { VmTemplateConfigRepository } from './vmTemplateConfig.repository';
import {
  ProvisionVmRequestDto,
  IpReservationDownstreamResponseDto,
  VmRetryRequestDto,
  VirtualMachineDto,
  ProvisionVmwareVmRequestDto,
} from './dto/provisionVm.dto';
import { ComputeWrapperService } from '../compute-wrapper/compute-wrapper.service';
import { RequestContext } from 'nestjs-request-context';
import { Tools, VMType, VmTypeRequestTypes } from './utils';
import { DatacentersRepository } from './datacenters.repository';
import { AssetsService } from '../assets/assets.service';
import { ConfigService } from '@nestjs/config';
import { EncryptionService } from '../encryption/encryption.service';
import { CachingService } from '../caching/caching.service';
import {
  BlueReferenceData,
  MorpheusData,
  ValidateHostNamesRequestDto,
  ValidateHostNamesRequestDtoV2,
} from './dto/compute.dto';
import * as ipRangeCheck from 'ip-range-check';
import * as IpAddress from 'ip-address';
export const ipv4 = IpAddress.Address4;
export const ipv6 = IpAddress.Address6;
export const ipRange = ipRangeCheck;
import { NebulaConfigRepository } from '../dbaas/nebulaConfig.repository';
import { IpmService } from '../ipm/ipm.service';
import { IPAddressType } from '../ipm/dto/ipAddressType';
import { LoggerService } from '../loggers/logger.service';
import { IamService } from '../iam/iam.service';
import { GetIpvAddressDto } from './dto/getAvailableIp.dto';
import { ProvisionBlueVmRequestDto } from './dto/provisionBlueVm.dto';
import { ServiceRequestEntity } from '../naas/entities/serviceRequest.entity';
import { RmqService } from '../rmq/rmq.service';
import {
  CUSTOM_SIZE,
  ENVIRONMENT_VARS,
  excludeFieldsInTotal,
  IpModes,
  MORPHEUS_CONSTANTS,
} from '../utils/constants';
import { ResourcesEntity } from '../naas/entities/resources.entity';
import { ResourcesService } from '../resources/resources.service';
import { PermissionKey } from '../iam/types';
import { ReConfigureDto } from './dto/reConfigure.dto';
import { EditVmRequestDto } from './dto/editVmRequest.dto';
import { ErrorDetails } from 'src/vmware-error-loggers/vmware-error-logger.service';
import { ERROR_DETAILS } from 'src/vmware-error-loggers/vmware-error-details';
@Injectable()
export class ComputeService {
  constructor(
    private readonly vmTemplateConfigRepository: VmTemplateConfigRepository,
    private readonly computeWrapperService: ComputeWrapperService,
    private readonly datacentersRepository: DatacentersRepository,
    private readonly assetsService: AssetsService,
    private configService: ConfigService,
    private encryptionService: EncryptionService,
    private readonly cachingService: CachingService,
    private readonly nebulaConfigRepository: NebulaConfigRepository,
    private readonly ipmService: IpmService,
    private readonly logger: LoggerService,
    private readonly iamService: IamService,
    private readonly rmqService: RmqService,
    private readonly resourcesService: ResourcesService,
    private readonly vmwareErrorlogService: ErrorDetails,
  ) {}

  async validateHostNames(
    validateHostNamesRequestDto: ValidateHostNamesRequestDto,
  ) {
    return await this.computeWrapperService.validateHostNames(
      validateHostNamesRequestDto,
    );
  }

  validateProjectId(project) {
    if (!project.appId) {
      throw new BadRequestException(
        `appId is not available for projectId: ${project.id}`,
      );
    }
  }

  //validateNetorkDetails like network id, ipv4, ipv6
  async validateNetorkDetails(provisionDbRequestDto, project, datacenters) {
    const map = new Map();
    const networkDetails = new Map();

    for (const datacenter of project.projectSettings.dataCenters) {
      map.set(datacenter.name, datacenter);
    }

    for (const item of provisionDbRequestDto.datacenterDetails) {
      const networks = map.get(item.datacenter).networks;

      const matchingDetails = networks.find(
        (network) => item.network.id === network.id,
      );

      if (!matchingDetails) {
        throw new BadRequestException(`Invalid network id ${item.network.id}`);
      }

      networkDetails.set(item.datacenter, matchingDetails);
    }

    return networkDetails;
  }

  async validateNetworkDetailsMultiENV(
    provisionDbRequestDto,
    project,
    datacenters,
  ) {
    const networkDetails = new Map();
    const networkSettings = project?.applications?.environments?.settings.find(
      (setting) => setting.type == 'VLAN',
    )?.configurations;
    for (const item of provisionDbRequestDto.datacenterDetails) {
      const dataCenterId = datacenters[item.datacenter].id;
      if (networkSettings.length) {
        let validNetwork = networkSettings.find(
          (networkSetting) =>
            networkSetting.dataCenter == dataCenterId &&
            networkSetting.domain ==
              provisionDbRequestDto.platformContext.domainId &&
            networkSetting.value.id == item.network.id,
        );
        if (validNetwork) {
          networkDetails.set(item.datacenter, validNetwork.value);
        } else {
          throw new BadRequestException(
            `Invalid network id ${item.network.id}`,
          );
        }
      }
    }
    return networkDetails;
  }

  async validateHostNamesV2(
    validateHostNamesRequestDto: ValidateHostNamesRequestDtoV2,
  ) {
    return await this.computeWrapperService.validateHostNamesV2(
      validateHostNamesRequestDto,
    );
  }

  private swapSpace(provisionVmRequestDto, templateConfig) {
    if (provisionVmRequestDto.swap > templateConfig.customMemory) {
      this.logger.log('swapSpace cannot be greater than custom Memory');
      throw new BadRequestException(
        'swapSpace cannot be greater than custom Memory',
      );
    }
  }

  async provisionVm(provisionVmRequestDto: ProvisionVmRequestDto) {
    let encryptedPassword = provisionVmRequestDto.password;
    if (
      provisionVmRequestDto.password &&
      provisionVmRequestDto.password ===
        this.configService.get('VM_DEFAULT_PASSWORD')
    ) {
      throw new BadRequestException('Vm Password cannot be Deafult Password');
    }

    if (
      provisionVmRequestDto.ipModes !== IpModes.DHCP &&
      !provisionVmRequestDto.network.subnetIpv4 &&
      !provisionVmRequestDto.network.subnetIpv6
    ) {
      throw new BadRequestException(
        'subnetIpv4 or subnetIpv6, at least one must be provided',
      );
    }

    let isEncrypted = false;
    if (provisionVmRequestDto.password) {
      isEncrypted = this.encryptionService.isEncrypted(
        provisionVmRequestDto.password,
      );
    }
    if (!isEncrypted && provisionVmRequestDto.password) {
      this.logger.log(
        'received non encrypted password as payload for vm create so calling encryption service to encrpyt',
      );
      encryptedPassword = await this.encryptionService.encrypt(
        provisionVmRequestDto.password,
      );
      provisionVmRequestDto.password = ''; //inorder to hide password from logs
    }

    if (
      provisionVmRequestDto.vmCount <= 1 &&
      provisionVmRequestDto.hostname?.length > 63
    ) {
      throw new BadRequestException('Vm Hostname cannot exceed 63 characters');
    } else if (
      provisionVmRequestDto.vmCount > 1 &&
      provisionVmRequestDto.hostname?.length > 60
    ) {
      throw new BadRequestException('Vm Hostname cannot exceed 60 characters');
    }

    this.logger.log(
      'fetching catalog 04 document for',
      provisionVmRequestDto.shortName,
    );
    const catalog04 = await this.iamService.getCatalog4ByShortName(
      provisionVmRequestDto.shortName,
    );
    this.logger.log('catalog 04 document:', catalog04);
    this.logger.log('fetching template conifg for cataogId:', catalog04?.id);

    const vmConfig: any = await this.getVmTemplateConfigByCatalogId(
      catalog04?.id,
      NebulaConfigMap.VIRTUAL_MACHINE,
    );

    const name = provisionVmRequestDto.name;
    provisionVmRequestDto.name = vmConfig.name;
    provisionVmRequestDto.catalogName = vmConfig.displayName;
    const datacenter = await this.datacentersRepository.findByName(
      provisionVmRequestDto.datacenter,
    );
    provisionVmRequestDto.inventoryAsn = datacenter.inventoryAsn;

    if (provisionVmRequestDto.addDisks) {
      for (const disk of provisionVmRequestDto.addDisks) {
        const maxDiskValue = +process.env.MAX_DISKVALUE;
        if (disk.diskValue > maxDiskValue) {
          this.logger.error(`diskValue cannot be greater than ${maxDiskValue}`);
          throw new BadRequestException(
            `diskValue cannot be greater than ${maxDiskValue}`,
          );
        }
      }
    }

    this.logger.debug(`VM size: ${provisionVmRequestDto?.size}`);
    const req = RequestContext.currentContext.req;
    const requestorPID = req?.user?.userId || null;
    provisionVmRequestDto.requestorPID = requestorPID;
    let templateConfig = {
      ...vmConfig.config.misc_config,
      [vmConfig.config.nebulaToMorpheusFieldMap.targetLayout]:
        provisionVmRequestDto.targetLayout.shortName,
      [vmConfig.config.nebulaToMorpheusFieldMap.targetImage]:
        `${provisionVmRequestDto.targetLayout.shortName}|${provisionVmRequestDto.targetLayout.id}`,
      [vmConfig.config.nebulaToMorpheusFieldMap.artifactory]:
        `${provisionVmRequestDto.cloudId}|${datacenter.artifactory}`,
    };

    if (provisionVmRequestDto.size == 'custom_size') {
      this.logger.log('adding total memory to config');
      let total = 0;
      for (const key in provisionVmRequestDto.customSizeOption) {
        if (!excludeFieldsInTotal.includes(key)) {
          total += Number(provisionVmRequestDto.customSizeOption[key]);
        }
      }
      provisionVmRequestDto.customSizeOption.total = total;

      if (total > 1000) {
        throw new BadRequestException('total memory must be less than 1000');
      }
      templateConfig = {
        ...templateConfig,
        ...provisionVmRequestDto?.customSizeOption,
      };
    } else {
      templateConfig = {
        ...templateConfig,
        ...vmConfig.config.size[provisionVmRequestDto.size],
      };
    }

    if (provisionVmRequestDto.swap) {
      this.swapSpace(provisionVmRequestDto, templateConfig);
    }

    const serviceRequest = {
      metadata: {
        serviceCatalog: {
          catalogName: ServiceCatalogName.PROVISION_VIRTUAL_SERVER,
          catalogType: ServiceCatalogType.NAAS,
        },
      },
      payload: {
        ...provisionVmRequestDto,
        templateConfig,
        requestPayloadVersion: 2,
        qualysActivationId: datacenter.qualysActivationId,
        qualysServerUrl: this.configService.get(
          ENVIRONMENT_VARS.QUALYS_SERVER_URL,
        ),
        qualysCustomerId: this.configService.get(
          ENVIRONMENT_VARS.QUALYS_CUSTOMER_ID,
        ),
      },
      requestType: VmTypeRequestTypes[name],

      status: RequestStatus.PENDING_APPROVAL,
    };
    this.logger.debug('Payload for VM ', serviceRequest.payload);
    serviceRequest.payload.password = encryptedPassword;
    serviceRequest.payload.requestorPID = requestorPID;
    const dbResponse = await this.assetsService.create(serviceRequest);
    const queued =
      await this.assetsService.queueIfApprovalNotRequired(dbResponse);

    return {
      id: dbResponse.serviceRequestId,
      serviceRequestId: dbResponse?.serviceRequestId,
      message: `VM Provisioning Request submitted for ${
        queued ? 'processing' : 'approval'
      }`,
    };
  }

  async provisionBlueVm(provisionBlueVmRequestDto: ProvisionBlueVmRequestDto) {
    this.logger.log(
      'fetching catalog 04 document for',
      provisionBlueVmRequestDto.shortName,
    );
    const catalog04 = await this.iamService.getCatalog4ByShortName(
      provisionBlueVmRequestDto.shortName,
    );
    this.logger.log('catalog 04 document:', catalog04);
    this.logger.log('fetching template conifg for cataogId:', catalog04?.id);

    const vmConfig: any = await this.getVmTemplateConfigByCatalogId(
      catalog04?.id,
      NebulaConfigMap.BLUE_VM,
    );

    const name = provisionBlueVmRequestDto.name;
    provisionBlueVmRequestDto.name = vmConfig.name;
    provisionBlueVmRequestDto.catalogName = vmConfig.displayName;

    if (provisionBlueVmRequestDto.addDisks) {
      for (const disk of provisionBlueVmRequestDto.addDisks) {
        const maxDiskValue = +process.env.BLUE_MAX_DISKVALUE;
        if (disk.diskValue > maxDiskValue) {
          this.logger.error(`diskValue cannot be greater than ${maxDiskValue}`);
          throw new BadRequestException(
            `diskValue cannot be greater than ${maxDiskValue}`,
          );
        }
      }
    }

    this.logger.debug(`VM size: ${provisionBlueVmRequestDto?.size}`);
    const req = RequestContext.currentContext.req;
    const requestorPID = req?.user?.userId || null;
    const requestorEmail = req.user?.email || null;
    provisionBlueVmRequestDto.requestorPID = requestorPID;
    provisionBlueVmRequestDto.requestorEmail = requestorEmail;

    const templateConfig = {
      ...vmConfig.config.size[provisionBlueVmRequestDto.size],
    };
    this.logger.debug('Template config ', templateConfig);
    const serviceRequest = {
      metadata: {
        serviceCatalog: {
          catalogName: ServiceCatalogName.PROVISION_VIRTUAL_SERVER,
          catalogType: ServiceCatalogType.IAAS,
        },
      },
      payload: {
        ...provisionBlueVmRequestDto,
        templateConfig,
        layout: vmConfig.config.morpheusCatalogId,
        platform: vmConfig.config.platform,
        cloudId: provisionBlueVmRequestDto.datacenter.id,
        requestPayloadVersion: 2,
      },
      requestType: VmTypeRequestTypes[name],

      status: RequestStatus.PENDING_APPROVAL,
    };
    this.logger.debug('Payload for VM ', serviceRequest.payload);
    serviceRequest.payload.requestorPID = requestorPID;
    const dbResponse = await this.assetsService.create(serviceRequest);

    const queued =
      await this.assetsService.queueIfApprovalNotRequired(dbResponse);

    return {
      id: dbResponse.serviceRequestId,
      serviceRequestId: dbResponse.serviceRequestId,
      message: `VM Provisioning Request submitted for ${
        queued ? 'processing' : 'approval'
      }`,
    };
  }

  async provisionVmRetry(
    requestType: RequestType,
    vmRetryRequestDto: VmRetryRequestDto,
  ) {
    // Push message to retry vm provisioning
    this.logger.log(`Pushing service request ${requestType} to queue`);
    const req = RequestContext.currentContext.req;
    const requestorPID = req?.user?.userId || null;
    const requestorEmail = req.user?.email || null;

    const resources: ResourcesEntity[] =
      await this.resourcesService.findByServiceRequestIdAndStatus(
        vmRetryRequestDto.requestId,
        ['SUCCESS'],
      );

    // Check if VM already provisioned or not.
    if (resources.length) {
      vmRetryRequestDto.virtualMachines?.forEach((vmDto: VirtualMachineDto) => {
        const resourceData: ResourcesEntity = resources.find(
          (item: ResourcesEntity) =>
            (
              item.resourcesDetails as Record<string, any>
            )?.config?.hostName?.toLowerCase() === vmDto.hostName.toLowerCase(),
        );
        if (resourceData) {
          throw new BadRequestException(
            `The VM request for hostname "${vmDto.hostName}" has already completed.`,
          );
        }
      });
    }

    // Update status
    const serviceRequest: ServiceRequestEntity =
      await this.assetsService.findByServiceRequestId(
        vmRetryRequestDto.requestId,
      );

    //checking Vm is related to VMware
    const isVmware = serviceRequest.requestType
      .toUpperCase()
      .includes(VMWARE_TYPE.VMWARE);

    //Check for MAX_RETRY_COUNT
    if (
      serviceRequest.retryCount &&
      serviceRequest.retryCount >=
        parseInt(this.configService.get(ENVIRONMENT_VARS.MAX_RETRY_COUNT))
    ) {
      throw new BadRequestException(
        `You have exceeded the maximum number of retries ${ENVIRONMENT_VARS.MAX_RETRY_COUNT}.`,
      );
    }

    await this.assetsService.update(serviceRequest.id, {
      status: RequestStatus.RETRYING,
    });
    await this.rmqService.pushMessage(
      isVmware ? RequestType.RETRY_VM_VMWARE : requestType,
      {
        id: vmRetryRequestDto.requestId,
        payload: {
          virtualMachines: vmRetryRequestDto.virtualMachines,
          requestorPID,
          requestorEmail,
        },
        serviceRequestId: vmRetryRequestDto.requestId,
      },
    );

    this.logger.log('Service request for retry submitted successfully.:', {
      id: vmRetryRequestDto.requestId,
      payload: vmRetryRequestDto.virtualMachines,
      serviceRequestId: vmRetryRequestDto.requestId,
    });

    return {
      id: vmRetryRequestDto.requestId,
      serviceRequestId: vmRetryRequestDto?.requestId,
      message: `Service request for retry submitted successfully.`,
    };
  }

  async getVmTemplateConfig(shortName: string, type: NebulaConfigMap) {
    const dbResponse = await this.nebulaConfigRepository.getNebulaConfig(
      shortName,
      type,
    );
    return dbResponse;
  }

  async retriggerVmCreation(vmRetryRequestDto: VmRetryRequestDto) {
    // Reset service request for new execution
    const updatedServiceRequest =
      await this.assetsService.resetServiceRequestForNewExecution(
        vmRetryRequestDto.requestId,
        { payload: vmRetryRequestDto },
      );

    const ipModes = updatedServiceRequest.payload['ipModes'];
    updatedServiceRequest.payload['vmCount'] =
      vmRetryRequestDto.virtualMachines.length;

    if (ipModes === 'static_auto') {
      updatedServiceRequest.payload['availableHostNames'] =
        vmRetryRequestDto.virtualMachines.map((vm) => vm.hostName);
    } else if (ipModes === 'static_manual') {
      updatedServiceRequest.payload['ipv4Addresses'] =
        vmRetryRequestDto.virtualMachines.map(
          (vm) => vm.ipAddress?.ipv4Address,
        );
      updatedServiceRequest.payload['ipv6Addresses'] =
        vmRetryRequestDto.virtualMachines.map(
          (vm) => vm.ipAddress?.ipv6Address,
        );
    } else {
      throw new BadRequestException(`Invalid IP mode: ${ipModes}`);
    }

    await this.rmqService.pushMessage(
      updatedServiceRequest.requestType,
      {
        id: updatedServiceRequest.id,
        payload: updatedServiceRequest.payload,
        serviceRequestId: vmRetryRequestDto.requestId,
        retryCount: updatedServiceRequest.retryCount,
      },
      null,
      updatedServiceRequest?.payload?.domain,
    );

    return updatedServiceRequest.payload;
  }

  async getVmTemplateConfigByCatalogId(
    catalogId: string,
    type: NebulaConfigMap,
  ) {
    const dbResponse =
      await this.nebulaConfigRepository.getNebulaConfigByCatalogId(
        catalogId,
        type,
      );
    return dbResponse;
  }

  async updateNebulaConfigByCatalogId(catalogId: string, config: object) {
    this.logger.log('updating latest config in db for caching', config);
    return await this.nebulaConfigRepository.updateNebulaConfigByCatalogId(
      catalogId,
      config,
    );
  }

  async fetchReferenceData(shortName: string) {
    this.logger.log('fetching catalog 04 document for', shortName);
    const catalog04 = await this.iamService.getCatalog4ByShortName(shortName);
    this.logger.log('catalog 04 document:', catalog04);
    this.logger.log('fetching template conifg for cataogId:', catalog04?.id);

    const templateConfig: any = await this.getVmTemplateConfigByCatalogId(
      catalog04?.id,
      NebulaConfigMap.VIRTUAL_MACHINE,
    );

    this.logger.log(
      'templateConfig :',
      templateConfig,
      ' for catalogId:',
      catalog04?.id,
    );

    const req = RequestContext.currentContext.req;
    const requestUser = req.user;
    const res = await this.computeWrapperService.fetchRefernceData(
      requestUser.email,
    );

    const layouts = await this.computeWrapperService.fetchLayouts(
      templateConfig.config.morpheusCatalogId,
    );
    return {
      size: templateConfig.config.size,
      groups: res.groups,
      layouts: layouts,
      vmMaxCount: process.env.VM_MAX_COUNT,
    };
  }

  async fetchBlueReferenceData(shortName: string): Promise<BlueReferenceData> {
    this.logger.log('fetching catalog 04 document for', shortName);
    const catalog04 = await this.iamService.getCatalog4ByShortName(shortName);
    this.logger.log('catalog 04 document:', catalog04);
    this.logger.log('fetching template conifg for cataogId:', catalog04?.id);

    const templateConfig: any = await this.getVmTemplateConfigByCatalogId(
      catalog04?.id,
      NebulaConfigMap.BLUE_VM,
    );

    this.logger.log(
      'templateConfig :',
      templateConfig,
      ' for catalogId:',
      catalog04?.id,
    );

    const groups = await this.computeWrapperService.fetchBlueGroups();

    const layouts = await this.computeWrapperService.fetchBlueLayouts(
      templateConfig.config.morpheusCatalogId,
    );
    const {
      backupOptions,
      patchingCycle,
      appTier,
      environment,
      datacenter,
      database,
    } = await this.checkCacheAndReturnMorpheusData(
      catalog04?.id,
      templateConfig.config,
    );
    return {
      size: templateConfig.config.size,
      groups: groups,
      layouts: layouts,
      vmMaxCount: process.env.VM_MAX_COUNT,
      backupOptions,
      patchingCycle,
      appTier,
      environment,
      datacenter,
      database,
      platform: templateConfig.config.platform,
    };
  }

  async checkCacheAndReturnMorpheusData(catalogId, config) {
    const timeDiff = this.getTimeDifference(config?.lastRefreshedAt);
    const timeThreshold = +process.env.BLUE_MORPHEUS_CACHE_THRESHOLD_MINUTES;
    if (!config?.lastRefreshedAt || timeDiff > timeThreshold) {
      this.logger.log(
        'no blue config data found in db cache, fetching from morpheus',
      );
      const refDataConfig = await this.getBlueMorpheusRefData();
      try {
        await this.updateNebulaConfigByCatalogId(catalogId, {
          ...refDataConfig,
          lastRefreshedAt: new Date(),
        });
      } catch (err) {
        this.logger.error(
          err,
          'Error in updating Morpheus Data in Nebula config',
        );
      }
      return refDataConfig;
    }
    this.logger.log('found cached config data from db!!');
    const {
      backupOptions,
      patchingCycle,
      appTier,
      environment,
      datacenter,
      database,
    } = config;
    return {
      backupOptions,
      patchingCycle,
      appTier,
      environment,
      datacenter,
      database,
    };
  }

  private getTimeDifference(updatedAt: string): number {
    const updatedTime = new Date(updatedAt).getTime();
    const currentTime = new Date().getTime();
    return (currentTime - updatedTime) / (1000 * 60); // Convert to minutes
  }

  async getBlueMorpheusRefData() {
    const optionTypeLists =
      await this.computeWrapperService.fetchOptionTypeList();
    const backupOptionsId = optionTypeLists.find(
      (e) => e.name === MORPHEUS_CONSTANTS.BACKUP_OPTIONS,
    )?.id;
    const appEnvId = optionTypeLists.find(
      (e) => e.name === MORPHEUS_CONSTANTS.APPLICATION_ENVIRONMENT,
    )?.id;
    const appTierId = optionTypeLists.find(
      (e) => e.name === MORPHEUS_CONSTANTS.APP_TIER,
    )?.id;
    const patchCycleId = optionTypeLists.find(
      (e) => e.name === MORPHEUS_CONSTANTS.PATCH_CYCLE,
    )?.id;
    const promises = [
      this.fetchBlueOptionTypeListItems(backupOptionsId),
      this.fetchBlueOptionTypeListItems(patchCycleId),
      this.fetchBlueOptionTypeListItems(appTierId),
      this.fetchBlueOptionTypeListItems(appEnvId),
      this.computeWrapperService.fetchBlueDatacenter(),
      this.computeWrapperService.fetchOptionTypeListItemsDb(),
    ];
    const results = await Promise.all(promises);
    const [
      backupOptions,
      patchingCycle,
      appTier,
      environment,
      datacenter,
      database,
    ] = results;
    return {
      backupOptions,
      patchingCycle,
      appTier,
      environment,
      datacenter,
      database,
    };
  }

  async fetchBlueOptionTypeListItems(id): Promise<MorpheusData[]> {
    return await this.computeWrapperService.fetchOptionTypeListItems(id);
  }
  async deleteVm(id: number) {
    if (!id) {
      throw new BadRequestException(`Invalid vm id`);
    }
    const response = await this.computeWrapperService.deleteVm(id);
    return response;
  }

  async fetchCacheResourceData(cloudId: number, email: string) {
    try {
      const resources = await this.cachingService.fetchResources(
        cloudId,
        email,
      );
      if (!resources) {
        this.logger.debug(
          'Data is not available in cache, trying to get it from morpheus',
        );
        return await this.computeWrapperService.fetchResources(cloudId, email);
      } else {
        return resources;
      }
    } catch (error) {
      this.logger.error(error, 'error while fetching cached data');
      return await this.computeWrapperService.fetchResources(cloudId, email);
    }
  }

  validateIpRangeInNetworkBlock(ipAddress: string, networkBlock: string): any {
    if (!ipRange(ipAddress, networkBlock)) {
      this.logger.debug(
        `ipaddress ${ipAddress} is not in range of network block ${networkBlock}`,
      );
      return false;
    }
    return true;
  }

  async validateStaticIps(staticIp) {
    try {
      const message = [];
      if (staticIp.ipv4) {
        if (staticIp.vmCount !== staticIp.ipv4Addresses.length) {
          throw new BadRequestException(
            'vm count and number of ipv4 addresses provided should match',
          );
        }

        staticIp.ipv4Addresses.forEach((ipAddress) => {
          const valid = this.validateIpRangeInNetworkBlock(
            ipAddress,
            staticIp.network.subnetIpv4,
          );
          if (!valid) {
            throw new BadRequestException(`invalid ip address ${ipAddress}`);
          }
        });
        let availableIpsResponse;
        try {
          availableIpsResponse = await this.ipmService.checkAvailabilityOfIps({
            ipAddresses: staticIp.ipv4Addresses,
            ipAddressType: IPAddressType.IPV4,
          });
        } catch (error) {
          this.vmwareErrorlogService.logError(
            error,
            'checkAvailabilityOfIps',
            ERROR_DETAILS.VMWARE_PROVISION_ERROR_CODE,
            ERROR_DETAILS.VMWARE_PROVISION_IP_AVAILABILITY_ERROR_MESSAGE,
          );
        }

        this.logger.debug('Available IP addresses ', availableIpsResponse);
        for (const ipAddress of availableIpsResponse) {
          if (!ipAddress.available) {
            message.push(`${ipAddress.ipAddress} is ${ipAddress.comment}`);
          }
        }
      }
      if (staticIp.ipv6) {
        if (staticIp.vmCount !== staticIp.ipv6Addresses.length) {
          throw new BadRequestException(
            'vm count and number of ipv6 addresses provided should match',
          );
        }
        staticIp.ipv6Addresses.forEach((ipAddress) => {
          const valid = this.validateIpRangeInNetworkBlock(
            ipAddress,
            staticIp.network.subnetIpv6,
          );
          if (!valid) {
            throw new BadRequestException(`invalid ip address ${ipAddress}`);
          }
        });

        let availableIpsResponse;
        try {
          availableIpsResponse = await this.ipmService.checkAvailabilityOfIps({
            ipAddresses: staticIp.ipv6Addresses,
            ipAddressType: IPAddressType.IPV6,
          });
        } catch (error) {
          this.vmwareErrorlogService.logError(
            error,
            'checkAvailabilityOfIps',
            ERROR_DETAILS.VMWARE_PROVISION_ERROR_CODE,
            ERROR_DETAILS.VMWARE_PROVISION_IP_AVAILABILITY_ERROR_MESSAGE,
          );
        }
        this.logger.debug('Available IP addresses ', availableIpsResponse);
        for (const ipAddress of availableIpsResponse) {
          if (!ipAddress.available) {
            message.push(`${ipAddress.ipAddress} is ${ipAddress.comment}`);
          }
        }
      }
      if (message.length) {
        throw new BadRequestException(`${message.join(',')}`);
      }
    } catch (error) {
      this.vmwareErrorlogService.logError(
        error,
        'validateStaticIps',
        ERROR_DETAILS.VMWARE_PROVISION_ERROR_CODE,
        ERROR_DETAILS.VMWARE_PROVISION_VALIDATE_STATIC_IP_ERROR_MESSAGE,
      );
    }
  }

  async getAvailableIps(data: GetIpvAddressDto) {
    this.logger.debug('Calling ipmService for getting available ips');
    return await this.ipmService.getAvailableIps(data);
  }

  async appendIpReservationDownstreamResponse(
    serviceRequestId: string,
    ipReservationDownstreamResponseDto: IpReservationDownstreamResponseDto,
  ): Promise<ServiceRequestEntity> {
    this.logger.debug(
      'Calling assetsService for updating DownstreamResponseData',
    );
    return await this.assetsService.appendDownstreamResponseData(
      serviceRequestId,
      ipReservationDownstreamResponseDto,
    );
  }

  async fetchCacheResourceDataGeneric(
    environment: string,
    cloudId: number,
    email: string,
  ) {
    try {
      return await this.computeWrapperService.fetchResourcesGeneric(
        environment,
        cloudId,
        email,
      );
    } catch (error) {
      this.logger.error(error, 'error while fetching cached data');
      return await this.computeWrapperService.fetchResources(cloudId, email);
    }
  }

  async provisionVmGeneric(
    environment: string,
    provisionVmRequestDto: ProvisionVmRequestDto,
  ) {
    let encryptedPassword = provisionVmRequestDto.password;
    if (
      provisionVmRequestDto.password &&
      provisionVmRequestDto.password ===
        this.configService.get('VM_DEFAULT_PASSWORD')
    ) {
      throw new BadRequestException('Vm Password cannot be Deafult Password');
    }

    if (
      provisionVmRequestDto.ipModes !== IpModes.DHCP &&
      !provisionVmRequestDto.network.subnetIpv4 &&
      !provisionVmRequestDto.network.subnetIpv6
    ) {
      throw new BadRequestException(
        'subnetIpv4 or subnetIpv6, at least one must be provided',
      );
    }

    let isEncrypted = false;
    if (provisionVmRequestDto.password) {
      isEncrypted = this.encryptionService.isEncrypted(
        provisionVmRequestDto.password,
      );
    }
    if (!isEncrypted && provisionVmRequestDto.password) {
      this.logger.log(
        'received non encrypted password as payload for vm create so calling encryption service to encrpyt',
      );
      encryptedPassword = await this.encryptionService.encrypt(
        provisionVmRequestDto.password,
      );
      provisionVmRequestDto.password = ''; //inorder to hide password from logs
    }

    if (
      provisionVmRequestDto.vmCount <= 1 &&
      provisionVmRequestDto.hostname?.length > 63
    ) {
      throw new BadRequestException('Vm Hostname cannot exceed 63 characters');
    } else if (
      provisionVmRequestDto.vmCount > 1 &&
      provisionVmRequestDto.hostname?.length > 60
    ) {
      throw new BadRequestException('Vm Hostname cannot exceed 60 characters');
    }

    this.logger.log(
      'fetching catalog 04 document for',
      provisionVmRequestDto.shortName,
    );
    const catalog04 = await this.iamService.getCatalog4ByShortName(
      provisionVmRequestDto.shortName,
    );
    this.logger.log('catalog 04 document:', catalog04);
    this.logger.log('fetching template conifg for cataogId:', catalog04?.id);

    const vmConfig: any = await this.getVmTemplateConfigByCatalogId(
      catalog04?.id,
      NebulaConfigMap.VIRTUAL_MACHINE,
    );

    const name = provisionVmRequestDto.name;
    provisionVmRequestDto.name = vmConfig.name;
    provisionVmRequestDto.catalogName = vmConfig.displayName;
    const datacenter = await this.datacentersRepository.findByName(
      provisionVmRequestDto.datacenter,
    );
    provisionVmRequestDto.inventoryAsn = datacenter.inventoryAsn;

    if (provisionVmRequestDto.addDisks) {
      for (const disk of provisionVmRequestDto.addDisks) {
        const maxDiskValue = +process.env.MAX_DISKVALUE;
        if (disk.diskValue > maxDiskValue) {
          this.logger.error(`diskValue cannot be greater than ${maxDiskValue}`);
          throw new BadRequestException(
            `diskValue cannot be greater than ${maxDiskValue}`,
          );
        }
      }
    }

    this.logger.debug(`VM size: ${provisionVmRequestDto?.size}`);
    const req = RequestContext.currentContext.req;
    const requestorPID = req?.user?.userId || null;
    provisionVmRequestDto.requestorPID = requestorPID;

    const templateConfig = {
      ...vmConfig.config.misc_config,
      [vmConfig.config.nebulaToMorpheusFieldMap.targetLayout]:
        provisionVmRequestDto.targetLayout.shortName,
      [vmConfig.config.nebulaToMorpheusFieldMap.targetImage]:
        `${provisionVmRequestDto.targetLayout.shortName}|${provisionVmRequestDto.targetLayout.id}`,
      ...vmConfig.config.size[provisionVmRequestDto.size],
      [vmConfig.config.nebulaToMorpheusFieldMap.artifactory]:
        `${provisionVmRequestDto.cloudId}|${datacenter.artifactory}`,
    };

    if (environment == VMType.RED_VM_ENVIRONMENT_TYPE) {
      if (provisionVmRequestDto.securityTools.includes(Tools.QUALYS)) {
        const validQualysRhelVersions = this.configService
          .get(ENVIRONMENT_VARS.QUALYS_ALLOWED_LAYOUT)
          .split(',');
        if (
          !validQualysRhelVersions.includes(
            provisionVmRequestDto.targetLayout.shortName,
          )
        ) {
          throw new BadRequestException(
            ` Qualys Installation is enabled only for ${this.configService.get(
              ENVIRONMENT_VARS.QUALYS_ALLOWED_LAYOUT,
            )} layout(s)`,
          );
        }
      }

      if (provisionVmRequestDto.securityTools.includes(Tools.CENTRIFY)) {
        const validCentrifyRhelVersions = this.configService
          .get(ENVIRONMENT_VARS.CENTRIFY_ALLOWED_LAYOUT)
          .split(',');
        if (
          !validCentrifyRhelVersions.includes(
            provisionVmRequestDto.targetLayout.shortName,
          )
        ) {
          throw new BadRequestException(
            `Centrify Installation is enabled only for ${this.configService.get(
              ENVIRONMENT_VARS.CENTRIFY_ALLOWED_LAYOUT,
            )} layout(s)`,
          );
        }
      }

      if (provisionVmRequestDto.complianceTools.includes(Tools.TANIUM)) {
        const validTaniumRhelVersions = this.configService
          .get(ENVIRONMENT_VARS.TANIUM_ALLOWED_LAYOUT)
          .split(',');
        if (
          !validTaniumRhelVersions.includes(
            provisionVmRequestDto.targetLayout.shortName,
          )
        ) {
          throw new BadRequestException(
            `Tanium Installation is only for ${this.configService.get(
              ENVIRONMENT_VARS.TANIUM_ALLOWED_LAYOUT,
            )} OS in Linux 8 & 9`,
          );
        }
      }
    }
    this.logger.debug('Template config ', templateConfig);
    const serviceRequest = {
      metadata: {
        serviceCatalog: {
          catalogName: ServiceCatalogName.PROVISION_VIRTUAL_SERVER,
          catalogType: ServiceCatalogType.NAAS,
        },
      },
      payload: {
        ...provisionVmRequestDto,
        templateConfig,
        requestPayloadVersion: 2,
        qualysActivationId: datacenter.qualysActivationId,
        qualysServerUrl: this.configService.get(
          ENVIRONMENT_VARS.QUALYS_SERVER_URL,
        ),
        qualysCustomerId: this.configService.get(
          ENVIRONMENT_VARS.QUALYS_CUSTOMER_ID,
        ),
      },
      requestType: VmTypeRequestTypes[name],

      status: RequestStatus.PENDING_APPROVAL,
    };
    this.logger.debug('Payload for VM ', serviceRequest.payload);
    serviceRequest.payload.password = encryptedPassword;
    serviceRequest.payload.requestorPID = requestorPID;
    const dbResponse = await this.assetsService.create(serviceRequest);
    const queued =
      await this.assetsService.queueIfApprovalNotRequired(dbResponse);

    return {
      id: dbResponse.serviceRequestId,
      serviceRequestId: dbResponse.serviceRequestId,
      message: `VM Provisioning Request submitted for ${
        queued ? 'processing' : 'approval'
      }`,
    };
  }

  async fetchReferenceDataGeneric(environment: string, shortName: string) {
    this.logger.log('fetching catalog 04 document for', shortName);
    const catalog04 = await this.iamService.getCatalog4ByShortName(shortName);
    this.logger.log('catalog 04 document:', catalog04);
    this.logger.log('fetching template conifg for cataogId:', catalog04?.id);

    const templateConfig: any = await this.getVmTemplateConfigByCatalogId(
      catalog04?.id,
      NebulaConfigMap.VIRTUAL_MACHINE,
    );

    this.logger.log(
      'templateConfig :',
      templateConfig,
      ' for catalogId:',
      catalog04?.id,
    );

    const req = RequestContext.currentContext.req;
    const requestUser = req.user;
    const res = await this.computeWrapperService.fetchRefernceDataGeneric(
      environment,
      requestUser.email,
    );

    const layouts = await this.computeWrapperService.fetchLayoutsGeneric(
      environment,
      templateConfig.config.morpheusCatalogId,
    );
    return {
      size: templateConfig.config.size,
      groups: res.groups,
      layouts: layouts,
      vmMaxCount: process.env.VM_MAX_COUNT,
    };
  }

  async validateHostNamesGeneric(
    envrionment: string,
    validateHostNamesRequestDto: ValidateHostNamesRequestDto,
  ) {
    return await this.computeWrapperService.validateHostNamesGeneric(
      envrionment,
      validateHostNamesRequestDto,
    );
  }

  async getGenericNetwork(
    environment: string,
    cloudId: string,
    catalogName: string,
  ) {
    const catalog = catalogName.includes('linux') ? 'linux' : 'windows';
    return await this.computeWrapperService.getGenericNetwork(
      environment,
      cloudId,
      catalog,
    );
  }

  async getGenericAppRef(environment: string, appRef: string) {
    return await this.computeWrapperService.getGenericAppRef(
      environment,
      appRef,
    );
  }
  async getGenericADGroup(environment: string, adGroup: string) {
    return await this.computeWrapperService.getGenericADGroup(
      environment,
      adGroup,
    );
  }

  private async getProjectDetails(projectId: string) {
    const project = await this.iamService.getProjectById(projectId);
    return project?.projectName;
  }

  async searchVmDetails(searchVMQueryDTO, environment) {
    this.logger.log('passing search vm details params');
    const searchVmDetails = await this.computeWrapperService.searchVmDetails(
      searchVMQueryDTO,
      environment,
    );

    const morpheusInstanceIds = searchVmDetails.instances.map(
      (instance) => instance?.id,
    );

    this.logger.log(
      'morpheus instanceids',
      JSON.stringify(morpheusInstanceIds),
    );

    const matchedDbRecords: any[] =
      await this.resourcesService.getResourcesByInstanceIds(
        morpheusInstanceIds,
      );
    this.logger.log('matches db records', matchedDbRecords);

    const matchedInstanceIds = new Map(
      matchedDbRecords.map((record) => [
        record?.resourcesDetails.id,
        record?.projectId,
      ]),
    );

    this.logger.log('matched instance ids', matchedInstanceIds);

    const updatedInstances = await Promise.all(
      searchVmDetails.instances
        .filter((instance) => instance?.status !== 'failed')
        .map(async (instance) => {
          const projectId = matchedInstanceIds.get(instance?.id);
          const projectName = projectId
            ? await this.getProjectDetails(projectId)
            : null;

          return {
            ...instance,
            nebulaOnboarded: matchedInstanceIds.has(instance?.id),
            projectName,
          };
        }),
    );

    const searchResults = {
      items: updatedInstances,
      pageInfo: searchVmDetails.pageInfo,
    };
    return searchResults;
  }

  private getUserDetail() {
    const req = RequestContext.currentContext.req;
    return {
      requestorEmail: req.user?.email,
      requestorPID: req?.user?.userId || null,
    };
  }

  async fetchUsersRoles() {
    const email = this.getUserDetail().requestorEmail;
    this.logger.log('passing email to get user roles');
    const userRoles = await this.computeWrapperService.fetchUsersRoles(email);
    this.logger.log('user roles received');
    return userRoles;
  }

  async getNetworkByNetworkId(id: number) {
    return await this.computeWrapperService.getNetworkByNetworkId(id);
  }

  async fetchReferenceDataVmware(
    domain: string,
    datacenter: string,
    shortName: string,
  ) {
    try {
      this.logger.log('fetching catalog 04 document for', shortName);
      let catalog04;
      try {
        catalog04 = await this.iamService.getCatalog4ByShortName(shortName);
      } catch (error) {
        this.vmwareErrorlogService.logError(
          error,
          'getCatalog4ByShortName',
          ERROR_DETAILS.VMWARE_FETCH_REFERNCE_ERROR_CODE,
          ERROR_DETAILS.VMWARE_FETCH_REFERNCE_IAM_SERVICE_ERROR_MESSAGE,
        );
      }

      let templateConfig: any;
      try {
        templateConfig = await this.getVmTemplateConfigByCatalogId(
          catalog04?.id,
          NebulaConfigMap.VIRTUAL_MACHINE,
        );
      } catch (error) {
        this.vmwareErrorlogService.logError(
          error,
          'getVmTemplateConfigByCatalogId',
          ERROR_DETAILS.VMWARE_FETCH_REFERNCE_ERROR_CODE,
          ERROR_DETAILS.VMWARE_FETCH_REFERNCE_TEMPLATE_CONFIG_ERROR_MESSAGE,
        );
      }

      this.logger.log(
        'templateConfig :',
        templateConfig,
        ' for catalogId:',
        catalog04?.id,
      );

      const req = RequestContext.currentContext.req;
      const requestUser = req.user;

      let layouts;
      try {
        layouts = await this.computeWrapperService.fetchLayoutsVmware(
          domain,
          datacenter,
          shortName,
        );
      } catch (error) {
        this.vmwareErrorlogService.logError(
          error,
          'fetchLayoutsVmware',
          ERROR_DETAILS.VMWARE_FETCH_REFERNCE_ERROR_CODE,
          ERROR_DETAILS.VMWARE_FETCH_REFERNCE_LAYOUT_SERVICE_ERROR_MESSAGE,
        );
      }

      this.logger.log('reference data: layouts -> ', layouts);
      const response = {
        size: templateConfig.config.size,
        layouts: layouts,
        vmMaxCount: process.env.VM_MAX_COUNT,
      };
      this.logger.log('reference data: -> ', response);
      return response;
    } catch (error) {
      this.vmwareErrorlogService.logError(
        error,
        'fetchReferenceDataVmware',
        ERROR_DETAILS.VMWARE_FETCH_REFERNCE_ERROR_CODE,
        ERROR_DETAILS.VMWARE_FETCH_REF_ERROR_MESSAGE,
      );
    }
  }

  async validateHostNamesVmware(
    domain: string,
    datacenter: string,
    validateHostNamesRequestDto: ValidateHostNamesRequestDto,
  ) {
    try {
      return await this.computeWrapperService.validateHostNamesVmware(
        domain,
        datacenter,
        validateHostNamesRequestDto,
      );
    } catch (error) {
      this.vmwareErrorlogService.logError(
        error,
        'validateHostNamesVmware',
        ERROR_DETAILS.VMWARE_VALIDATE_HOSTNAME_ERROR_CODE,
        ERROR_DETAILS.VMWARE_VALIDATE_HOSTNAME_ERROR_MESSAGE,
      );
    }
  }

  async provisionVmVmware(
    domain: string,
    datacenter: string,
    provisionVmRequestDto: ProvisionVmwareVmRequestDto,
  ) {
    try {
      let encryptedPassword = provisionVmRequestDto.password;
      if (
        provisionVmRequestDto.password &&
        provisionVmRequestDto.password ===
          this.configService.get('VM_DEFAULT_PASSWORD')
      ) {
        this.logger.log('Vm Password cannot be Deafult Password');
        throw new BadRequestException('Vm Password cannot be Deafult Password');
      }

      if (
        provisionVmRequestDto.ipModes !== IpModes.DHCP &&
        !provisionVmRequestDto.network.subnetIpv4 &&
        !provisionVmRequestDto.network.subnetIpv6
      ) {
        this.logger.log(
          'subnetIpv4 or subnetIpv6, at least one must be provided',
        );
        throw new BadRequestException(
          'subnetIpv4 or subnetIpv6, at least one must be provided',
        );
      }

      let isEncrypted = false;
      if (provisionVmRequestDto.password) {
        isEncrypted = this.encryptionService.isEncrypted(
          provisionVmRequestDto.password,
        );
      }
      if (!isEncrypted && provisionVmRequestDto.password) {
        this.logger.log(
          'received non encrypted password as payload for vm create so calling encryption service to encrpyt',
        );
        try {
          encryptedPassword = await this.encryptionService.encrypt(
            provisionVmRequestDto.password,
          );
        } catch (error) {
          this.vmwareErrorlogService.logError(
            error,
            'encrypt',
            ERROR_DETAILS.VMWARE_PROVISION_ERROR_CODE,
            ERROR_DETAILS.VMWARE_PROVISION_ENCRYPTION_ERROR_MESSAGE,
          );
        }

        provisionVmRequestDto.password = ''; //inorder to hide password from logs
      }

      if (
        provisionVmRequestDto.vmCount <= 1 &&
        provisionVmRequestDto.hostname?.length > 63
      ) {
        throw new BadRequestException(
          'Vm Hostname cannot exceed 63 characters',
        );
      } else if (
        provisionVmRequestDto.vmCount > 1 &&
        provisionVmRequestDto.hostname?.length > 60
      ) {
        throw new BadRequestException(
          'Vm Hostname cannot exceed 60 characters',
        );
      }

      this.logger.log(
        'fetching catalog 04 document for',
        provisionVmRequestDto.shortName,
      );
      let catalog04;
      try {
        catalog04 = await this.iamService.getCatalog4ByShortName(
          provisionVmRequestDto.shortName,
        );
        this.logger.log('catalog 04 document:', catalog04);
        this.logger.log(
          'fetching template conifg for cataogId:',
          catalog04?.id,
        );
      } catch (error) {
        this.vmwareErrorlogService.logError(
          error,
          'getCatalog4ByShortName',
          ERROR_DETAILS.VMWARE_PROVISION_ERROR_CODE,
          ERROR_DETAILS.VMWARE_PROVISION_IAM_SERVICE_ERROR_MESSAGE,
        );
      }

      let vmConfig;
      try {
        vmConfig = await this.getVmTemplateConfigByCatalogId(
          catalog04?.id,
          NebulaConfigMap.VIRTUAL_MACHINE,
        );
        this.logger.log('vmConfig document:', vmConfig);
      } catch (error) {
        this.vmwareErrorlogService.logError(
          error,
          'getVmTemplateConfigByCatalogId',
          ERROR_DETAILS.VMWARE_PROVISION_ERROR_CODE,
          ERROR_DETAILS.VMWARE_PROVISION_TEMPLATE_CONFIG_ERROR_MESSAGE,
        );
      }

      const name = provisionVmRequestDto.name;

      this.logger.log('name:', name);

      provisionVmRequestDto.name = vmConfig.name;
      provisionVmRequestDto.catalogName = vmConfig.displayName;
      let datacenterRecord;
      try {
        datacenterRecord = await this.datacentersRepository.findByName(
          provisionVmRequestDto.datacenter,
        );

        this.logger.log('datacenter document:', datacenterRecord);
      } catch (error) {
        this.vmwareErrorlogService.logError(
          error,
          'findByName',
          ERROR_DETAILS.VMWARE_PROVISION_ERROR_CODE,
          ERROR_DETAILS.VMWARE_PROVISION_REPO_ERROR_MESSAGE,
        );
      }

      provisionVmRequestDto.inventoryAsn = datacenterRecord.inventoryAsn;

      if (provisionVmRequestDto.addDisks) {
        for (const disk of provisionVmRequestDto.addDisks) {
          const maxDiskValue = +process.env.MAX_DISKVALUE;
          if (disk.diskValue > maxDiskValue) {
            this.logger.error(
              `diskValue cannot be greater than ${maxDiskValue}`,
            );
            throw new BadRequestException(
              `diskValue cannot be greater than ${maxDiskValue}`,
            );
          }
        }
      }

      this.logger.debug(`VM size: ${provisionVmRequestDto?.size}`);
      const req = RequestContext.currentContext.req;
      const requestorPID = req?.user?.userId || null;
      provisionVmRequestDto.requestorPID = requestorPID;

      let templateConfig;
      if (provisionVmRequestDto.size == CUSTOM_SIZE) {
        this.logger.log('adding total memory to config');
        let total = 0;
        for (const key in provisionVmRequestDto.customSizeOption) {
          if (!excludeFieldsInTotal.includes(key)) {
            total += Number(provisionVmRequestDto.customSizeOption[key]);
          }
        }
        provisionVmRequestDto.customSizeOption.total = total;

        if (total > Number(process.env.TOTAL_MEMORY_VALUE)) {
          throw new BadRequestException('total memory must be less than 1000');
        }
        templateConfig = {
          ...provisionVmRequestDto?.customSizeOption,
        };
      } else {
        templateConfig = {
          ...vmConfig.config.size[provisionVmRequestDto.size],
        };
      }

      if (provisionVmRequestDto.swap) {
        this.swapSpace(provisionVmRequestDto, templateConfig);
      }
      this.logger.debug('Template config ', templateConfig);
      const serviceRequest = {
        metadata: {
          serviceCatalog: {
            catalogName: ServiceCatalogName.PROVISION_VIRTUAL_SERVER,
            catalogType: ServiceCatalogType.IAAS,
          },
        },
        payload: {
          ...provisionVmRequestDto,
          defaultQuota: vmConfig.config?.defaultQuota,
          templateConfig,
          domain,
          datacenter,
          requestPayloadVersion: 2,
          qualysActivationId: datacenterRecord.qualysActivationId,
          qualysServerUrl: this.configService.get(
            ENVIRONMENT_VARS.QUALYS_SERVER_URL,
          ),
          qualysCustomerId: this.configService.get(
            ENVIRONMENT_VARS.QUALYS_CUSTOMER_ID,
          ),
        },
        requestType: VmTypeRequestTypes[name],

        status: RequestStatus.PENDING_APPROVAL,
      };
      this.logger.debug('Payload for VM ', serviceRequest.payload);
      serviceRequest.payload.password = encryptedPassword;
      serviceRequest.payload.requestorPID = requestorPID;
      const userGroups = req?.user?.groups || null;

      let allowedGroups;
      try {
        allowedGroups = await this.iamService.findCatalogL4Groups(
          provisionVmRequestDto.shortName,
          PermissionKey.CREATE,
        );
      } catch (error) {
        this.vmwareErrorlogService.logError(
          error,
          'findCatalogL4Groups',
          ERROR_DETAILS.VMWARE_PROVISION_ERROR_CODE,
          ERROR_DETAILS.VMWARE_PROVISION_CATALOG_L4_ERROR_MESSAGE,
        );
      }

      const commonGroups = allowedGroups?.filter((group) =>
        userGroups.includes(group.name),
      );
      const commonGroupsResponse = commonGroups.map((group) => ({
        id: group.id,
        name: group.name,
      }));

      serviceRequest.payload.groups = commonGroupsResponse;
      serviceRequest.payload.defaultQuota = vmConfig.config.defaultQuota;

      let dbResponse;
      try {
        dbResponse = await this.assetsService.create(serviceRequest);
      } catch (error) {
        this.vmwareErrorlogService.logError(
          error,
          'create',
          ERROR_DETAILS.VMWARE_PROVISION_ERROR_CODE,
          ERROR_DETAILS.VMWARE_PROVISION_ASSET_CREATE_ERROR_MESSAGE,
        );
      }

      let queued;
      try {
        queued =
          await this.assetsService.queueIfApprovalNotRequired(dbResponse);
      } catch (error) {
        this.vmwareErrorlogService.logError(
          error,
          'queueIfApprovalNotRequired',
          ERROR_DETAILS.VMWARE_PROVISION_ERROR_CODE,
          ERROR_DETAILS.VMWARE_PROVISION_ASSET_QUEUE_ERROR_MESSAGE,
        );
      }

      return {
        id: dbResponse.serviceRequestId,
        message: `VM Provisioning Request submitted for
      ${queued ? 'processing' : 'approval'}`,
      };
    } catch (error) {
      this.vmwareErrorlogService.logError(
        error,
        'provisionVmVmware',
        ERROR_DETAILS.VMWARE_PROVISION_ERROR_CODE,
        ERROR_DETAILS.VMWARE_PROVISION_ERROR,
      );
    }
  }

  private async reConfigureVmRequestChange(payload) {
    const reConfigureVmRequest = {
      metadata: {
        serviceCatalog: {
          catalogName: ServiceCatalogName.RECONFIGURE_VM,
          catalogType: ServiceCatalogType.IAAS,
        },
      },
      payload: payload,
      requestType: RequestType.RECONFIGURE_VM,
      status: RequestStatus.PENDING_APPROVAL,
    };

    this.logger.debug(
      'serviceRequest for ReConfigure VM ',
      reConfigureVmRequest,
    );
    const dbResponse = await this.assetsService.create(reConfigureVmRequest);

    const queued =
      await this.assetsService.queueIfApprovalNotRequired(dbResponse);

    reConfigureVmRequest['serviceRequestId'] = dbResponse.serviceRequestId;
    reConfigureVmRequest['id'] = dbResponse.id;

    return {
      id: dbResponse.serviceRequestId,
      message: `VM ReConfigure Request submitted for ${
        queued ? 'processing' : 'approval'
      }`,
    };
  }

  async reConfigureVm(
    domain: string,
    datacenter: string,
    reConfigureDto: ReConfigureDto,
  ): Promise<any> {
    const req = RequestContext.currentContext.req;
    const requestorPID = req?.user?.userId || null;
    const requestorEmail = req.user?.email || null;
    const commonPayload = {
      ...reConfigureDto,
      domain,
      datacenter,
      requestorPID,
      requestorEmail,
    };
    this.logger.log(
      'Passing reConfigure DTO to create service request.',
      reConfigureDto,
    );

    const newServiceRequest =
      await this.reConfigureVmRequestChange(commonPayload);
    return newServiceRequest;
  }

  async editVmSystemUpdate(data: EditVmRequestDto, message) {
    try {
      let docDetails;
      try {
        docDetails = await this.resourcesService.getResourceById(
          data.resourceId,
        );
      } catch (error) {
        this.vmwareErrorlogService.logError(
          error,
          'getResourceById',
          ERROR_DETAILS.VMWARE_EDIT_VM_ERROR_CODE,
          ERROR_DETAILS.VMWARE_EDIT_VM_FETCH_RESOURCE_ERROR_MESSAGE,
        );
      }

      if (!docDetails) {
        throw new NotFoundException(
          `Document not found with resourceName: ${data.resourceName}`,
        );
      }
      const editVmdetails = [];
      if (!docDetails.systemUpdate.editVmdetails) {
        const details = message.payload;
        editVmdetails.push(details);
        this.logger.log('forming editVmdetails array to add in systemUpdate');
        docDetails.systemUpdate['editVmdetails'] = editVmdetails;
      } else {
        this.logger.log('adding editVm details in systemUpdate');
        docDetails.systemUpdate.editVmdetails.push(message.payload);
      }
      this.logger.log('storing edit vm details in systemUpdate');
      try {
        await this.resourcesService.updateResourcesSystemUpdateByResourceId(
          data.resourceId,
          docDetails.systemUpdate,
        );
      } catch (error) {
        this.vmwareErrorlogService.logError(
          error,
          'updateResourcesSystemUpdateByResourceId',
          ERROR_DETAILS.VMWARE_EDIT_VM_ERROR_CODE,
          ERROR_DETAILS.VMWARE_EDIT_VM_UPDATE_RESOURCE_ERROR_MESSAGE,
        );
      }
    } catch (error) {
      this.vmwareErrorlogService.logError(
        error,
        'editVmSystemUpdate',
        ERROR_DETAILS.VMWARE_EDIT_VM_ERROR_CODE,
        ERROR_DETAILS.VMWARE_EDIT_VM_SYSTEM_UPDATE_ERROR_MESSAGE,
      );
    }
  }

  async editVmVmware(
    domain: string,
    datacenter: string,
    data: EditVmRequestDto,
  ) {
    try {
      this.logger.log('Edit vm data ', data);
      const req = RequestContext.currentContext.req;
      const requestUser = req.user;
      const userGroups = req?.user?.groups || null;
      let allowedGroups;
      try {
        allowedGroups = await this.iamService.findCatalogL4Groups(
          data.shortName,
          PermissionKey.CREATE,
        );
      } catch (error) {
        this.vmwareErrorlogService.logError(
          error,
          'findCatalogL4Groups',
          ERROR_DETAILS.VMWARE_EDIT_VM_ERROR_CODE,
          ERROR_DETAILS.VMWARE_EDIT_VM_CATALOG_L4_ERROR_MESSAGE,
        );
      }

      this.logger.log('filtering common groups in edit Vm');
      const commonGroups = allowedGroups?.filter((group) =>
        userGroups.includes(group.name),
      );
      const commonGroupsResponse = commonGroups.map((group) => ({
        id: group.id,
        name: group.name,
      }));

      this.logger.log('building the message to send to queue in editVm');
      const message = {
        id: data.resourceId,
        payload: {
          domain: domain,
          datacenter: datacenter,
          ...data,
          groups: commonGroupsResponse,
          requesterEmail: requestUser.email,
          requesterPID: requestUser.userId,
          requestAt: new Date(),
        },
        serviceRequestId: '',
      };
      this.logger.log(
        'calling editVmSystemUpdate to update editvm details in systemUpdate',
      );
      try {
        await this.editVmSystemUpdate(data, message);
      } catch (error) {
        this.vmwareErrorlogService.logError(
          error,
          'editVmSystemUpdate',
          ERROR_DETAILS.VMWARE_EDIT_VM_ERROR_CODE,
          ERROR_DETAILS.VMWARE_EDIT_VM_SYSTEM_UPDATE_ERROR_MESSAGE,
        );
      }

      this.logger.log('updated the systemUpdate with editVm details');
      try {
        await this.rmqService.pushMessage(RequestType.EDIT_VM, message);
      } catch (error) {
        this.vmwareErrorlogService.logError(
          error,
          'pushMessage',
          ERROR_DETAILS.VMWARE_EDIT_VM_ERROR_CODE,
          ERROR_DETAILS.VMWARE_EDIT_VM_RMQ_MESSAGE_QUEUE_ERROR_MESSAGE,
        );
      }
      return {
        successCode: HttpStatus.OK,
        message: 'Edit vm has been successfully queued',
      };
    } catch (error) {
      this.vmwareErrorlogService.logError(
        error,
        'editVmVmware',
        ERROR_DETAILS.VMWARE_EDIT_VM_ERROR_CODE,
        ERROR_DETAILS.VMWARE_EDIT_VM_ERROR_MESSAGE,
      );
    }
  }
}
