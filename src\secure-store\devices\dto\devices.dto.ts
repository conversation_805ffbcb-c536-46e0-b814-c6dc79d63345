import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';

export class CreateSecretDeviceAssociationDto {
  @IsNumber()
  @IsNotEmpty()
  deviceId: number;

  @IsString()
  @IsNotEmpty()
  secretId: string;

  @IsString()
  @IsNotEmpty()
  sourceSystem: string;
}

export class UpdateSecretDeviceAssociationDto extends CreateSecretDeviceAssociationDto {
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    description:
      'If Provided the record will be updated else new one will be inserted',
    example: 'NEB-SEC-LINK-1000',
  })
  secretAssociationId?: string;
}
