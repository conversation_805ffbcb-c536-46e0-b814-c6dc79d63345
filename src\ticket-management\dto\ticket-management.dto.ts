import { IsString, IsNotEmpty, IsDefined } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class ValidateCrqTicketRequestDto {
  @ApiProperty({
    description: 'CRQ Ticket ID',
    example: 'CRQ000001080174',
  })
  @IsString()
  @IsNotEmpty()
  @IsDefined()
  crqTicketId: string;
}

export class ValidateCrqTicketResponseDto {
  status: string;
  message: string;
  crqTicketStatus: string;
}

export interface ValidateCrqDto {
  crq_results: CrqResults[];
  task_results: object;
}

interface CrqResults {
  status: string;
  msg: string;
  change_id: string;
  change_status: string;
  change_status_reason: string;
  change_summary: string;
  scheduled_start_date: string;
  scheduled_end_date: string;
  coordinator_name: string;
  crq_reason: string;
  crq_class: string;
  change_approvals: object;
  change_entry_id: string;
}

export interface GetMopIdFromCrqResponseDto {
  rows: MopIdData[];
}

interface MopIdData {
  MOPID: number;
  MOPKey: string;
  UploadKey: string;
  CreateDate: string;
  ModifiedDate: string;
  SubmitterPID: string;
  SubmitterName: string;
  SubmitterTitle: string;
  SubmitterEmail: string;
  SubmitterRemedyLoginID: string;
  LastModifiedByPID: string;
  LastModifiedByName: object;
  MOPStatusID: number;
  MOPStatusLabel: string;
  MOPTypeID: number;
  MOPTypeLabel: string;
  MOPVerticalID: number;
  MOPVerticalOrg: string;
  MOPVerticalPrefix: string;
  MOPVerticalName: string;
  MOPVerticalDisplay: string;
  MaxApprovalPositionLevel: string;
  MaxApprovalHierarchyLevel: number;
  MOPTitle: string;
  MOPCleanTitle: string;
  MOPTitleKey: string;
  MOPSummary: string;
  MOPPreProcedure: string;
  MOPProcedure: string;
  MOPPostProcedure: string;
  MOPTestProcedure: string;
  MOPBackOutProcedure: string;
  SearchTags: string;
  MOPUltBossPID: string;
  ClonedFromMOPID: number;
  ProcessPickupID: number;
  ProcessPickupIDLabel: string;
  ProcessCount: number;
  ProcessTries: number;
  ChangeID: string;
  ChangeStatus: string;
  ChangeStatusReason: string;
  ChangeTiming: string;
  ChangeReason: string;
  RelatedIncidentID: string;
  ChangeEntryID: string;
  CRQPathID: number;
  CRQPathLabel: string;
  CRQSummary: string;
  CRQDetail: string;
  CRQCoordinatorGroup: string;
  CRQCoordinatorLogin: string;
  CRQCoordinatorName: string;
  CRQCoordinatorTitle: string;
  CRQSiteName: string;
  CRQProductCategoryTier1: string;
  CRQProductCategoryTier2: string;
  CRQProductCategoryTier3: string;
  CRQScheduledStartDate: string;
  CRQScheduledEndDate: string;
  CRQServiceImpact: string;
  CRQDowntimeMinutes: number;
  CRQLBIActual: number;
  CRQLBIPotential: number;
  CRQInternalImpact: string;
  CRQInternalImpactService: string;
  CRQInternalImpactDetail: string;
  CRQInternalImpactLocation: string;
  CRQMetroRegionalImpact: string;
  CRQMetroRegionalImpactType: string;
  CRQMetroRegionalImpactPath: string;
  CRQBackboneImpact: string;
  CRQBackboneImpactType: string;
  CRQBackboneImpactPath: string;
  CRQTransportImpactDetail: string;
  SubmitterBoss1: string;
  SubmitterBoss2: string;
  SubmitterBoss3: string;
  SubmitterBoss4: string;
  SubmitterBoss5: string;
  SubmitterBoss6: string;
  SubmitterBoss7: string;
  SubmitterBoss8: string;
  SubmitterBoss9: string;
  SubmitterBoss10: string;
  SubmitterBoss11: string;
  SubmitterBoss12: string;
  UploadStatusMismatch: string;
  ExpirationDate: string;
  LastApproveDate: string;
  LastRejectDate: null;
  LastRejectComment: string;
  RejectCount: number;
  MOPApprovers: string;
  MOPPeerReviewers: string;
  MOPEditors: string;
}

export class AddCommentRequestDTO {
  @ApiProperty({
    description: 'The mopID of remedy ticket',
    example: 'ABC12345',
  })
  @IsString()
  @IsNotEmpty()
  mopId: string;

  @ApiProperty({
    description: 'add comments in the comments array',
    example: ['test1', 'test2'],
    required: true,
  })
  @IsNotEmpty()
  comments: string[];
}

export class AddCommentResponsetDTO {
  msgs: string[];
  status: string;
  message?: string;
  success?: boolean;
}
