import {
  Controller,
  Post,
  Body,
  Get,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
  Res,
  UseGuards,
  Param,
  HttpException,
  HttpStatus,
  Put,
  Query,
  Patch,
  Version,
} from '@nestjs/common';
import { FirewallV2Service } from './firewallv2.service';
import { Response } from 'express';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiBearerAuth,
  ApiConsumes,
  ApiBody,
  ApiProduces,
  ApiOkResponse,
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiSecurity,
} from '@nestjs/swagger';
import { extname } from 'path';
import {
  FirwallV1MigrateDto,
  JiraDropdowns,
  ServiceRequestResponseDto,
  createFileRequestDto,
  createFirewallRequestDto,
  ImpactedDevicesRequestDto,
  FirewallRules,
} from './dto/firewallv2.request.dto';
import { Permission } from '../../iam/permission.decorator';
import {
  API_RESPONSE_STATUS,
  JiraActions,
  OrganizationName,
  RequestStatus,
  RequestType,
} from '../../types';
import { PermissionKey } from '../../iam/types';
import { PermissionGuard } from '../../iam/permission.guard';
import { ConfigService } from '@nestjs/config';
import { LoggerService } from '../../loggers/logger.service';
import {
  CreateRiskAnalysisResultRequestDto,
  CreateRiskAnalysisResultRequestDto as RiskAnalysisResultDto,
} from '../dto/firewall.risk-analysis-result.request.dto';
import { CreateRiskAnalysisResultResponseDto } from '../dto/firewall.risk-analysis-result.response.dto';
import { TufinTaskResponseDto } from '../dto/firewall.tufin.job.response.dto';
import {
  TufinDevicesDto,
  TufinTaskRequestDto,
  UpdateTufinDevicesDto,
} from '../dto/firewall.tufin.job.request.dto';
import { FirewallRequestCreateDto } from './dto/firewallRequest.request.dto';
import { UpdateJiraDetailsDto } from './dto/updateFirewallRequest.dto';
import {
  FirewallTicketDetailsResponseDto,
  ImpactedDevicesResponseDto,
} from './dto/firewall.ticket-details.response.dto';
import { FirewallRequestEntity } from '../entity/firewallRequest.entity';
import { Anonymous } from '../../auth/anonymous.decorator';
import {
  TufinResponseDetails,
  TufinResponses,
} from './dto/updateTufinDetails.dto';
import { UpdatePollRequestDto } from './dto/updatePollRequest.request,dto';
import { ResubmitFirewallRequestDto } from './dto/resubmitFirewallRequest.request.dto';
import { ResubmitFirewallRequestEntity } from '../entity/resubmitFirewallRequest.entity';
import { DownloadDFMDto } from './dto/subRequest.df.download.request.dto';
import { GenericRetryTicketRequestDto } from './dto/retryTicketDetails.dto';
import { JiraRequestDto } from './dto/jira.request.dto';
import { FirewallV2JiraService } from './firewallv2.jira.service';

@ApiTags('Security')
@ApiSecurity('x-nebula-authorization')
@Controller('security/firewall')
@UseGuards(PermissionGuard)
export class FirewallV2Controller {
  private readonly maxFirewallRulesFileSize: number;

  constructor(
    private readonly firewallV2Service: FirewallV2Service,
    private readonly firewallV2JiraService: FirewallV2JiraService,
    private configService: ConfigService,
    private readonly logger: LoggerService,
  ) {
    this.maxFirewallRulesFileSize = parseInt(
      this.configService.get<string>(
        'MAX_FIREWALL_RULES_FILE_SIZE_LIMIT_IN_MB',
        '1',
      ),
    );
  }

  @Version('2')
  @ApiBearerAuth()
  @Post('fileupload')
  @UseInterceptors(FileInterceptor('file'))
  @ApiBody({
    required: true,
    type: 'multipart/form-data',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @ApiConsumes('multipart/form-data')
  async parseFile(@UploadedFile() file: Express.Multer.File): Promise<any> {
    this.logger.log('Uploading file');
    try {
      if (!file || extname(file.originalname) != '.xlsx') {
        this.logger.debug('Invalid file or incorrect extension provide');
        throw new BadRequestException(
          'Please provide file with correct extension',
        );
      }

      if (file.size > this.maxFirewallRulesFileSize * 1024 * 1024) {
        this.logger.debug(
          `File size exceeds the limit of ${this.maxFirewallRulesFileSize} MB`,
        );
        throw new BadRequestException(
          `File size exceeds the limit of ${this.maxFirewallRulesFileSize} MB`,
        );
      }

      this.logger.debug('file uploaded');
      return await this.firewallV2Service.firewallBulkImport(file.buffer);
    } catch (error) {
      this.logger.error(error, 'Error occurred during file upload');
      throw new BadRequestException(`${error.message}`);
    }
  }

  @Version('2')
  @ApiBearerAuth()
  @Post('requests')
  @Permission(RequestType.FIREWALL_V2, PermissionKey.CREATE)
  async createFirewallReq(
    @Body() createFirewallRequestDto: createFirewallRequestDto,
  ): Promise<ServiceRequestResponseDto> {
    this.logger.log('creating firewall V2 request', createFirewallRequestDto);
    return await this.firewallV2Service.createFirewallRequest(
      createFirewallRequestDto,
    );
  }

  @Version('2')
  @Get('template')
  @ApiOkResponse({
    schema: {
      type: 'string',
      format: 'binary',
    },
  })
  @ApiProduces('text/xlsx')
  async getTemplate(@Res() response: Response) {
    const result = await this.firewallV2Service.getFirewallTemplate();

    response.set({
      'Content-Type': 'text/xlsx',
    });
    response.download(`${result}`);
  }

  @Get('jira/regions-groups')
  async getExtattrsDef(): Promise<JiraDropdowns> {
    this.logger.log('cloud API controller to get the Jira drop downs');
    const etrattDef = await this.firewallV2Service.getJiraRegionAndGroups();
    this.logger.debug('Response from service call', etrattDef);
    return etrattDef;
  }

  @Get('issueExists/:id')
  async fetchIssue(@Param('id') id: string) {
    this.logger.log(
      'Calling security service to fetch issue from jira service for id: ',
      id,
    );
    const fetchedIssue = await this.firewallV2Service.fetchIssue(id);
    this.logger.debug('Response from service call ', fetchedIssue);
    return fetchedIssue;
  }

  // @Anonymous()
  @Put('tufin/job/serviceRequest/:requestId/tufinTicket/:tufinTicketId')
  @ApiOperation({ summary: 'Update tufin job status' })
  @ApiParam({
    name: 'requestId',
    type: 'string',
    description: 'The nebula request id',
    required: true,
  })
  @ApiParam({
    name: 'tufinTicketId',
    type: 'string',
    description: 'The tufin ticket id',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'The request was processed successfully.',
    type: TufinTaskResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'The requestId or tufinTicketId not found.',
    type: TufinTaskResponseDto,
  })
  @ApiResponse({
    status: 500,
    description: 'An unexpected error occurred.',
    type: TufinTaskResponseDto,
  })
  async updateTufinTask(
    @Param('requestId') requestId: string,
    @Param('tufinTicketId') tufinTicketId: string,
    @Body()
    tufinJobRequestDto: TufinTaskRequestDto,
    @Res() res: Response,
  ): Promise<Response> {
    this.logger.log(
      `Received request to update tufin task for serviceRequest ${requestId},
      tufin ticket: ${tufinTicketId}`,
    );

    this.logger.debug(`TufinTaskRequestDto: ${tufinJobRequestDto}`);

    try {
      const response =
        await this.firewallV2Service.updateTufinTaskStatus(tufinJobRequestDto);
      const result: TufinTaskResponseDto = {
        status: API_RESPONSE_STATUS.SUCCESS,
        message: response.message,
      };
      this.logger.debug(
        `Tufin task updated successfully : ${JSON.stringify(result)}`,
      );
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      this.logger.error(
        error,
        `Error occurred while updating tufin task for serviceRequest: ${requestId},
      tufin ticket: ${tufinTicketId}`,
      );
      const status =
        error instanceof HttpException
          ? error.getStatus()
          : HttpStatus.INTERNAL_SERVER_ERROR;
      const message =
        error && error.message ? error.message : 'An unexpected error occurred';

      const errorResponse: TufinTaskResponseDto = {
        status: API_RESPONSE_STATUS.FAILED,
        message: message,
      };

      return res.status(status).json(errorResponse);
    }
  }

  @Get('risk-analysis-results')
  @ApiOperation({
    summary:
      'Get risk analysis results by Tufin Ticket ID or Service Request ID',
  })
  @ApiQuery({
    name: 'tufinTicketId',
    required: false,
    description: 'The Tufin Ticket ID to filter results',
  })
  @ApiQuery({
    name: 'requestId',
    required: false,
    description: 'The Service Request ID to filter results',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved risk analysis results',
    type: RiskAnalysisResultDto,
    isArray: true,
  })
  @ApiResponse({
    status: 404,
    description: 'No risk analysis results found for the provided criteria',
  })
  @ApiResponse({
    status: 500,
    description: 'An error occurred while fetching risk analysis results',
  })
  async getRiskResults(
    @Query('tufinTicketId') tufinTicketId: string,
    @Query('requestId') requestId: string,
    @Res() res: Response,
  ): Promise<Response> {
    try {
      this.logger.log(
        `fetching risk analysis result for 
        requestId: ${requestId}, tufinTicketId: ${tufinTicketId}`,
      );
      const results: RiskAnalysisResultDto[] =
        await this.firewallV2Service.getRiskResultsByParams(
          tufinTicketId,
          requestId,
        );

      if (results.length === 0) {
        return res.status(HttpStatus.NOT_FOUND).json({
          status: API_RESPONSE_STATUS.FAILED,
          message: 'No risk analysis results found for the provided criteria',
        });
      }

      return res.status(HttpStatus.OK).json({
        status: API_RESPONSE_STATUS.SUCCESS,
        data: results,
      });
    } catch (error) {
      this.logger.error(
        error,
        `An error occurred while fetching risk analysis results for 
        requestId: ${requestId}, tufinTicketId: ${tufinTicketId}`,
      );
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        status: API_RESPONSE_STATUS.FAILED,
        message: 'An error occurred while fetching risk analysis results',
      });
    }
  }

  // @Anonymous()
  @Get('ticket-details-n8n')
  @ApiOperation({
    summary: 'Get firewall ticket details by Service Request ID',
  })
  @ApiQuery({
    name: 'serviceRequestId',
    required: true,
    description: 'The Service Request ID to filter results',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved firewall ticket details',
    type: FirewallTicketDetailsResponseDto,
    isArray: true,
  })
  @ApiResponse({
    status: 404,
    description: 'No firewall ticket details found for the provided criteria',
  })
  @ApiResponse({
    status: 500,
    description: 'An error occurred while fetching firewall ticket details',
  })
  async getFirewallTicketDetailsN8N(
    @Query('serviceRequestId') serviceRequestId: string,
    @Res() res: Response,
  ): Promise<Response> {
    this.logger.log(
      `request received for fetching firewall ticket details for serviceRequestId: ${serviceRequestId}`,
    );
    const results: Partial<FirewallRequestEntity>[] =
      await this.firewallV2Service.getFirewallTicketDetailsN8N(
        serviceRequestId,
      );

    if (results.length === 0) {
      this.logger.debug(
        `No firewall request ticket details found for serviceRequestId: ${serviceRequestId}`,
      );
      return res.status(HttpStatus.NOT_FOUND).json({
        status: API_RESPONSE_STATUS.FAILED,
        message:
          'No firewall request ticket details found for the provided criteria',
      });
    }

    return res.status(HttpStatus.OK).json({
      status: API_RESPONSE_STATUS.SUCCESS,
      data: results,
    });
  }

  @Get('ticket-details')
  @ApiOperation({
    summary: 'Get firewall ticket details by Service Request ID',
  })
  @ApiQuery({
    name: 'serviceRequestId',
    required: true,
    description: 'The Service Request ID to filter results',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved firewall ticket details',
    type: FirewallTicketDetailsResponseDto,
    isArray: true,
  })
  @ApiResponse({
    status: 404,
    description: 'No firewall ticket details found for the provided criteria',
  })
  @ApiResponse({
    status: 500,
    description: 'An error occurred while fetching firewall ticket details',
  })
  async getFirewallTicketDetails(
    @Query('serviceRequestId') serviceRequestId: string,
    @Res() res: Response,
  ): Promise<Response> {
    this.logger.debug(
      `request received for fetching firewall ticket details for serviceRequestId: ${serviceRequestId}`,
    );
    const results: FirewallTicketDetailsResponseDto[] =
      await this.firewallV2Service.getFirewallTicketDetails(serviceRequestId);

    if (results.length === 0) {
      this.logger.debug(
        `No firewall request ticket details found for serviceRequestId: ${serviceRequestId}`,
      );
      return res.status(HttpStatus.NOT_FOUND).json({
        status: API_RESPONSE_STATUS.FAILED,
        message:
          'No firewall request ticket details found for the provided criteria',
      });
    }
    const priorityOrder = [
      OrganizationName.RED_APS,
      OrganizationName.CORPORATE,
      OrganizationName.UNKNOWN,
      OrganizationName.RED_CBO,
      OrganizationName.AWS,
      OrganizationName.IPV6,
      OrganizationName.NA_FAILED,
      OrganizationName.NA_NOIMPACT,
    ];

    results.sort((a, b) => {
      return (
        priorityOrder.indexOf(a.organizationName as OrganizationName) -
        priorityOrder.indexOf(b.organizationName as OrganizationName)
      );
    });
    return res.status(HttpStatus.OK).json({
      status: API_RESPONSE_STATUS.SUCCESS,
      data: results,
    });
  }

  @ApiBearerAuth()
  @Patch('cancel/:id')
  async cancelServiceRequest(@Param('id') id: string) {
    this.logger.debug(`Cancel service request called with id:${id}`);
    return await this.firewallV2Service.cancelServiceRequest(id);
  }

  @Patch('cancel/forceCancel/:id')
  async forceCancelServiceRequest(@Param('id') id: string) {
    this.logger.debug(`Force cancel service request called with id:${id}`);
    return await this.firewallV2Service.cancelServiceRequest(id, true);
  }

  @ApiBearerAuth()
  @Patch('cancel/:requestId/:subRequestId')
  async cancelSubRequest(
    @Param('requestId') requestId: string,
    @Param('subRequestId') subRequestId: string,
    @Query('forcecancel') forceCancel: boolean = false,
  ): Promise<{ updatedStatus: RequestStatus; message: string }> {
    this.logger.debug(
      `Cancel sub request called with id:${requestId} and subRequestId:${subRequestId}`,
    );
    if (!subRequestId || !requestId) {
      throw new BadRequestException(`subRequestId and requestId are mandatory`);
    }
    return await this.firewallV2Service.cancelSubRequest(
      requestId,
      subRequestId,
      RequestStatus.CANCELLING,
      forceCancel,
    );
  }

  @ApiBearerAuth()
  @Patch('close/:requestId/:subRequestId')
  async closeSubRequest(
    @Param('requestId') requestId: string,
    @Param('subRequestId') subRequestId: string,
    @Query('forceclose') forceClose: boolean = false,
  ): Promise<{ updatedStatus: RequestStatus; message: string }> {
    this.logger.debug(
      `Close sub request called with id:${requestId} and subRequestId:${subRequestId}`,
    );
    if (!subRequestId || !requestId) {
      throw new BadRequestException(`subRequestId and requestId are mandatory`);
    }
    return await this.firewallV2Service.closeSubRequestV2(
      requestId,
      subRequestId,
      RequestStatus.CLOSED_MANUALLY,
      forceClose,
    );
  }

  // @Anonymous()
  @Post('firewall-sub-requests')
  async createNewFirewallRequestResult(
    @Body() data: FirewallRequestCreateDto,
  ): Promise<FirewallRequestEntity> {
    this.logger.log(`request received for inserting new document ${data}`);
    const result =
      await this.firewallV2Service.createNewFirewallRequestResult(data);
    this.logger.debug('New firewall request result', result);
    return result;
  }

  // @Anonymous()
  @Patch('update-firewall-sub-request/:subRequest')
  async updateSubRequestBySubRequestId(
    @Param('subRequest') subRequestId: string,
    @Body() data: Partial<FirewallRequestEntity>,
    @Query('skipupdate') skipServiceRequestUpdate : boolean = false
  ): Promise<FirewallRequestEntity> {
    this.logger.log(
      `Request received for updating subrequest: ${subRequestId}. Data: ${data}`,
    );
    return await this.firewallV2Service.update(subRequestId, data, skipServiceRequestUpdate);
  }

  // @Anonymous()
  @Post('storage/file-upload')
  async uploadFirewallRulesOfSecureChangeTicket(
    @Body() data: createFileRequestDto,
  ) {
    this.logger.log('Received storage file upload request', data);
    return await this.firewallV2Service.createAndUploadFirewallRulesFile(data);
  }

  // @Anonymous()
  @Post('path-analysis')
  async savePathAnalysisResult(@Body() data: TufinDevicesDto[]) {
    this.logger.log(`request received for inserting new document ${data}`);
    return await this.firewallV2Service.saveTufinPathAnalysis(data);
  }

  @Patch('update-tufin-devices')
  async updateTufinDevices(@Body() data: UpdateTufinDevicesDto) {
    this.logger.log(`Request received for updating tufin devices ${data}`);
    return await this.firewallV2Service.updateTufinDevices(data);
  }

  @Get('path-analysis/:serviceRequestId/:ruleId')
  async getPathAnalysisResult(
    @Param('serviceRequestId') serviceRequestId: string,
    @Param('ruleId') ruleId: number,
  ) {
    this.logger.log(
      `request received for getPathAnalysisResult ${serviceRequestId},${ruleId}`,
    );
    const result = await this.firewallV2Service.getPathAnalysis(
      serviceRequestId,
      ruleId,
    );
    this.logger.debug('Path analysis result', result);
    return result;
  }

  @Get('devices/:serviceRequestId')
  async getAllDevicesByRequestId(
    @Param('serviceRequestId') serviceRequestId: string,
  ) {
    this.logger.log(`request received for getAllDevices ${serviceRequestId}`);
    const result =
      await this.firewallV2Service.getAllDevicesByRequestId(serviceRequestId);
    this.logger.debug('All devices by requestId', result);
    return result;
  }

  // @Anonymous()
  @Post('poll/update')
  async UpdateSubRequestJiraStatus(@Body() data: UpdatePollRequestDto[]) {
    this.logger.log(
      'Received request to update subrequests',
      JSON.stringify(data),
    );
    return await this.firewallV2Service.UpdateSubRequestJiraStatus(data);
  }

  // @Anonymous()
  @Get('rules/:requestId')
  async getRulesUsingRuleIds(
    @Param('requestId') requestId: string,
    @Body() data: number[],
  ) {
    this.logger.log(
      `Received request to get rules for using ruleIds ${requestId} ${JSON.stringify(data)}`,
    );
    if (!data.length) {
      throw new BadRequestException(`pass atleast one ruleId`);
    }
    return await this.firewallV2Service.getRulesUsingRuleIds(requestId, data);
  }

  @Get('path-analysis/image/:serviceRequestId/:ruleId')
  async getPathAnalysisImageResult(
    @Param('serviceRequestId') serviceRequestId: string,
    @Param('ruleId') ruleId: number,
    @Res() response: Response,
  ) {
    this.logger.log(
      `request received for getPathAnalysisImageResult ${serviceRequestId},${ruleId}`,
    );
    const result = await this.firewallV2Service.getPathAnalysisImage(
      serviceRequestId,
      ruleId,
    );

    this.logger.log(`Adding Headers for the response`);
    const stream = response.writeHead(200, {
      'Content-Type': `image/png`,
      'Content-disposition': `attachment; filename=pathAnalysisResult.png`,
    });

    result.pipe(stream);
  }

  @Put('tufin/close/:ticketId')
  async closeTufinTicket(@Param('ticketId') ticketId: string) {
    this.logger.log(`request received for closing ticket ${ticketId},`);
    const result = await this.firewallV2Service.closeTufinTicket(ticketId);
    this.logger.debug('Close tufin ticket result', result);
    return result;
  }

  @Post('migrate/firewall-v1')
  async firewallMigration(@Body() data: FirwallV1MigrateDto[]) {
    this.logger.log(`Adding firewall v1 to new Queue ${data}`);
    await this.firewallV2Service.addToMigrationQueue(data);
  }

  @Post('migrate/v1-request/:serviceRequestId')
  async migrateFirewallV1Request(
    @Param('serviceRequestId') serviceRequestId: string,
  ): Promise<ServiceRequestResponseDto> {
    this.logger.debug(
      `migrate firewall request v1 to v2 for request id ${serviceRequestId}`,
    );
    return await this.firewallV2Service.migrateFirewallV1toV2(serviceRequestId);
  }
  @Post('sub-request-notification/:subRequestId')
  async sendNotificationForSubrequest(
    @Param('subRequestId') subRequestId: string,
  ) {
    this.logger.log(
      'sub request notification controller is running this is sub request id',
      subRequestId,
    );
    return await this.firewallV2Service.sendFirewallSubrequestNotification(
      subRequestId,
    );
  }

  @Post('resubmit-request')
  async createNewResubmitFirewallRequest(
    @Body() data: ResubmitFirewallRequestDto[],
  ) {
    this.logger.log(
      `request received for inserting new resubmit firewall request ${data}`,
    );
    const result =
      await this.firewallV2Service.createNewResubmitFirewallRequest(data);
    this.logger.debug('New Resubmit request: ', result);

    return result;
  }

  @Get('resubmit-request')
  async getAllResubmitRequests() {
    this.logger.log(
      `request received for getting all resubmit firewall request`,
    );
    const result = await this.firewallV2Service.getAllResubmitRequests();
    this.logger.debug('Resubmit requests: ', result);

    return result;
  }

  @Get('resubmit-request/:serviceRequestId')
  async getResubmitRequest(
    @Param('serviceRequestId') serviceRequestId: string,
  ) {
    this.logger.log(
      `request received for getting resubmit firewall request ${serviceRequestId}`,
    );
    const result =
      await this.firewallV2Service.getResubmitRequest(serviceRequestId);
    this.logger.debug('Resubmit requests: ', result);

    return result;
  }

  @Patch('update/resubmit-request/:serviceRequestId')
  async updateResubmitRequest(
    @Param('serviceRequestId') serviceRequestId: string,
    @Body() updateRequestObj: { status: string; resubmittedId: string },
  ) {
    this.logger.log(
      `request received for updating resubmit firewall request ${serviceRequestId} ${JSON.stringify(updateRequestObj)}`,
    );
    const result = await this.firewallV2Service.updateResubmitRequest(
      serviceRequestId,
      updateRequestObj,
    );
    this.logger.debug('Updated Resubmit request: ', result);

    return result;
  }

  @Get('impacted-devices')
  async getFirewallImpactedDevices(
    @Query() impactedDevicesReq: ImpactedDevicesRequestDto,
  ): Promise<ImpactedDevicesResponseDto> {
    this.logger.debug(
      `Request received for fetching impacted devices for serviceRequestId: ${impactedDevicesReq.serviceRequestId},
       subRequestId: ${impactedDevicesReq.subRequestId} with reverse : ${impactedDevicesReq.reverse}`,
    );
    return await this.firewallV2Service.getFirewallImpactedDevices(
      impactedDevicesReq,
    );
  }

  @Get('firewallrules/download/:id')
  @ApiOkResponse({
    schema: {
      type: 'string',
      format: 'binary',
    },
  })
  @ApiProduces('text/xlsx')
  async downloadFirewallRulesFile(
    @Res() response: Response,
    @Param('id') id: string,
  ) {
    if (!!id) {
      this.logger.debug(`Download all firewall rules file for id: ${id}`);

      const result =
        await this.firewallV2Service.createAndGetFirewallRuleFile(id);
      this.logger.log('All firewall rules file downloaded');

      const stream = response.writeHead(200, {
        'Content-Type': `${result.headers['content-type']}`,
        'Content-disposition': `${result.headers['content-disposition']}`,
      });

      result.pipe(stream);
    } else {
      this.logger.error(`Invalid id ${id}`);
      throw new BadRequestException('Invalid id');
    }
  }

  @Post('reflow/tufin/:subRequestId')
  async reflowTufintRequest(@Param('subRequestId') subRequestId: string) {
    this.logger.log(
      `request received for  reflowing tufin request for subreq: ${subRequestId}`,
    );
    const result = await this.firewallV2Service.reflowTufinTask(subRequestId);
    this.logger.debug('Reflow requests results: ', result);

    return result;
  }

  @Get('subrequest/dfm')
  @ApiOkResponse({
    schema: {
      type: 'string',
      format: 'binary',
    },
  })
  async getSubRequestDFM(
    @Body() data: DownloadDFMDto,
    @Res() response: Response,
  ) {
    this.logger.log(
      `request received for download dfm for subrequest with payload ${JSON.stringify(data)}`,
    );
    const result = await this.firewallV2Service.subRequestDFMDownload(data);

    this.logger.log(`downloaded dfm for subrequest`);
    const stream = response.writeHead(200, {
      'Content-Type': `${result.headers['content-type']}`,
      'Content-disposition': `${result.headers['content-disposition']}`,
    });

    result.pipe(stream);
  }

  @Post('retry/ticket')
  async resubmitTicketAction(@Body() data: GenericRetryTicketRequestDto) {
    this.logger.log(
      `Resubmitting the ticket with payload ${JSON.stringify(data)}`,
    );
    const result = await this.firewallV2Service.resubmitTicketOperation(data);
    this.logger.debug('Resubmitted ticket results: ', result);
    return result;
  }

  @Patch('ticket/remedy/:serviceRequestId/:subRequestId')
  async getTicketDetailsAndUpdateStatus(
    @Param('serviceRequestId') serviceRequestId: string,
    @Param('subRequestId') subRequestId: string,
  ) {
    this.logger.log(
      `Remedy - Updating the remedy ticket status and serviceRequest status using ${serviceRequestId}}`,
    );
    const result =
      await this.firewallV2Service.updateRemedyTicketDetailsInFirewallRequests(
        serviceRequestId,
        subRequestId,
      );
    this.logger.debug('Updated ticket results: ', result);
    return result;
  }

  @Post('split/rules')
  async splitIpv4Rules(@Body() data: FirewallRules[]) {
    this.logger.log(`received request to split ipv4 rules`);
    return await this.firewallV2Service.splitIpv4Rules(data);
  }

  @Post('jira/issue')
  async createJiraIssue(@Body() data: JiraRequestDto) {
    this.logger.log(`received request to check and create jira ticket`);
    return await this.firewallV2JiraService.checkAndCreateJiraIssue(data);
  }

  @Post('jira/issue/:action/:serviceRequestId/:subRequestId/:jiraId')
  async updateJiraIssue(
    @Param('jiraId') jiraId: string,
    @Param('subRequestId') subRequestId: string,
    @Param('serviceRequestId') serviceRequestId: string,
    @Param('action') action: JiraActions,
  ) {
    this.logger.log(`received request to ${action} ${jiraId} jira ticket`);
    if (action !== JiraActions.CANCEL && action !== JiraActions.CLOSE) {
      this.logger.error(`Invalid jira action ${action} for ${jiraId}`);
      throw new BadRequestException(
        `Invalid jira action ${action} for ${jiraId}`,
      );
    }
    return await this.firewallV2JiraService.checkAndUpdateJiraTicket(
      jiraId,
      subRequestId,
      serviceRequestId,
      action,
    );
  }

  @Get('dfm/:subRequestId')
  async getSubRequestStorageInfo(@Param('subRequestId') subRequestId: string) {
    this.logger.log(`received request to get storage info of ${subRequestId}`);
    return await this.firewallV2JiraService.getSubRequestStorageInfo(
      subRequestId,
    );
  }

  @Post('org/upload/attachment/:serviceRequestId/:subRequestId')
  async uploadAttachmentsForCorporate(
    @Param('serviceRequestId') serviceRequestId: string,
    @Param('subRequestId') subRequestId: string,
  ) {
    this.logger.log(
      `received request to upload other orgs dfm's in cherwell for ${subRequestId}`,
    );
    return await this.firewallV2JiraService.uploadAttachmentsForCorporate(
      serviceRequestId,
      subRequestId,
    );
  }

  @Patch('update/status/:serviceRequestId')
  async updateServiceRequestStatus(
    @Param('serviceRequestId') serviceRequestId: string,
  ) {
    this.logger.log(
      `received request to upload service request status for firewall v2 ${serviceRequestId}`,
    );
    const response =
      await this.firewallV2Service.updateServiceRequestStatus(serviceRequestId);
    //response has either true or false
    if (response) {
      return {
        status: HttpStatus.OK,
        message: `Status update for ${serviceRequestId} is sucessful`,
      };
    } else {
      return {
        status: HttpStatus.INTERNAL_SERVER_ERROR,
        message: `Status update for ${serviceRequestId} is not successful`,
      };
    }
  }
}
