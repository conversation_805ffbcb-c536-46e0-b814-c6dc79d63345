import { Injectable, BadRequestException, NotFoundException } from '@nestjs/common';
import { AssetsService } from '../../assets/assets.service';
import { LoggerService } from '../../loggers/logger.service';
import { MultiEnvIAMService } from '../iam/iam.service';
import {
    RequestStatus,
    RequestType,
    ServiceCatalogName,
    ServiceCatalogType,
} from 'src/types';
import { OnboardGroupRequest } from './dto/group-onboard.request.dto';
import { createGroupResponse } from './dto/group-onboard.response.dto';

@Injectable()
export class GroupOnboardService {
    constructor(
        private readonly assetsService: AssetsService,
        private readonly logger: LoggerService,
        private readonly multienvIamService: MultiEnvIAMService,
    ) {}

    async acceptOnboardGroupRequest(
        onboardGroupRequest: OnboardGroupRequest
    ): Promise<createGroupResponse> {
        this.logger.log('Group On-board Request', onboardGroupRequest);

        // Validate Organization ID
        const organizations = await this.multienvIamService.getOrganizationsById({
            organizationId: onboardGroupRequest.organizationId,
        } as any);
        if (!organizations || organizations.length === 0) {
            throw new NotFoundException(
                `Organization with ID ${onboardGroupRequest.organizationId} not found.`,
            );
        }

        // Validate Vertical ID
        const vertical = await this.multienvIamService.getVerticalsById({
            verticalId: onboardGroupRequest.verticalId,
        } as any);
        if (!vertical || vertical.length === 0) {
            throw new NotFoundException(
                `Vertical with ID ${onboardGroupRequest.verticalId} not found.`,
            );
        }

        // Validate Department ID
        const department = await this.multienvIamService.getDepartments({
            departmentId: onboardGroupRequest.departmentId,
        } as any);
        if (!department || department.length === 0) {
            throw new NotFoundException(
                `Department with ID ${onboardGroupRequest.departmentId} not found.`,
            );
        }

        const metadata = {
            serviceCatalog: {
                catalogName: ServiceCatalogName.ONBOARD_GROUP,
                catalogType: ServiceCatalogType.Onboarding,
            },
        };

        const serviceRequest = {
            metadata,
            payload: {
                ...onboardGroupRequest,
            },
            requestType: RequestType.ONBOARD_GROUP,
            status: RequestStatus.PENDING_APPROVAL,
        };

        this.logger.debug('serviceRequest: ', { serviceRequest });
        const dbResponse = await this.assetsService.create(serviceRequest);
        this.logger.debug('Response from DB ', dbResponse);
        const queued =
            await this.assetsService.queueIfApprovalNotRequired(dbResponse);

        return {
            id: dbResponse.id,
            serviceRequestId:dbResponse.serviceRequestId,
            message: `Request submitted for ${queued ? 'processing' : 'approval'}`,
        };
    }

}
