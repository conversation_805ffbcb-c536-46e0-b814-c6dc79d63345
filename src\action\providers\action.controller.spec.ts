import { Test, TestingModule } from '@nestjs/testing';
import { LoggerService } from '../../loggers/logger.service';
import { ActionService } from './action.service';
import { ActionController } from './action.controller';
import { ActionStatus } from '../enums/ActionStatus.enum';

type MockActionService = Partial<Record<keyof ActionService, jest.Mock>>;

describe('ActionController', () => {
  let controller: ActionController;
  let service: MockActionService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ActionController],
      providers: [
        LoggerService,
        {
          provide: ActionService,
          useValue: {
            update: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<ActionController>(ActionController);
    service = module.get<MockActionService>(ActionService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('updateAction', () => {
    it('should update action in db using actionId', async () => {
      const actionId = 'NEB-ACTION-8749';
      const status = ActionStatus.REQUEST_RAISED;

      const mockResponse = {
        acknowledged: true,
        modifiedCount: 1,
        upsertedId: null,
        upsertedCount: 0,
        matchedCount: 1,
      };
      jest.spyOn(service, 'update').mockResolvedValueOnce(mockResponse);
      const response = await controller.updateAction(actionId, status);
      expect(response).toEqual(mockResponse);
    });
  });
});
