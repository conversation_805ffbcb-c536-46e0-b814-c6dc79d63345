import { Inject } from '@nestjs/common';
import { AxiosInstance } from 'axios';
import {
  MongoDBResourceDetails,
  ResourcesRequestDto,
} from './entities/resources.entity';
import { ActionTypes, RequestStatus, RequestType } from '../types';
import { ENVIRONMENT_VARS } from '../utils/constants';
import { ConfigService } from '@nestjs/config';
import { CatalogTypes } from '../resources/utils';
import { LoggerService } from '../loggers/logger.service';
import { ROTATION_STATUS } from '../secure-store/enums/secret_rotation_request_type';
import { TOKEN_STATUS } from '../secure-store/enums/secret_rotation_request_type';
import { SecretType } from '../secure-store/secretsMetadata/types/secretsMetadata.enum';
export class IntegrationNotificationService {
  constructor(
    @Inject('INTEGRATION_SERVICE_API')
    private readonly integrationApi: AxiosInstance,
    private readonly configService: ConfigService,
    private readonly logger: LoggerService,
  ) {}

  constructNotificationParams(serviceRequestAllDetails: any) {
    const notificationParams = serviceRequestAllDetails.map(
      (serviceRequestData) => {
        const resourceParams = [];
        for (const [key, value] of Object.entries(serviceRequestData)) {
          if (value != null) {
            resourceParams.push({ key: key, value: value });
          }
        }
        return { resourceParams: resourceParams };
      },
    );

    return notificationParams;
  }

  async notifyServiceRequestStatusToIntegrationApi(
    serviceRequest: any,
    statusMessage: string,
    action: string,
  ) {
    const requestType = this.getNetworkRequestType(serviceRequest.requestType);
    const serviceRequestDetails = [
      {
        PID: serviceRequest.createdBy,
        REQUEST_ID: serviceRequest.serviceRequestId,
        REQUEST_TYPE: requestType,
        STATUS: serviceRequest.status,
        STATUS_MESSAGE: statusMessage,
      },
    ];
    if (
      serviceRequest?.requestType === RequestType.FIREWALL &&
      serviceRequest?.status === RequestStatus.PROCESSING &&
      action === ActionTypes.UPDATE &&
      serviceRequest?.downstreamResponseData?.href
    ) {
      serviceRequestDetails[0]['Jira Id'] =
        `<a href="${serviceRequest?.downstreamResponseData?.href}">${serviceRequest?.downstreamResponseData?.key}</a>`;
    }

    if (
      serviceRequest?.requestType === RequestType.COMMON_FIREWALL_POLICY &&
      action === ActionTypes.UPDATE &&
      serviceRequest?.downstreamResponseData?.gitlabDetails?.webUrl
    ) {
      serviceRequestDetails[0]['Merge Request'] =
        `<a href="${serviceRequest?.downstreamResponseData?.gitlabDetails?.webUrl}">${serviceRequest?.downstreamResponseData?.gitlabDetails?.mergeRequestId}</a>`;
    }
    const notificationRequestPayload = {
      requestId: serviceRequest.serviceRequestId,
      requestLink:
        serviceRequest?.status ==
          (RequestStatus.MIGRATED || RequestStatus.PARTIALLY_MIGRATED) &&
        serviceRequest.requestType == RequestType.FIREWALL
          ? `${this.configService.get(ENVIRONMENT_VARS.ENVIRONMENT_BASE_URL)}/requests/${serviceRequest?.systemUpdate?.FirewallV2ServiceRequestId}`
          : (serviceRequest.requestType == RequestType.FIREWALL_V2 || serviceRequest.requestType == RequestType.DNP)
            ? `${this.configService.get(ENVIRONMENT_VARS.ENVIRONMENT_BASE_URL)}/requests/${serviceRequest.serviceRequestId}`
            : `${this.configService.get(ENVIRONMENT_VARS.ENVIRONMENT_BASE_URL)}${serviceRequest.payload.deeplinkUrl || serviceRequest.payload[0]?.deeplinkUrl}?requestID=${serviceRequest.id}`,
      templateId: process.env.REQUESTER_TEMPLATE_ID,
      userEmail:
        serviceRequest.requesterEmail == '' ||
        serviceRequest.requesterEmail == null
          ? null
          : serviceRequest.requesterEmail,
      notifications: [],
      type: requestType.replace(/_/g, ' '),
      message:
        action === 'CREATE'
          ? `${requestType.replace(/_/g, ' ')} request is submitted`
          : `${requestType.replace(/_/g, ' ')} request is updated`,
      metricsLink: 'abc',
      notificationParams: this.constructNotificationParams(
        serviceRequestDetails,
      ),
      audit: {},
    };
    return await this.integrationApi.post(
      `notification/`,
      notificationRequestPayload,
    );
  }

  async notifyApprovalServiceRequestStatusToIntegrationApi(
    serviceRequest: any,
    emailList: string,
    message: string,
  ) {
    const requestType = this.getNetworkRequestType(serviceRequest.requestType);
    const serviceRequestDetails = [
      {
        PID: serviceRequest.createdBy,
        REQUEST_ID: serviceRequest.serviceRequestId,
        REQUEST_TYPE: requestType,
        CREATED_BY: serviceRequest.requesterEmail
          ? serviceRequest.requesterEmail
          : serviceRequest.createdBy,
        STATUS: serviceRequest.status,
      },
    ];
    const notificationRequestPayload = {
      requestId: serviceRequest.serviceRequestId,
      requestLink: `${this.configService.get(ENVIRONMENT_VARS.ENVIRONMENT_BASE_URL)}${this.configService.get(ENVIRONMENT_VARS.APPROVALS_EMAIL_URL)}${serviceRequest.serviceRequestId}`,
      templateId: process.env.APPROVER_NOTIFICATION_TEMPLATE_ID,
      userEmail: emailList,
      notifications: [],
      type: `${requestType}_APPROVAL_REQUEST`.replace(/_/g, ' '),
      message: message,
      metricsLink: 'abc',
      notificationParams: this.constructNotificationParams(
        serviceRequestDetails,
      ),
      audit: {},
    };

    return await this.integrationApi.post(
      `notification/`,
      notificationRequestPayload,
    );
  }
  async notifyApprovalOfFirewallSubRequestToIntegrationApi(
    serviceRequest: any,
    subrequest: any,
    emailList: string,
    message: string,
  ) {
    this.logger.log(
      'notify approval of firewall sub request to integration started service request==',
      serviceRequest,
      'subrequest',
      subrequest,
      'email list',
      emailList,
      'message',
      message,
    );
    const requestType = this.getNetworkRequestType(serviceRequest.requestType);
    this.logger.log(
      'get request type in integration in notifyApproval function',
      requestType,
    );
    const serviceRequestDetails = [
      {
        PID: serviceRequest.createdBy,
        REQUEST_ID: serviceRequest.serviceRequestId,
        SUB_REQUEST_ID: subrequest?.subRequestId,
        REQUEST_TYPE: requestType,
        CREATED_BY: serviceRequest.requesterEmail
          ? serviceRequest.requesterEmail
          : serviceRequest.createdBy,
        STATUS: subrequest.status,
      },
    ];
    this.logger.log(
      'service request details in notify approval of sub request',
      serviceRequestDetails,
    );
    const notificationRequestPayload = {
      requestId: serviceRequest.serviceRequestId,
      requestLink: `${this.configService.get(ENVIRONMENT_VARS.ENVIRONMENT_BASE_URL)}/approvals/${serviceRequest.serviceRequestId}`,
      templateId: process.env.SUBREQUEST_APPROVER_NOTIFICATION_TEMPLATE_ID,
      userEmail: emailList,
      notifications: [],
      type: `${requestType}_APPROVAL_REQUEST`.replace(/_/g, ' '),
      message: message,
      metricsLink: 'abc',
      notificationParams: this.constructNotificationParams(
        serviceRequestDetails,
      ),
      audit: {},
    };
    this.logger.log(
      'payload of notify approve sub request',
      notificationRequestPayload,
    );
    return await this.integrationApi.post(
      `notification/`,
      notificationRequestPayload,
    );
  }

  async notifyResourceCreationToIntegrationApi(
    resource: ResourcesRequestDto,
    statusMessage: string,
    serviceRequestDetails: {
      id: string;
      deeplinkUrl: string;
      userEmail: string;
    },
  ) {
    const resourceDetails = resource.resourcesDetails as MongoDBResourceDetails;
    const notificationParams = [
      {
        REQUEST_ID: resource.requestId,
        RESOURCE_ID: resource.resourceId,
        REQUEST_TYPE: resource.catalogType,
        STATUS: resource.status,
        STATUS_MESSAGE: statusMessage,
        DB_NAME: resourceDetails.DBParams.database_name,
        DB_VERSION:
          resourceDetails.DBParams.mongodb_version ??
          resourceDetails.DBParams.postgres_version ??
          resourceDetails.DBParams.redis_version,
        DB_USER_NAME: resourceDetails.DBParams.db_username,
        DB_PASSWORD: resourceDetails.DBParams.db_password,
      },
    ];

    const notificationRequestPayload = {
      requestId: resource.requestId,
      requestLink: `${this.configService.get(ENVIRONMENT_VARS.ENVIRONMENT_BASE_URL)}${serviceRequestDetails.deeplinkUrl}?requestID=${serviceRequestDetails.id}`,
      templateId: process.env.REQUESTER_TEMPLATE_ID,
      userEmail: serviceRequestDetails.userEmail,
      notifications: [],
      type: resource.catalogType.replace(/_/g, ' '),
      message: `${resource.catalogType.replace(/_/g, ' ')} request is created. Please find connection string as below ${resourceDetails?.connectionString}`,
      metricsLink: 'abc',
      notificationParams: this.constructNotificationParams(notificationParams),
      audit: {},
    };

    return await this.integrationApi.post(
      `notification/`,
      notificationRequestPayload,
    );
  }

  async notifyPureStorageResourceRestoreToIntegrationApi(
    resource,
    status,
    requestDetails: {
      id: string;
      deeplinkUrl: string;
      userEmail: string;
      PID: string;
    },
  ) {
    const serviceRequestDetails = [
      {
        PID: requestDetails.PID,
        REQUEST_ID: resource.requestId,
        REQUEST_TYPE:
          resource.catalogType.toLowerCase() === CatalogTypes.S3
            ? RequestType.RESTORE_STORAGE_S3
            : RequestType.RESTORE_STORAGE_NFS,
        STATUS: status,
        STATUS_MESSAGE: `Your request status is changed to ${status}`,
      },
    ];

    const notificationRequestPayload = {
      requestId: resource.requestId,
      requestLink: `${this.configService.get(ENVIRONMENT_VARS.ENVIRONMENT_BASE_URL)}${requestDetails.deeplinkUrl}?requestID=${requestDetails.id}`,
      templateId: process.env.REQUESTER_TEMPLATE_ID,
      userEmail: requestDetails.userEmail,
      notifications: [],
      type: `RESTORE STORAGE ${resource.catalogType.replace(/_/g, ' ')}`,
      message: `RESTORE STORAGE ${resource.catalogType.replace(/_/g, ' ')} request is ${status}`,
      metricsLink: 'abc',
      notificationParams: this.constructNotificationParams(
        serviceRequestDetails,
      ),
      audit: {},
    };

    return await this.integrationApi.post(
      `notification/`,
      notificationRequestPayload,
    );
  }

  getNetworkRequestType(requestType: string) {
    let response: string;
    switch (requestType) {
      case 'CREATE_AVAILABLE_NETWORK':
        response = 'RESERVE_IP_ADDRESS_BLOCK';
        break;
      case 'RESERVE_IP':
        response = 'UPDATE_IP_ADDRESS_BLOCK';
        break;
      case 'RELEASE_IP':
        response = 'RELEASE_IP';
        break;
      default:
        response = requestType;
        break;
    }
    return response;
  }

  async notifyApproverCommentToIntegrationApi(
    serviceRequest,
    subRequest,
    email,
    approveOrRejectRequestDto,
    message,
    approvalGroup,
  ) {
    this.logger.log(
      'notify approval of firewall sub request to integration started service request==',
      serviceRequest,
      'subrequest',
      subRequest,
      'email list',
      email,
      'message',
      message,
      'approver comment',
      approveOrRejectRequestDto.approverComment,
      'approvalGroupt',
      approvalGroup,
    );
    const requestType = this.getNetworkRequestType(serviceRequest.requestType);
    this.logger.log(
      'get request type in integration in notifyApproval function',
      requestType,
    );
    const serviceRequestDetails = [
      {
        APPROVER_ID: serviceRequest.approvalDetails
          .filter((item) => item.approvalGroup === approvalGroup.groups[0])
          .map((item) => item.approvedOrRejectedBy)
          .toString(),
        REQUEST_ID: serviceRequest.serviceRequestId,
        SUB_REQUEST_ID: subRequest
          .filter(
            (ele) => ele.organizationName === approveOrRejectRequestDto.subType,
          )
          .map((ele) => ele.subRequestId)
          ?.join(','),
        REQUEST_TYPE: requestType,
        CREATED_BY: serviceRequest.requesterEmail
          ? serviceRequest.requesterEmails
          : serviceRequest.createdBy,
        STATUS: subRequest.status,
        COMMENT: approveOrRejectRequestDto.approverComment,
      },
    ];
    this.logger.log(
      'service request details to noftify user',
      serviceRequestDetails,
    );
    const notificationRequestPayload = {
      requestId: serviceRequest.serviceRequestId,
      requestLink: `${this.configService.get(ENVIRONMENT_VARS.ENVIRONMENT_BASE_URL)}/approvals?requestID=${serviceRequest.serviceRequestId}`,
      templateId: process.env.APPROVER_COMMENT_NOTIFICATION_TEMPLATE_ID,
      userEmail: email,
      notifications: [],
      type: `${requestType}_APPROVAL_COMMENT`.replace(/_/g, ' '),
      message: approveOrRejectRequestDto.approverComment,
      metricsLink: 'abc',
      notificationParams: this.constructNotificationParams(
        serviceRequestDetails,
      ),
      audit: {},
    };
    this.logger.log(
      'payload of notify approve sub request',
      notificationRequestPayload,
    );
    return await this.integrationApi.post(
      `notification/`,
      notificationRequestPayload,
    );
  }

  getNotificationMessage(state: string, message?:string) {
    switch (state) {
      case TOKEN_STATUS.EXPIRING:
        return 'This is to inform you that a secret will be expired soon. Please check rotation process promptly to avoid disruption in services.';
        break;
      case TOKEN_STATUS.TOKEN_ROTATION_INITIATION_FAILED:
        return `This is to inform you that a secret rotation initiation failed with reason <strong>${message}</strong>`;
        break;
      case TOKEN_STATUS.TOKEN_ROTATION_INITIATED:
        return 'This is a reminder that as part of <strong>secret rotation process</strong>,<br><strong>Nebula</strong> is initiating a secret rotation process to ensure continued security and compliance.';
        break;
      case ROTATION_STATUS.COMPLETED:
        return `This is a reminder that secret rotation process is completed with the status <strong>SUCCESS</strong>. Password is successfully attached to the secretKey.`;
        break;
      case ROTATION_STATUS.FAILED:
        return message&&message!=null? `Secret Rotation is failed with a reason,<strong> ${message}</strong>` :`This is a reminder that secret rotation process is completed with the status <strong>${state}</strong>. Password is not attached to the secretKey.`;
        break;
      case ROTATION_STATUS.UNKNOWN:
        return `This is a reminder that secret rotation process is completed with the status <strong>${state}</strong>. Password is not attached to the secretKey.`;
        break;
      default:
        return 'Unknown status received. Please verify the status value or contact the <strong>Nebula Webex Channel</strong>.';
        break;
    }
  }

  async notifyTokenExpiryToNameSpaceGroup(
    requestId: string,
    type: string,
    email: string,
    vaultDetails: {
      project?: string;
      namespace: string;
      path: string;
      vaultSecretKey: string;
    },
    rotationState: TOKEN_STATUS | ROTATION_STATUS,
     message?: string,
  ) {
    let notificationMessage = this.getNotificationMessage(rotationState, message);

    const resourcePayload = Object.keys(vaultDetails).map((resource) => {
      return { key: resource, value: vaultDetails[resource] };
    });

    const notificationRequestPayload = {
      requestId: requestId,
      type: type,
      templateId: process.env.TOKEN_EXPIRY_NOTIFICATION_TEMPLATE_ID,
      message: notificationMessage,
      metricsLink: 'abc',
      userEmail: email,
      notificationParams: [
        {
          resourceParams: resourcePayload,
        },
      ],
    };

    this.logger.log(
      'payload of notify token expiry',
      notificationRequestPayload,
    );
    return await this.integrationApi.post(
      `notification/`,
      notificationRequestPayload,
    );
  }

  async notifySecretDeviceAssociationChangesToNameSpaceGroup(
    requestId: string,
    type: SecretType,
    email: string,
    action: RequestType.ADD_DEVICE | RequestType.REMOVE_DEVICE,
    vaultDetails: {
      project?: string;
      namespace?: string;
    },
    deviceAssociationPayload:{devicename:string, secretkey:any}[]
  ) {
    let VaultDetails = 
      `<tr>
      <td style="padding:8px; border: 1px solid #ccc">${vaultDetails.project}</td>
      <td style="padding:8px; border: 1px solid #ccc">${vaultDetails.namespace}</td>
      </tr>`;
    let notificationMessage = `This is to inform you that the listed devices has been <strong>${action === RequestType.ADD_DEVICE ? 'Added' : 'Removed'}</strong> as part of our infrastructure's <strong>secret-device association</strong> process.</br>
    <table style="boder-collapse: collapase">
    <thead>
    <tr>
    <th style="padding:8px; border: 1px solid #ccc"><String>project</Strong> </th>
    <th style="padding:8px; border: 1px solid #ccc"><String>namespace</Strong></th>
    </tr>
    </thead>
    <tbody>${VaultDetails}</tbody>
    </table>`;
    
    const notificationRequestPayload = {
      requestId: requestId,
      type: type,
      templateId: process.env.TOKEN_EXPIRY_NOTIFICATION_TEMPLATE_ID,
      message: notificationMessage,
      metricsLink: 'abc',
      userEmail: email,
      notificationParams: this.constructNotificationParams(deviceAssociationPayload)
    };

    this.logger.log(
      'payload of device asspcoation',
      notificationRequestPayload,
    );
    return await this.integrationApi.post(
      `notification/`,
      notificationRequestPayload,
    );
  }
}
