import { DecryptPasswordResponseDto } from './decrypt.response.dto';

describe('Descrypt Response DTO', () => {
  it('should create an instance with correct properties', () => {
    const request = new DecryptPasswordResponseDto();
    request.decryptedPassword = 'testing';

    expect(request).toBeInstanceOf(DecryptPasswordResponseDto);
    expect(request.decryptedPassword).toBe('testing');
  });
});
