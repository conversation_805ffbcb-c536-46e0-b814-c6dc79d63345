import 'reflect-metadata';
import { SecretsPoliciesDto } from './secretPolicies.dto';
import { validate } from 'class-validator';
import { SecretPolicyTypes, Status } from '../types/secretsPolicies.enum';

describe('SecretsPoliciesDto', () => {
  it('should validate a correct DTO', async () => {
    const dto = new SecretsPoliciesDto();
    dto.policyName = 'Test';
    dto.type = SecretPolicyTypes.VAULT_POLICY;
    dto.description = 'Test description';
    dto.policyRules = {
      passwordDescription: 'Test',
      acceptedSpecialCharacters: '!,@,#,$',
      totalCharactersLength: 12,
      specialCharactersCount: 3,
      lowerCaseLettersCount: 3,
      upperCaseLettersCount: 3,
      numericalCharactersCount: 3,
    };
    dto.resourceId = 'parent123';
    dto.status = 'ACTIVE';

    const errors = await validate(dto);
    expect(errors.length).toBe(0);
  });

  it('should fail validation when required fields are missing', async () => {
    const dto = new SecretsPoliciesDto(); // missing required fields

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    const errorProps = errors.map((e) => e.property);
    expect(errorProps).toContain('type');
  });

  it('should conditionally validate vault fields only for VAULT_POLICY type', async () => {
    const dto = new SecretsPoliciesDto();
    dto.type = SecretPolicyTypes.VAULT_POLICY;
    dto.status = 'ACTIVE';

    // Missing vaultHistoryVersion and secretVersion
    const errors = await validate(dto);
    const errorProps = errors.map((e) => e.property);
  });
});
