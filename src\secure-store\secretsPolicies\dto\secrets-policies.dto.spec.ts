import 'reflect-metadata';
import { SecretsPoliciesDto } from './secretPolicies.dto';
import { validate } from 'class-validator';
import { SecretPolicyTypes, Status } from '../types/secretsPolicies.enu';

describe('SecretsPoliciesDto', () => {
  it('should validate a correct DTO', async () => {
    const dto = new SecretsPoliciesDto();
    dto.secretId = 'secret123';
    dto.type = SecretPolicyTypes.VAULT_POLICY;
    dto.description = 'Test description';
    dto.parentResourceID = 'parent123';
    dto.status = Status.ACTIVE;
    dto.secretTTLInHours = 24;
    dto.active = true;
    dto.namespace = 'default';
    dto.vaultHistoryVersion = 1;
    dto.secretVersion = 2;

    const errors = await validate(dto);
    expect(errors.length).toBe(0);
  });

  it('should fail validation when required fields are missing', async () => {
    const dto = new SecretsPoliciesDto(); // missing required fields

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    const errorProps = errors.map((e) => e.property);
    expect(errorProps).toContain('secretId');
    expect(errorProps).toContain('type');
  });

  it('should conditionally validate vault fields only for VAULT_POLICY type', async () => {
    const dto = new SecretsPoliciesDto();
    dto.secretId = 'secret123';
    dto.type = SecretPolicyTypes.VAULT_POLICY;
    dto.active = true;

    // Missing vaultHistoryVersion and secretVersion
    const errors = await validate(dto);
    const errorProps = errors.map((e) => e.property);
    expect(errorProps).toContain('vaultHistoryVersion');
    expect(errorProps).toContain('secretVersion');
  });
});
