export enum RequestType {
  CREATE_AVAILABLE_NETWORK = 'CREATE_AVAILABLE_NETWORK',
  CREATE_ORGANIZATION = 'CREATE_ORGANIZATION',
  ADD_DEVICE = 'ADD_DEVICE',
  DNP = 'DNP',
  RESERVE_IP = 'RESERVE_IP',
  REMOVE_DEVICE = 'REMOVE_DEVICE',
  RELEASE_IP = 'RELEASE_IP',
  FIREWALL = 'FIREWALL',
  CREATE_VM_LINUX7 = 'CREATE_VM_LINUX7',
  CREATE_VM_LINUX89 = 'CREATE_VM_LINUX89',
  CREATE_VM_UBUNTU = 'CREATE_VM_UBUNTU',
  CREATE_VM_WINDOWS = 'CREATE_VM_WINDOWS',
  CREATE_VM_LINUX89_LAB = 'CREATE_VM_LINUX89_LAB',
  CREATE_VM_LINUX89_LAB_ADMIN = 'CREATE_VM_LINUX89_LAB_ADMIN',
  CREATE_VM_UBUNTU_LAB = 'CREATE_VM_UBUNTU_LAB',
  CREATE_VM_WINDOWS_LAB = 'CREATE_VM_WINDOWS_LAB',
  CREATE_VM_LINUX89_VMWARE = 'CREATE_VM_LINUX89_VMWARE',
  CREATE_VM_LINUX89_ADMIN_VMWARE = 'CREATE_VM_LINUX89_ADMIN_VMWARE',
  CREATE_VM_UBUNTU_VMWARE = 'CREATE_VM_UBUNTU_VMWARE',
  CREATE_VM_UBUNTU_ADMIN_VMWARE = 'CREATE_VM_UBUNTU_ADMIN_VMWARE',
  CREATE_VM_WINDOWS_VMWARE = 'CREATE_VM_WINDOWS_VMWARE',
  CREATE_VM_WINDOWS_ADMIN_VMWARE = 'CREATE_VM_WINDOWS_ADMIN_VMWARE',
  CREATE_DB_MONGODB6 = 'CREATE_DB_MONGODB6',
  CREATE_DB_MONGODB7 = 'CREATE_DB_MONGODB7',
  CREATE_DB_POSTGRESDB15 = 'CREATE_DB_POSTGRESDB15',
  CREATE_DB_POSTGRESDB16 = 'CREATE_DB_POSTGRESDB16',
  CREATE_DB_REDISDB6 = 'CREATE_DB_REDISDB6',
  CREATE_DB_REDISDB7 = 'CREATE_DB_REDISDB7',
  CREATE_DB_ORACLEDB19C = 'CREATE_DB_ORACLEDB19C',
  CREATE_DB_ORACLEDB21C = 'CREATE_DB_ORACLEDB21C',
  DELETE_VM = 'DELETE_VM',
  DELETE_VM_VMWARE = 'DELETE_VM_VMWARE',
  DELETE_BLUE_VM = 'DELETE_BLUE_VM',
  DELETE_S3 = 'DELETE_S3',
  DELETE_NFS = 'DELETE_NFS',
  DELETE_DB = 'DELETE_DB',
  RESTORE_STORAGE_S3 = 'RESTORE_STORAGE_S3',
  RESTORE_STORAGE_NFS = 'RESTORE_STORAGE_NFS',
  COMMON_FIREWALL_POLICY = 'COMMON_FIREWALL_POLICY',
  FIREWALL_V2 = 'FIREWALL_V2',
  RESUBMIT_FIREWALL_V2 = 'RESUBMIT_FIREWALL_V2',
  DYNAMIC_ACCESS_POLICIES = 'DYNAMIC_ACCESS_POLICIES',
  S3 = 'CREATE_STORAGE_S3',
  MODIFY_STORAGE_S3 = 'MODIFY_STORAGE_S3',
  REGENERATE_KEYS = 'REGENERATE_KEYS',
  NFS = 'CREATE_STORAGE_NFS',
  MODIFY_NFS = 'MODIFY_STORAGE_NFS',
  STOP_VM = 'STOP_VM',
  START_VM = 'START_VM',
  RESTART_VM = 'RESTART_VM',
  CREATE_LINUX_CORPNET = 'CREATE_LINUX_CORPNET',
  CREATE_WINDOWS_CORPNET = 'CREATE_WINDOWS_CORPNET',
  AWS_CREATE_SUBACCOUNT = 'AWS_CREATE_SUBACCOUNT',
  AWS_SUBACCOUNT_GROUPS = 'AWS_SUB_ACCOUNT_GROUPS',
  CREATE_JIRA_STORY = 'CREATE_JIRA_STORY',
  MANAGE_SUB_ACCOUNT_GROUP_PERMISSION = 'MANAGE_SUB_ACCOUNT_GROUP_PERMISSION',
  MANAGE_PERMISSION_SET_CREATE = 'MANAGE_PERMISSION_SET_CREATE',
  MANAGE_PERMISSION_SET_REMOVE = 'MANAGE_PERMISSION_SET_REMOVE',
  AWS_CREATE_USER = 'AWS_CREATE_USER',
  CREATE_LB_F5 = 'CREATE_LB_F5',
  CREATE_INTERNAL_CERTIFICATE = 'CREATE_INTERNAL_CERTIFICATE',
  RETRY_VM = 'RETRY_VM',
  AWS_REMOVE_USER = 'AWS_REMOVE_USER',
  FIREWALL_V1_MIGRATE = 'FIREWALL_V1_MIGRATE',
  BULK_VM_IMPORT = 'BULK_VM_IMPORT',
  ONBOARD_PROJECT = 'ONBOARD_PROJECT',
  CATALOG_ACCESS_REQUEST = 'CATALOG_ACCESS_REQUEST',
  ONBOARD_GROUP = 'ONBOARD_GROUP',
  SPEC_FLOW_S3 = 'SPEC_FLOW_S3',
  SPEC_FLOW_EC2 = 'SPEC_FLOW_EC2',
  SPEC_FLOW_EKS = 'SPEC_FLOW_EKS',
  PUBLIC_CLOUD_VPC = 'PUBLIC_CLOUD_VPC',
  DELETE_PUBLIC_CLOUD = 'DELETE_PUBLIC_CLOUD',
  CREATE_NAMESPACE = 'CREATE_NAMESPACE',
  RECONFIGURE_VM = 'RECONFIGURE_VM',
  ZERO_TOUCH_PROVISIONING = 'ZERO_TOUCH_PROVISIONING',
  EDIT_VM = 'EDIT_VM',
}

// The order of items in RequestStatus should be take in consideration as that is used for sorting.
export enum RequestStatus {
  PENDING_APPROVAL = 'PENDING APPROVAL',
  APPROVED = 'APPROVED',
  AUTO_APPROVED = 'AUTO APPROVED',
  REJECTED = 'REJECTED',
  PROCESSING = 'PROCESSING',
  SUBMITTED = 'SUBMITTED',
  SUCCESS = 'COMPLETED',
  FAILED = 'FAILED',
  PARTIAL = 'COMPLETED WITH ERROR',
  RELEASED = 'RELEASED',
  PENDING_PIPELINE_CHECK = 'PENDING PIPELINE CHECK',
  PENDING_DAP_PROCESS = 'PENDING DAP PROCESS',
  PENDING_DEPLOYMENT = 'PENDING DEPLOYMENT',
  CREATED = 'CREATED',
  PARTIALLY_APPROVED = 'PARTIALLY APPROVED',
  PARTIALLY_REJECTED = 'PARTIALLY REJECTED',
  PARTIAL_SUCCESS = 'PARTIALLY COMPLETED',
  CANCELLED = 'CANCELLED',
  PENDING_RISK_ANALYSIS = 'PENDING RISK ANALYSIS',
  PENDING_DESIGNER_RESULTS = 'PENDING DESIGNER RESULTS',
  TIMED_OUT = 'TIMED OUT',
  CLOSED = 'CLOSED',
  NO_CHANGE_REQUIRED = 'NO CHANGE REQUIRED',
  RETRYING = 'RETRYING',
  CLOSED_MANUALLY = 'CLOSED MANUALLY',
  MIGRATED = 'MIGRATED',
  CANCELLING = 'CANCELLING',
  PARTIALLY_MIGRATED = 'PARTIALLY_MIGRATED',
  MIGRATION_FAILED = 'MIGRATION_FAILED',
}

export enum jobStatus {
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
  PROCESSING = 'PROCESSING',
}

export enum TufinTaskName {
  RISK_ANALYSIS = 'Security Review',
  DESIGNER = 'Technical Review',
  IMPLEMENTATION = 'Implementation Review',
  VERIFICATION = 'Verification Review',
}

export enum TufinTaskStatus {
  NOT_IMPLEMENTED = 'Not implemented',
  FULLY_IMPLEMENTED = 'Fully implemented',
}

export enum TufinWorkflowStepStatus {
  FAILED = 'Fail',
  SUCCESS = 'Success',
}

export enum TufinWorkflowStepName {
  DESIGNER = 'designer',
}

export enum ServiceCatalogName {
  CREATE_IP_POOL = 'IaaS-Naas-Create-IP-Pool',
  CREATE_ORG = 'IaaS-Naas-Create-Org',
  ADD_DEVICE = 'IaaS-Naas-Add-Device',
  OFFBOARD_DEVICE = 'IaaS-Naas-Offboard-Device',
  DNP = 'IaaS-Naas-DNP',
  REMOVE_DEVICE = 'IaaS-Naas-Remove-Device',
  RESERVE_IP = 'IaaS-NaaS-Reserve-IP',
  RELEASE_IP = 'IaaS-NaaS-Release-IP',
  PROVISION_VIRTUAL_SERVER = 'IaaS-Compute-Virtual-Server',
  FIREWALL = 'IaaS-NaaS-Firewall',
  PROVISION_MONGO_DB = 'PaaS-DbaaS-Create-MongoDB',
  PROVISION_POSTGRES_DB = 'PaaS-DbaaS-Create-PostgresDB',
  PROVISION_REDIS_DB = 'PaaS-DbaaS-Create-RedisDB',
  PROVISION_ORACLE_DB = 'PaaS-DbaaS-Create-OracleDB',
  COMMON_FIREWALL_POLICY = 'IaaS-NaaS-Common-Firewall-Policy',
  FIREWALL_V2 = 'IaaS-NaaS-Firewall-v2',
  DYNAMIC_ACCESS_POLICIES = 'IaaS-NaaS-Dynamic-Access-Policies',
  S3 = 'IaaS-Storage-S3',
  NFS = 'IaaS-Storage-NFS',
  AWS_CREATE_SUBACCOUNT = 'Onboard-SysAcc-AWS-Acc-IAM',
  AWS_SUBACCOUNT_GROUPS = 'Onboard-SysAcc-AWS-Acc-MSAG',
  MANAGE_SUB_ACCOUNT_GROUP_PERMISSION = 'Onboard-SysAcc-AWS-Acc-MSAGP',
  MANAGE_PERMISSION_SET_CREATE = 'Onboard-SysAcc-AWS-Acc-MPSC',
  MANAGE_PERMISSION_SET_REMOVE = 'Onboard-SysAcc-AWS-Acc-MPSR',
  AWS_CREATE_USER = 'Onboard-SysAcc-AWS-Acc-MU',
  F5_LB = 'Iaas-LB-F5',
  INTERNAL_CERTIFICATE = 'Iaas-Naas-Internal-Certificate',
  FIREWALL_V1_MIGRATE = 'Iaas-Nass-Firewall-v1-Migration',
  BULK_VM_IMPORT = 'Iaas-Compute-Bulk-VM-Import',
  DELETE_VM = 'Iaas-Compute-Delete-VM',
  DELETE_VM_VMWARE = 'Iaas-Compute-Delete-VM-VMWARE',
  DELETE_DB = 'PaaS-DbaaS-Delete-DB',
  ONBOARD_PROJECT = 'OnBoard-Project',
  CATALOG_ACCESS_REQUEST = 'Catalog-Access-Request',
  ONBOARD_GROUP = 'OnBoard-Group',
  SPEC_FLOW_S3 = 'Spec-Flow-S3-Create',
  SPEC_FLOW_EC2 = 'Spec-Flow-EC2-Create',
  SPEC_FLOW_EKS = 'Spec-Flow-EKS-Create',
  PUBLIC_CLOUD_VPC = 'Public-Cloud-VPC-Create',
  DELETE_PUBLIC_CLOUD = 'Public-Cloud-Asset-Delete',
  CREATE_NAMESPACE = 'Create-Namespace',
  RECONFIGURE_VM = 'RECONFIGURE_VM',
  CREATE_ZTP = 'IaaS-NaaS-ZTP-Create',
}

export enum ShortName {
  MONGODB6 = 'mongodb6',
  MONGODB7 = 'mongodb7',
  POSTGRES15 = 'postgresdb15',
  POSTGRES16 = 'postgresdb16',
  REDISDB6 = 'redisdb6',
  REDISDB7 = 'redisdb7',
  ORACLEDB19C = 'oracledb19c',
  ORACLEDB21C = 'oracledb21c',
}

export enum DbSize {
  SMALL = 'small',
  MEDIUM = 'medium',
  LARGE = 'large',
}

export const GenericRequestTypes = {
  'IaaS-Compute-Virtual-Server': 'NEB-IAAS-VM',
  'Iaas-Compute-Bulk-VM-Import': 'NEB-IAAS-VM',
  'Iaas-Compute-Delete-VM': 'NEB-IAAS-VM',
  'PaaS-DbaaS-Delete-DB': 'NEB-PAAS-DB',
  'PaaS-DbaaS-Create-MongoDB': 'NEB-PAAS-DB',
  'PaaS-DbaaS-Create-PostgresDB': 'NEB-PAAS-DB',
  'PaaS-DbaaS-Create-RedisDB': 'NEB-PAAS-DB',
  'PaaS-DbaaS-Create-OracleDB': 'NEB-PAAS-DB',
  'IaaS-NaaS-Firewall': 'NEB-IAAS-FW',
  'IaaS-NaaS-Reserve-IP': 'NEB-IAAS-RES-IP',
  'IaaS-NaaS-Release-IP': 'NEB-IAAS-REL-IP',
  'IaaS-Naas-Create-Org': 'NEB-IAAS-CMO',
  'IaaS-Naas-Add-Device': 'NEB-IAAS-OND',
  'IaaS-Naas-Remove-Device': 'NEB-IAAS-OFFD',
  'IaaS-Naas-DNP': 'NEB-IAAS-DNP',
  'IaaS-Naas-Create-IP-Pool': 'NEB-IAAS-CIP',
  'IaaS-NaaS-Common-Firewall-Policy': 'NEB-IAAS-CFP',
  'IaaS-NaaS-Firewall-v2': 'NEB-IAAS-FW-V2',
  'IaaS-NaaS-Dynamic-Access-Policies': 'NEB-IAAS-DAP',
  'IaaS-Storage-S3': 'NEB-IAAS-S3',
  'IaaS-Storage-NFS': 'NEB-IAAS-NFS',
  'Onboard-SysAcc-AWS-Acc-IAM': 'NEB-ONB-AWS-SA',
  'Onboard-SysAcc-AWS-Acc-MSAG': 'NEB-ONB-AWS-MSAG',
  'Onboard-SysAcc-AWS-Acc-MSAGP': 'NEB-ONB-AWS-MSAGP',
  'Onboard-SysAcc-AWS-Acc-MPSC': 'NEB-ONB-AWS-MPSC',
  'Onboard-SysAcc-AWS-Acc-MU': 'NEB-ONB-AWS-MU',
  'Iaas-LB-F5': 'NEB-IAAS-LB-F5',
  'Iaas-Naas-Internal-Certificate': 'NEB-IAAS-ICERT',
  'Onboard-SysAcc-AWS-Acc-MPSR': 'NEB-ONB-AWS-MPSR',
  'OnBoard-Project': 'NEB-ONB-PRJ',
  'Catalog-Access-Request': 'NEB-CATALOG-ACC-REQ',
  'OnBoard-Group': 'NEB-ONB-GRP',
  'Spec-Flow-S3-Create': 'NEB-SPEC-S3',
  'Spec-Flow-EC2-Create': 'NEB-SPEC-EC2',
  'Spec-Flow-EKS-Create': 'NEB-SPEC-EKS',
  'Public-Cloud-VPC-Create': 'NEB-PUB-VPC',
  'Public-Cloud-Asset-Delete': 'NEB-PUB-DEL',
  'Iaas-Compute-Delete-VM-VMWARE': 'NEB-IAAS-VM',
  'Create-Namespace': 'NEB-VAULT-NAMESPACE',
  RECONFIGURE_VM: 'NEB-IAAS-VM',
  'VAULT-ROTATABLE-SECRET': 'NEB-VAULT-ROT-SECRET',
  'VAULT-NORMAL-SECRET': 'NEB-VAULT-NOR-SECRET',
  'VAULT-TOKEN-SECRET': 'NEB-VAULT-TOKEN-SECRET',
  'IaaS-NaaS-ZTP-Create': 'NEB-IAAS-ZTP',
};

export enum CounterTypes {
  SERVICE_REQUEST = 'SERVICE_REQUEST',
  RESOURCES = 'RESOURCES',
  FIREWALL_SUB_REQUEST = 'FIREWALL_SUB_REQUESTS',
  SECRETS_METADATA = 'SECRETS_METADATA',
  SECRET_DEVICE_ASSOCIATION = 'SECRET_DEVICE_ASSOCIATION',
  ACTION = 'ACTION',
}

export const GenericResourceTypes = {
  'IaaS-Compute-Virtual-Server': 'NEB-RES-VM',
  'Iaas-Compute-Bulk-VM-Import': 'NEB-RES-VM',
  'IaaS-NaaS-Firewall': 'NEB-RES-FIR',
  'PaaS-DbaaS-Create-MongoDB': 'NEB-RES-DB',
  'PaaS-DbaaS-Create-PostgresDB': 'NEB-RES-DB',
  'PaaS-DbaaS-Create-RedisDB': 'NEB-RES-DB',
  'PaaS-DbaaS-Create-OracleDB': 'NEB-RES-DB',
  'Firewall-Sub-Request': 'NEB-FW-SUB-REQ',
  'IaaS-Storage-S3': 'NEB-RES-S3',
  'IaaS-Storage-NFS': 'NEB-RES-NFS',
  'Iaas-Naas-Internal-Certificate': 'NEB-RES-INTERNAL-CERTIFICATE',
  'Iaas-LB-F5': 'NEB-RES-LB-F5',
  'Spec-Flow-EC2-Create': 'NEB-RES-SPEC-EC2',
  'Spec-Flow-S3-Create': 'NEB-RES-SPEC-S3',
  'Spec-Flow-EKS-Create': 'NEB-RES-SPEC-EKS',
  'Public-Cloud-VPC-Create': 'NEB-RES-PUB-VPC',
  SecretDeviceAssociation: 'NEB-SEC-LINK',
  'Create-Namespace': 'NEB-RES-VAULT-NAMESPACE',
  action: 'NEB-ACTION',
};

export enum ServiceCatalogType {
  IAAS = 'Iaas',
  NAAS = 'NaaS',
  DBAAS = 'DbaaS',
  Onboarding = 'Onboarding',
  Multitenancy = 'Multitenancy',
  Specflow = 'Specflow',
}

export enum ApprovalStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  AUTO_APPROVED = 'AUTO APPROVED',
  REJECTED = 'REJECTED',
  NA = 'NA',
  PARTIALLY_APPROVED = 'PARTIALLY APPROVED',
  PARTIALLY_REJECTED = 'PARTIALLY REJECTED',
}

export enum NebulaConfigMap {
  DBAAS = 'DBAAS',
  VIRTUAL_MACHINE = 'VIRTUALMACHINE',
  BLUE_VM = 'BLUE_VM',
  F5_LB = 'F5_LB',
  INTERNAL_CERTIFICATE = 'INTERNAL_CERTIFICATE',
  FIREWALL_V2 = 'FIREWALL_V2',
}

export enum ActionTypes {
  UPDATE = 'UPDATE',
  CREATE = 'CREATE',
}

export enum JobStatusEnum {
  SUCCESS = 'SUCCESS',
  FAILURE = 'FAILURE',
  IN_PROGRESS = 'IN_PROGRESS',
  PENDING = 'PENDING',
}

export enum API_RESPONSE_STATUS {
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
  PARTIAL_SUCCESS = 'PARTIAL_SUCCESS',
  PARTIAL_COMPLETED = 'PARTIALLY COMPLETED',
}

export enum IpType {
  IPV4 = 'IPV4',
  IPV6 = 'IPV6',
}

export enum EventPatterns {
  COMMON_FIREWAL_POLICY_CREATE = 'COMMON_FIREWALL_POLICY_CREATE',
  COMMON_FIREWALL_POLICY_APPROVED = 'COMMON_FIREWALL_POLICY_APPORVED',
  COMMON_FIREWALL_POLICY_REJECTED = 'COMMON_FIREWALL_POLICY_REJECTED',
  DYNAMIC_ACCESS_POLICIES = 'DYNAMIC_ACCESS_POLICIES',
  FIREWALL_V2_CANCELLED = 'FIREWALL_V2_CANCELLED',
  FIREWALL_V2_SUBREQUEST_CANCELLED = 'FIREWALL_V2_SUBREQUEST_CANCELLED',
  FIREWALL_V2_CREATED = 'FIREWALL_V2_CREATED',
  FIREWALL_V2_APPROVED = 'FIREWALL_V2_APPROVED',
  FIREWALL_V2_REJECTED = 'FIREWALL_V2_REJECTED',
  DNP_CREATED = 'DNP_CREATED',
  DNP_APPROVED = 'DNP_APPROVED',
  FIREWALL_V2_SUBREQUEST_CLOSED = 'FIREWALL_V2_SUBREQUEST_CLOSED',
  FIREWALL_V2_RESUBMIT_REQUEST = 'FIREWALL_V2_RESUBMIT_REQUEST',
  FIREWALL_V2_APPROVED_ORGANIZATION = 'FIREWALL_V2_APPROVED_ORGANIZATION',
  FIREWALL_V2_REJECTED_ORGANIZATION = 'FIREWALL_V2_REJECTED_ORGANIZATION',
}

// Enum for Result Types
export enum ResultType {
  SUCCESSFUL = 'SUCCESSFUL',
  PARTIALLY_SUCCESSFUL = 'PARTIALLY SUCCESSFUL',
  FAILED = 'FAILED',
}

//Enum for webhook request types
export enum WEBHOOK_REQUEST_TYPE {
  GIT_UPDATES = 'GIT-PIPELINE-UPDATE',
  DAP_UPDATES = 'DYNAMIC-ACCESS-POLICIES',
  DAP_DEPLOYMENT_UPDATE = 'DYNAMIC-ACCESS-POLICIES-DEPLOYMENT',
  DNP_DAP_UPDATE = 'DNP_DAP_UPDATE',
  VM_CONFIGURE = 'VM_CONFIGURE',
}

// Enum for Device Result Types
export enum DeviceResultType {
  SUCCESSFUL = 'SUCCESSFUL',
  FAILED = 'FAILED',
  NOT_DEPLOYED = 'NOT DEPLOYED',
  ROLLED_BACK = 'ROLLED BACK',
}

//Tufin ticket status
export enum TUFIN_TICKET_STATUS {
  IN_PROGRESS = 'In Progress',
  TICKET_REJECTED = 'Ticket Rejected',
  TICKET_CLOSED = 'Ticket Closed',
  TICKET_CANCELLED = 'Ticket Cancelled',
  TICKET_RESOLVED = 'Ticket Resolved',
  UNKNOWN = 'UNKNOWN',
}

export enum OrganizationName {
  RED_CBO = 'red_cbo',
  UNKNOWN = 'unknown',
  RED_APS = 'red_aps',
  CORPORATE = 'corporate',
  NA_FAILED = 'na_failed',
  NA_NOIMPACT = 'no_change_required',
  AWS = 'aws',
}

export enum CATALOGTYPE {
  LINUX_CORPNET = 'linux corpnet',
  WINDOWS_CORPNET = 'windows corpnet',
}

export enum TUFIN_NETWORK_OBJECT_TYPE {
  UNKNOWN = 'UNKNOWN',
  HOST = 'host_network_object',
  SUBNET = 'subnet_network_object',
  RANGE = 'range_network_object',
  GROUP = 'network_object_group',
  TRANSPORT = 'transport_service',
}

export enum DbVmOptions {
  CREATE_NEW_SERVER = 'Create new server',
}

export enum TicketStatusEnum {
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
}

export enum RETRY_TICKET_STATUS {
  CREATED = 'CREATED',
  CLOSED = 'CLOSED',
  CANCELLED = 'CANCELLED',
  CREATE_FAILED = 'FAILED',
  CLOSE_FAILED = 'CLOSE FAILED',
  CANCEL_FAILED = 'CANCEL FAILED',
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
  IN_PROGRESS = 'IN PROGRESS',
}

export enum RETRY_TICKET_ACTIONS {
  CREATED = 'CREATED',
  CLOSED = 'CLOSED',
  CANCELLED = 'CANCELLED',
}

export enum VM_COMPUTE {
  CPU = 'cpu',
  STORAGE = 'storage',
  MEMORY = 'memory',
}

export enum DOMAIN_TYPE {
  APVSRED = 'APVS-RED',
  PACE = 'PACE',
}
