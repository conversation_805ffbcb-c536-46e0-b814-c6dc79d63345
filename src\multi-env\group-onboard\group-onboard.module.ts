import { Module } from '@nestjs/common';
import { GroupOnboardController } from './group-onboard.controller';
import { GroupOnboardService } from './group-onboard.service';
import { MultiEnvIAMModule } from '../iam/iam.module';

@Module({
  imports: [MultiEnvIAMModule],
  controllers: [GroupOnboardController],
  providers: [GroupOnboardService],
  exports: [GroupOnboardService],
})
export class GroupModule {}
