// secrets-policies.schema.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { MongooseModule, getModelToken } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  SecretsPolicies,
  SecretsPoliciesSchema,
} from './secretsPolicies.schema';
import { SecretPolicyTypes, Status } from '../types/secretsPolicies.enu';
import { MongoMemoryServer } from 'mongodb-memory-server';
import mongoose from 'mongoose';

describe('SecretsPolicies Schema', () => {
  let mongoServer: MongoMemoryServer;
  let secretsPoliciesModel: Model<SecretsPolicies>;

  beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();

    const module: TestingModule = await Test.createTestingModule({
      imports: [
        MongooseModule.forRoot(uri),
        MongooseModule.forFeature([
          { name: SecretsPolicies.name, schema: SecretsPoliciesSchema },
        ]),
      ],
    }).compile();

    secretsPoliciesModel = module.get<Model<SecretsPolicies>>(
      getModelToken(SecretsPolicies.name),
    );
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  it('should create and save a valid SecretsPolicies document', async () => {
    const policy = new secretsPoliciesModel({
      secretId: 'secret123',
      type: SecretPolicyTypes.VAULT_POLICY,
      policyId: 'policy123',
      policyName: 'Vault Policy',
      description: 'Policy description',
      parentResourceID: 'parent123',
      status: Status.ACTIVE,
      secretTTLInHours: 24,
      active: true,
      createdBy: 'admin',
      updatedBy: 'admin',
      namespace: 'default',
      vaultHistoryVersion: 1,
      secretVersion: 2,
    });

    const savedPolicy = await policy.save();

    expect(savedPolicy._id).toBeDefined();
    expect(savedPolicy.secretId).toBe('secret123');
    expect(savedPolicy.type).toBe(SecretPolicyTypes.VAULT_POLICY);
    expect(savedPolicy.status).toBe(Status.ACTIVE);
    expect(savedPolicy.active).toBe(true);
  });

  it('should fail validation if required fields are missing', async () => {
    const policy = new secretsPoliciesModel({});

    let error;
    try {
      await policy.save();
    } catch (err) {
      error = err;
    }

    expect(error).toBeDefined();
    expect(error.name).toBe('ValidationError');
    expect(error.errors.secretId).toBeDefined();
    expect(error.errors.type).toBeDefined();
  });

  it('should default status to ACTIVE if not provided', async () => {
    const policy = new secretsPoliciesModel({
      secretId: 'secret456',
      type: SecretPolicyTypes.VAULT_POLICY,
    });

    const savedPolicy = await policy.save();
    expect(savedPolicy.status).toBe(Status.ACTIVE);
  });

  it('should default active to true if not provided', async () => {
    const policy = new secretsPoliciesModel({
      secretId: 'secret789',
      type: SecretPolicyTypes.VAULT_POLICY,
    });

    const savedPolicy = await policy.save();
    expect(savedPolicy.active).toBe(true);
  });
});
