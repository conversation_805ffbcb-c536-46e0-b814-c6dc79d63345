// secrets-policies.schema.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { MongooseModule, getModelToken } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  SecretsPolicies,
  SecretsPoliciesSchema,
} from './secretsPolicies.schema';
import { SecretPolicyTypes, Status } from '../types/secretsPolicies.enum';
import { MongoMemoryServer } from 'mongodb-memory-server';
import mongoose from 'mongoose';

describe('SecretsPolicies Schema', () => {
  let mongoServer: MongoMemoryServer;
  let secretsPoliciesModel: Model<SecretsPolicies>;

  beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();

    const module: TestingModule = await Test.createTestingModule({
      imports: [
        MongooseModule.forRoot(uri),
        MongooseModule.forFeature([
          { name: SecretsPolicies.name, schema: SecretsPoliciesSchema },
        ]),
      ],
    }).compile();

    secretsPoliciesModel = module.get<Model<SecretsPolicies>>(
      getModelToken(SecretsPolicies.name),
    );
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  it('should create and save a valid SecretsPolicies document', async () => {
    const mockPayload = {
      policyId: 'NEB-VAULT-PASSWORD-POLICY-12236',
      type: 'VAULT-POLICY',
      policyName: 'testpolic',
      description: 'test',
      resourceId: 'NEB-RES-VAULT-NAMESPACE-1000',
      status: 'ACTIVE',
      secretTTLInHours: 700,
      active: true,
      createdBy: 'P3226653',
      createdDate: '2025-06-12T11:58:42.210Z',
      updatedBy: 'P3226653',
      updatedDate: '2025-06-12T11:58:42.210Z',
      namespace: 'nebula-stamp',
    };

    const policy = new secretsPoliciesModel(mockPayload);
    const savedPolicy = await policy.save();

    expect(savedPolicy._id).toBeDefined();
    expect(savedPolicy.type).toBe(SecretPolicyTypes.VAULT_POLICY);
    expect(savedPolicy.status).toBe(Status.ACTIVE);
  });

  it('should default status to ACTIVE if not provided', async () => {
    const mockPayload = {
      policyId: 'NEB-VAULT-PASSWORD-POLICY-12236',
      type: 'VAULT-POLICY',
      policyName: 'testpolic',
      description: 'test',
      resourceId: 'NEB-RES-VAULT-NAMESPACE-1000',
      status: 'ACTIVE',
      secretTTLInHours: 700,
      active: true,
      createdBy: 'P3226653',
      createdDate: '2025-06-12T11:58:42.210Z',
      updatedBy: 'P3226653',
      updatedDate: '2025-06-12T11:58:42.210Z',
      namespace: 'nebula-stamp',
    };
    const policy = new secretsPoliciesModel(mockPayload);

    const savedPolicy = await policy.save();
    expect(savedPolicy.status).toBe(Status.ACTIVE);
  });

  it('should default active to true if not provided', async () => {
    const mockPayload = {
      policyId: 'NEB-VAULT-PASSWORD-POLICY-12236',
      type: 'VAULT-POLICY',
      policyName: 'testpolic',
      description: 'test',
      resourceId: 'NEB-RES-VAULT-NAMESPACE-1000',
      status: 'ACTIVE',
      secretTTLInHours: 700,
      active: true,
      createdBy: 'P3226653',
      createdDate: '2025-06-12T11:58:42.210Z',
      updatedBy: 'P3226653',
      updatedDate: '2025-06-12T11:58:42.210Z',
      namespace: 'nebula-stamp',
    };
    const policy = new secretsPoliciesModel(mockPayload);

    const savedPolicy = await policy.save();
    expect(savedPolicy.status).toBe(Status.ACTIVE);
  });
});
