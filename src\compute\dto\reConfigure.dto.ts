import { Transform, Type } from 'class-transformer';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsBoolean,
  IsObject,
  IsNotEmptyObject,
  ValidateNested,
  IsDefined,
  IsNumber,
  IsArray,
  IsIP,
  IsEnum,
} from 'class-validator';

export enum Action {
  Add = 'Add',
  Delete = 'Delete',
}

export class VolumeDto {
  @IsNumber()
  @IsNotEmpty()
  size: number;

  @IsString()
  @IsNotEmpty()
  diskFileSystem: string;

  @IsString()
  @IsNotEmpty()
  diskName: string;

  @IsOptional()
  @IsEnum(Action, {
    message: 'Action must be either Add or Delete',
  })
  action?: Action;
}

export class VmWareNetworkDto {
  @IsNotEmpty()
  @IsString()
  id: string;

  @IsString()
  @IsOptional()
  ipMode: string;

  @IsNotEmpty()
  @IsString()
  network: string;

  @IsString()
  @IsOptional()
  subnetIpv4?: string;

  @IsString()
  @IsOptional()
  subnetIpv6?: string;

  @IsBoolean()
  @IsOptional()
  ipv4?: boolean;

  @IsBoolean()
  @IsOptional()
  ipv6?: boolean;

  @IsString()
  @IsOptional()
  ipv4Address: string;

  @IsString()
  @IsOptional()
  ipv6Address: string;

  @IsOptional()
  @IsEnum(Action, {
    message: 'Action must be either Add or Delete',
  })
  action?: Action;
}

export class ReConfigureDto {
  @IsString()
  @IsNotEmpty()
  resourceId: string;

  @IsObject({ each: true })
  @ValidateNested()
  @Type(() => VolumeDto)
  @IsDefined()
  @IsOptional()
  volumes?: VolumeDto[];

  @IsNumber()
  @IsOptional()
  memory?: number;

  @IsNumber()
  @IsOptional()
  coreCount?: number;

  @IsNumber()
  @IsOptional()
  coresPerSocket?: number;

  @IsObject({ each: true })
  @ValidateNested()
  @Type(() => VmWareNetworkDto)
  @IsDefined()
  @IsOptional()
  networks?: VmWareNetworkDto[];

  @IsOptional()
  @IsString()
  deeplinkUrl: string;
}
