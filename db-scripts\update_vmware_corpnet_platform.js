const session = db.getMongo().startSession();
session.startTransaction();

try {
  const linuxConfig = db.nebulaconfigs.findOne({ shortName: "linuxcorpnet" });

  if (!linuxConfig) {
    print("No document found with shortName 'linuxcorpnet'. Aborting transaction.");
    session.abortTransaction();
  } else {
    const result = db.nebulaconfigs.updateOne(
      { shortName: "linuxcorpnet", "config.platform": "RedHat" },
      { $set: { "config.platform": "Linux" } }
    );

    print(`${result.modifiedCount} updated successfully.`);
    session.commitTransaction();
  }
} catch (e) {
  print("Error occurred:", e);
  session.abortTransaction();
} finally {
  session.endSession();
}
