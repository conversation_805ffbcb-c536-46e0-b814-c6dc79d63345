import { Module } from '@nestjs/common';
import { GoldenConfigWrapperService } from './golden-config-wrapper.service';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';
import { ENVIRONMENT_VARS } from '../utils/constants';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';

@Module({
  providers: [
    GoldenConfigWrapperService,
    {
      provide: 'GOLDEN_CONFIG_API',
      inject: [ConfigService, REQUEST],
      useFactory: (
        configService: ConfigService,
        req: Request,
      ): AxiosInstance => {
        return axios.create({
          baseURL: configService.get(
            ENVIRONMENT_VARS.GOLDEN_CONFIG_SERVICE_BASE_URL,
          ),
          headers: {
            Accept: req.headers.accept,
            sourcesystem: req.headers.sourcesystem,
            sourceserver: req.headers.sourceserver,
            trackingid: req.headers.trackingid,
            timestamp: req.headers.timestamp,
          },
        });
      },
    },
    {
      provide: 'NEBULA_GOLDEN_CONFIG_API',
      inject: [ConfigService, REQUEST],
      useFactory: (
        configService: ConfigService,
        req: Request,
      ): AxiosInstance => {
        return axios.create({
          baseURL: configService.get(
            ENVIRONMENT_VARS.NEBULA_GOLDEN_CONFIG_SERVICE_BASE_URL,
          ),
        });
      },
    },
  ],

  exports: [GoldenConfigWrapperService],
})
export class GoldenConfigWrapperModule {}
