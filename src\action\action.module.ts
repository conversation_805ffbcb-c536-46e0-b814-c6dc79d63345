import { Global, Module } from '@nestjs/common';
import { ActionService } from './providers/action.service';
import { ActionRepository } from './repositories/action.repository';
import { ActionController } from './providers/action.controller';

@Global()
@Module({
  controllers:[ActionController],
  providers: [ActionService, ActionRepository],
  exports: [ActionService],
})
export class ActionModule {}
