const session = db.getMongo().startSession();
session.startTransaction();

const collectionLevel01 = 'cataloglevel01';
const collectionLevel02 = 'menvcataloglevel02';
const collectionLevel03 = 'menvcataloglevel03';
const collectionLevel04 = 'menvcataloglevel04';

const pid = 'P3285719';

function getLevel01Id() {
  const docLevel1 = db[collectionLevel01].findOne({ shortName: 'paas' });
  if (!docLevel1) {
    throw new Error('tools in cataloglevel01 document does not exists');
  }

  print('Verified tools in cataloglevel01 document');

  return docLevel1._id;
}

function createLevel2CatalogItem(id) {
  const objLevel2 = {
    level02Name: 'Container Orchestration',
    level01Id: id,
    description: 'Container for CaaS',
    icon: 'BuildOutlined',
    isDeleted: false,
    shortName: 'containerorchestration',
    enabled: true,
    createdBy: pid,
    createdAt: new Date(),
    updatedAt: new Date(),
    __v: 0,
    updatedBy: pid,
  };

  const docLevel2Exists = db[collectionLevel02].findOne({
    shortName: 'containerorchestration',
  });

  if (docLevel2Exists) {
    db[collectionLevel02].updateOne(
      { shortName: 'containerorchestration' },
      { $set: objLevel2 },
    );

    print('Updated containerorchestration tile');
  } else {
    const { sequenceNumber } = db[collectionLevel02]
      .find({})
      .sort({ sequenceNumber: -1 })
      .limit(1)
      .next();

    db[collectionLevel02].insertOne({
      ...objLevel2,
      sequenceNumber: sequenceNumber + 1,
    });

    print('Added containerorchestration tile');
  }
}

function getLevel02Id() {
  const docLevel2 = db[collectionLevel02].findOne({
    shortName: 'containerorchestration',
  });
  if (!docLevel2) {
    throw new Error(
      'containerorchestration in cataloglevel02 document does not exists',
    );
  }

  print('Verified containerorchestration in cataloglevel02 document');

  return docLevel2._id;
}

function createLevel3CatalogItem(id) {
  const objLevel3 = {
    level03Name: 'K8s',
    level02Id: id,
    description: 'K8s for CaaS',
    icon: 'BuildOutlined',
    isDeleted: false,
    shortName: 'k8s',
    enabled: true,
    createdBy: pid,
    createdAt: new Date(),
    updatedAt: new Date(),
    __v: 0,
    updatedBy: pid,
  };

  const docLevel3Exists = db[collectionLevel03].findOne({
    shortName: 'k8s',
  });

  if (docLevel3Exists) {
    db[collectionLevel03].updateOne({ shortName: 'k8s' }, { $set: objLevel3 });

    print('Updated k8s tile');
  } else {
    const { sequenceNumber } = db[collectionLevel03]
      .find({})
      .sort({ sequenceNumber: -1 })
      .limit(1)
      .next();

    db[collectionLevel03].insertOne({
      ...objLevel3,
      sequenceNumber: sequenceNumber + 1,
    });

    print('Added k8s tile');
  }
}

function getLevel03Id() {
  const docLevel3 = db[collectionLevel03].findOne({
    shortName: 'k8s',
  });

  if (!docLevel3) {
    throw new Error('k8s in cataloglevel03 document does not exists');
  }

  return docLevel3._id;
}

function createMapviewTile(id) {
  const objLevel4Mapview = {
    name: 'Corpnet CaaS',
    level03Id: id,
    enabled: true,
    description: 'Corpnet CaaS Structure',
    icon: 'BuildOutlined',
    approvalRequired: false,
    isDeleted: false,
    shortName: 'corpnetcaas',
    component: 'CapacityPlanningMapview',
    createdBy: pid,
    createdAt: new Date(),
    updatedAt: new Date(),
    __v: 0,
    updatedBy: pid,
  };

  const docLevel4MapviewExists = db[collectionLevel04].findOne({
    shortName: 'corpnetcaas',
  });

  if (docLevel4MapviewExists) {
    db[collectionLevel04].updateOne(
      { shortName: 'corpnetcaas' },
      { $set: objLevel4Mapview },
    );

    print('Updated corpnetcaas tile');
  } else {
    const { sequenceNumber } = db[collectionLevel04]
      .find({})
      .sort({ sequenceNumber: -1 })
      .limit(1)
      .next();

    db[collectionLevel04].insertOne({
      ...objLevel4Mapview,
      sequenceNumber: sequenceNumber + 1,
    });

    print('Added corpnetcaas tile');
  }
}

function addToolsTiles() {
  const level01Id = getLevel01Id();
  createLevel2CatalogItem(level01Id);

  const level02Id = getLevel02Id();
  createLevel3CatalogItem(level02Id);

  const level03Id = getLevel03Id();
  createMapviewTile(level03Id); //this is required
}

try {
  addToolsTiles();
} catch (e) {
  session.abortTransaction();
  print('Transaction aborted due to error', e);
} finally {
  session.endSession();
}
