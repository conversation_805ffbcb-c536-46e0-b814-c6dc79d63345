import { Test } from '@nestjs/testing';
import { SecretsMetadataService } from './secretsMetadata.service';
import { SecretsMetadataRepository } from './secretsMetadata.repository';

describe('SecretsMetadataService', function () {
  let service: SecretsMetadataService;
  let mockSecretsMetadataRepository = {
    getSecretsMetaDataBySecretId: jest.fn(),
    fetchSecretsMetadataExpiringIn2mins: jest.fn(),
    reactivateSecretsMetaData: jest.fn(),
    getSecretsMetadataListForRenewal: jest.fn(),
    getAllAutoRenewVaultTokens: jest.fn(),
    updateRenewedTokenMetaData: jest.fn(),
  };
  beforeEach(async function () {
    const module = await Test.createTestingModule({
      providers: [
        SecretsMetadataService,
        {
          provide: SecretsMetadataRepository,
          useValue: mockSecretsMetadataRepository,
        },
      ],
    }).compile();
    service = module.get(SecretsMetadataService);
  });

  it('should be defined', async function () {
    expect(service).toBeDefined();
  });

  describe('getSecretsMetaDataBySecretId', function () {
    let mockResult = [
      {
        _id: '684bfe9757310b4e1e68a0fe',
        secretId: 'NEB-VAULT-ROT-SECRET-1063',
        type: 'ROTATABLE-SECRET',
        description: 'test secrets',
        resourceId: 'NEB-VAULT-TOKEN-8382',
        secretTTLInHours: 30,
        rotationType: 'Auto',
        nextRotationDate: '2025-06-20T18:35:29.421Z',
        vaultNamespace: 'nebula-stamp',
        vaultPath: 'dev/mock',
        deviceUserNameSecretId: 'NEB-VAULT-NOR-SECRET-1062',
        devicePasswordKey: 'mockkey',
        policyId: 'NEB-VAULT-PASSWORD-POLICY-12234',
        status: 'SUCCESS',
        active: true,
        error: [],
        createdAt: '2025-06-13T10:33:59.151Z',
        updatedAt: '2025-06-13T10:33:59.151Z',
        __v: 0,
      },
      {
        _id: '684bfe9757310b4e1e68a0fc',
        secretId: 'NEB-VAULT-NOR-SECRET-1062',
        type: 'NORMAL-SECRET',
        description: 'test secrets',
        resourceId: 'NEB-VAULT-TOKEN-8382',
        vaultNamespace: 'nebula-stamp',
        vaultPath: 'dev/mock',
        devicePasswordKey: 'mockUserKey',
        policyId: 'NEB-VAULT-PASSWORD-POLICY-12234',
        status: 'SUCCESS',
        active: true,
        error: [],
        createdAt: '2025-06-13T10:33:59.123Z',
        updatedAt: '2025-06-13T10:33:59.123Z',
        __v: 0,
      },
    ];
    let mockids = ['NEB-VAULT-ROT-SECRET-1063', 'NEB-VAULT-NOR-SECRET-1062'];
    it('should return single secrets metadata if provided value of the id param is string', async function () {
      mockSecretsMetadataRepository.getSecretsMetaDataBySecretId.mockResolvedValue(
        mockResult[0],
      );
      const result = await service.getSecretsMetaDataBySecretId(mockids[0]);
      expect(result).toEqual(mockResult[0]);
    });
    it('should return array secrets metadata if provided value of the id param is array', async function () {
      const sortBySecretId = (a, b) => a.secretId.localeCompare(b.secretId);
      mockSecretsMetadataRepository.getSecretsMetaDataBySecretId.mockResolvedValue(
        mockResult,
      );
      const result = await service.getSecretsMetaDataBySecretId(mockids);
      expect(result.sort(sortBySecretId)).toEqual(
        mockResult.sort(sortBySecretId),
      );
    });
  });

  describe('fetchSecretsMetadataExpiringIn2mins', function () {
    it('should return record of secrets expiring in next 2mins', async function () {
      let mockResponse = {
        id: '684bfe9757310b4e1e68a0fe',
        secretId: 'NEB-VAULT-ROT-SECRET-1063',
        type: 'ROTATABLE-SECRET',
        description: 'test secrets',
        resourceId: 'NEB-VAULT-TOKEN-8382',
        secretTTLInHours: 30,
        rotationType: 'Auto',
        nextRotationDate: '2025-06-20T18:35:29.421Z',
        vaultNamespace: 'nebula-stamp',
        vaultPath: 'dev/mock',
        deviceUserNameSecretId: 'NEB-VAULT-NOR-SECRET-1062',
        devicePasswordKey: 'mockkey',
        policyId: 'NEB-VAULT-PASSWORD-POLICY-12234',
        status: 'SUCCESS',
        active: true,
        error: [],
        createdAt: '2025-06-13T10:33:59.151Z',
        updatedAt: '2025-06-13T10:33:59.151Z',
        __v: 0,
        expiryDate: '2025-06-16T03:13:28.326Z',
      };
      mockSecretsMetadataRepository.fetchSecretsMetadataExpiringIn2mins.mockResolvedValue(
        mockResponse,
      );
      const now = new Date();
      const twoMinutesLater = new Date(now.getTime() + 2 * 60 * 1000);
      const result = await service.fetchSecretsMetadataExpiringIn2mins(
        now,
        twoMinutesLater,
      );
      expect(result).toEqual(mockResponse);
    });
  });

  it('should call getSecretsMetadataListForRenewal with default divisor', async () => {
    const mockResult = [{ id: 'secret1' }];
    mockSecretsMetadataRepository.getSecretsMetadataListForRenewal.mockResolvedValue(
      mockResult,
    );
    const result = await service.getSecretsMetadataListForRenewal();

    expect(
      mockSecretsMetadataRepository.getSecretsMetadataListForRenewal,
    ).toHaveBeenCalledWith(2);
    expect(result).toEqual(mockResult);
  });

  it('should call reactivateSecretsMetaData with calculated date', async () => {
    const duration = 5; // hours
    const now = new Date();
    const expectedDate = new Date(now);
    expectedDate.setHours(expectedDate.getHours() - duration);

    jest.useFakeTimers().setSystemTime(now);
    const mockResult = ['reactivatedSecret'];
    mockSecretsMetadataRepository.reactivateSecretsMetaData.mockResolvedValue(
      mockResult,
    );

    const result = await service.reactivateSecrets(duration);

    expect(
      mockSecretsMetadataRepository.reactivateSecretsMetaData,
    ).toHaveBeenCalledWith(expectedDate);
    expect(result).toEqual(mockResult);
    jest.useRealTimers();
  });

  it('should call getAllAutoRenewVaultTokens', async () => {
    const mockTokens = [{ id: 'token1' }];
    mockSecretsMetadataRepository.getAllAutoRenewVaultTokens.mockResolvedValue(
      mockTokens,
    );

    const result = await service.getAllAutoRenewVaultTokens();

    expect(
      mockSecretsMetadataRepository.getAllAutoRenewVaultTokens,
    ).toHaveBeenCalled();
    expect(result).toEqual(mockTokens);
  });

  it('should call updateRenewedTokenMetaData with correct arguments', async () => {
    const secretId = 'secret123';
    const renewalDate = new Date('2025-06-20');
    const expiryDate = new Date('2025-12-20');
    const mockUpdateResult = { acknowledged: true };

    mockSecretsMetadataRepository.updateRenewedTokenMetaData.mockResolvedValue(
      mockUpdateResult,
    );

    const result = await service.updateRenewedTokenMetaData(
      secretId,
      renewalDate,
      expiryDate,
    );

    expect(
      mockSecretsMetadataRepository.updateRenewedTokenMetaData,
    ).toHaveBeenCalledWith(secretId, renewalDate, expiryDate);
    expect(result).toEqual(mockUpdateResult);
  });
});
