import { Modu<PERSON> } from '@nestjs/common';
import { LoggerModule } from '../loggers/logger.module';
import { VmwareAdminService } from './vmware.admin.service';
import { VmwareAdminController } from './vmware.admin.controller';
import { RequestContextModule } from 'nestjs-request-context';
import { ApprovalsModule } from '../approvals/approvals.module';
import { AuthModule } from '../auth/auth.module';
import { IntegrationNotificationModule } from '../naas/integration.notification.module';
import { StatusNotificationModule } from '../statusNotification/statusNotification.module';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';
import { VMWareErrorLoggers } from '../vmware-error-loggers/vmware-error-logger.module';

@Module({
  imports: [
    LoggerModule,
    RequestContextModule,
    IntegrationNotificationModule,
    ApprovalsModule,
    StatusNotificationModule,
    AuthModule,
    VMWareErrorLoggers,
  ],
  controllers: [VmwareAdminController],
  providers: [
    VmwareAdminService,
    {
      provide: 'VMWARE_ADMIN_SERVICE_API',
      inject: [ConfigService],
      useFactory: (configService: ConfigService): AxiosInstance => {
        return axios.create({
          baseURL: configService.get('VMWARE_ADMIN_SERVICE_BASE_URL'),
        });
      },
    },
  ],
  exports: [VmwareAdminService],
})
export class VmwareAdminModule {}
