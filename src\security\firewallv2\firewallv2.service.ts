import {
  BadRequestException,
  ForbiddenException,
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import * as uuid from 'uuid';
import {
  RequestType,
  RequestStatus,
  ServiceCatalogName,
  ServiceCatalogType,
  ApprovalStatus,
  IpType,
  EventPatterns,
  TUFIN_TICKET_STATUS,
  OrganizationName,
  RETRY_TICKET_STATUS,
  RETRY_TICKET_ACTIONS,
  API_RESPONSE_STATUS,
  NebulaConfigMap,
  JiraActions,
} from '../../types';
import {
  FirewallRulesDto,
  FirewallRule,
  createFirewallRequestDto,
  ObjectStorageDto,
  JiraDropdowns,
  createFileRequestDto,
  FirwallV1MigrateDto,
  ImpactedDevicesRequestDto,
  FirewallRules,
} from './dto/firewallv2.request.dto';
import * as ipRangeCheck from 'ip-range-check';
import * as IpAddress from 'ip-address';
export const ipv4 = IpAddress.Address4;
export const ipv6 = IpAddress.Address6;
export const ipRange = ipRangeCheck;
import { IPv4CidrRange, IPv6CidrRange } from 'ip-num/IPRange';
import { IPv4 } from 'ip-num/IPNumber';
import * as tmp from 'tmp';
import { AxiosInstance } from 'axios';
import {
  withResponseErrorHandler,
  validateBusinessRequestDate,
} from '../../utils/helpers';
import {
  createAllRulesExcel,
  createRulesExcel,
  createRulesExcelForIpv6,
  extractFirewallRules,
  removeEmptyRules,
} from './firewallv2-utils/file-upload';
import { StorageRepository } from '../../objectStorage/storage.repository';
import { ObjectStorageService } from '../../objectStorage/object.storage.service';
import { RequestContext } from 'nestjs-request-context';
import { AssetsService } from '../../assets/assets.service';
import { SubTypeApprovalsService } from '../../approvals/sub-type-approval/sub-type-approvals.service';
import { LoggerService } from '../../loggers/logger.service';
import { validateFirewallRules } from './firewallv2-utils/validations/validations';
import { RmqService } from '../../rmq/rmq.service';
import { FirewallRequestDetailsRepository } from '../repository/firewallRequestDetails-repository';
import {
  CreateRiskAnalysisResultRequestDto,
  CreateRiskAnalysisResultRequestDto as RiskAnalysisResultDto,
} from '../dto/firewall.risk-analysis-result.request.dto';
import { CreateRiskAnalysisResultResponseDto } from '../dto/firewall.risk-analysis-result.response.dto';
import {
  TufinDevicesDto,
  TufinTaskRequestDto,
  UpdateTufinDevicesDto,
} from '../dto/firewall.tufin.job.request.dto';
import { TufinTaskResponseDto } from '../dto/firewall.tufin.job.response.dto';
import { FirewallRequestCreateDto } from './dto/firewallRequest.request.dto';
import {
  FirewallTicketDetailsResponseDto,
  ImpactedDevicesResponseDto,
} from './dto/firewall.ticket-details.response.dto';
import { FirewallRequestEntity } from '../entity/firewallRequest.entity';
import { ServiceRequestEntity } from '../../naas/entities/serviceRequest.entity';
import { ActivityLoggerWrapperService } from '../../activity-logs-wrapper/activity-logger-wrapper.service';
import { ConfigService } from '@nestjs/config';
import {
  ENVIRONMENT_VARS,
  FirewallV1MigrationStatus,
  JiraStatus,
  TicketType,
} from '../../utils/constants';
import { CreateDesignerResultResponseDto } from '../dto/firewall.designer-result.response.dto';
import { DesignerResultsRequestDto } from '../dto/firewall.designer-result.request.dto';
import { TufinDeviceRepository } from '../repository/tufin.path-analysis.repository';
import { UpdatePollRequestDto } from './dto/updatePollRequest.request,dto';
import { IncomingTaskResultRequestDto } from '../dto/firewall.incoming.designer-result.request.dto';
import { FirewallDbOperationResult } from '../repository/firewall.db-operation.result';
import { TicketManagementWrapperService } from '../../ticket-management-service-wrapper/ticket-management-wrapper-service';
import { StatusNotificationService } from 'src/statusNotification/statusNotification.service';
import { IPType } from '../../load-balancer/f5/enum/constans';
import { ResubmitFirewallRequestDto } from './dto/resubmitFirewallRequest.request.dto';
import { ResubmitFirewallRequestRepository } from '../repository/resubmitFirewallRequest.repository';
import { ProcessStatus } from '../../activity-logs/types/process-status.enum';
import {
  GenericRetryTicketRequestDto,
  TicketStatusUpdateDto,
} from './dto/retryTicketDetails.dto';
import { JiraService } from '../../jira-management/jira-managment.service';
import { AwsIpamService } from '../../aws-ipam/aws.ipam.service';
import { AwsIpamDto } from '../../aws-ipam/dto/aws.ipam.dto';
import { TufinService } from '../../tufin/tufin.service';
import { JobStatusEnum } from '../schema/firewallRequest.schema';
import { SecureChangeRequestDto } from './dto/secure.change.dto';
import { NebulaConfigRepository } from '../../dbaas/nebulaConfig.repository';
import { JiraRequestDto } from './dto/jira.request.dto';
import { JiraFileUploadRequestDto } from './dto/jira.file.upload.request.dto';
import { CatalogStepsService } from '../../catalog-steps/catalog-steps.service';

@Injectable()
export class FirewallV2Service {
  constructor(
    private readonly firewallRequestRepository: FirewallRequestDetailsRepository,
    private readonly objectStorageService: ObjectStorageService,
    private readonly storageRepository: StorageRepository,
    private readonly assetsService: AssetsService,
    private readonly storageService: ObjectStorageService,
    private readonly subTypeApprovalsService: SubTypeApprovalsService,
    private readonly statusNotificationService: StatusNotificationService,
    @Inject('JIRA_SERVICE_API') private readonly jiraServiceApi: AxiosInstance,

    @Inject('TUFIN_SERVICE_API')
    private readonly tufinServiceApi: AxiosInstance,
    private readonly logger: LoggerService,
    private readonly rmqService: RmqService,
    private readonly activityLoggerService: ActivityLoggerWrapperService,
    private readonly configService: ConfigService,
    private readonly pathAnalysisRespository: TufinDeviceRepository,
    private readonly ticketManagementWrapperService: TicketManagementWrapperService,
    private readonly subTypeApprovals: SubTypeApprovalsService,
    private readonly resubmitFirewallRequestRepository: ResubmitFirewallRequestRepository,
    private readonly nebulaConfigRepository: NebulaConfigRepository,
    private readonly awsIpamService: AwsIpamService,
    private readonly jiraWrapperServiceApi: JiraService,
    private readonly tufinService: TufinService,
    private readonly catalogStepsService: CatalogStepsService,
  ) {}

  async firewallBulkImport(buffer: Buffer): Promise<FirewallRulesDto> {
    this.logger.log('Firewall bulk import');
    const parseFiledOutput = await extractFirewallRules(buffer);
    const firewallRules = await removeEmptyRules(parseFiledOutput);

    const protocolConfig =
      await this.nebulaConfigRepository.getNebulaConfigByType(
        NebulaConfigMap.FIREWALL_V2,
      );
    const validatedOutput = await validateFirewallRules(
      firewallRules,
      protocolConfig?.config,
      true,
    );
    const finalResponse = {
      firewallRules: validatedOutput,
    };
    this.logger.debug('bulk import successfull', finalResponse);
    return finalResponse;
  }

  async createFirewallRequest(
    createFirewallRequestDto: createFirewallRequestDto,
  ) {
    this.logger.log(
      `Create firewall request received: ${createFirewallRequestDto}`,
    );
    createFirewallRequestDto.businessRequestDate &&
      validateBusinessRequestDate(createFirewallRequestDto.businessRequestDate);

    let validIpv4Rules;
    let validIpv6Rules;
    let validFirewallRequest = true;
    let validipv4FirewallRequest = true;
    let validipv6FirewallRequest = true;
    const protocolConfig =
      await this.nebulaConfigRepository.getNebulaConfigByType(
        NebulaConfigMap.FIREWALL_V2,
      );

    if (createFirewallRequestDto.firewallRules.ipv4) {
      this.logger.log('validating firewall rule ipv4');
      validIpv4Rules = validateFirewallRules(
        createFirewallRequestDto.firewallRules.ipv4.firewallRules,
        protocolConfig?.config,
      );
      validIpv4Rules.forEach((rule) => {
        if (rule.valid == false) {
          validipv4FirewallRequest = false;
        }
      });

      //optimization of ipv4 firewall rules
      try {
        this.logger.log('calling optimizeFirewallRules for ipv4');
        const optimizedIpv4Rules = await this.optimizeFirewallRules(
          createFirewallRequestDto.firewallRules.ipv4.firewallRules,
        );
        createFirewallRequestDto.firewallRules.ipv4['optimizedIpv4'] =
          optimizedIpv4Rules;
      } catch (error) {
        this.logger.log(`Error while optimizing ipv4 rules: ${error}`);
      }
    }

    if (createFirewallRequestDto.firewallRules.ipv6) {
      this.logger.log('validating firewall rule ipv6');
      validIpv6Rules = validateFirewallRules(
        createFirewallRequestDto.firewallRules.ipv6.firewallRules,
        protocolConfig?.config,
      );
      validIpv6Rules.forEach((rule) => {
        if (rule.valid == false) {
          validipv6FirewallRequest = false;
        }
      });

      //optimization of ipv6 firewall rules
      try {
        const optimizedIpv6Rules = await this.optimizeFirewallRules(
          createFirewallRequestDto.firewallRules.ipv6.firewallRules,
        );
        createFirewallRequestDto.firewallRules.ipv6['optimizedIpv6'] =
          optimizedIpv6Rules;
      } catch (error) {
        this.logger.log(`Error while optimizing ipv6 rules ${error}`);
      }
    }

    if (validipv4FirewallRequest != true || validipv6FirewallRequest != true) {
      validFirewallRequest = false;
    }

    this.logger.log('valid firewallRequest or not ', validFirewallRequest);
    if (validFirewallRequest == true) {
      createFirewallRequestDto['summary'] =
        `Firewall Request - ${createFirewallRequestDto.projectName}`;
      createFirewallRequestDto['userName'] =
        createFirewallRequestDto.createdBy ?? this.getUserContext().userId;
      createFirewallRequestDto['description'] =
        `New Firewall Request - ${createFirewallRequestDto.projectName} - Opened by - ${createFirewallRequestDto.projectCreator}`;
      createFirewallRequestDto['netopsaskTicket'] =
        createFirewallRequestDto.netopsaskTicket;

      createFirewallRequestDto['nebulaProject'] =
        createFirewallRequestDto.nebulaProject;
      const serviceRequest = {
        metadata: {
          serviceCatalog: {
            catalogName: ServiceCatalogName.FIREWALL_V2,
            catalogType: ServiceCatalogType.NAAS,
          },
        },
        requestType: RequestType.FIREWALL_V2,
        status: RequestStatus.CREATED,
        approvalStatus: ApprovalStatus.NA,
        payload: createFirewallRequestDto,
        createdBy: createFirewallRequestDto.createdBy,
        requesterEmail: createFirewallRequestDto.requesterEmail,
      };

      const dbResponse = await this.assetsService.create(serviceRequest);
      this.logger.debug('Response DB ', dbResponse);
      this.logger.debug('final payload', createFirewallRequestDto);
      this.logger.debug(`Pushing service request ${dbResponse.id} to queue`);

      //create all rules excel

      const responseFromAllRulesExcelCreation =
        this.createAndUploadAllRulesExcel(
          createFirewallRequestDto,
          serviceRequest,
          dbResponse,
        );
      this.logger.debug(
        'Response after creation and uploading of all rules excel',
        responseFromAllRulesExcelCreation,
      );
      //create all rules excel completed and uploaded

      if (createFirewallRequestDto.firewallRules.ipv6) {
        const createIpv6RequestDto = {};
        createIpv6RequestDto['date'] = createFirewallRequestDto.date;
        createIpv6RequestDto['projectName'] =
          createFirewallRequestDto.projectName;
        createIpv6RequestDto['projectCreator'] =
          createFirewallRequestDto.projectCreator;
        createIpv6RequestDto['appId'] = createFirewallRequestDto.appId;
        createIpv6RequestDto['date'] = createFirewallRequestDto.date;
        createIpv6RequestDto['firewallRules'] =
          createFirewallRequestDto.firewallRules.ipv6.firewallRules;
        const fileResponse =
          await createRulesExcelForIpv6(createIpv6RequestDto);
        this.logger.debug('Rules excel for Ipv6 ', fileResponse);
        const uploadedData = await this.uploadV2FileToStorage(fileResponse);
        createFirewallRequestDto.firewallRules.ipv6['storage'] = {
          bucketName: uploadedData.bucketName,
          fileName: uploadedData.fileName,
          filePath: uploadedData.filePath,
        };

        this.logger.debug('dbResponse', dbResponse);
        const storageRequest = {
          requestType: serviceRequest.requestType,
          requestId: dbResponse.serviceRequestId,
          fileType: 'xlsx',
          createdBy: createFirewallRequestDto.createdBy,
          ...uploadedData,
        };
        const storageResponse =
          await this.storageRepository.create(storageRequest);
        this.logger.debug('storageResponse ', storageResponse);
      }

      await this.rmqService.pushMessage(
        dbResponse.requestType,
        {
          id: dbResponse.id,
          payload: createFirewallRequestDto,
          serviceRequestId: dbResponse.serviceRequestId,
        },
        EventPatterns.FIREWALL_V2_CREATED,
      );
      return {
        id: dbResponse.id,
        serviceRequestId: dbResponse?.serviceRequestId,
        message: `Request submitted for processing`,
      };
    } else {
      throw new BadRequestException(
        'Request Could not be Submitted due to Invalid Firewall Rules',
      );
    }
  }

  async optimizeFirewallRules(rules: FirewallRule[]) {
    this.logger.log('inside optimizeFirewallRules');
    const bySource = new Map();
    const byDestination = new Map();
    const optimization_split_length = this.configService.get(
      ENVIRONMENT_VARS.OPTIMIZATION_SPLIT_LENGTH,
    );
    // Group by source or destination
    for (const rule of rules) {
      const srcKey = `${rule.source.ipAddress}|${rule.protocol}`;
      const dstKey = `${rule.destination.ipAddress}|${rule.protocol}`;
      // Group by source
      if (!bySource.has(srcKey)) {
        bySource.set(srcKey, {
          ruleTemplate: rule,
          destinations: new Map(), // key: ip, value: port
        });
      }
      bySource
        .get(srcKey)
        .destinations.set(rule.destination.ipAddress, rule.destination.port);

      // Group by destination
      if (!byDestination.has(dstKey)) {
        byDestination.set(dstKey, {
          ruleTemplate: rule,
          sources: new Map(), // key: ip, value: port
        });
      }
      byDestination
        .get(dstKey)
        .sources.set(rule.source.ipAddress, rule.source.port);
    }
    const sourceOptimized = [];
    for (const [key, group] of bySource.entries()) {
      const [srcIP, protocol] = key.split('|');
      const baseLength = srcIP.length + protocol.length + 50;
      const destIpPortPairs = Array.from(group.destinations.entries()); // [ [ip, port], ... ]
      const ipList = destIpPortPairs.map(([ipAddress]) => ipAddress);

      if (baseLength + ipList.join(',').length > optimization_split_length) {
        const chunks = await this.splitIpChunks(
          new Set(ipList),
          baseLength,
          optimization_split_length,
        );
        for (const chunk of chunks) {
          const rule = JSON.parse(JSON.stringify(group.ruleTemplate));
          const ports = chunk.flatMap((ipAddress) =>
            group.destinations.get(ipAddress).split(','),
          );
          rule.destination.ipAddress = chunk.join(',');
          rule.destination.port = [...new Set(ports)].join(',');
          rule.optimizedBy = 'source';
          sourceOptimized.push(rule);
        }
      } else {
        this.logger.log('else block: optimization by source');
        const rule = JSON.parse(JSON.stringify(group.ruleTemplate));
        rule.destination.ipAddress = ipList.join(',');
        const allPorts = destIpPortPairs.flatMap(([_, port]) =>
          protocol.split(','),
        );
        rule.destination.port = [...new Set(allPorts)].join(',');
        rule.optimizedBy = 'source';
        sourceOptimized.push(rule);
      }
    }
    const destinationOptimized = [];
    for (const [key, group] of byDestination.entries()) {
      const [dstIP, protocol] = key.split('|');
      const baseLength = dstIP.length + protocol.length + 50;
      const srcIpPortPairs = Array.from(group.sources.entries());
      const ipList = srcIpPortPairs.map(([ipAddress]) => ipAddress);

      if (baseLength + ipList.join(',').length > optimization_split_length) {
        const chunks = await this.splitIpChunks(
          new Set(ipList),
          baseLength,
          optimization_split_length,
        );
        for (const chunk of chunks) {
          const rule = JSON.parse(JSON.stringify(group.ruleTemplate));
          const ports = chunk.flatMap((ipAddress) =>
            group.sources.get(ipAddress).split(','),
          );
          rule.source.ipAddress = chunk.join(',');
          rule.source.port = [...new Set(ports)].join(',');
          rule.optimizedBy = 'destination';
          destinationOptimized.push(rule);
        }
      } else {
        this.logger.log('else block: optimization by destination');
        const rule = JSON.parse(JSON.stringify(group.ruleTemplate));
        rule.source.ipAddress = ipList.join(',');
        const allPorts = srcIpPortPairs.flatMap(([_, port]) =>
          protocol.split(','),
        );
        rule.source.port = [...new Set(allPorts)].join(',');
        rule.optimizedBy = 'destination';
        destinationOptimized.push(rule);
      }
    }
    return sourceOptimized.length <= destinationOptimized.length
      ? {
          optimizedBy: 'source',
          firewallRules: sourceOptimized.map((elementAt, key) => {
            return { ...elementAt, ruleId: key + 1 };
          }),
        }
      : {
          optimizedBy: 'destination',
          firewallRules: destinationOptimized.map((elementAt, key) => {
            return { ...elementAt, ruleId: key + 1 };
          }),
        };
  }

  async splitIpChunks(ipSet, baseLength, maxLength) {
    this.logger.log('splitIpChunks Called');
    const ips = Array.from(ipSet);
    const chunks = [];
    let currentChunk = [];
    let currentLength = baseLength;

    for (const ip of ips) {
      const estimatedLength = ip.toString().length + 1; // comma or just the IP
      if (currentLength + estimatedLength > maxLength) {
        chunks.push(currentChunk);
        currentChunk = [];
        currentLength = baseLength;
      }
      currentChunk.push(ip);
      currentLength += estimatedLength;
    }

    if (currentChunk.length > 0) {
      chunks.push(currentChunk);
    }

    return chunks;
  }

  async createAndUploadAllRulesExcel(
    createFirewallRequestDto,
    serviceRequest,
    dbResponse,
  ) {
    const mergedFirewallRulesDto = {
      ...createFirewallRequestDto,
      firewallRules: [
        ...createFirewallRequestDto.firewallRules?.ipv4?.firewallRules,
        ...createFirewallRequestDto.firewallRules?.ipv6?.firewallRules,
      ],
    };

    const allRulesFileResponse = await createAllRulesExcel(
      mergedFirewallRulesDto,
    );
    const uploadedData = await this.uploadFileToStorage(allRulesFileResponse);

    this.logger.debug('dbResponse', dbResponse);
    const storageRequest = {
      requestType: serviceRequest.requestType,
      requestId: dbResponse.serviceRequestId,
      fileType: 'xlsx',
      createdBy: createFirewallRequestDto.createdBy,
      ...uploadedData,
    };
    const storageResponse = await this.storageRepository.create(storageRequest);
    this.logger.debug('storageResponse for all rules files', storageResponse);
    return storageResponse;
  }

  private async uploadFileToStorage(file: Buffer) {
    this.logger.log(`new-file ${file.buffer}`);
    const uniqueTime = new Date()
      .toISOString()
      .replaceAll('-', '')
      .replaceAll(':', '')
      .replaceAll('.', '');
    const payload: ObjectStorageDto = {
      bucketName: process.env.OBJECTSTORAGE_BUCKET,
      fileName: `firewallRules${uniqueTime}.xlsx`,
      filePath: `${process.env.OBJECTSTORAGE_FIREWALL_ALL_RULES_PATH}/${uniqueTime}`,
    };
    this.logger.debug(`filePath: ${payload.filePath}`);
    const response = await this.objectStorageService.uploadFirewall(
      file,
      payload,
    );
    if (response.status == API_RESPONSE_STATUS.SUCCESS) {
      this.logger.debug('File uploaded into object storage');
      return { ...payload };
    } else {
      this.logger.debug(
        `file upload failed,errorCode: ${response.errorCode},errorMessage: ${response.errorMessage}`,
      );
      throw new InternalServerErrorException(response.errorMessage);
    }
  }

  private async uploadV2FileToStorage(file: Buffer) {
    this.logger.log(`new-file ${file.buffer}`);
    const uniqueTime = new Date()
      .toISOString()
      .replaceAll('-', '')
      .replaceAll(':', '')
      .replaceAll('.', '');
    const payload: ObjectStorageDto = {
      bucketName: process.env.OBJECTSTORAGE_BUCKET,
      fileName: `firewallRules${uniqueTime}.xlsx`,
      filePath: `${process.env.OBJECTSTORAGE_FIREWALL_PATH}/${uniqueTime}`,
    };
    this.logger.debug(`filePath: ${payload.filePath}`);
    const response = await this.objectStorageService.uploadFirewall(
      file,
      payload,
    );
    if (response.status == API_RESPONSE_STATUS.SUCCESS) {
      this.logger.debug('File uploaded into object storage');
      return { ...payload };
    } else {
      this.logger.debug(
        `file upload failed,errorCode: ${response.errorCode},errorMessage: ${response.errorMessage}`,
      );
      throw new InternalServerErrorException(response.errorMessage);
    }
  }

  async getFirewallTemplate(id?: string): Promise<string> {
    const file = `${__dirname}/../../../template/firewallTemplate.xlsx`;
    return file;
  }

  async getJiraRegionAndGroups(): Promise<JiraDropdowns> {
    this.logger.log('calling jira service to get jira Region and Group');
    return await this.jiraWrapperServiceApi.getJiraRegionAndGroups();
  }

  async fetchIssue(id: string): Promise<any> {
    this.logger.log(`Calling jira service fetch issue: ${id}`);
    return await this.jiraWrapperServiceApi.fetchIssue(id);
  }

  getUserContext(): any {
    const req = RequestContext.currentContext.req;
    const requestUser = req.user;
    return requestUser;
  }

  async getRiskResultsByParams(
    tufinTicketId?: string,
    serviceRequestId?: string,
  ): Promise<RiskAnalysisResultDto[]> {
    this.logger.log(
      `Getting risk results by params for ${tufinTicketId} ${serviceRequestId}`,
    );
    return await this.firewallRequestRepository.getRiskResults({
      tufinTicketId,
      requestId: serviceRequestId,
    });
  }

  async updatePendingDesignerResultStatus(requestId: string) {
    this.logger.debug(
      'Fetch ticket details to get the status of IPV4 requests for serviceRequestId',
      requestId,
    );
    const ticketDetails =
      await this.firewallRequestRepository.getFirewallTicketDetails(requestId);

    this.logger.debug(
      'Checking risk analysis is received for all the tufin tickets for serviceRequestId',
      requestId,
    );

    this.logger.log(
      `Ticket details for service request id ${requestId}`,
      ticketDetails,
    );
    // filtering only ipv4 ticket for which organization name is not red_cbo since already we are marking pending approval for red_cbo request
    //added aws as part of apsre 10344
    const ipv4TicketsDetails = ticketDetails.filter(
      (ticket) =>
        ticket.ipType === IpType.IPV4 &&
        ![
          OrganizationName.NA_FAILED,
          OrganizationName.NA_NOIMPACT,
          OrganizationName.RED_CBO,
          OrganizationName.AWS,
        ].includes(ticket.organizationName as OrganizationName),
    );

    this.logger.log('IPv4 ticket details', JSON.stringify(ipv4TicketsDetails));
    const ipv4TicketsDetailsOnlyRedCbo = ticketDetails.filter(
      (ticket) =>
        ticket.ipType === IpType.IPV4 &&
        ticket.organizationName === OrganizationName.RED_CBO,
    );

    this.logger.log(
      'IPv4 ticket details of RedCbo',
      JSON.stringify(ipv4TicketsDetailsOnlyRedCbo),
    );

    const ipv6TicketDetails = ticketDetails.filter(
      (ticket) =>
        ticket.ipType === IpType.IPV6 &&
        ticket?.status === RequestStatus.PROCESSING,
    );

    this.logger.log('IPv6 ticket details', JSON.stringify(ipv6TicketDetails));

    const autoApprovedRequests = ticketDetails.filter(
      (ticket) => ticket.approvalStatus === ApprovalStatus.AUTO_APPROVED,
    );

    this.logger.log(
      'auto approved ticket details',
      JSON.stringify(autoApprovedRequests),
    );

    //we will mark riskAnalsysisUpdated as true once we receive risk analysis for the subrequest, so checking riskANalysisUpdated instead of statsu of subrequest
    const allRiskAnalysisRecieved = ipv4TicketsDetails.every(
      (ticket) =>
        ticket.ticketDetails?.tufin?.riskAnalysisUpdated === true ||
        ticket?.status === RequestStatus.FAILED ||
        ticket?.status === RequestStatus.PENDING_DESIGNER_RESULTS,
    );

    this.logger.log(
      'All risk analysis result recieved',
      allRiskAnalysisRecieved,
    );
    if (allRiskAnalysisRecieved) {
      this.logger.debug(
        'Risk analysis result is received for all the tufin tickets. Updating the status of service request to PENDING DESIGNER RESULTS for serviceRequestId',
        requestId,
      );

      //fetching service request using payload nebularequestid
      const serviceRequest: ServiceRequestEntity =
        await this.assetsService.getByServiceRequestId(requestId);

      const impactedOrg = [
        ...new Set(
          ipv4TicketsDetails
            .filter(
              (ticket) =>
                ticket.organizationName &&
                ticket?.status === RequestStatus.PENDING_DESIGNER_RESULTS,
            )
            .map((ticket) => ticket.organizationName),
        ),
      ];
      this.logger.debug(
        'Impacted Organization for this serviceRequestId',
        requestId,
      );

      //TODO: Check for the below status for approval status.
      //Removed approval status as pending since service request status is pending desinger result

      if (impactedOrg.length > 0) {
        this.logger.log(
          'Updating status to pending designer result',
          requestId,
        );
        await this.assetsService.update(requestId, {
          status: RequestStatus.PENDING_DESIGNER_RESULTS,
        });
        this.logger.log(
          'Pending designer result updated successfully',
          requestId,
        );
      } else if (autoApprovedRequests.length) {
        this.logger.log(
          'Updating status to processing for auto approved subrequests',
          requestId,
        );
        await this.assetsService.update(requestId, {
          status: RequestStatus.PROCESSING,
        });
      } else {
        this.logger.log(
          'Updating service request status from updatePendingDesignerResultStatus ',
          requestId,
        );
        await this.updateServiceRequestStatus(requestId);
      }

      //calling update pending approval method
      //just to make sure request doesn't get stucked in pending designer analysis
      // if we have red cbo request and other organization subrequest are getting failed.
      //if subrequest are failed it wont proceed further i.e. it wont trigger updatepeding method
      const allFailedIpv4 = ipv4TicketsDetails.every(
        (ticket) => ticket.status === RequestStatus.FAILED,
      );
      this.logger.log('All sub request getting failed', allFailedIpv4);
      //Rmoved approval process for red cbo so commented below code
      // if (ipv4TicketsDetailsOnlyRedCbo.length && allFailedIpv4) {
      //   await this.updatePendingApprovalStatus(serviceRequest.serviceRequestId);
      // }
    } else {
      this.logger.debug(
        'Risk Analysis is pending for some of the tufin tickets for serviceRequestId',
        requestId,
      );
    }
  }

  async updatePendingApprovalStatus(requestId: string) {
    this.logger.log(
      'Fetch ticket details to get the status of IPV4 requests for serviceRequestId',
      requestId,
    );
    const ticketDetails =
      await this.firewallRequestRepository.getFirewallTicketDetails(requestId);

    this.logger.log(
      `ticket details for service request id ${requestId}`,
      JSON.stringify(ticketDetails),
    );
    this.logger.debug(
      'Checking designer result is received for all the tufin tickets for serviceRequestId',
      requestId,
    );

    //excluded red cbo because state is already PENDING APPROVAL, NA tickets
    //added aws as part of apsre 10344
    const ipv4TicketsDetailswithOutRedCbo = ticketDetails.filter(
      (ticket) =>
        ticket.ipType === IpType.IPV4 &&
        ![
          OrganizationName.NA_FAILED,
          OrganizationName.NA_NOIMPACT,
          OrganizationName.RED_CBO,
          OrganizationName.AWS,
        ].includes(ticket.organizationName as OrganizationName),
    );
    this.logger.log(
      'Ipv4 ticket detials without RedCbo',
      JSON.stringify(ipv4TicketsDetailswithOutRedCbo),
    );
    const ipv6TicketDetails = ticketDetails.filter(
      (ticket) =>
        ticket.ipType === IpType.IPV6 &&
        ticket?.status === RequestStatus.PROCESSING,
    );

    this.logger.log('Ipv6 ticket details', JSON.stringify(ipv6TicketDetails));

    const autoApprovedRequests = ticketDetails.filter(
      (ticket) => ticket.approvalStatus === ApprovalStatus.AUTO_APPROVED,
    );

    this.logger.log(
      'auto approved ticket details',
      JSON.stringify(autoApprovedRequests),
    );

    const allDesignerResultsRecieved = ipv4TicketsDetailswithOutRedCbo.every(
      (ticket) =>
        ticket?.ticketDetails?.tufin?.designerResultsUpdated === true ||
        ticket?.status === RequestStatus.FAILED ||
        ticket?.status === RequestStatus.PENDING_APPROVAL,
    );

    this.logger.log('All designer result received', allDesignerResultsRecieved);
    if (allDesignerResultsRecieved) {
      const ipv4TicketsWithRedCbo = ticketDetails.filter(
        (ticket) => ticket.ipType === IpType.IPV4,
      );
      this.logger.debug(
        'Designer result is received for all the tufin tickets and update the status of service request to Pending Approval for serviceRequestId',
        requestId,
      );

      //fetching service request using payload nebularequestid
      this.logger.log('Fetching service request', requestId);
      const serviceRequest: ServiceRequestEntity =
        await this.assetsService.getByServiceRequestId(requestId);

      this.logger.log(`fetched service request ${serviceRequest}`);

      const impactedOrg = [
        ...new Set(
          ipv4TicketsWithRedCbo
            .filter(
              (ticket) =>
                ticket.organizationName &&
                ticket.status === RequestStatus.PENDING_APPROVAL,
            )
            .map((ticket) => ticket.organizationName),
        ),
      ];
      this.logger.debug(
        'Impacted Organization for this serviceRequestId',
        requestId,
      );
      if (impactedOrg.length > 0) {
        this.logger.log(
          'Updating request status to pending approval``',
          requestId,
        );
        await this.assetsService.update(requestId, {
          status: RequestStatus.PENDING_APPROVAL,
          approvalStatus: ApprovalStatus.PENDING,
          applicableSubTypes: impactedOrg,
        });
      } else if (autoApprovedRequests.length) {
        this.logger.log(
          'Updating request status to processing for auto approved subrequest',
          requestId,
        );
        await this.assetsService.update(requestId, {
          status: RequestStatus.PROCESSING,
        });
      } else {
        this.logger.log(
          'Updating service request status to failed from updatePendingApprovalStatus ',
          requestId,
        );
        await this.updateServiceRequestStatus(requestId);
      }
    } else {
      this.logger.debug(
        'Designer result is pending for some of the tufin tickets for serviceRequestId',
        requestId,
      );
    }
  }

  async createRiskAnalysisResults(
    createRiskAnalysisResults: CreateRiskAnalysisResultRequestDto,
  ): Promise<CreateRiskAnalysisResultResponseDto> {
    this.logger.log(
      `creating risk analysis ${JSON.stringify(createRiskAnalysisResults)}`,
    );
    const subRequest =
      await this.firewallRequestRepository.findByServiceRequestIdAndTufinId(
        createRiskAnalysisResults.requestId,
        createRiskAnalysisResults.tufinTicketId,
      );
    this.logger.log(`Sub request: ${JSON.stringify(subRequest)}`);
    let result;
    //commented if condition to handle rerun of tufin task
    // if (subRequest.status != RequestStatus.FAILED) {
    result = await this.firewallRequestRepository.updateRiskAnalysis(
      createRiskAnalysisResults.requestId,
      createRiskAnalysisResults.tufinTicketId,
      createRiskAnalysisResults.riskAnalysis,
    );
    // } else {
    //   this.logger.log(
    //     'Skipped update risk analysis because tufin is closing manually for: ',
    //     createRiskAnalysisResults,
    //   );
    // }

    await this.updatePendingDesignerResultStatus(
      createRiskAnalysisResults.requestId,
    );

    if (!result.success) {
      this.logger.error(
        result,
        `Error while creting risk analysis for ${createRiskAnalysisResults}`,
      );
      throw new HttpException(result.message, HttpStatus.NOT_FOUND);
    }
    return {
      status: 'success',
      message: 'Risk analysis result updated successfully',
    };
  }

  async createDesignerResults(
    designerResultsRequestDto: DesignerResultsRequestDto,
    enableApproval: boolean,
  ): Promise<CreateDesignerResultResponseDto> {
    this.logger.log(`creating designer analysis ${designerResultsRequestDto}`);
    const subRequest =
      await this.firewallRequestRepository.findByServiceRequestIdAndTufinId(
        designerResultsRequestDto.requestId,
        designerResultsRequestDto.tufinTicketId,
      );
    this.logger.log(`Sub request: ${subRequest}`);
    let result;
    //commented if condition to handle rerun of tufin task
    // if (subRequest.status != RequestStatus.FAILED) {
    result = await this.firewallRequestRepository.updateDesignerResult(
      designerResultsRequestDto.requestId,
      designerResultsRequestDto.tufinTicketId,
      designerResultsRequestDto.designerResults,
      enableApproval,
    );
    // } else {
    //   this.logger.log(
    //     'Skipped update designer result because tufin is closing manually for: ',
    //     designerResultsRequestDto,
    //   );
    // }

    //TODO: Need to verify this before merging PR.
    await this.updatePendingApprovalStatus(designerResultsRequestDto.requestId);

    if (!result.success) {
      this.logger.error(
        result,
        `Error while adding designer results for ${designerResultsRequestDto.requestId}`,
      );
      throw new HttpException(result.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    return {
      status: 'success',
      message: 'Designer result updated successfully',
    };
  }

  async updateWorkflowStepsResult(
    serviceRequestId: string,
    tufinTicketId: string,
    taskResult: IncomingTaskResultRequestDto,
  ): Promise<boolean> {
    try {
      this.logger.log(
        `Req received to update workflowstep result for serviceRequestId :${serviceRequestId}, tufinTicketId: ${tufinTicketId} ${taskResult}`,
      );
      const dbResponse: FirewallDbOperationResult<FirewallRequestEntity> =
        await this.firewallRequestRepository.updateWorkflowStepsResult(
          serviceRequestId,
          tufinTicketId,
          taskResult,
        );
      this.logger.log('DB response: ', dbResponse);

      return dbResponse.success;
    } catch (error) {
      this.logger.error(
        error,
        `Failed to update workflow step result for serviceRequestId :${serviceRequestId}, tufinTicketId: ${tufinTicketId}`,
      );
      return false;
    }
  }

  async updateTufinTaskStatus(
    tufinTaskRequestDto: TufinTaskRequestDto,
  ): Promise<TufinTaskResponseDto> {
    try {
      //Get tufin ticket status from tufin service.
      this.logger.log(
        `Get tufin ticket status from tufin service. ${tufinTaskRequestDto}`,
      );
      const tufinTicketStatusResponse = await withResponseErrorHandler(
        this.tufinServiceApi.get(
          `firewall/ticket/${tufinTaskRequestDto.tufinTicketId}`,
        ),
      );

      this.logger.log(
        `Tufin api response for ticket details: ${JSON.stringify(tufinTicketStatusResponse)}`,
      );

      if (!tufinTicketStatusResponse || !tufinTicketStatusResponse.status) {
        this.logger.error(
          `Invalid tufin ticket response. Unable to update tufin ticket status for ${tufinTaskRequestDto.tufinTicketId}`,
        );
      }

      //Update the tufin ticket status and also the status of the firewall request based on the status of the
      // tufin ticket. We identify the status for firewall request based on the
      // logic in tufinTicketStatusToNebulaServiceRequestStatus.
      const result = await this.firewallRequestRepository.updateTufinStatus(
        tufinTaskRequestDto.requestId,
        tufinTaskRequestDto.tufinTicketId,
        tufinTaskRequestDto,
        tufinTicketStatusResponse.status,
        this.tufinTicketStatusToNebulaServiceRequestStatus(
          tufinTicketStatusResponse.status,
        ),
      );
      this.logger.log(`Update tufin status result: ${result}`);

      if (!result.success) {
        throw new HttpException(
          result.message,
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }

      this.logger.log(
        'Updating service request status from updateTufinTaskStatus ',
        tufinTaskRequestDto.requestId,
      );
      await this.updateServiceRequestStatus(tufinTaskRequestDto.requestId);

      return {
        status: 'success',
        message: 'Tufin task and service request updated successfully',
      };
    } catch (error) {
      this.logger.error(
        error.stack,
        `Exception while fetching tufin ticket status for ${tufinTaskRequestDto.tufinTicketId}.`,
      );
      throw new Error(
        `Unexpected error while fetching tufin ticket status. ${error?.message}. Please check logs for more details`,
      );
    }
  }

  /**
   * We derive Nebula equivalent status based on the status of tufin ticket.
   */
  private tufinTicketStatusToNebulaServiceRequestStatus(
    tufinTicketStatus: string,
  ) {
    switch (tufinTicketStatus) {
      case TUFIN_TICKET_STATUS.IN_PROGRESS:
        return RequestStatus.PROCESSING;

      case TUFIN_TICKET_STATUS.TICKET_REJECTED:
        return RequestStatus.REJECTED;

      //We need to revisit this status
      case TUFIN_TICKET_STATUS.TICKET_CLOSED:
        return RequestStatus.SUCCESS;

      case TUFIN_TICKET_STATUS.TICKET_CANCELLED:
        return RequestStatus.CANCELLED;

      case TUFIN_TICKET_STATUS.TICKET_RESOLVED:
        return RequestStatus.SUCCESS;

      default:
        return RequestStatus.FAILED;
    }
  }

  /**
   * Derives a new status for service request based on statuses of tufin tickets.

   *	The function checks the provided list of status values and applies the following logic:
   *
   *	1.	Null Handling:
   *		- If any of the status values is null, the new status will be null i.e. no need to change service request status.
   *	    - Based on the implemented logic, we can get null if we have not yet status for a firewall request.
   *
   *	2.	Non-terminal Status Handling:
   *		- If any status value is not in the predefined terminal statuses, the new status will be null i.e. no need to change service request status..
   *        - This means that there is at-least one ticket that has not yet reached terminal status condition.
   *
   *	3.	Terminal Status Handling:
   *		- Terminal statuses are: REJECTED, PARTIAL_SUCCESS, CANCELLED, SUCCESS, FAILED.
   *		- If all statuses in the list are the same and among the terminal statuses, the new status will be that terminal status.
   *		- If there is a mix of different terminal statuses, the new status will be 'FAILED'.
   *
   *	@param {Array<RequestStatus | null>} statusList - An array of status values which can include both terminal statuses, non-terminal statuses, and null.
   *	@returns {RequestStatus | null} - The derived new status based on the conditions described above.
   *
   *	Examples:
   *
   *			1.	If the status list is [RequestStatus.CANCELLED, RequestStatus.CANCELLED], the function will return CANCELLED.
   *			2.	If the status list contains any non-terminal statuses (e.g., [RequestStatus.PENDING_APPROVAL]), the function will return 'NO_CHANGE'.
   *			3.	If the status list contains null, the function will return 'NO_CHANGE'.
   *			4.	If there is a mix of terminal statuses (e.g., [RequestStatus.SUCCESS, RequestStatus.FAILED]), the function will return 'FAILED'.
   */
  private deriveNewServiceRequestStatus(
    statusList: string[],
  ): RequestStatus | null {
    const TERMINAL_STATUSES = [
      RequestStatus.REJECTED,
      RequestStatus.PARTIAL_SUCCESS,
      RequestStatus.CANCELLED,
      RequestStatus.SUCCESS,
      RequestStatus.FAILED,
    ];

    this.logger.log(`Tufin tickets statuses : ${statusList}`);

    // If there is at least one null status, return null
    if (statusList.includes(null)) {
      this.logger.log(
        `There is at-least one tufin ticket for which we have not yet set any status.`,
      );
      return null;
    }

    // Check if there are any non-terminal statuses
    const hasNonTerminalStatus = statusList.some(
      (status) => !TERMINAL_STATUSES.includes(status as RequestStatus),
    );

    if (hasNonTerminalStatus) {
      this.logger.log(
        `There is at-least one ticket which is still in progress and has not yet reached terminal state.`,
      );
      return null;
    }

    // If all filtered statuses are the same and are terminal, return that status
    const uniqueStatuses = [...new Set(statusList)]; // Get unique statuses
    if (uniqueStatuses.length === 1) {
      this.logger.log(
        `All the tufin tickets has same terminal status i.e. ${uniqueStatuses[0]}.`,
      );
      return uniqueStatuses[0] as RequestStatus; // Return the single unique terminal status
    }

    // If there's a mix of terminal statuses, return 'FAILED'
    //Instead FAILED returning PARTIAL COMPLETED -10/25/2024
    this.logger.log(
      `All the tufin tickets has reached terminal status with at-least one or all are not successful.`,
    );
    return RequestStatus.PARTIAL_SUCCESS;
  }

  async createNewFirewallRequestResult(data: FirewallRequestCreateDto) {
    this.logger.log(
      `request received for inserting new document ${JSON.stringify(data)}`,
    );

    if (data?.createSecureChange) {
      this.logger.log(
        `createSecureChange flag is true, creating a tufin ticket`,
      );
      const ticketDetails = await this.createSecureChangeTicket(
        data.secureChangePayload,
      );
      this.logger.log(`Tufin ticket details ${JSON.stringify(ticketDetails)}`);
      data.ticketDetails = {
        tufin: { ...ticketDetails, error: [] },
        ...data.ticketDetails,
      };
    }

    const result = await this.firewallRequestRepository.createRequest(data);
    this.logger.debug('New doucment inserted successfully', result);
    return result;
  }

  async createSecureChangeTicket(data: SecureChangeRequestDto) {
    return await this.tufinService.createSecureChange(data);
  }

  async createNewResubmitFirewallRequest(data: ResubmitFirewallRequestDto[]) {
    this.logger.log(
      `request received for inserting new resubmit documents ${JSON.stringify(data)}`,
    );
    const checkDuplicate =
      await this.resubmitFirewallRequestRepository.getRequest(
        data[0].serviceRequestId,
      );

    this.logger.log(`checkDuplicate ${checkDuplicate}`);
    if (checkDuplicate) {
      throw new BadRequestException(
        `${data[0].serviceRequestId} is already submitted`,
      );
    }

    const serviceRequest = await this.assetsService.get(
      data[0].serviceRequestId,
    );

    this.logger.log(`service request status ${serviceRequest.status}`);
    if (serviceRequest.status === RequestStatus.SUCCESS) {
      throw new BadRequestException(
        `${data[0].serviceRequestId} with status COMPLETED cannot resubmitted`,
      );
    }

    const result =
      await this.resubmitFirewallRequestRepository.createRequests(data);
    this.logger.debug('New documents inserted successfully', result);

    this.logger.debug(`Pushing resubmit request to queue`);
    const eventPattern: EventPatterns =
      EventPatterns.FIREWALL_V2_RESUBMIT_REQUEST;

    await this.rmqService.pushMessage(
      RequestType.RESUBMIT_FIREWALL_V2,
      {
        id: '',
        payload: data[0],
        serviceRequestId: data[0].serviceRequestId,
      },
      eventPattern,
    );
    this.logger.debug(`Pushed resubmit request to queue`);
    return result;
  }

  async getAllResubmitRequests() {
    this.logger.log(`request received for getting all resubmit documents`);
    const result =
      await this.resubmitFirewallRequestRepository.getAllRequests();
    this.logger.debug('all documents fetched successfully', result);
    return result;
  }

  async getResubmitRequest(serviceRequest: string) {
    this.logger.log(
      `request received for getting resubmit documents ${serviceRequest}`,
    );
    const result =
      await this.resubmitFirewallRequestRepository.getRequest(serviceRequest);
    this.logger.debug('document fetched successfully', result);
    return result;
  }

  async updateResubmitRequest(serviceRequestId, updateRequestObj) {
    this.logger.log(
      `request received for updating resubmit document ${serviceRequestId} ${updateRequestObj}`,
    );
    const result = await this.resubmitFirewallRequestRepository.updateRequest(
      serviceRequestId,
      updateRequestObj,
    );
    this.logger.debug('doucment updated successfully', result);
    return result;
  }

  async getApprovalDetails(
    ticketDetails,
  ): Promise<FirewallTicketDetailsResponseDto[]> {
    this.logger.log(
      `Get the approval details from Sub Type Approval Service`,
      ticketDetails,
    );
    const subTypeApprovals =
      await this.subTypeApprovalsService.findOneByRequestType(
        RequestType.FIREWALL_V2,
      );
    this.logger.log(`Subtype approvals`, subTypeApprovals);

    if (subTypeApprovals) {
      this.logger.log(`Get the groups for the logged in user`);
      const { groups: userGroups } = this.getUserContext();
      const { subTypeToGroupMap } = subTypeApprovals;
      const selectedSubtype = subTypeToGroupMap.filter((subTypesDetails) =>
        subTypesDetails.groups.some((group) => userGroups.includes(group)),
      );
      if (selectedSubtype?.length) {
        this.logger.debug(
          `Update the hasApproveAccess attribute for the request if user has approve or reject access`,
        );
        ticketDetails = ticketDetails.map((ticketDetail) => ({
          ...ticketDetail,
          hasApproveAccess: selectedSubtype.some(
            (subtypeDetails) =>
              subtypeDetails.subType === ticketDetail.organizationName,
          ),
        }));
      }
    }

    return ticketDetails;
  }

  async update(
    subRequestId: string,
    updates: Partial<FirewallRequestEntity>,
    skipServiceRequestUpdate: boolean =false,
  ): Promise<FirewallRequestEntity> {
    this.logger.log(`Update ${subRequestId}, ${updates}`);
    const result = await this.firewallRequestRepository.update(
      subRequestId,
      updates,
    );
    this.logger.log(`Update result: ${result}`);
    if (
      !skipServiceRequestUpdate &&
      result?.requestId &&
      updates?.status &&
      [
        RequestStatus.SUCCESS,
        RequestStatus.FAILED,
        RequestStatus.PARTIAL_SUCCESS,
        RequestStatus.CLOSED_MANUALLY,
        RequestStatus.CANCELLED,
        RequestStatus.COMPLETED_EXTERNALLY,
        RequestStatus.CANCELLED_EXTERNALLY,
        RequestStatus.REJECTED,
      ].includes(updates?.status)
    ) {
      this.logger.log(
        'Updating service request status from update ',
        result.requestId,
      );
      await this.updateServiceRequestStatus(result.requestId);
    }
    return result;
  }

  async getFirewallTicketDetails(
    serviceRequestId?: string,
  ): Promise<FirewallTicketDetailsResponseDto[]> {
    this.logger.log(
      `Calling firewall request repository to fetch firewall ticket details for serviceRequestId: ${serviceRequestId}`,
    );

    const ticketDetails =
      await this.firewallRequestRepository.getFirewallTicketDetails(
        serviceRequestId,
      );
    this.logger.log(`Ticket details`, ticketDetails);

    this.logger.debug(
      `Get the approval details for the tickets created under serviceRequestId: ${serviceRequestId}`,
    );

    const modifiedTicketDetails = await this.getApprovalDetails(ticketDetails);
    this.logger.debug(
      `Approval details fetched successfully for subRequestId:${serviceRequestId}`,
      modifiedTicketDetails,
    );
    return modifiedTicketDetails;
  }

  async getFirewallTicketDetailsN8N(
    serviceRequestId?: string,
  ): Promise<Partial<FirewallRequestEntity>[]> {
    this.logger.log(
      `Calling firewall request repository to fetch firewall ticket details for serviceRequestId:
       ${serviceRequestId}`,
    );

    const ticketDetails =
      await this.firewallRequestRepository.getFirewallTicketDetails(
        serviceRequestId,
      );

    this.logger.debug(
      `firewall ticket details for serviceRequestId:
       ${serviceRequestId}`,
      ticketDetails,
    );

    return ticketDetails;
  }

  async updateSubRequestStatusAndQueueForProcessing(
    subRequestId: string,
    status: RequestStatus,
  ) {
    this.logger.log(
      `Updating subrequest status and queue for processing ${subRequestId} ${status}`,
    );
    const updateBody = {
      status,
      approvalStatus:
        status === RequestStatus.REJECTED
          ? ApprovalStatus.REJECTED
          : ApprovalStatus.NA,
    };
    const updatedSubRequest = await this.firewallRequestRepository.update(
      subRequestId,
      updateBody,
    );
    this.logger.log(`Updated subrequest ${updatedSubRequest}`);

    this.logger.debug(`Pushing subRequest request ${subRequestId} to queue`);

    let eventPattern: EventPatterns;
    if (status === RequestStatus.REJECTED) {
      eventPattern = EventPatterns.FIREWALL_V2_REJECTED_ORGANIZATION;
    } else {
      eventPattern = EventPatterns.FIREWALL_V2_SUBREQUEST_CANCELLED;
    }

    await this.rmqService.pushMessage(
      RequestType.FIREWALL_V2,
      {
        id: updatedSubRequest.id,
        payload: updatedSubRequest,
        serviceRequestId: updatedSubRequest.requestId,
      },
      eventPattern,
    );

    return updatedSubRequest;
  }

  async updateCancelledStatusAndQueueForProcessing(
    serviceRequestId: string,
    status: RequestStatus.CANCELLING,
    canPushMessage: boolean,
  ) {
    this.logger.log(
      `Updating cancelled status and queue for processing ${serviceRequestId} ${status} ${canPushMessage}`,
    );
    const { startedAt, completedAt } =
      this.assetsService.getStartedAndCompletedDate(status, serviceRequestId);
    const updateBody = {
      status,
      startedAt,
      completedAt,
      approvalStatus: ApprovalStatus.NA,
    };
    const updatedServiceRequest = await this.assetsService.update(
      serviceRequestId,
      updateBody,
    );
    this.logger.log(`Updated service request: ${updatedServiceRequest}`);

    if (canPushMessage) {
      this.logger.debug(`Pushing service request ${serviceRequestId} to queue`);
      const eventPattern: EventPatterns = EventPatterns.FIREWALL_V2_CANCELLED;

      await this.rmqService.pushMessage(
        updatedServiceRequest.requestType,
        {
          id: updatedServiceRequest.id,
          payload: updatedServiceRequest.payload,
          serviceRequestId: updatedServiceRequest.serviceRequestId,
        },
        eventPattern,
      );
    }

    return updatedServiceRequest;
  }

  async cancelServiceRequest(id: string, forceToCancel: boolean = false) {
    this.logger.log(`Cancel service request for id: ${id} ${forceToCancel}`);
    const serviceRequest = await this.assetsService.findById(id);
    this.logger.log(`Service request: ${serviceRequest}`);
    if (!serviceRequest) {
      this.logger.error(`No service request found for the id:${id}`);
      throw new NotFoundException();
    }
    if (serviceRequest.requestType !== RequestType.FIREWALL_V2) {
      throw new BadRequestException(
        `Only ${RequestType.FIREWALL_V2} requests can be cancelled`,
      );
    }
    if (!forceToCancel) {
      if (serviceRequest.status !== RequestStatus.PENDING_APPROVAL) {
        throw new BadRequestException(
          `Request with status other than ${RequestStatus.PENDING_APPROVAL} cannot be cancelled`,
        );
      }
      const evaluationResult = await this.canCancel(serviceRequest);
      if (!evaluationResult) {
        throw new ForbiddenException('Only Requestor can cancel the request');
      }
    }

    //while calling cancel api from n8n getFirewallTicketDetails is expecting token so calling getFirewallTicketDetailsN8N
    //in case of force cancel
    const subRequests = !forceToCancel
      ? await this.getFirewallTicketDetails(serviceRequest.serviceRequestId)
      : await this.getFirewallTicketDetailsN8N(serviceRequest.serviceRequestId);

    if (
      subRequests.every((request) => request.status === RequestStatus.CANCELLED)
    ) {
      throw new InternalServerErrorException(
        `Already all subRequests are cancelled`,
      );
    }

    //we need to update status of subrequest as well
    if (serviceRequest.requestType === RequestType.FIREWALL_V2) {
      //get the subrequest list

      //update the status and approval status of each subrequest
      for (const request of subRequests) {
        if (request.status !== RequestStatus.CANCELLED) {
          try {
            await this.update(request.subRequestId, {
              status: RequestStatus.CANCELLING,
              approvalStatus: ApprovalStatus.NA,
            });
          } catch (err) {
            this.logger.error(
              err,
              `Error while updating status od subrequest ${request.subRequestId}`,
            );
          }
        }
      }
    }
    await this.updateCancelledStatusAndQueueForProcessing(
      id,
      RequestStatus.CANCELLING,
      true,
    );

    return {
      updatedStatus: RequestStatus.CANCELLING,
      message: `The request is now ${RequestStatus.CANCELLING.toLowerCase()}`,
    };
  }

  async cancelSubRequest(
    requestId: string,
    subRequestId: string,
    status: RequestStatus,
    forceCancel?: boolean,
  ) {
    this.logger.log(`Cancel sub request ${requestId} ${subRequestId}`);
    const serviceRequest =
      await this.assetsService.findByGenericRequestId(requestId);
    this.logger.log(`Service request: ${serviceRequest}`);
    if (!serviceRequest) {
      this.logger.error(`No service request found for the id:${requestId}`);
      throw new NotFoundException(`Request doesn't exists with ${requestId}`);
    }
    if (serviceRequest.requestType !== RequestType.FIREWALL_V2) {
      throw new BadRequestException(
        `Only ${RequestType.FIREWALL_V2} requests can be cancelled`,
      );
    }

    const subRequest =
      await this.firewallRequestRepository.findBySubRequestId(subRequestId);
    this.logger.log(`Sub request: ${subRequest}`);
    if (!subRequest) {
      this.logger.error(`No subRequest  found for the id:${subRequestId}`);
      throw new NotFoundException(
        `subRequest doesn't exists with ${subRequestId}`,
      );
    }

    if (!forceCancel) {
      if (
        subRequest.status !== RequestStatus.PENDING_APPROVAL ||
        serviceRequest.status !== RequestStatus.PENDING_APPROVAL
      ) {
        throw new BadRequestException(
          `Request with status other than ${RequestStatus.PENDING_APPROVAL} cannot be cancelled`,
        );
      }
      const evaluationResult = await this.canCancel(serviceRequest, subRequest);
      if (!evaluationResult) {
        throw new ForbiddenException('Only Requestor can cancel the request');
      }
    }

    await this.updateSubRequestStatusAndQueueForProcessing(
      subRequestId,
      status,
    );

    await this.updateApplicableSubTypes(requestId, serviceRequest, subRequest);

    await this.updateServiceRequestStatus(requestId);

    return {
      updatedStatus: RequestStatus.CANCELLING,
      message: `The sub request is now ${RequestStatus.CANCELLING.toLowerCase()}`,
    };
  }

  async updateApplicableSubTypes(requestId, serviceRequest, subRequest) {
    this.logger.log(
      `Update applicable subtypes for service request after cancelling/ closing ${requestId} ${serviceRequest} ${subRequest}`,
    );
    const allSubRequests =
      await this.firewallRequestRepository.getFirewallTicketDetails(requestId);
    this.logger.log(`All Subrequests: ${allSubRequests}`);

    //remove cancelled/ closed organization from approval list and update to Approved status if required and queue
    const currentApprovalList =
      serviceRequest.applicableSubTypes as OrganizationName[];

    const cancelledOrg = currentApprovalList.filter(
      (orgName) => orgName === subRequest.organizationName,
    );

    //check length of subrequests with cancelled/ closed org
    const requestsWithoutUpdatedRequest = allSubRequests.filter(
      (request) => request.subRequestId !== subRequest.subRequestId,
    );
    const pendingOrgSubRequest = requestsWithoutUpdatedRequest.filter(
      (request) =>
        request.organizationName === subRequest.organizationName &&
        request.status === RequestStatus.PENDING_APPROVAL,
    );

    let updatedApprovalList;
    if (pendingOrgSubRequest.length === 0) {
      updatedApprovalList = currentApprovalList.filter(
        (orgName) => orgName !== subRequest.organizationName,
      );
      const query: any = {};

      query.applicableSubTypes = updatedApprovalList;

      this.logger.log(`Updating service request`);
      await this.assetsService.update(requestId, query);
      this.logger.log(`Updated service request`);
    }
  }

  async updateServiceRequestAfterCancelling(
    requestId,
    serviceRequest,
    subRequest,
  ) {
    this.logger.log(
      `Update service request after cancelling ${requestId} ${serviceRequest} ${subRequest}`,
    );
    const allSubRequests =
      await this.firewallRequestRepository.getFirewallTicketDetails(requestId);
    this.logger.log(`All Subrequests: ${allSubRequests}`);

    //remove cancelled organization from approval list and update to Approved status if required and queue
    const currentApprovalList =
      serviceRequest.applicableSubTypes as OrganizationName[];

    const cancelledOrg = currentApprovalList.filter(
      (orgName) => orgName === subRequest.organizationName,
    );

    //check length of subrequests with cancelled org
    const requestsWithoutCancelledRequest = allSubRequests.filter(
      (request) => request.subRequestId !== subRequest.subRequestId,
    );
    const cancelledOrgSubRequest = requestsWithoutCancelledRequest.filter(
      (request) =>
        request.organizationName === subRequest.organizationName &&
        request.status === RequestStatus.PENDING_APPROVAL,
    );

    let updatedApprovalList;
    if (cancelledOrgSubRequest.length === 0) {
      updatedApprovalList = currentApprovalList.filter(
        (orgName) => orgName !== subRequest.organizationName,
      );
    } else {
      updatedApprovalList = currentApprovalList;
    }

    //exclude cancalled organization from list
    const validSubRequests = allSubRequests.filter(
      (subRequest) =>
        subRequest.status !== RequestStatus.CANCELLED &&
        subRequest.status !== RequestStatus.CANCELLING &&
        subRequest.status !== RequestStatus.FAILED &&
        subRequest.status !== RequestStatus.SUCCESS,
    );

    const ipv4Tickets = validSubRequests.filter(
      (ticket) =>
        ticket.ipType === IpType.IPV4 &&
        ![OrganizationName.NA_FAILED, OrganizationName.NA_NOIMPACT].includes(
          ticket.organizationName as OrganizationName,
        ),
    );

    //check if service request can be approved
    const canApproved = ipv4Tickets.length
      ? ipv4Tickets.every(
          (subRequest) => subRequest.status === RequestStatus.APPROVED,
        )
      : false;

    //check if only ipv6 is processing and remaining are cancelled
    const ipv6Processing = validSubRequests.filter(
      (ticket) =>
        ticket.ipType === IPType.IPV6 &&
        ticket.status === RequestStatus.PROCESSING,
    );

    // check if service request to be marked as CANCELLED
    //check if all subrequest are cancelled
    const allCancelled = allSubRequests.every(
      (request) =>
        request.status === RequestStatus.CANCELLED ||
        request.status === RequestStatus.CANCELLING ||
        request.status === RequestStatus.FAILED ||
        request.status === RequestStatus.SUCCESS,
    );

    const query: any = {};
    let canPushMessage = false;
    query.applicableSubTypes = updatedApprovalList;
    if (ipv6Processing.length && !ipv4Tickets.length) {
      query.status = RequestStatus.PROCESSING;
      query.approvalStatus = ApprovalStatus.NA;
    } else if (canApproved) {
      query.approvalStatus = ApprovalStatus.APPROVED;
      query.status = RequestStatus.APPROVED;
      canPushMessage = true;
    } else if (allCancelled) {
      query.approvalStatus = ApprovalStatus.NA;
      query.status = RequestStatus.CANCELLED;
      const { startedAt, completedAt } =
        this.assetsService.getStartedAndCompletedDate(
          RequestStatus.CANCELLED,
          requestId,
        );
      query.startedAt = startedAt;
      query.completedAt = completedAt;
      canPushMessage = false;
    }

    this.logger.log(`Updating service request`);
    await this.assetsService.update(requestId, query);
    this.logger.log(`Updated service request`);

    if (canPushMessage) {
      await this.pushMessage(
        serviceRequest,
        EventPatterns.FIREWALL_V2_APPROVED,
      );
    }
  }

  async pushMessage(serviceRequest, event) {
    try {
      this.logger.log(`Push message ${serviceRequest} ${event}`);
      await this.rmqService.pushMessage(
        serviceRequest.requestType,
        {
          id: serviceRequest.id,
          payload: serviceRequest.payload,
          serviceRequestId: serviceRequest.serviceRequestId,
        },
        event,
      );
    } catch (err) {
      this.logger.error(
        err,
        `Error while pushing cancel message into queue ${serviceRequest} ${event}`,
      );
    }
  }

  async closeSubRequest(requestId: string, subRequestId: string) {
    this.logger.log(`Close subrequest ${requestId} ${subRequestId}`);
    const serviceRequest =
      await this.assetsService.findByGenericRequestId(requestId);
    if (!serviceRequest) {
      this.logger.error(`No service request found for the id:${requestId}`);
      throw new NotFoundException(`Request doesn't exists with ${requestId}`);
    }
    this.logger.log(`Service request`, serviceRequest);
    const subRequest =
      await this.firewallRequestRepository.findBySubRequestId(subRequestId);
    if (!subRequest) {
      this.logger.error(`No subRequest  found for the id:${subRequestId}`);
      throw new NotFoundException(
        `subRequest doesn't exists with ${subRequestId}`,
      );
    }
    this.logger.log(`Subrequest`, subRequest);
    if (subRequest.status !== RequestStatus.FAILED) {
      throw new BadRequestException(
        `Request with status other than ${RequestStatus.FAILED} cannot be closed`,
      );
    }
    const tufinStatus = await withResponseErrorHandler(
      this.tufinServiceApi.get(
        `firewall/ticket/${subRequest.ticketDetails?.tufin?.ticketId}`,
      ),
    );
    this.logger.log(`Tufin status`, tufinStatus);
    if (tufinStatus?.status !== TUFIN_TICKET_STATUS.TICKET_CLOSED) {
      this.logger.log(
        `tufin ticket status ${tufinStatus?.status}, we can close only if status id ticket closed for ${requestId} ${subRequestId}`,
      );
      throw new BadRequestException(
        `Tufin with status other than Ticket Closed cannot be closed`,
      );
    }

    const canClose = await this.canClose(serviceRequest, subRequest);

    if (!canClose) {
      this.logger.log(
        `User doesn't belong to ${subRequest.organizationName} approval group`,
      );
      throw new ForbiddenException(
        `User doesn't belong to ${subRequest.organizationName} approval group`,
      );
    }

    //updating latest tufin status ticket closed in subrequets as well
    const updatedSubRequest = await this.update(subRequestId, {
      status: RequestStatus.CLOSED_MANUALLY,
      //@ts-ignore
      'ticketDetails.tufin.status': TUFIN_TICKET_STATUS.TICKET_CLOSED,
    });
    this.logger.log(`Updated subrequest`, updatedSubRequest);

    await this.rmqService.pushMessage(
      RequestType.FIREWALL_V2,
      {
        id: updatedSubRequest.id,
        payload: updatedSubRequest,
        serviceRequestId: updatedSubRequest.requestId,
      },
      EventPatterns.FIREWALL_V2_SUBREQUEST_CLOSED,
    );

    return {
      updatedStatus: RequestStatus.CLOSED,
      message: `The request is now ${RequestStatus.CLOSED.toLowerCase()}`,
    };
  }

  async closeSubRequestV2(
    requestId: string,
    subRequestId: string,
    status: RequestStatus,
    forceClose?: boolean,
  ) {
    this.logger.log(`Close subrequest ${requestId} ${subRequestId}`);
    const serviceRequest =
      await this.assetsService.findByGenericRequestId(requestId);
    if (!serviceRequest) {
      this.logger.error(`No service request found for the id:${requestId}`);
      throw new NotFoundException(`Request doesn't exists with ${requestId}`);
    }
    this.logger.log(`Service request`, JSON.stringify(serviceRequest));
    const subRequest =
      await this.firewallRequestRepository.findBySubRequestId(subRequestId);
    if (!subRequest) {
      this.logger.error(`No subRequest  found for the id:${subRequestId}`);
      throw new NotFoundException(
        `subRequest doesn't exists with ${subRequestId}`,
      );
    }
    this.logger.log(`Subrequest`, JSON.stringify(subRequest));

    if (!forceClose) {
      const canClose = await this.canClose(serviceRequest, subRequest);

      if (!canClose) {
        this.logger.log(
          `User doesn't belong to ${subRequest.organizationName} approval group`,
        );
        throw new ForbiddenException(
          `User doesn't belong to ${subRequest.organizationName} approval group`,
        );
      }
    }

    //user can close any type of subrequest, execute below only for redaps, corporate, unknown
    if (
      [
        OrganizationName.RED_APS,
        OrganizationName.CORPORATE,
        OrganizationName.UNKNOWN,
      ].includes(subRequest.organizationName as OrganizationName)
    ) {
      if (subRequest?.ticketDetails?.tufin?.ticketId) {
        //Cancell the tufin ticket
        const tufinResponse = await this.tufinService.cancelTufinTicket(
          subRequest.ticketDetails.tufin.ticketId,
        );
        this.logger.log(`Tufin ticket is cancelled for ${subRequestId}`);
      } else {
        this.logger.warn(`Tufin ticket doesn't exists for ${subRequestId}`);
      }
    }

    //updating latest tufin status ticket closed in subrequets as well
    let updateObject;
    if (subRequest.approvalStatus === ApprovalStatus.PENDING) {
      updateObject = {
        status: status,
        approvalStatus: ApprovalStatus.NA,
        //@ts-ignore
        'ticketDetails.tufin.status': TUFIN_TICKET_STATUS.TICKET_CANCELLED,
      };

      //update the applicable subtypes in serice request, we need to remove approval from subtypes for the org if status is pending approval
      await this.updateApplicableSubTypes(
        requestId,
        serviceRequest,
        subRequest,
      );
    } else {
      updateObject = {
        status: status,
        //@ts-ignore
        'ticketDetails.tufin.status': TUFIN_TICKET_STATUS.TICKET_CANCELLED,
      };
    }
    this.logger.log(`updating subrequest with ${JSON.stringify(updateObject)}`);
    const updatedSubRequest = await this.update(subRequestId, updateObject);
    this.logger.log(`Updated subrequest`, JSON.stringify(updatedSubRequest));

    //push message into queue to close the associated tickets
    await this.rmqService.pushMessage(
      RequestType.FIREWALL_V2,
      {
        id: updatedSubRequest.id,
        payload: updatedSubRequest,
        serviceRequestId: updatedSubRequest.requestId,
      },
      EventPatterns.FIREWALL_V2_SUBREQUEST_CLOSED,
    );
    this.logger.log(
      `Pushed message for ${updatedSubRequest.requestId} into ${EventPatterns.FIREWALL_V2_SUBREQUEST_CLOSED} queue`,
    );

    //check and update the service request if needed
    await this.updateServiceRequestStatus(requestId);

    return {
      updatedStatus: RequestStatus.CLOSED_MANUALLY,
      message: `The request is now ${RequestStatus.CLOSED_MANUALLY.toLowerCase()}`,
    };
  }

  async canCancel(
    serviceRequest: ServiceRequestEntity,
    subRequest?: FirewallRequestEntity,
  ): Promise<boolean> {
    this.logger.log(`can cancel ${serviceRequest} ${subRequest}`);
    const user = RequestContext.currentContext.req.user;
    let usergroups = [];
    if (subRequest?.subRequestId) {
      const subTypeApproval =
        await this.subTypeApprovalsService.findOneByRequestType(
          RequestType.FIREWALL_V2,
        );
      this.logger.log(`Subtype approval ${subTypeApproval}`);

      usergroups = await this.subTypeApprovals.getUserSubTypes(
        user['groups'],
        subTypeApproval,
      );
      this.logger.log(`Usergroups ${usergroups}`);
    }

    if (
      usergroups.includes(subRequest?.organizationName) ||
      user.userId === serviceRequest.createdBy
    ) {
      return true;
    } else {
      return false;
    }
  }

  async canClose(
    serviceRequest: ServiceRequestEntity,
    subRequest: FirewallRequestEntity,
  ) {
    this.logger.log(`can close ${serviceRequest} ${subRequest}`);
    const user = RequestContext.currentContext.req.user;
    const subTypeApproval =
      await this.subTypeApprovalsService.findOneByRequestType(
        RequestType.FIREWALL_V2,
      );
    this.logger.log(`Subtype approval ${subTypeApproval}`);
    const usergroups = await this.subTypeApprovals.getUserSubTypes(
      user['groups'],
      subTypeApproval,
    );
    this.logger.log(`Usergroups ${usergroups}`);
    if (usergroups.includes(subRequest.organizationName)) {
      return true;
    } else {
      return false;
    }
  }

  async createAndUploadFirewallRulesFile(data: createFileRequestDto) {
    this.logger.log(`Create and upload firewall rules file ${data}`);
    const createFileRequestDto = {};
    createFileRequestDto['date'] = data.date;
    createFileRequestDto['projectName'] = data.projectName;
    createFileRequestDto['projectCreator'] = data.projectCreator;
    createFileRequestDto['appId'] = data.appId;
    createFileRequestDto['date'] = data.date;
    createFileRequestDto['firewallRules'] = data.accessRequest;
    const fileResponse = await createRulesExcel(createFileRequestDto);
    this.logger.debug('returnfrom ', fileResponse);
    const uploadedData = await this.uploadV2FileToStorage(fileResponse);
    this.logger.log(`Uploaded data ${uploadedData}`);
    //fetch serviceRequest
    const serviceRequest = await this.assetsService.findByServiceRequestId(
      data.requestId,
    );
    this.logger.log(`Service request ${serviceRequest}`);
    const response = {
      bucketName: uploadedData.bucketName,
      fileName: uploadedData.fileName,
      filePath: uploadedData.filePath,
    };
    const storageRequest = {
      requestType: data.requestType,
      requestId: data.requestId,
      subRequestId: data.subRequestId,
      fileType: 'xlsx',
      createdBy: serviceRequest?.createdBy,
      ...uploadedData,
    };
    const storageResponse = await this.storageRepository.create(storageRequest);
    this.logger.debug('storageResponse ', storageResponse);

    return response;
  }

  async saveTufinPathAnalysis(data: TufinDevicesDto[]) {
    this.logger.debug('saveTufinPathAnalysis service', data);
    const response = this.pathAnalysisRespository.saveTufinDevices(data);
    this.logger.debug('Save tufin path analysis response ', response);
    return response;
  }

  async updateTufinDevices(data: UpdateTufinDevicesDto) {
    this.logger.debug('updateTufinDevices service', data);
    const response = this.pathAnalysisRespository.updateTufinDevices(data);
    this.logger.debug('updated tufin devices response ', response);
    return response;
  }

  async getPathAnalysis(serviceRequestId, ruleId) {
    this.logger.debug('getPathAnalysis service', serviceRequestId, ruleId);
    const response = await this.pathAnalysisRespository.getTufinDevices(
      serviceRequestId,
      ruleId,
    );
    this.logger.debug('Get path analysis response ', response);
    return response;
  }

  async getAllDevicesByRequestId(serviceRequestId) {
    this.logger.debug('getAllDevices ', serviceRequestId);
    const response =
      await this.pathAnalysisRespository.getAllTufinDevices(serviceRequestId);
    this.logger.debug('Get all devices response ', response);
    return response;
  }

  async getPathAnalysisImage(serviceRequestId, ruleId) {
    this.logger.debug('getPathAnalysisImage service', serviceRequestId, ruleId);
    const response = await this.pathAnalysisRespository.getTufinDevices(
      serviceRequestId,
      ruleId,
    );

    this.logger.log('Received request to get path analysis image', response);
    const storagePayload: ObjectStorageDto = {
      bucketName: response.storage?.bucketName,
      fileName: response.storage?.fileName,
      filePath: response.storage?.filePath,
    };
    this.logger.log(
      'Downloading file from storage with payload',
      storagePayload,
    );
    const image = await this.storageService.download(storagePayload);
    return image;
  }

  async UpdateSubRequestJiraStatus(data: UpdatePollRequestDto[]) {
    this.logger.log(`Updating subrequest Jira status ${JSON.stringify(data)}`);
    // iterate over each object
    for (const request of data) {
      try {
        //get service request from db using id
        const serviceRequest = await this.assetsService.findById(
          request.requestId,
        );
        if (!serviceRequest) {
          throw new NotFoundException(
            `service request not found for ${request.requestId}`,
          );
        }
        // get subrequests using request id
        const subRequests = await this.getFirewallTicketDetailsN8N(
          serviceRequest.serviceRequestId,
        );
        this.logger.debug('Subrequests: ', subRequests);

        if (!subRequests.length) {
          throw new NotFoundException(
            `No subrequests found for id ${serviceRequest.serviceRequestId}`,
          );
        }

        await this.updateSubRequestAndServiceRequest(
          subRequests,
          serviceRequest,
          request,
        );
      } catch (err) {
        this.logger.error(
          err,
          `Error while update ipv6 subrequest ${request.subRequestId} ${request.requestId} ${err}`,
        );
      }
    }
  }

  async updateSubRequestAndServiceRequest(
    subrequests: Partial<FirewallRequestEntity>[],
    serviceRequest: ServiceRequestEntity,
    updatedJiraObject: UpdatePollRequestDto,
  ) {
    this.logger.log(
      `Updating subrequest and servicerequest for  ${JSON.stringify(updatedJiraObject)}`,
    );
    // using 'find' to get the  subrequest using subrequest id for service request
    const subRequest = subrequests.find(
      (request) => request.subRequestId === updatedJiraObject.subRequestId,
    );

    if (!subRequest) {
      this.logger.log(
        `Subrequest not found for ${updatedJiraObject.subRequestId}`,
      );
      return;
    }

    if (
      [
        OrganizationName.IPV6,
        OrganizationName.RED_CBO,
        OrganizationName.AWS,
        OrganizationName.UNKNOWN,
        OrganizationName.RED_APS,
      ].includes(subRequest.organizationName as OrganizationName)
    ) {
      if (
        [
          RequestStatus.CANCELLED,
          RequestStatus.SUCCESS,
          RequestStatus.CANCELLED_EXTERNALLY,
          RequestStatus.COMPLETED_EXTERNALLY,
          RequestStatus.CLOSED_MANUALLY,
          RequestStatus.REJECTED,
        ].includes(subRequest.status) &&
        [
          JiraStatus.CANCELLED,
          JiraStatus.COMPLETED_WITHOUT_CUSTOMER_VALIDATION,
          JiraStatus.COMPLETED_WITH_CUSTOMER_VALIDATION,
          RequestStatus.CLOSED,
          RequestStatus.CANCELLED as string,
        ].includes(subRequest.ticketDetails?.jira?.status)
      ) {
        this.logger.log(
          `Subrequests is already ${subRequest.status} for ${updatedJiraObject.subRequestId}`,
        );
        return;
      }
    }

    //update subrequest jira status
    try {
      if (subRequest) {
        const subRequestStatus = this.jiraStatusToRequestStatusMapping(
          updatedJiraObject.status,
        );
        if (
          [
            OrganizationName.NA_FAILED,
            OrganizationName.NA_NOIMPACT,
            OrganizationName.RED_APS,
            OrganizationName.UNKNOWN,
          ].includes(subRequest.organizationName as OrganizationName) &&
          ![
            RequestStatus.SUCCESS,
            RequestStatus.CANCELLED,
            RequestStatus.FAILED,
          ].includes(subRequestStatus)
        ) {
          this.logger.log(
            `For NETDCOPSFW tickets we update only if status is end status -> current status ${subRequestStatus}`,
          );
          return;
        }
        let payload;
        //Addded this if condition to handle update jira status if subrequest is in end status and jira is not in end status
        if (
          [OrganizationName.UNKNOWN, OrganizationName.RED_APS].includes(
            subRequest.organizationName as OrganizationName,
          ) &&
          [
            RequestStatus.CANCELLED,
            RequestStatus.SUCCESS,
            RequestStatus.FAILED,
            RequestStatus.REJECTED,
            RequestStatus.CLOSED_MANUALLY,
          ].includes(subRequest.status) &&
          ![
            JiraStatus.COMPLETED_WITH_CUSTOMER_VALIDATION,
            JiraStatus.COMPLETED_WITHOUT_CUSTOMER_VALIDATION,
            JiraStatus.CANCELLED,
            RequestStatus.CLOSED,
            RequestStatus.CANCELLED as string,
          ].includes(subRequest.ticketDetails.jira.status)
        ) {
          payload = {
            'ticketDetails.jira.status': updatedJiraObject.status,
          };
        } else if (
          [OrganizationName.NA_FAILED, OrganizationName.NA_NOIMPACT].includes(
            subRequest.organizationName as OrganizationName,
          )
        ) {
          payload = {
            'ticketDetails.jira.status': updatedJiraObject.status,
          };
        } else if (
          [OrganizationName.UNKNOWN, OrganizationName.RED_APS].includes(
            subRequest.organizationName as OrganizationName,
          )
        ) {
          payload = {
            status:
              subRequestStatus === RequestStatus.SUCCESS
                ? RequestStatus.COMPLETED_EXTERNALLY
                : RequestStatus.CANCELLED_EXTERNALLY,
            'ticketDetails.jira.status': updatedJiraObject.status,
          };
        } else {
          payload = {
            status: subRequestStatus,
            'ticketDetails.jira.status': updatedJiraObject.status,
          };
        }
        await this.firewallRequestRepository.update(
          subRequest.subRequestId,
          payload,
        );

        if (subRequest?.ticketDetails?.cherwell?.ticketId) {
          this.logger.log(
            `Going to update Cherwell status ${subRequest.subRequestId} ${subRequestStatus}`,
          );
          await this.updateCherwellStatus(subRequest, subRequestStatus);
          this.logger.log(`Updated Cherwell status`);
        } else {
          this.logger.log(
            `Cherwell doesn't exists for ${subRequest.subRequestId}`,
          );
        }

        if (
          subRequest?.ticketDetails?.tufin?.ticketId &&
          ![
            TUFIN_TICKET_STATUS.TICKET_CANCELLED,
            TUFIN_TICKET_STATUS.TICKET_CLOSED as string,
          ].includes(subRequest?.ticketDetails?.tufin?.status)
        ) {
          this.logger.log(
            `Going to update tufin status for ${subRequest.subRequestId}`,
          );
          await this.updateTufinStatus(subRequest);
          this.logger.log(`Updated tufin status`);
        } else {
          this.logger.log(
            `Tufin doesn't exists for ${subRequest.subRequestId}`,
          );
        }

        this.logger.log(
          'Updating service request status from updateSubRequestAndServiceRequest ',
          serviceRequest.serviceRequestId,
        );
        //update applicable subtype for firewall v2
        if (
          serviceRequest.requestType === RequestType.FIREWALL_V2 &&
          [OrganizationName.UNKNOWN, OrganizationName.RED_APS].includes(
            subRequest.organizationName as OrganizationName,
          )
        ) {
          const applicableSubTypes = serviceRequest.applicableSubTypes;
          const updateSubTypes = applicableSubTypes.filter(
            (subType) => subType !== subRequest.organizationName,
          );
          await this.assetsService.update(serviceRequest.serviceRequestId, {
            applicableSubTypes: updateSubTypes,
          });
        }

        await this.updateServiceRequestStatus(serviceRequest.serviceRequestId);
        this.logger.log(`Updated service request status`);
      } else {
        this.logger.log(
          `Subrequest not found (or) subrequest is completed/cancelled for ${updatedJiraObject.subRequestId}`,
        );
      }
    } catch (err) {
      this.logger.error(
        err,
        `Error while updating ipv6 subrequest ${serviceRequest.serviceRequestId}  ${subRequest}`,
      );
    }
  }

  async updateCherwellStatus(
    subRequest: Partial<FirewallRequestEntity>,
    subRequestStatus: string,
  ) {
    try {
      this.logger.log(
        `Updating Cherwell status ${subRequest} ${subRequestStatus}`,
      );
      if (subRequest.ticketDetails?.cherwell?.ticketId) {
        this.logger.log(`${subRequest.subRequestId} has cherwell ticket`);
        if (subRequestStatus === RequestStatus.SUCCESS) {
          this.logger.log(
            `subrequest status is ${subRequestStatus}, so moving cherwell ${subRequest.ticketDetails?.cherwell?.ticketId} to closed`,
          );
          await this.updateSubRequestWithCherwellStatus(
            subRequest,
            RequestStatus.CLOSED,
            {
              ticketingSystem: TicketType.CHERWELL,
              ticketId: subRequest.ticketDetails?.cherwell?.ticketId,
              completeReason: 'Success',
            },
          );
          this.logger.log(
            `Updated cherwell ${subRequest.ticketDetails?.cherwell?.ticketId} to closed`,
          );
        } else if (
          subRequestStatus === RequestStatus.CANCELLED ||
          subRequestStatus === RequestStatus.FAILED
        ) {
          this.logger.log(
            `subrequest status is ${subRequestStatus}, so moving cherwell ${subRequest.ticketDetails?.cherwell?.ticketId} to cancelled`,
          );
          await this.updateSubRequestWithCherwellStatus(
            subRequest,
            RequestStatus.CANCELLED,
            {
              ticketingSystem: TicketType.CHERWELL,
              ticketId: subRequest.ticketDetails?.cherwell?.ticketId,
              cancelReason: 'No longer needed',
            },
          );
          this.logger.log(
            `Updated cherwell ${subRequest.ticketDetails?.cherwell?.ticketId} to cancelled`,
          );
        }
      }
    } catch (err) {
      this.logger.error(
        err,
        `Error while updating cherwell for ${subRequest.subRequestId} ${subRequest.requestId}`,
      );
    }
  }

  async updateSubRequestWithCherwellStatus(
    subRequest,
    cherwellStatus,
    updatedObject,
  ) {
    try {
      this.logger.log(
        `Updating subrequest with Cherwell status ${subRequest} ${cherwellStatus} ${updatedObject}`,
      );
      this.logger.log(`Calling Ticket management service to close ticket`);
      await this.ticketManagementWrapperService.closeTicket(updatedObject);
      this.logger.log(`Updating subrequest with cherwell status`);
      await this.firewallRequestRepository.update(subRequest.subRequestId, {
        //@ts-ignore
        'ticketDetails.cherwell.status': cherwellStatus,
      });
    } catch (err) {
      this.logger.error(
        err,
        `Error while ${cherwellStatus} cherwell ticket for ${subRequest.subRequestId}`,
      );
      let status;
      if (cherwellStatus === RequestStatus.CLOSED) {
        status = 'CLOSE FAILED';
      } else {
        status = 'CANCEL FAILED';
      }
      this.logger.log(`Updating subrequest`);
      await this.firewallRequestRepository.update(subRequest.subRequestId, {
        //@ts-ignore
        'ticketDetails.cherwell.status': `${status}`,
        'ticketDetails.cherwell.error': [
          {
            errorCode: err?.status,
            errorMessage: err?.message,
          },
        ],
      });
    }
  }

  async updateTufinStatus(subRequest: Partial<FirewallRequestEntity>) {
    try {
      this.logger.log(`Updating Tufin status for  ${subRequest.subRequestId}`);
      await this.sendActivity(
        subRequest.requestId,
        subRequest.subRequestId,
        ENVIRONMENT_VARS.FW_CANCEL_TUFIN_TICKET_STEP,
        ProcessStatus.STARTED,
        0,
      );
      const response = await this.tufinService.cancelTufinTicket(
        subRequest.ticketDetails.tufin.ticketId,
      );
      const payload: any = {
        //@ts-ignore
        'ticketDetails.tufin.status': TUFIN_TICKET_STATUS.TICKET_CANCELLED,
      };
      if (subRequest.approvalStatus === ApprovalStatus.PENDING) {
        payload.approvalStatus = ApprovalStatus.NA;
      }

      await this.update(subRequest.subRequestId, payload);
      await this.sendActivity(
        subRequest.requestId,
        subRequest.subRequestId,
        ENVIRONMENT_VARS.FW_CANCEL_TUFIN_TICKET_STEP,
        ProcessStatus.COMPLETED,
        0,
        response,
      );
    } catch (err) {
      this.logger.error(
        err,
        `Error while updating tufin for ${subRequest.subRequestId} ${subRequest.requestId}`,
      );
      await this.update(subRequest.subRequestId, {
        //@ts-ignore
        'ticketDetails.tufin.status': RequestStatus.CANCEL_FAILED,
        'ticketDetails.tufin.error': [
          {
            errorCode: err?.response?.statusCode ?? '500',
            errorMessage:
              err?.response?.message ?? 'Error while creating jira ticket',
          },
        ],
      });
      await this.sendActivity(
        subRequest.requestId,
        subRequest.subRequestId,
        ENVIRONMENT_VARS.FW_CANCEL_TUFIN_TICKET_STEP,
        ProcessStatus.FAILED,
        0,
        undefined,
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message:
            err?.response?.message ?? 'Error while cancelling tufin ticket',
        },
      );
    }
  }

  async getRulesUsingRuleIds(requestId: string, data: number[]) {
    this.logger.log(`Getting rules using ruleIds ${requestId} ${data}`);
    const serviceRequest: any =
      await this.assetsService.findByServiceRequestId(requestId);
    if (!serviceRequest) {
      throw new NotFoundException(`Service request with id ${requestId}`);
    }
    this.logger.log(`Service request ${serviceRequest}`);
    const ipv4Rules =
      serviceRequest.payload?.firewallRules?.ipv4?.firewallRules;
    const accessRequest = [];
    for (const rule of ipv4Rules) {
      if (data.includes(rule.ruleId)) {
        accessRequest.push({ ruleInfo: rule });
      }
    }
    // returning in below format so that no  changes required for create excel method
    return { accessRequest: accessRequest };
  }

  jiraStatusToRequestStatusMapping(jiraStatus: JiraStatus): RequestStatus {
    switch (jiraStatus) {
      case JiraStatus.NEW:
      case JiraStatus.CBO_INTAKE:
      case JiraStatus.INPROGRESS:
      case JiraStatus.VALIDATION:
      case JiraStatus.PENDING_REVIEW:
      case JiraStatus.APPROVED_FOR_WORK:
      case JiraStatus.ON_HOLD:
      case JiraStatus.REOPENED_CUSTOMER_REQUEST[0]:
      case JiraStatus.REOPENED_CUSTOMER_REQUEST[1]:
      case JiraStatus.REOPENED_REWORK[0]:
      case JiraStatus.REOPENED_REWORK[1]:
      case JiraStatus.REOPENED_INVESTIGATE[0]:
      case JiraStatus.REOPENED_INVESTIGATE[1]:
      case JiraStatus.OPERATIONAL_SUPPORT[0]:
      case JiraStatus.MANAGER_REVIEW:
      case JiraStatus.IMPLEMENTATION:
      case JiraStatus.PD_REVIEW:
      case JiraStatus.OE_REVIEW:
      case JiraStatus.PENDING_REQUESTOR:
      case JiraStatus.IN_DEVELOPMENT:
      case JiraStatus.IN_REVIEW:
      case JiraStatus.READY_TO_WORK:
      case JiraStatus.BACK_TO_INPROGRESS:
      case JiraStatus.CREATE_TICKET:
      case JiraStatus.In_QUEUE[0]:
      case JiraStatus.In_QUEUE[1]:
      case JiraStatus.READY_FOR_TEST:
      case JiraStatus.UPDATE_PARENT_TICKET:
      case JiraStatus.UNDER_REVIEW:
      case JiraStatus.PENDING_FW_IMPLEMENTATION:
      case JiraStatus.PENDING_CUSTOMER_INPUT:
      case JiraStatus.REOPENED:
        return RequestStatus.PROCESSING;
      case JiraStatus.COMPLETED_WITHOUT_CUSTOMER_VALIDATION:
      case JiraStatus.COMPLETED_WITH_CUSTOMER_VALIDATION:
      case JiraStatus.RESOLVED:
      case JiraStatus.CLOSED:
      case JiraStatus.COMPLETED:
        return RequestStatus.SUCCESS;
      case JiraStatus.CANCELLED:
        return RequestStatus.CANCELLED;
      case JiraStatus.REJECTED:
        return RequestStatus.REJECTED;
      case JiraStatus.FALLOUT:
      case JiraStatus.DENIED:
        return RequestStatus.FAILED;
      default:
        return RequestStatus.PROCESSING;
    }
  }
  /**
   * Updates the overall status of a service request based on the status of its associated firewall tickets.
   *
   * @param {string} requestId - Nebula service request.
   *
   * The method retrieves all firewall ticket details associated with the given `requestId`, filters out the tickets of type IPv4,
   * and analyzes their statuses. Based on the combination of statuses, the following logic is applied:
   *
   * 1. If a mix of `FAILED`, `SUCCESS`, and other statuses is found, the process is considered still in progress,
   *    and no status update is made.
   * 2. If a mix of `FAILED` and `SUCCESS` statuses is found, the overall status is set to `PARTIAL_SUCCESS`.
   * 3. If all statuses are `FAILED`, the overall status is set to `FAILED`.
   * 4. If all statuses are `SUCCESS`, the overall status is set to `SUCCESS`.
   * 5. If only irrelevant statuses (neither `FAILED` nor `SUCCESS`) are found, no status update is made.
   *
   */
  async updateServiceRequestStatus(
    requestId: string,
    errorMessage?: string,
  ): Promise<boolean> {
    try {
      this.logger.log(
        'Request received to update service request status of ',
        requestId,
      );
      const ticketDetails =
        await this.firewallRequestRepository.getFirewallTicketDetails(
          requestId,
        );
      this.logger.log('Ticket details: ', ticketDetails);

      if (!ticketDetails.length) {
        this.logger.log(`No subRequests found for ${requestId}`);
        return;
      }

      //TODO - commented below code and need to check with arun why we are filtering only ipv4
      // const ipv4TicketsDetails = ticketDetails.filter(
      //   (ticket) => ticket.ipType === IpType.IPV4,
      // );

      let overallStatus: RequestStatus;
      let overallApprovalStatus: ApprovalStatus;

      // Extract relevant statuses (failed, success, or others)
      const relevantStatuses = ticketDetails.map((ticket) => ticket.status);
      this.logger.log('Relevant status: ', relevantStatuses);

      //Extract relevant approval status (Approved, Auto Approved, or others)
      //for cancelled subrequest, approval status will be NA. we dont want to consider cancelled status since derive logic doesn't handle these status
      //TODO: we may need to add new approval status for cancelled subrequest to remove below filter
      const subRequestsApprovalStatuses = ticketDetails
        .filter(
          (ticket) =>
            ![
              RequestStatus.CANCELLED,
              RequestStatus.CANCELLING,
              RequestStatus.CANCELLED_EXTERNALLY,
              RequestStatus.COMPLETED_EXTERNALLY,
              RequestStatus.CLOSED_MANUALLY,
            ].includes(ticket.status),
        )
        .map((ticket) => ticket.approvalStatus);
      this.logger.log(
        `Subrequests approval status: `,
        subRequestsApprovalStatuses,
      );

      // intentionally commented these lines dont remove, kept for futire refernce
      // const hasFailed = relevantStatuses.includes(RequestStatus.FAILED);
      // const hasSuccess = relevantStatuses.includes(RequestStatus.SUCCESS);
      // const hasClosedManually = relevantStatuses.includes(
      //   RequestStatus.CLOSED_MANUALLY,
      // );
      // const hasCancelled = relevantStatuses.includes(RequestStatus.CANCELLED);
      // const hasOtherStatuses = relevantStatuses.some(
      //   (status) =>
      //     status !== RequestStatus.FAILED &&
      //     status !== RequestStatus.SUCCESS &&
      //     status !== RequestStatus.CLOSED_MANUALLY &&
      //     status !== RequestStatus.CANCELLED,
      // );

      // // if we have otherstatuses like processing.... no need to check for conditions we can return directly
      // if (hasOtherStatuses) {
      //   // Case: Mix of failed, success, and other statuses
      //   this.logger.log(`Few tickets are still in progress.`);
      //   //added PROCESSING status to handle certain status changes in case of reflow of tufin task
      //   overallStatus = RequestStatus.PROCESSING;
      // } else if (hasFailed && hasSuccess && hasClosedManually) {
      //   overallStatus = RequestStatus.PARTIAL_SUCCESS; //Mix of failed and success
      // } else if (hasFailed && hasSuccess) {
      //   overallStatus = RequestStatus.PARTIAL_SUCCESS;
      // } else if (hasFailed && !hasSuccess && !hasClosedManually) {
      //   overallStatus = RequestStatus.FAILED; //All failed
      // } else if (hasSuccess && !hasFailed && !hasClosedManually) {
      //   overallStatus = RequestStatus.SUCCESS; // Case: All success
      // } else {
      //   this.logger.log(`Invalid statuses of tickets.`);
      //   return;
      // }

      overallStatus = this.getOverAllStatus(relevantStatuses);
      overallApprovalStatus = this.getOverAllApprovalStatus(
        subRequestsApprovalStatuses,
      );
      if (!overallStatus) {
        this.logger.log(
          `Can't determine the over all status.Service request cannot be updated`,
        );
        return;
      }
      const serviceRequest =
        await this.assetsService.findByServiceRequestId(requestId);

      if (overallStatus === serviceRequest.status) {
        this.logger.warn(
          `Not updating the service request as overall status ${overallStatus} and current service request status ${serviceRequest.serviceRequestId} are same`,
        );
        return;
      }
      this.logger.log('Updated service request: ', serviceRequest);
      const steps = [
        {
          eventCode: this.configService.get(
            ENVIRONMENT_VARS.COMPLETING_LOG_FW2_CODE,
          ),
          name: this.configService.get(
            ENVIRONMENT_VARS.COMPLETING_LOG_FW2_NAME,
          ),
        },
      ];
      const resourceNameForLog = uuid.v4();
      //to log start activity for completing the service request
      if([
        RequestStatus.SUCCESS,
        RequestStatus.FAILED,
        RequestStatus.PARTIAL_SUCCESS,
        RequestStatus.CLOSED_MANUALLY,
        RequestStatus.CANCELLED,
        RequestStatus.COMPLETED_EXTERNALLY,
        RequestStatus.CANCELLED_EXTERNALLY,
        RequestStatus.REJECTED,
      ].includes(overallStatus)){
        try {
          this.logger.log('Sending start activity for ', steps);
          const traceId = uuid.v4();
          await this.activityLoggerService.sendActivity(
            ProcessStatus.STARTED,
            steps,
            requestId,
            {},
            traceId,
            0,
            serviceRequest,
            serviceRequest.requestType,
            [],
            undefined,
            undefined,
            undefined,
            resourceNameForLog,
          );
        } catch (err) {
          this.logger.error(
            err,
            `Error while logging start activity for completing service request for ${requestId}`,
          );
        }
      }
      this.logger.log(`Updating service request with status`);
      const updateObject: {
        status: RequestStatus;
        approvalStatus?: ApprovalStatus;
        downstreamError?: any[];
        completedAt?: Date;
        startedAt?: Date;
      } = {
        status: overallStatus,
      };

      if (
        !serviceRequest.completedAt &&
        [
          RequestStatus.SUCCESS,
          RequestStatus.FAILED,
          RequestStatus.CANCELLED,
          RequestStatus.CANCELLED_EXTERNALLY,
          RequestStatus.CLOSED_MANUALLY,
          RequestStatus.REJECTED,
          RequestStatus.COMPLETED_EXTERNALLY,
          RequestStatus.AUTO_APPROVED,
          RequestStatus.APPROVED,
          RequestStatus.PARTIALLY_APPROVED,
          RequestStatus.PARTIAL_SUCCESS,
        ].includes(overallStatus)
      ) {
        updateObject.completedAt = new Date();
      }

      if (
        !serviceRequest.startedAt &&
        relevantStatuses.some((status) =>
          [
            RequestStatus.PROCESSING,
            RequestStatus.SUCCESS,
            RequestStatus.FAILED,
            RequestStatus.CANCELLED,
            RequestStatus.CANCELLING,
            RequestStatus.CANCELLED_EXTERNALLY,
            RequestStatus.CLOSED_MANUALLY,
            RequestStatus.REJECTED,
            RequestStatus.COMPLETED_EXTERNALLY,
          ].includes(status),
        )
      ) {
        updateObject.startedAt = new Date();
      }
      if (
        overallApprovalStatus &&
        overallApprovalStatus !== serviceRequest.approvalStatus
      ) {
        updateObject.approvalStatus = overallApprovalStatus;
      }

      if (errorMessage) {
        const downStreamError = serviceRequest.downstreamError;
        downStreamError.push({
          errorCode: '500',
          data: { message: errorMessage },
        });
        updateObject.downstreamError = downStreamError;
      }
      await this.assetsService.update(requestId, updateObject);
      //to log End activity for completing the service request
      if( [
        RequestStatus.SUCCESS,
        RequestStatus.FAILED,
        RequestStatus.PARTIAL_SUCCESS,
        RequestStatus.CLOSED_MANUALLY,
        RequestStatus.CANCELLED,
        RequestStatus.COMPLETED_EXTERNALLY,
        RequestStatus.CANCELLED_EXTERNALLY,
        RequestStatus.REJECTED,
      ].includes(overallStatus)){
        try {
          const traceId = uuid.v4();
          if (overallStatus == RequestStatus.FAILED) {
            this.logger.log('Sending failed activity for ', steps);
            await this.activityLoggerService.sendActivity(
              ProcessStatus.FAILED,
              steps,
              requestId,
              {},
              traceId,
              0,
              serviceRequest,
              serviceRequest.requestType,
              [],
              undefined,
              undefined,
              { statusCode: '500', message: 'Service request failed' },
              resourceNameForLog,
            );
          } else {
            this.logger.log('Sending end activity for ', steps);
            await this.activityLoggerService.sendActivity(
              ProcessStatus.COMPLETED,
              steps,
              requestId,
              {},
              traceId,
              0,
              serviceRequest,
              serviceRequest.requestType,
              [],
              undefined,
              'Service request updated successfully',
              undefined,
              resourceNameForLog,
            );
          }
        } catch (err) {
          this.logger.error(
            err,
            `Error while logging End activity for completing service request for ${requestId}`,
          );
        }
      }
      return true;
    } catch (error) {
      this.logger.error(
        error,
        `Error while updating service request status for ${requestId}`,
      );
      return false;
    }
  }

  getOverAllStatus(relevantStatuses) {
    let overallStatus: RequestStatus;

    this.logger.log('Relevant status: ', relevantStatuses);
    if (
      relevantStatuses.some(
        (ele) => ele === RequestStatus.PENDING_RISK_ANALYSIS,
      )
    ) {
      overallStatus = RequestStatus.PENDING_RISK_ANALYSIS;
    } else if (
      relevantStatuses.some(
        (ele) => ele === RequestStatus.PENDING_DESIGNER_RESULTS,
      )
    ) {
      overallStatus = RequestStatus.PENDING_DESIGNER_RESULTS;
    } else if (
      relevantStatuses.some((ele) => ele === RequestStatus.PENDING_APPROVAL)
    ) {
      overallStatus = RequestStatus.PENDING_APPROVAL;
    } else if (
      relevantStatuses.some((ele) => ele === RequestStatus.PROCESSING)
    ) {
      overallStatus = RequestStatus.PROCESSING;
    } else if (relevantStatuses.every((ele) => ele === RequestStatus.SUCCESS)) {
      overallStatus = RequestStatus.SUCCESS;
    } else if (relevantStatuses.every((ele) => ele === RequestStatus.FAILED)) {
      overallStatus = RequestStatus.FAILED;
    } else if (
      relevantStatuses.every(
        (ele) =>
          ele === RequestStatus.CANCELLED || ele === RequestStatus.CANCELLING,
      )
    ) {
      overallStatus = RequestStatus.CANCELLED;
    } else if (
      relevantStatuses.every((ele) => ele === RequestStatus.CLOSED_MANUALLY)
    ) {
      overallStatus = RequestStatus.CLOSED_MANUALLY;
    } else if (
      relevantStatuses.every((ele) => ele === RequestStatus.AUTO_APPROVED)
    ) {
      overallStatus = RequestStatus.AUTO_APPROVED;
    } else if (
      relevantStatuses.every(
        (ele) =>
          ele === RequestStatus.APPROVED || ele === RequestStatus.AUTO_APPROVED,
      )
    ) {
      overallStatus = RequestStatus.APPROVED;
    } else if (
      relevantStatuses.every((ele) => ele === RequestStatus.REJECTED)
    ) {
      overallStatus = RequestStatus.REJECTED;
    } else if (
      relevantStatuses.every(
        (ele) =>
          ele === RequestStatus.APPROVED ||
          ele === RequestStatus.REJECTED ||
          ele === RequestStatus.AUTO_APPROVED,
      )
    ) {
      overallStatus = RequestStatus.PARTIALLY_APPROVED;
    } else if (
      relevantStatuses.every(
        (ele) =>
          ele === RequestStatus.CANCELLING ||
          ele === RequestStatus.CANCELLED ||
          ele === RequestStatus.CLOSED_MANUALLY,
      )
    ) {
      overallStatus = RequestStatus.CLOSED_MANUALLY;
    } else if (
      relevantStatuses.every(
        (ele) => ele === RequestStatus.COMPLETED_EXTERNALLY,
      )
    ) {
      overallStatus = RequestStatus.COMPLETED_EXTERNALLY;
    } else if (
      relevantStatuses.every(
        (ele) => ele === RequestStatus.CANCELLED_EXTERNALLY,
      )
    ) {
      overallStatus = RequestStatus.CANCELLED_EXTERNALLY;
    } else if (
      relevantStatuses.every(
        (ele) =>
          ele === RequestStatus.CANCELLED_EXTERNALLY ||
          ele === RequestStatus.CANCELLED ||
          ele === RequestStatus.CANCELLING ||
          ele === RequestStatus.REJECTED,
      )
    ) {
      overallStatus = RequestStatus.CANCELLED;
    } else if (
      relevantStatuses.every(
        (ele) =>
          ele === RequestStatus.COMPLETED_EXTERNALLY ||
          ele === RequestStatus.CANCELLED_EXTERNALLY,
      )
    ) {
      overallStatus = RequestStatus.COMPLETED_EXTERNALLY;
    } else if (
      relevantStatuses.every(
        (ele) =>
          ele === RequestStatus.COMPLETED_EXTERNALLY ||
          ele === RequestStatus.CANCELLED_EXTERNALLY ||
          ele === RequestStatus.CANCELLED ||
          ele === RequestStatus.CANCELLING ||
          ele === RequestStatus.CLOSED_MANUALLY ||
          ele === RequestStatus.REJECTED,
      )
    ) {
      overallStatus = RequestStatus.PARTIAL_SUCCESS;
    } else if (
      relevantStatuses.every(
        (ele) =>
          ele === RequestStatus.FAILED ||
          ele === RequestStatus.CANCELLED ||
          ele === RequestStatus.CANCELLING ||
          ele === RequestStatus.CLOSED_MANUALLY ||
          ele === RequestStatus.REJECTED ||
          ele === RequestStatus.CANCELLED_EXTERNALLY,
      )
    ) {
      overallStatus = RequestStatus.FAILED;
    } else if (
      relevantStatuses.every(
        (ele) =>
          ele === RequestStatus.SUCCESS ||
          ele === RequestStatus.FAILED ||
          ele === RequestStatus.CANCELLED ||
          ele === RequestStatus.CANCELLING ||
          ele === RequestStatus.CLOSED_MANUALLY ||
          ele === RequestStatus.REJECTED ||
          ele === RequestStatus.COMPLETED_EXTERNALLY ||
          ele === RequestStatus.CANCELLED_EXTERNALLY,
      )
    ) {
      overallStatus = RequestStatus.PARTIAL_SUCCESS;
    } else {
      return;
    }

    return overallStatus;
  }

  getOverAllApprovalStatus(subRequestsApprovalStatuses: ApprovalStatus[]) {
    let overallApprovalStatus: ApprovalStatus;
    this.logger.log(
      'Subrequests approval status: ',
      subRequestsApprovalStatuses,
    );

    if (subRequestsApprovalStatuses.length) {
      //for red aps, unknown, corporate at the time of creation status will be NA
      if (
        subRequestsApprovalStatuses.some(
          (status) => status === ApprovalStatus.NA,
        )
      ) {
        overallApprovalStatus = ApprovalStatus.NA;
      } else if (
        subRequestsApprovalStatuses.some(
          (status) => status === ApprovalStatus.PENDING,
        )
      ) {
        overallApprovalStatus = ApprovalStatus.PENDING;
      } else if (
        subRequestsApprovalStatuses.every(
          (status) => status === ApprovalStatus.AUTO_APPROVED,
        )
      ) {
        overallApprovalStatus = ApprovalStatus.AUTO_APPROVED;
      } else if (
        subRequestsApprovalStatuses.every(
          (status) =>
            status === ApprovalStatus.APPROVED ||
            status === ApprovalStatus.AUTO_APPROVED,
        )
      ) {
        overallApprovalStatus = ApprovalStatus.APPROVED;
      } else if (
        subRequestsApprovalStatuses.every(
          (status) => status === ApprovalStatus.REJECTED,
        )
      ) {
        overallApprovalStatus = ApprovalStatus.REJECTED;
      } else if (
        subRequestsApprovalStatuses.every(
          (status) =>
            status === ApprovalStatus.REJECTED ||
            status === ApprovalStatus.AUTO_APPROVED,
        )
      ) {
        overallApprovalStatus = ApprovalStatus.REJECTED;
      } else if (
        subRequestsApprovalStatuses.every(
          (status) =>
            status === ApprovalStatus.REJECTED ||
            status === ApprovalStatus.APPROVED ||
            status === ApprovalStatus.AUTO_APPROVED,
        )
      ) {
        overallApprovalStatus = ApprovalStatus.PARTIALLY_APPROVED;
      } else {
        overallApprovalStatus = ApprovalStatus.NA;
      }
    } else {
      overallApprovalStatus = ApprovalStatus.NA;
    }

    return overallApprovalStatus;
  }

  async closeTufinTicket(tufinTicketId: string) {
    //call tufin service to close ticket.
    try {
      this.logger.log(`call tufin service to close ticket ${tufinTicketId}`);
      const tufinTicketStatusResponse = await withResponseErrorHandler(
        this.tufinServiceApi.put(`firewall/tufin/close/${tufinTicketId}`),
      );

      this.logger.log(
        `Tufin api response for ticket details: ${JSON.stringify(tufinTicketStatusResponse)}`,
      );
      return tufinTicketStatusResponse;
    } catch (error) {
      this.logger.error(
        error,
        `Error while closing tufin ticket ${tufinTicketId}`,
      );
      throw new InternalServerErrorException(
        'error while closing tufin ticket',
        error,
      );
    }
  }

  async addToMigrationQueue(data: FirwallV1MigrateDto[]) {
    this.logger.log(`Add to migration queue ${data}`);
    await this.rmqService.pushMessage(RequestType.FIREWALL_V1_MIGRATE, {
      id: null,
      payload: data,
      serviceRequestId: null,
    });
  }

  async migrateFirewallV1toV2(requestId) {
    try {
      let createFirewallRequestDto;
      const dataFetchedFromDb = await this.assetsService.get(requestId);
      this.logger.log('Data fetched from DB', dataFetchedFromDb);

      createFirewallRequestDto = dataFetchedFromDb.payload;

      this.logger.log(
        'business date',
        createFirewallRequestDto.businessRequestDate,
      );
      createFirewallRequestDto.businessRequestDate &&
        this.validateFirewallV1BusinessDate(
          createFirewallRequestDto.businessRequestDate,
        );

      this.logger.log(
        'v1 firewall rules',
        createFirewallRequestDto.firewallRules,
      );
      const downstreamResponseData = dataFetchedFromDb?.downstreamResponseData;

      // converting and validating v1 payload to v2
      const validFirewallRequest = await this.validateFirewallv1Rule(
        createFirewallRequestDto,
      );
      this.logger.log('valid firewallRequest or not ', validFirewallRequest);
      this.logger.log(
        'converted v2 firewall payload',
        createFirewallRequestDto,
      );

      if (
        validFirewallRequest == true &&
        dataFetchedFromDb?.status !=
          (RequestStatus.MIGRATED || RequestStatus.PARTIALLY_MIGRATED)
      ) {
        const project = this.configService.get(
          ENVIRONMENT_VARS.MIGRATED_V1_PROJECT,
        );
        //Replacing special characters in projectName with space - For Tufin restriction
        createFirewallRequestDto.projectName =
          createFirewallRequestDto.projectName?.replace(/[^\w\s]/gi, ' ');
        createFirewallRequestDto['summary'] =
          `Firewall Request - ${createFirewallRequestDto.projectName}`;
        createFirewallRequestDto['userName'] =
          createFirewallRequestDto.userName;
        createFirewallRequestDto['description'] =
          `New Firewall Request - ${createFirewallRequestDto.projectName} - Opened by - ${createFirewallRequestDto.projectCreator}`;
        createFirewallRequestDto['netopsaskTicket'] =
          createFirewallRequestDto.netopsaskTicket;
        createFirewallRequestDto['firewallV1RequestId'] = requestId;
        createFirewallRequestDto['deeplinkUrl'] = this.configService.get(
          ENVIRONMENT_VARS.MIGRATED_V2_DEEPLINK_URL,
        );
        if (project) {
          createFirewallRequestDto['nebulaProject'] = project;
        }
        createFirewallRequestDto['jiraIssueLink'] = [];
        if (downstreamResponseData) {
          createFirewallRequestDto['jiraIssueLink'].push(
            downstreamResponseData['key'],
          );
        }

        const serviceRequest = {
          metadata: {
            serviceCatalog: {
              catalogName: ServiceCatalogName.FIREWALL_V2,
              catalogType: ServiceCatalogType.NAAS,
            },
          },
          requestType: RequestType.FIREWALL_V2,
          status: RequestStatus.CREATED,
          approvalStatus: ApprovalStatus.NA,
          payload: createFirewallRequestDto,
          createdBy: dataFetchedFromDb.createdBy,
          startedAt: dataFetchedFromDb.startedAt,
          requesterEmail: dataFetchedFromDb.requesterEmail,
          createdAt: dataFetchedFromDb.createdAt,
        };

        const dbResponse = await this.assetsService.create(serviceRequest);

        this.logger.log('Response DB ', dbResponse);
        if (createFirewallRequestDto?.firewallRules.ipv6) {
          const storageResponse = await this.createExcelAndStoragePayload(
            createFirewallRequestDto,
            dbResponse,
            serviceRequest,
          );
          this.logger.log('final payload', createFirewallRequestDto);
          this.logger.log('Ipv6storageResponse ', storageResponse);
        }

        this.logger.log(`Pushing service request ${dbResponse.id} to queue`);
        await this.rmqService.pushMessage(
          dbResponse.requestType,
          {
            id: dbResponse.id,
            payload: createFirewallRequestDto,
            serviceRequestId: dbResponse.serviceRequestId,
          },
          EventPatterns.FIREWALL_V2_CREATED,
        );

        this.logger.log(
          'Adding firewall v2 service request id to Firewall v1 service request ',
        );

        return {
          id: dbResponse.id,
          serviceRequestId: dbResponse?.serviceRequestId,
          message: `Request submitted for processing`,
        };
      } else {
        throw new BadRequestException(
          'Request could not be submitted due to invalid firewall rules or already migrated or partially migrated',
        );
      }
    } catch (error) {
      this.logger.error(error, 'Failed to migrate firewall v1 request');
      await this.updateFirewallV1Migratation(
        requestId,
        null,
        FirewallV1MigrationStatus.FAILED,
      );
      throw new BadRequestException(error);
    }
  }

  async updateFirewallV1Migratation(
    serviceRequestIdV1,
    serviceRequestIdV2,
    status,
    requestStatus?,
  ) {
    this.logger.log(
      `Update firewall v1 migration ${serviceRequestIdV1}  ${serviceRequestIdV2} ${status}`,
    );
    let firewallV1UpdateDoc;
    if (requestStatus) {
      firewallV1UpdateDoc = {
        status: requestStatus,
        systemUpdate: {
          FirewallV2ServiceRequestId: serviceRequestIdV2,
          migrationStatus: status,
        },
      };
    } else {
      firewallV1UpdateDoc = {
        systemUpdate: {
          FirewallV2ServiceRequestId: serviceRequestIdV2,
          migrationStatus: status,
        },
      };
    }
    this.logger.log(
      'Adding firewall v2 service request id to Firewall v1 service request ',
    );
    await this.assetsService.update(serviceRequestIdV1, firewallV1UpdateDoc);
  }

  async uploadToStorage(serviceRequest, dbResponse, uploadedData, createdBy?) {
    this.logger.log(
      `Upload to storage ${serviceRequest}  ${dbResponse} ${uploadedData}`,
    );
    const storageRequest = {
      requestType: serviceRequest.requestType,
      requestId: dbResponse.id,
      fileType: 'xlsx',
      ...uploadedData,
      createdBy: createdBy,
    };
    const result = await this.storageRepository.create(storageRequest);
    this.logger.log(`Upload to storage result ${result}`);
    return result;
  }

  async createExcelAndStoragePayload(
    createFirewallRequestDto,
    dbResponse,
    serviceRequest,
  ) {
    this.logger.log(
      `Create excel and storage payload ${createFirewallRequestDto}  ${dbResponse} ${serviceRequest}`,
    );
    const RequestDto = {};
    RequestDto['date'] = createFirewallRequestDto.date;
    RequestDto['projectName'] = createFirewallRequestDto.projectName;
    RequestDto['projectCreator'] = createFirewallRequestDto.projectCreator;
    RequestDto['appId'] = createFirewallRequestDto.appId;
    RequestDto['firewallRules'] =
      createFirewallRequestDto.firewallRules.ipv6.firewallRules;
    const fileResponse = await createRulesExcelForIpv6(RequestDto);
    this.logger.debug('Rules excel for Ipv6 ', fileResponse);
    const uploadedData = await this.uploadV2FileToStorage(fileResponse);
    this.logger.log(`Uploaded data ${uploadedData}`);
    createFirewallRequestDto.firewallRules.ipv6['storage'] = {
      bucketName: uploadedData.bucketName,
      fileName: uploadedData.fileName,
      filePath: uploadedData.filePath,
    };
    const storageResponse = await this.uploadToStorage(
      serviceRequest,
      dbResponse,
      uploadedData,
      dbResponse.createdBy,
    );
    this.logger.log(`Storage response ${storageResponse}`);
    return storageResponse;
  }

  async validateFirewallv1Rule(createFirewallRequestDto) {
    this.logger.log(`Validate firewall v1 rule ${createFirewallRequestDto}`);
    const protocolConfig =
      await this.nebulaConfigRepository.getNebulaConfigByType(
        NebulaConfigMap.FIREWALL_V2,
      );
    const validatedRules = validateFirewallRules(
      createFirewallRequestDto.firewallRules,
      protocolConfig?.config,
    );
    let validFirewallRequest = true;
    validatedRules.forEach((rule) => {
      if (rule.valid == false) {
        validFirewallRequest = false;
      }
    });
    this.logger.debug('valid or not ', validFirewallRequest);
    if (validFirewallRequest == true) {
      this.logger.log('validated rules', validatedRules);
      const ipv4Array = [];
      const ipv6Array = [];
      validatedRules.forEach((rule) => {
        this.logger.log('ipversion', rule.ipVersion);
        if (rule.ipVersion == 'v4') {
          ipv4Array.push(rule);
        } else if (rule.ipVersion == 'v6') {
          ipv6Array.push(rule);
        }
      });
      createFirewallRequestDto.firewallRules = {
        ipv4: { firewallRules: ipv4Array },
        ipv6: { firewallRules: ipv6Array },
      };
    }

    return validFirewallRequest;
  }
  async validateFirewallV1BusinessDate(data) {
    this.logger.log(`Validate firewall v1 Business date ${data}`);
    if (
      data.businessRequestDate &&
      data.businessRequestDate - data.createdAt < 20
    ) {
      this.logger.log(
        'Business Request Date must be at least 20 days from today',
      );
      throw new BadRequestException(
        'Business Request Date must be at least 20 days from today',
      );
    }
  }
  async sendFirewallSubrequestNotification(subRequestId) {
    this.logger.log(`Send firewall subrequest notification ${subRequestId}`);
    try {
      this.logger.log(
        'firewall v2 sub request notification service is running',
      );

      const subRequest =
        await this.firewallRequestRepository.findBySubRequestId(subRequestId);
      const serviceRequest = await this.assetsService.findByServiceRequestId(
        subRequest?.requestId,
      );

      this.logger.log('sub request has fetched successfully', subRequest);
      this.logger.log(`Service request ${serviceRequest}`);

      this.logger.log('Sending subrequest notification');
      await this.statusNotificationService.firewallSubrequestNotification(
        serviceRequest,
        subRequest,
      );

      this.logger.log('sub request notification sent successfully');

      this.logger.log(
        'After sending the sub request notification isSubRequestInValidated set to true',
      );
      if (subRequest.ipType != IPType.IPV6) {
        this.logger.log('Updating isSubRequestInValidated false for ipv6 ');
        await this.update(subRequestId, {
          isSubRequestInValidated: true,
        });
      }

      return { message: 'sub request notification sent successfully' };
    } catch (error) {
      this.logger.error(
        error,
        `error during sending firewall v2 sub request notification for ${subRequestId}`,
      );
      throw new Error(error);
    }
  }

  async getFirewallImpactedDevices(
    impactedDevicesReq: ImpactedDevicesRequestDto,
  ): Promise<ImpactedDevicesResponseDto> {
    this.logger.log(`Get firewall impacted devices ${impactedDevicesReq}`);
    const response = {
      serviceRequestId: impactedDevicesReq.serviceRequestId,
      subRequestId: impactedDevicesReq.subRequestId,
      impactedDevices: [],
    };
    const firewallReqData =
      await this.firewallRequestRepository.findByServiceRequestIdAndSubRequestId(
        impactedDevicesReq.serviceRequestId,
        impactedDevicesReq.subRequestId,
      );
    if (!firewallReqData) {
      this.logger.error(
        `No firewall request details found for serviceRequestId:${impactedDevicesReq.serviceRequestId},
         subRequestId: ${impactedDevicesReq.subRequestId}`,
      );
      throw new NotFoundException(`Firewall request details not found`);
    }
    this.logger.log(`Firewall req ${firewallReqData}`);
    if (
      Array.isArray(firewallReqData.ruleIds) &&
      firewallReqData.ruleIds.length > 0
    ) {
      let tufinDevices =
        await this.pathAnalysisRespository.getTufinDevicesByRuleIds(
          impactedDevicesReq.serviceRequestId,
          firewallReqData.ruleIds,
          impactedDevicesReq.reverse,
        );

      if (impactedDevicesReq.reverse == 'true') {
        tufinDevices = tufinDevices.filter((ele) => {
          if (ele.reversePath) return ele;
        });
        tufinDevices = tufinDevices?.map((ele) => {
          if (ele.reversePath)
            return { ...ele.reversePath, ruleId: ele.ruleId };
        });
        tufinDevices.forEach((device) => {
          if (device?.target?.length) {
            device.target = device?.target?.filter(
              (targetInfo) =>
                targetInfo.organization == firewallReqData.organizationName,
            );
          }
        });
      } else {
        tufinDevices.forEach((device) => {
          device.target = device.target.filter(
            (targetInfo) =>
              targetInfo.organization == firewallReqData.organizationName,
          );
        });
      }

      response.impactedDevices = tufinDevices;
    }
    return response;
  }

  async resetTufinWorkflowData(subRequestId: string): Promise<{
    statusCode: number;
    message: string;
  }> {
    try {
      this.logger.debug(
        `Going to reset tufin workflow data for subRequestId: ${subRequestId}`,
      );
      const updateBody = {
        'ticketDetails.tufin.riskAnalysis': [],
        'ticketDetails.tufin.designerResults': null,
        'ticketDetails.tufin.riskAnalysisUpdated': false,
        'ticketDetails.tufin.designerResultsUpdated': false,
        status: RequestStatus.PENDING_RISK_ANALYSIS,
        approvalStatus: ApprovalStatus.NA,
      };
      const condition = {
        subRequestId: subRequestId,
        'ticketDetails.tufin.ticketId': { $exists: true },
      };
      await this.firewallRequestRepository.updateFirewallRequest(
        subRequestId,
        updateBody,
        condition,
      );
      this.logger.log(
        `Tufin workflow data reset done for subRequestId: ${subRequestId}`,
      );
      return {
        statusCode: HttpStatus.OK,
        message: 'Success',
      };
    } catch (error) {
      this.logger.error(
        error,
        `Error while doing Tufin workflow data reset for subRequestId: ${subRequestId}`,
      );
      throw new InternalServerErrorException(
        `Error while doing Tufin workflow data reset. ${error?.message}`,
      );
    }
  }

  async resetServiceRequestApproval(
    organization,
    serviceRequest: ServiceRequestEntity,
  ) {
    try {
      this.logger.debug(
        `Going to reset approval and status for service request: ${serviceRequest.serviceRequestId}`,
      );

      //get approval group using organization
      const subTypeApprovals =
        await this.subTypeApprovalsService.findOneByRequestType(
          RequestType.FIREWALL_V2,
        );
      this.logger.log(`Subtype approvals`, subTypeApprovals);

      //get group name for reflow organziation
      const reflowedSubRequestGroupNames: string[] =
        subTypeApprovals.subTypeToGroupMap.find(
          (approval) => approval.subType === organization,
        ).groups;

      //
      const updatedApprovals = serviceRequest.approvalDetails.filter(
        (approval) =>
          !reflowedSubRequestGroupNames.includes(approval.approvalGroup),
      );

      const updateBody = {
        approvalDetails: updatedApprovals,
      };

      await this.assetsService.update(
        serviceRequest.serviceRequestId,
        updateBody,
      );
      this.logger.log(
        `Tufin workflow data reset done for service request : ${serviceRequest.serviceRequestId}`,
      );
      return {
        statusCode: HttpStatus.OK,
        message: 'Success',
      };
    } catch (error) {
      this.logger.error(
        error,
        `Error while doing Tufin workflow data reset for service request: ${serviceRequest.serviceRequestId}`,
      );
      throw new InternalServerErrorException(
        `Error while doing Tufin workflow data reset for service request. ${error?.message}`,
      );
    }
  }

  async createAndGetFirewallRuleFile(id: string) {
    const initialSearchResponse = await this.getFirewallRulesFile(id);

    if (initialSearchResponse == false) {
      let createFirewallRequestDto;
      const dataFetchedFromDb = await this.assetsService.get(id);
      this.logger.log('Data fetched from DB', dataFetchedFromDb.payload);

      createFirewallRequestDto = dataFetchedFromDb.payload;

      //create all rules excel
      const serviceRequest = {
        requestType: RequestType.FIREWALL_V2,
      };
      const dbResponse = {
        serviceRequestId: id,
      };
      const responseFromAllRulesExcelCreation =
        await this.createAndUploadAllRulesExcel(
          createFirewallRequestDto,
          serviceRequest,
          dbResponse,
        );
      this.logger.debug(
        'Response after creation and uploading of all rules excel',
        responseFromAllRulesExcelCreation,
      );
      //create all rules excel completed and uploaded
      const searchResponse = await this.getFirewallRulesFile(id);
      if (searchResponse == false) {
        this.logger.error(`File not uploaded for the given id ${id}`);
        throw new BadRequestException(
          `File not uploaded for the given id ${id}`,
        );
      }
      return searchResponse;
    } else {
      return initialSearchResponse;
    }
  }

  async getFirewallRulesFile(id: string) {
    let flag = false;
    const initialStorageEntities =
      await this.storageRepository.findV2RuleStorageByRequestId(id);
    for (const storageEntity of initialStorageEntities) {
      this.logger.log('Storge Entity', storageEntity);
      const fileNameArray = storageEntity.filePath.split('/');
      if (storageEntity && fileNameArray.includes('allRules')) {
        const filePath = storageEntity.filePath;
        const bucketName = storageEntity.bucketName;
        const fileName = storageEntity.fileName;
        this.logger.debug(
          `storage location bucket:${bucketName}, file:${fileName}, path:${filePath} `,
        );
        const objectStorageDto: ObjectStorageDto = {
          bucketName,
          fileName,
          filePath,
        };
        flag = true;
        return this.objectStorageService.download(objectStorageDto);
      }
    }
    if (flag == false) {
      return false;
    }
  }

  async reflowTufinTask(subRequestId: string): Promise<TufinTaskResponseDto> {
    const req = RequestContext?.currentContext?.req;
    try {
      const subRequest =
        await this.firewallRequestRepository.findBySubRequestId(subRequestId);
      if (!subRequest) {
        this.logger.error(`No subRequest  found for the id:${subRequestId}`);
        throw new NotFoundException(
          `subRequest doesn't exists with ${subRequestId}`,
        );
      }
      this.logger.log(`Subrequest`, subRequest);

      const serviceRequest = await this.assetsService.findByGenericRequestId(
        subRequest.requestId,
      );
      if (!serviceRequest) {
        this.logger.error(
          `No service request  found for the id:${subRequest.requestId}`,
        );
        throw new NotFoundException(
          `service request doesn't exists with ${subRequest.requestId}`,
        );
      }

      const tufinTicketId = subRequest?.ticketDetails?.tufin?.ticketId;
      const tufinStatus = await withResponseErrorHandler(
        this.tufinServiceApi.get(`firewall/ticket/${tufinTicketId}`),
      );
      this.logger.log(`Tufin status`, tufinStatus);
      if (
        tufinStatus?.status === TUFIN_TICKET_STATUS.TICKET_CLOSED ||
        tufinStatus?.status === TUFIN_TICKET_STATUS.TICKET_CANCELLED
      ) {
        this.logger.log(
          `tufin ticket status ${tufinStatus?.status}, reflow cannot happen for closed/ cancelled tufin tickets`,
        );
        throw new BadRequestException(
          `Tufin with status Ticket Closed/ Cancelled cannot be reflowed`,
        );
      }
      //reset firewall request collections workflow data before reflow
      await this.resetTufinWorkflowData(subRequestId);

      //rest status and approval for the service request using subrequest org
      await this.resetServiceRequestApproval(
        subRequest.organizationName,
        serviceRequest,
      );

      try {
        //Get tufin ticket reflow from tufin service.
        this.logger.log(
          `reflow tufin ticket from tufin service. ${tufinTicketId}`,
        );
        const tufinTicketReflowResponse = await withResponseErrorHandler(
          this.tufinServiceApi.get(`firewall/reflow/${tufinTicketId}`),
        );

        this.logger.log(
          `Tufin api response for reflow details: ${JSON.stringify(tufinTicketReflowResponse)}`,
        );

        if (!tufinTicketReflowResponse || !tufinTicketReflowResponse.status) {
          this.logger.error(
            `Invalid tufin ticket response. Unable to update tufin ticket status for ${tufinTicketId}`,
          );
        }
      } catch (err) {
        //update subrequest as failed
        await this.update(subRequestId, { status: RequestStatus.FAILED });

        //update service request according to subrequests
        await this.updateServiceRequestStatus(serviceRequest.serviceRequestId);

        this.logger.error(
          err.stack,
          `Exception while tufin tasks reflow for subreq: ${subRequestId}.`,
        );
        throw new InternalServerErrorException(err);
      }

      await this.firewallRequestRepository.updateReflowHistory(
        {
          reflowedAt: new Date(),
          reflowedBy: req?.user?.userId,
          reflowedStatus: JobStatusEnum.SUCCESS,
        },
        subRequestId,
      );

      return {
        status: 'success',
        message: 'Tufin task reflow started successfully',
      };
    } catch (error) {
      this.logger.error(
        error.stack,
        `Exception while tufin tasks reflow for subreq: ${subRequestId}.`,
      );
      await this.firewallRequestRepository.updateReflowHistory(
        {
          reflowedAt: new Date(),
          reflowedBy: req?.user?.userId,
          reflowedStatus: JobStatusEnum.FAILURE,
        },
        subRequestId,
      );
      throw new BadRequestException(
        `Error while reflowing tufin ticket. ${error?.message}. Please check logs for more details`,
      );
    }
  }

  async subRequestDFMDownload(storagePayload) {
    this.logger.log(
      'Downloading file from storage with payload',
      storagePayload,
    );
    const image = await this.storageService.download(storagePayload);
    return image;
  }

  async retryCherwellTicketOperation(data: GenericRetryTicketRequestDto) {
    let retryCreateData;
    let result = {
      status: '',
      ticketId: '',
      metadata: { ticketUrl: '' },
      message: '',
    };
    if (data.action === RETRY_TICKET_ACTIONS.CREATED) {
      try {
        retryCreateData = await this.buildTicketPayload(data);
        this.logger.log(
          `Cherwell - Resubmitting ${data.ticketingSystem} ticket creation request`,
        );
        const retryResult =
          await this.ticketManagementWrapperService.retryTicket(
            retryCreateData,
          );
        result = retryResult;
        if (result?.status === RETRY_TICKET_STATUS.SUCCESS) {
          result.status = RETRY_TICKET_STATUS.CREATED;
          result.message = result.message.toString();
          this.logger.log(
            `Cherwell - Updating ${data.ticketingSystem} ticket status in firewallRequest repository for subRequestId: ${data.subRequestId}`,
          );
          try {
            await this.updateCherwellTicketStatus({
              subRequestId: data.subRequestId,
              status: RETRY_TICKET_STATUS.CREATED,
              ticketId: result.ticketId,
              ticketUrl: result.metadata?.ticketUrl,
              error: [],
            });
          } catch (updateError) {
            this.logger.error(
              updateError,
              `Cherwell - Failed to update ticket status after successful creation retry for subRequestId: ${data.subRequestId}`,
            );
          }
        }
      } catch (retryError) {
        this.logger.error(
          retryError,
          `Cherwell - Ticket creation retry failed for subRequestId: ${data.subRequestId}`,
        );
        retryError.response.status = RETRY_TICKET_STATUS.CREATE_FAILED;
        try {
          await this.updateCherwellTicketStatus({
            subRequestId: data.subRequestId,
            status: RETRY_TICKET_STATUS.CREATE_FAILED,
            error: [
              {
                errorCode: retryError?.status,
                errorMessage: retryError?.message,
              },
            ],
          });
        } catch (updateError) {
          this.logger.error(
            updateError,
            `Cherwell - Failed to update ticket status after failed creation retry for subRequestId: ${data.subRequestId}`,
          );
        }
        throw retryError;
      }
    } else if (
      data.action === RETRY_TICKET_ACTIONS.CLOSED ||
      data.action === RETRY_TICKET_ACTIONS.CANCELLED
    ) {
      try {
        this.logger.log(
          `Cherwell - Resubmitting ${data.ticketingSystem} ${data.action} ticket.`,
        );
        const retryResult =
          await this.ticketManagementWrapperService.retryTicket(data);
        result = retryResult;
        if (result?.status === RETRY_TICKET_STATUS.SUCCESS) {
          result.status = data.action;
          result.message = `Ticket ${data.action.toLowerCase()} successfully`;
          this.logger.log(
            `Cherwell - Updating ${data.ticketingSystem} ticket ${data.action} status for subRequestId: ${data.subRequestId}`,
          );
          try {
            await this.updateCherwellTicketStatus({
              subRequestId: data.subRequestId,
              status: data.action,
              error: [],
            });
          } catch (updateError) {
            this.logger.error(
              updateError,
              `Cherwell - Failed to update ticket status after successful ${data.action} retry for subRequestId: ${data.subRequestId}`,
            );
          }
        }
      } catch (retryError) {
        this.logger.error(
          retryError,
          `Cherwell - Ticket ${data.action} retry failed for subRequestId: ${data.subRequestId}`,
        );
        let status = null;
        if (data.action === RETRY_TICKET_ACTIONS.CLOSED) {
          status = RETRY_TICKET_STATUS.CLOSE_FAILED;
          retryError.response.status = RETRY_TICKET_STATUS.CLOSE_FAILED;
        } else {
          status = RETRY_TICKET_STATUS.CANCEL_FAILED;
          retryError.response.status = RETRY_TICKET_STATUS.CANCEL_FAILED;
        }
        this.logger.log(
          `Cherwell - Updating ${data.ticketingSystem} ticket ${status} status using ${data.subRequestId}`,
        );
        try {
          await this.updateCherwellTicketStatus({
            subRequestId: data.subRequestId,
            status: status,
            error: [
              {
                errorCode: retryError?.status,
                errorMessage: retryError?.message,
              },
            ],
          });
        } catch (updateError) {
          this.logger.error(
            updateError,
            `Cherwell - Failed to update ticket status after failed ${data.action} retry for subRequestId: ${data.subRequestId}`,
          );
        }
        throw retryError;
      }
    } else {
      this.logger.error(
        `Cherwell - Incorrect retry operation on ${data.ticketingSystem} ticket: ${data.action}`,
      );
      throw new Error(`Incorrect retry operation: ${data.action}`);
    }
    return result;
  }

  async retryRemedyTicketOperation(data: GenericRetryTicketRequestDto) {
    let retryCreateData: GenericRetryTicketRequestDto;
    let result = {
      status: '',
      ticketId: '',
      metadata: { ticketUrl: '' },
      message: '',
    };

    if (data.action === RETRY_TICKET_ACTIONS.CREATED) {
      try {
        retryCreateData = await this.buildTicketPayload(data);
        this.logger.log(
          `Remedy - Resubmitting ${data.ticketingSystem} ticket creation request`,
        );
        const retryResult =
          await this.ticketManagementWrapperService.retryTicket(
            retryCreateData,
          );
        result = retryResult;
        this.logger.log(
          `Remedy - Retry ${data.ticketingSystem} ticket creation response: ${JSON.stringify(result)}`,
        );
        this.logger.log(
          `Remedy - Updating Remedy ticket status for serviceRequestId: ${data.serviceRequestId}`,
        );
        const getAllSubrequests =
          await this.firewallRequestRepository.getFirewallTicketDetails(
            data.serviceRequestId,
          );
        const getRedApsSubRequests = getAllSubrequests
          .filter(
            (subrequest) =>
              subrequest.organizationName === OrganizationName.RED_APS &&
              subrequest.ticketDetails.tufin,
          )
          .map((request) => request.subRequestId);
        if (result?.status === RETRY_TICKET_STATUS.SUCCESS) {
          try {
            const remedyMopId = result?.ticketId;
            result.ticketId = '';
            result.status = RETRY_TICKET_STATUS.IN_PROGRESS;
            result.message = `Ticket creation is ${RETRY_TICKET_STATUS.IN_PROGRESS.toLowerCase()}`;
            await this.updateRemedyAndServiceRequestStatus({
              subRequestId: getRedApsSubRequests,
              status: RETRY_TICKET_STATUS.IN_PROGRESS,
              ticketUrl: result.metadata?.ticketUrl,
              mopId: remedyMopId,
              error: [],
            });
          } catch (updateError) {
            this.logger.error(
              updateError,
              `Remedy - Failed to update status after successful creation retry for serviceRequestId: ${data.serviceRequestId}`,
            );
          }
        }
      } catch (retryError) {
        this.logger.error(
          retryError,
          `Remedy - Error during ${data.ticketingSystem} ticket creation retry for ${data.serviceRequestId}: ${retryError?.message} ${retryError?.stack}`,
        );
        retryError.response.status = RETRY_TICKET_STATUS.FAILED;
        this.logger.log(
          `Remedy - Updating ${data.ticketingSystem} with ${RETRY_TICKET_STATUS.CREATE_FAILED} status `,
        );
        try {
          const getAllSubrequests =
            await this.firewallRequestRepository.getFirewallTicketDetails(
              data.serviceRequestId,
            );
          const getRedApsSubRequests = getAllSubrequests
            .filter(
              (subrequest) =>
                subrequest.organizationName === OrganizationName.RED_APS &&
                subrequest.ticketDetails.tufin,
            )
            .map((request) => request.subRequestId);
          await this.updateRemedyAndServiceRequestStatus({
            subRequestId: getRedApsSubRequests,
            status: RETRY_TICKET_STATUS.CREATE_FAILED,
            error: [
              {
                errorCode: retryError?.status,
                errorMessage: retryError?.message,
              },
            ],
          });
        } catch (updateError) {
          this.logger.error(
            updateError,
            `Remedy - Failed to update status after failed creation retry for serviceRequestId: ${data.serviceRequestId}`,
          );
        }
        return retryError?.response;
      }
    } else if (data.action === RETRY_TICKET_ACTIONS.CANCELLED) {
      let subrequestPayload;
      try {
        subrequestPayload =
          await this.firewallRequestRepository.findBySubRequestId(
            data.subRequestId,
          );
        const getAllSubrequests =
          await this.firewallRequestRepository.getFirewallTicketDetails(
            data.serviceRequestId,
          );
        if (!subrequestPayload?.ticketDetails?.remedy?.mopId) {
          this.logger.error(
            `Remedy - mopId not found for subRequestId: ${data.subRequestId} during cancel retry.`,
          );
          return {
            ...result,
            status: RequestStatus.FAILED,
            error: [{ errorMessage: 'mopId not found' }],
          };
        }
        data.ticketId = subrequestPayload.ticketDetails.remedy.mopId;

        const getRedApsSubRequests = getAllSubrequests.filter(
          (subrequest) =>
            subrequest.organizationName === OrganizationName.RED_APS,
        );
        let areAllTufinTicketsCancelled = null;
        for (const subRequest of getRedApsSubRequests) {
          const tufinStatus = await withResponseErrorHandler(
            this.tufinServiceApi.get(
              `firewall/ticket/${subRequest.ticketDetails?.tufin?.ticketId}`,
            ),
          );
          if (tufinStatus.status === 'Ticket Cancelled') {
            areAllTufinTicketsCancelled = true;
          } else {
            areAllTufinTicketsCancelled = false;
            break;
          }
        }
        if (areAllTufinTicketsCancelled) {
          try {
            this.logger.log(
              `Remedy - Resubmitting ${data.ticketingSystem} ${data.action} ticket.`,
            );
            const retryResult =
              await this.ticketManagementWrapperService.retryTicket(data);
            result = retryResult;
            if (result?.status === RETRY_TICKET_STATUS.SUCCESS) {
              result.status = RETRY_TICKET_STATUS.CANCELLED;
              result.ticketId = subrequestPayload.ticketDetails.remedy.ticketId;
              result.message = 'Ticket cancelled successfully';
              this.logger.log(
                `Remedy - Updating ${data.ticketingSystem} ticket ${data.action} status for serviceRequestId: ${data.serviceRequestId}`,
              );
              const getRedApsSubRequestIds = getAllSubrequests
                .filter(
                  (subrequest) =>
                    subrequest.organizationName === OrganizationName.RED_APS &&
                    subrequest.ticketDetails.tufin,
                )
                .map((request) => request.subRequestId);
              try {
                await this.updateRemedyAndServiceRequestStatus({
                  subRequestId: getRedApsSubRequestIds,
                  status: data.action,
                  error: [],
                });
              } catch (updateError) {
                this.logger.error(
                  updateError,
                  `Remedy - Failed to update status after successful ${data.action} retry for serviceRequestId: ${data.serviceRequestId}`,
                );
              }
            }
          } catch (retryError) {
            this.logger.error(
              retryError,
              `Remedy - Ticket ${data.action} retry failed for serviceRequestId: ${data.serviceRequestId}`,
            );
            const status = RETRY_TICKET_STATUS.CANCEL_FAILED;
            retryError.response.status = RETRY_TICKET_STATUS.CANCEL_FAILED;
            this.logger.log(
              `Remedy - Updating ${data.ticketingSystem} ticket ${status} status using ${data.serviceRequestId}`,
            );
            try {
              const getAllSubrequests =
                await this.firewallRequestRepository.getFirewallTicketDetails(
                  data.serviceRequestId,
                );
              const getRedApsSubRequests = getAllSubrequests
                .filter(
                  (subrequest) =>
                    subrequest.organizationName === OrganizationName.RED_APS &&
                    subrequest.ticketDetails.tufin,
                )
                .map((request) => request.subRequestId);
              await this.updateRemedyAndServiceRequestStatus({
                subRequestId: getRedApsSubRequests,
                status: status,
                error: [
                  {
                    errorCode: retryError?.status,
                    errorMessage: retryError?.message,
                  },
                ],
              });
            } catch (updateError) {
              this.logger.error(
                updateError,
                `Remedy - Failed to update status after failed ${data.action} retry for serviceRequestId: ${data.serviceRequestId}`,
              );
            }
            return retryError?.response;
          }
        } else {
          this.logger.error(
            `The remaining tufin tickets in red_aps should be cancelled in order to cancel remedy ticket in serviceRequestId: ${data.serviceRequestId}`,
          );
          throw new BadRequestException(
            `The remaining tufin tickets in red_aps should be cancelled in order to cancel remedy ticket in serviceRequestId: ${data.serviceRequestId}`,
          );
        }
      } catch (retryError) {
        this.logger.error(
          retryError,
          `Remedy - Retry to cancel ticket failed for subRequestId: ${data.subRequestId}`,
        );
        retryError.response.status = RETRY_TICKET_STATUS.CANCEL_FAILED;
        return retryError.response;
      }
    } else if (data.action === RETRY_TICKET_ACTIONS.CLOSED) {
      let subrequestPayload;
      try {
        const getAllSubrequests =
          await this.firewallRequestRepository.getFirewallTicketDetails(
            data.serviceRequestId,
          );
        const getAllRedApsRequests = getAllSubrequests.filter(
          (request) => request.organizationName === OrganizationName.RED_APS,
        );
        subrequestPayload =
          await this.firewallRequestRepository.findBySubRequestId(
            data.subRequestId,
          );
        let areAllTufinTicketsClosed = null;
        for (const subRequest of getAllRedApsRequests) {
          const tufinStatus = await withResponseErrorHandler(
            this.tufinServiceApi.get(
              `firewall/ticket/${subRequest.ticketDetails?.tufin?.ticketId}`,
            ),
          );
          if (tufinStatus.status === TUFIN_TICKET_STATUS.TICKET_CLOSED) {
            areAllTufinTicketsClosed = true;
          } else {
            areAllTufinTicketsClosed = false;
            break;
          }
        }
        if (subrequestPayload?.organizationName !== OrganizationName.RED_APS) {
          this.logger.error(
            `the CRQ ticket cannot be closed since the ticket doesnot belong to red-aps organization in serviceRequestId:${data.serviceRequestId}`,
          );
          throw new BadRequestException(
            `the CRQ ticket cannot be closed since the ticket doesnot belong to red-aps organization in serviceRequestId:${data.serviceRequestId}`,
          );
        }
        if (areAllTufinTicketsClosed) {
          data.ticketId = subrequestPayload.ticketDetails.remedy.mopId;
          try {
            this.logger.log(
              `Remedy - Resubmitting ${data.ticketingSystem} ${data.action} ticket.`,
            );
            const retryResult =
              await this.ticketManagementWrapperService.retryTicket(data);
            result = retryResult;
            if (result?.status === RETRY_TICKET_STATUS.SUCCESS) {
              result.status = RETRY_TICKET_STATUS.CLOSED;
              result.ticketId = subrequestPayload.ticketDetails.remedy.ticketId;
              result.message = 'Ticket closed successfully';
              this.logger.log(
                `Remedy - Updating ${data.ticketingSystem} ticket ${data.action} status for serviceRequestId: ${data.serviceRequestId}`,
              );
              const getRedApsSubRequestIds = getAllSubrequests
                .filter(
                  (subrequest) =>
                    subrequest.organizationName === OrganizationName.RED_APS &&
                    subrequest.ticketDetails.tufin,
                )
                .map((request) => request.subRequestId);
              try {
                await this.updateRemedyAndServiceRequestStatus({
                  subRequestId: getRedApsSubRequestIds,
                  status: data.action,
                  error: [],
                });
              } catch (updateError) {
                this.logger.error(
                  updateError,
                  `Remedy - Failed to update status after successful ${data.action} retry for serviceRequestId: ${data.serviceRequestId}`,
                );
              }
            }
          } catch (retryError) {
            this.logger.error(
              retryError,
              `Remedy - Ticket ${data.action} retry failed for serviceRequestId: ${data.serviceRequestId}`,
            );
            const status = RETRY_TICKET_STATUS.CLOSE_FAILED;
            retryError.response.status = RETRY_TICKET_STATUS.CLOSE_FAILED;
            this.logger.log(
              `Remedy - Updating ${data.ticketingSystem} ticket ${status} status using ${data.subRequestId}`,
            );
            try {
              const getAllSubrequests =
                await this.firewallRequestRepository.getFirewallTicketDetails(
                  data.serviceRequestId,
                );
              const getRedApsSubRequests = getAllSubrequests
                .filter(
                  (subrequest) =>
                    subrequest.organizationName === OrganizationName.RED_APS &&
                    subrequest.ticketDetails.tufin,
                )
                .map((request) => request.subRequestId);
              await this.updateRemedyAndServiceRequestStatus({
                subRequestId: getRedApsSubRequests,
                status: status,
                error: [
                  {
                    errorCode: retryError?.status,
                    errorMessage: retryError?.message,
                  },
                ],
              });
            } catch (updateError) {
              this.logger.error(
                updateError,
                `Remedy - Failed to update status after failed ${data.action} retry for serviceRequestId: ${data.serviceRequestId}`,
              );
            }
            return retryError?.response;
          }
        } else {
          this.logger.error(
            `The remaining tufin tickets in red_aps should be closed in order to close remedy ticket in serviceRequestId: ${data.serviceRequestId}`,
          );
          throw new BadRequestException(
            `The remaining tufin tickets in red_aps should be closed in order to close remedy ticket in serviceRequestId: ${data.serviceRequestId}`,
          );
        }
      } catch (retryError) {
        this.logger.error(
          retryError,
          `Remedy - Retry to close ticket failed for serviceRequestId: ${data.serviceRequestId}`,
        );
        retryError.response.status = RETRY_TICKET_STATUS.CLOSE_FAILED;
        return retryError.response;
      }
    } else {
      this.logger.error(
        `Remedy - Incorrect retry operation on ${data.ticketingSystem} ticket: ${data.action}`,
      );
      return {
        ...result,
        status: RequestStatus.FAILED,
        error: [{ errorMessage: `Incorrect retry operation: ${data.action}` }],
      };
    }
    return result;
  }

  async resubmitTicketOperation(data: GenericRetryTicketRequestDto) {
    this.logger.log(
      `Attempting to resubmit ticket for subRequestId: ${data.subRequestId}, ticketingSystem: ${data.ticketingSystem}, action: ${data.action}`,
      data,
    );

    if (data.ticketingSystem === TicketType.CHERWELL) {
      return this.retryCherwellTicketOperation(data);
    } else if (data.ticketingSystem === TicketType.REMEDY) {
      return this.retryRemedyTicketOperation(data);
    } else {
      this.logger.error(
        `Unsupported ticketing system for retry: ${data.ticketingSystem} for subRequestId: ${data.subRequestId}`,
      );
    }
  }

  async updateRemedyAndServiceRequestStatus(data: TicketStatusUpdateDto) {
    try {
      this.logger.log(
        `Remedy - Updating remedy status ${data.status} in red_aps and servicerequest status`,
      );
      const result = [];
      for (const request of data.subRequestId) {
        if (data.status === RETRY_TICKET_STATUS.CANCELLED) {
          result.push(
            await this.update(request, {
              status: RequestStatus.CANCELLED,
              //@ts-ignore
              'ticketDetails.remedy.status': data.status,
              'ticketDetails.remedy.error': data.error,
            }),
          );
        } else if (data.status === RETRY_TICKET_STATUS.CLOSED) {
          result.push(
            await this.update(request, {
              status: RequestStatus.SUCCESS,
              //@ts-ignore
              'ticketDetails.remedy.status': data.status,
              'ticketDetails.remedy.error': data.error,
            }),
          );
        } else if (data.status === RETRY_TICKET_STATUS.CREATED) {
          result.push(
            await this.update(request, {
              //@ts-ignore
              'ticketDetails.remedy': {
                ticketId: data.ticketId,
                status: data.status,
                ticketUrl: data.ticketUrl,
                mopId: data.mopId,
                error: data.error,
              },
            }),
          );
        } else if (data.status === RETRY_TICKET_STATUS.IN_PROGRESS) {
          result.push(
            await this.update(request, {
              //@ts-ignore
              'ticketDetails.remedy': {
                ticketId: '',
                status: data.status,
                ticketUrl: data.ticketUrl,
                mopId: data.mopId,
                error: data.error,
              },
            }),
          );
        } else if (data.status === RETRY_TICKET_STATUS.CREATE_FAILED) {
          result.push(
            await this.update(request, {
              //@ts-ignore
              'ticketDetails.remedy.ticketId': '',
              'ticketDetails.remedy.status': `${data.status}`,
              'ticketDetails.remedy.error': [
                {
                  errorCode: data.error[0].errorCode,
                  errorMessage: data.error[0].errorMessage,
                },
              ],
            }),
          );
        } else {
          result.push(
            await this.update(request, {
              //@ts-ignore
              'ticketDetails.remedy.status': `${data.status}`,
              'ticketDetails.remedy.error': [
                {
                  errorCode: data.error[0].errorCode,
                  errorMessage: data.error[0].errorMessage,
                },
              ],
            }),
          );
        }
      }
      return result;
    } catch (error) {
      this.logger.error(
        error,
        `Remedy - Error while updating remedy in red_aps and ServiceRequest Status ${error.message} ${error.stack}`,
      );
      throw error;
    }
  }

  async updateCherwellTicketStatus(data: TicketStatusUpdateDto) {
    try {
      this.logger.log(`Cherwell - Updating cherwell status ${data.status}`);
      if (
        data.status === RETRY_TICKET_STATUS.CLOSED ||
        data.status === RETRY_TICKET_STATUS.CANCELLED
      ) {
        await this.firewallRequestRepository.update(
          data.subRequestId.toString(),
          {
            //@ts-ignore
            'ticketDetails.cherwell.status': data.status,
            'ticketDetails.cherwell.error': data.error,
          },
        );
      } else if (data.status === RETRY_TICKET_STATUS.CREATED) {
        await this.firewallRequestRepository.update(
          data.subRequestId.toString(),
          {
            //@ts-ignore
            'ticketDetails.cherwell': {
              ticketId: data.ticketId,
              status: data.status,
              ticketUrl: data.ticketUrl,
              error: data.error,
            },
          },
        );
      } else if (data.status === RETRY_TICKET_STATUS.CREATE_FAILED) {
        await this.firewallRequestRepository.update(
          data.subRequestId.toString(),
          {
            //@ts-ignore
            'ticketDetails.cherwell.ticketId': '',
            'ticketDetails.cherwell.status': `${data.status}`,
            'ticketDetails.cherwell.error': [
              {
                errorCode: data.error[0].errorCode,
                errorMessage: data.error[0].errorMessage,
              },
            ],
          },
        );
      } else {
        await this.firewallRequestRepository.update(
          data.subRequestId.toString(),
          {
            //@ts-ignore
            'ticketDetails.cherwell.status': `${data.status}`,
            'ticketDetails.cherwell.error': [
              {
                errorCode: data.error[0].errorCode,
                errorMessage: data.error[0].errorMessage,
              },
            ],
          },
        );
      }
    } catch (error) {
      this.logger.error(
        error,
        `Cherwell - Error while updating cherwell status ${error.message} ${error.stack}`,
      );
      throw error;
    }
  }

  async buildTicketPayload(data: GenericRetryTicketRequestDto) {
    this.logger.log('Building ticket Payload');
    const retryCreateData: GenericRetryTicketRequestDto = {
      action: 'CREATED',
      ticketingSystem: data.ticketingSystem,
      title: 'Add Security Policy',
      description: `${data.serviceRequestId} : Need firewall rule to be implemented for attached DFM`,
      scheduleStartDateTime: new Date()
        .toISOString()
        .split('.')[0]
        .concat('-06:00'),
      scheduleEndDateTime: new Date(Date.now() + 24 * 60 * 60 * 1000)
        .toISOString()
        .split('.')[0]
        .concat('-06:00'),
      requestingUser: '',
      userPID: data?.userPID,
      serviceRequestId: data?.serviceRequestId,
    };
    if (data.ticketingSystem === TicketType.CHERWELL) {
      retryCreateData.requestingUser = this.configService.get(
        ENVIRONMENT_VARS.CHERWELL_RETRY_REQUESTING_USER,
      );
      const initialStorageEntities =
        await this.storageRepository.findV2RuleStorageByRequestId(
          retryCreateData.serviceRequestId,
        );
      if (initialStorageEntities) {
        for (const storageEntity of initialStorageEntities) {
          this.logger.log('Storge Entity', storageEntity);
          const fileNameArray = storageEntity.filePath.split('/');
          if (storageEntity && !fileNameArray.includes('allRules')) {
            const filePath = storageEntity.filePath;
            const bucketName = storageEntity.bucketName;
            const fileName = storageEntity.fileName;
            this.logger.debug(
              `storage location bucket:${bucketName}, file:${fileName}, path:${filePath} `,
            );
            retryCreateData.attachmentInfo = {
              bucketName,
              fileName,
              filePath,
            };
          }
        }
      }
    } else {
      retryCreateData.requestingUser = this.configService.get(
        ENVIRONMENT_VARS.REMEDY_RETRY_REQUESTING_USER,
      );
      const firewallRequestData =
        await this.firewallRequestRepository.findBySubRequestId(
          data.subRequestId,
        );
      retryCreateData.comments = [
        {
          comment_text: `${firewallRequestData.ticketDetails?.jira?.ticketId},${firewallRequestData.requestId}`,
        },
      ];
    }
    return retryCreateData;
  }

  async updateRemedyTicketDetailsInFirewallRequests(
    serviceRequestId: string,
    subRequestId: string,
  ) {
    try {
      const firewallRequestData =
        await this.firewallRequestRepository.findBySubRequestId(subRequestId);

      if (!firewallRequestData?.ticketDetails?.remedy?.mopId) {
        this.logger.warn(
          `Remedy - mopId not found for subRequestId: ${subRequestId}, cannot fetch updated ticket details.`,
        );
        throw new BadRequestException(
          `mopId not found for subRequestId: ${subRequestId}, cannot fetch updated ticket details.`,
        );
      }

      const getTicketPayloadDetails = {
        ticketingSystem: TicketType.REMEDY,
        ticketId: firewallRequestData.ticketDetails.remedy.mopId,
      };

      this.logger.log(
        `Remedy - Fetching updated ticket details for CRQ: ${getTicketPayloadDetails.ticketId}`,
      );
      const ticketDetails =
        await this.ticketManagementWrapperService.getTicketDetails(
          getTicketPayloadDetails,
        );

      if (
        ticketDetails?.ticketingSystemId !== 'N/A' &&
        ticketDetails?.status === API_RESPONSE_STATUS.SUCCESS
      ) {
        this.logger.log(
          `Remedy - Authorizing Remedy Ticket: ${ticketDetails.ticketingSystemId}`,
        );
        try {
          await this.ticketManagementWrapperService.authorizeTicket(
            ticketDetails,
          );
        } catch (authorizeError) {
          this.logger.error(
            authorizeError,
            `Remedy - Error authorizing ticket ${ticketDetails.ticketingSystemId} for ${serviceRequestId}`,
          );
        }

        this.logger.log(
          `Remedy - Calling Call in Remedy API for CRQ: ${getTicketPayloadDetails.ticketId}`,
        );
        try {
          await this.ticketManagementWrapperService.callInTicket(
            getTicketPayloadDetails,
          );
        } catch (callInError) {
          this.logger.error(
            callInError,
            `Remedy - Error calling in ticket ${getTicketPayloadDetails.ticketId} for ${serviceRequestId}`,
          );
        }

        let firewallUpdateResponse;
        this.logger.log(
          `Remedy - Updating Remedy ticket status in red_aps for serviceRequestId: ${serviceRequestId}`,
        );
        const getAllSubrequests =
          await this.firewallRequestRepository.getFirewallTicketDetails(
            serviceRequestId,
          );
        const getRedApsSubRequests = getAllSubrequests
          .filter(
            (subrequest) =>
              subrequest.organizationName === OrganizationName.RED_APS &&
              subrequest.ticketDetails.tufin,
          )
          .map((request) => request.subRequestId);
        if (ticketDetails.status === RETRY_TICKET_STATUS.SUCCESS) {
          try {
            firewallUpdateResponse =
              await this.updateRemedyAndServiceRequestStatus({
                subRequestId: getRedApsSubRequests,
                status: RETRY_TICKET_STATUS.CREATED,
                ticketId: ticketDetails.ticketingSystemId,
                ticketUrl: ticketDetails.metadata?.ticketUrl,
                mopId: firewallRequestData.ticketDetails.remedy.mopId,
                error: [],
              });
          } catch (updateError) {
            this.logger.error(
              updateError,
              `Remedy - Error updating Remedy ticket status in red_aps for ${serviceRequestId}`,
            );
          }
        }

        if (firewallUpdateResponse[0]?.ticketDetails?.jira?.ticketId) {
          const labelData = {
            issueId: firewallUpdateResponse[0].ticketDetails.jira.ticketId,
            lable: [ticketDetails.ticketingSystemId],
          };
          this.logger.log(
            `Jira - Adding label ${ticketDetails.ticketingSystemId} to issue ${labelData.issueId}`,
          );
          try {
            const addLabelResult =
              await this.jiraWrapperServiceApi.addLabels(labelData);
            this.logger.log(
              `Jira - Label added successfully: ${JSON.stringify(addLabelResult)}`,
            );
          } catch (jiraError) {
            this.logger.error(
              jiraError,
              `Jira - Error adding label ${ticketDetails.ticketingSystemId} to issue ${labelData.issueId}`,
            );
          }
        }
        return firewallUpdateResponse[0].ticketDetails.remedy;
      } else {
        this.logger.warn(
          `Remedy - The CRQ ticket creation is in progress for serviceRequestId: ${serviceRequestId}`,
        );
        return {
          status: RETRY_TICKET_STATUS.IN_PROGRESS,
          message: `Remedy - The CRQ ticket creation is in progress for serviceRequestId: ${serviceRequestId}`,
        };
      }
    } catch (error) {
      this.logger.error(
        error,
        `Remedy - Error while fetching Remedy ticket creation details for ${subRequestId} ${error?.message} ${error?.stack}`,
      );
      this.logger.log(
        `Remedy - Updating Remedy with ${RETRY_TICKET_STATUS.CREATE_FAILED} status for ${serviceRequestId}`,
      );
      try {
        const getAllSubrequests =
          await this.firewallRequestRepository.getFirewallTicketDetails(
            serviceRequestId,
          );
        const getRedApsSubRequests = getAllSubrequests
          .filter(
            (subrequest) =>
              subrequest.organizationName === OrganizationName.RED_APS,
          )
          .map((request) => request.subRequestId);
        await this.updateRemedyAndServiceRequestStatus({
          subRequestId: getRedApsSubRequests,
          status: RETRY_TICKET_STATUS.CREATE_FAILED,
          error: [{ errorCode: error?.status, errorMessage: error?.message }],
        });
      } catch (updateError) {
        this.logger.error(
          updateError,
          `Remedy - Error updating status to CREATE_FAILED for ${serviceRequestId}`,
        );
      }
      throw error;
    }
  }

  async splitIpv4Rules(data: FirewallRules[]) {
    const response = {
      awsIpamRules: [],
      ipv4Rules: [],
    };
    const awsIpamData = await this.awsIpamService.fetchDocuments();
    if (!awsIpamData || !awsIpamData.length) {
      //incase of any error while fetching aws subnets assign all rules to ipv4 rules
      this.logger.warn(`Error while identifying aws subnets`);
      response.ipv4Rules = [...data];
      return response;
    }
    for (const rule of data) {
      if (
        await this.checkIpUsingCidr(
          rule.source.ipAddress,
          rule.destination.ipAddress,
          awsIpamData,
        )
      ) {
        response.awsIpamRules.push(rule);
      } else {
        response.ipv4Rules.push(rule);
      }
    }
    return response;
  }

  async checkIpUsingCidr(
    sourceIp: string,
    destinationIp: string,
    awsIpamData: AwsIpamDto[],
  ) {
    //ip address can have comma seperate value
    const sourceIps = sourceIp.split(',');
    const destinationIps = destinationIp.split(',');
    for (const data of awsIpamData) {
      let sourceflag = true,
        destinationflag = true;
      try {
        const subnetIp = new IpAddress.Address4(data.cidrBlock);
        for (const ip of sourceIps) {
          //we can have range in ip address field
          const ips = ip.split('-');
          for (const ipadd of ips) {
            const range = new IpAddress.Address4(ipadd.trim());
            if (!range.isInSubnet(subnetIp)) {
              sourceflag = false;
              break;
            }
          }
          if (!sourceflag) {
            break;
          }
        }
        for (const ip of destinationIps) {
          const ips = ip.split('-');
          for (const ipadd of ips) {
            const range = new IpAddress.Address4(ipadd.trim());
            if (!range.isInSubnet(subnetIp)) {
              destinationflag = false;
              break;
            }
          }
          if (!destinationflag) {
            break;
          }
        }

        if (sourceflag || destinationflag) {
          return true;
        }
      } catch (error) {
        this.logger.error(
          error,
          `Error while identify with aws subnet ${data}`,
        );
      }
    }
    return false;
  }

  async sendActivity(
    requestId,
    subRequestId,
    logStep,
    activityLogStatus,
    retryCount?,
    genericResponse?,
    error?,
  ) {
    try {
      this.logger.log(
        `Send ${activityLogStatus} activity: ${requestId} ${subRequestId} ${logStep}`,
      );
      const traceId = uuid.v4();
      const serviceRequest: ServiceRequestEntity =
        await this.assetsService.getByServiceRequestId(requestId);

      this.logger.log(`fetched service request ${serviceRequest}`);
      //fetch steps for catalog

      const steps = await this.getCatalogStepsForFirewallV2();

      const index = this.configService.get(logStep);

      await this.activityLoggerService.sendActivity(
        activityLogStatus,
        steps,
        requestId,
        {},
        traceId,
        index,
        serviceRequest,
        '',
        [],
        undefined,
        genericResponse,
        error,
        subRequestId,
        retryCount,
      );
    } catch (err) {
      this.logger.error(
        err,
        `Error while inserting ${activityLogStatus}  for ${subRequestId}`,
      );
    }
  }

  async getCatalogStepsForFirewallV2() {
    try {
      const type = RequestType.FIREWALL_V2;
      const catalogStepsObj =
        await this.catalogStepsService.findActiveStepsByRequestType(type);
      const steps = catalogStepsObj.activityLogSteps.sort(
        (a, b) => a.sequence - b.sequence,
      );
      this.logger.debug(`steps for webhook ${steps}`);

      return steps;
    } catch (err) {
      this.logger.error(
        err,
        'error while fetching catalog steps using request type',
      );
    }
  }
}
