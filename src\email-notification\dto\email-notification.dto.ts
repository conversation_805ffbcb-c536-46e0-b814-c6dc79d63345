import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>otE<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsObject, IsE<PERSON> } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class EmailNotificationRequestDto {
  @ApiProperty({ description: 'Template ID from MongoDB' })
  @IsString()
  @IsNotEmpty()
  templateId: string;

  @ApiProperty({ description: 'Recipient email address' })
  @IsEmail()
  @IsNotEmpty()
  recipientEmail: string;

  @ApiProperty({ description: 'Email subject' })
  @IsString()
  @IsNotEmpty()
  subject: string;

  @ApiProperty({ description: 'Main message content' })
  @IsString()
  @IsNotEmpty()
  message: string;

  @ApiPropertyOptional({ description: 'Key-value pairs for template replacement' })
  @IsObject()
  @IsOptional()
  replacementData?: { [key: string]: string };

  @ApiPropertyOptional({ description: 'Array of data for table rows' })
  @IsArray()
  @IsOptional()
  tableData?: Array<{ [key: string]: string }>;
}

export class AwsCherwellEmailRequestDto {
  @ApiProperty({ description: 'Recipient email address' })
  @IsEmail()
  @IsNotEmpty()
  recipientEmail: string;

  @ApiProperty({ description: 'Cherwell work item number' })
  @IsString()
  @IsNotEmpty()
  workItemNumber: string;

  @ApiProperty({ description: 'Service request ID' })
  @IsString()
  @IsNotEmpty()
  serviceRequestId: string;

  @ApiProperty({ description: 'List of detected AWS devices' })
  @IsArray()
  @IsNotEmpty()
  detectedDevices: string[];
}

export class EmailTemplateDto {
  @ApiProperty({ description: 'Template ID' })
  @IsString()
  @IsNotEmpty()
  templateId: string;

  @ApiPropertyOptional({ description: 'Template location' })
  @IsString()
  @IsOptional()
  templateLocation?: string;

  @ApiProperty({ description: 'Notification configurations' })
  @IsArray()
  notifications: Array<{
    type: string;
    address: string;
    message: string;
    contentType: string;
    title: string;
  }>;
}

export class EmailNotificationResponseDto {
  @ApiProperty({ description: 'Success message' })
  @IsString()
  message: string;

  @ApiProperty({ description: 'Template ID used' })
  @IsString()
  templateId: string;

  @ApiProperty({ description: 'Recipient email' })
  @IsString()
  recipientEmail: string;

  @ApiProperty({ description: 'Email subject' })
  @IsString()
  subject: string;
}
