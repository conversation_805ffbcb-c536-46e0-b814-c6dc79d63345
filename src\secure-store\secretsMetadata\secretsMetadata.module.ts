import { <PERSON>du<PERSON> } from '@nestjs/common';
import { SecretsMetadataController } from './secretsMetadata.controller';
import { SecretsMetadataService } from './secretsMetadata.service';
import { SecretsMetadataRepository } from './secretsMetadata.repository';

@Module({
  controllers: [SecretsMetadataController],
  providers: [SecretsMetadataService, SecretsMetadataRepository],
  exports: [SecretsMetadataService],
})
export class SecretsMetadataModule {}
