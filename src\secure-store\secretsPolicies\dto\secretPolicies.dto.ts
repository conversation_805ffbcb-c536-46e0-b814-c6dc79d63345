import {
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  <PERSON>N<PERSON>ber,
  IsOptional,
  IsString,
  ValidateIf,
} from 'class-validator';
import { SecretPolicyTypes, Status } from '../types/secretsPolicies.enu';
import { Transform, Type } from 'class-transformer';

export class SecretsPoliciesDto {
  @IsNotEmpty()
  @IsString()
  secretId: string;

  @IsNotEmpty()
  @IsEnum(SecretPolicyTypes)
  type: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsString()
  @IsOptional()
  parentResourceID?: string;

  @IsEnum(Status)
  @IsOptional()
  status?: string;

  @IsNumber()
  @IsOptional()
  secretTTLInHours?: number;

  @IsBoolean()
  @IsOptional()
  active: boolean;

  @IsOptional()
  @IsString()
  namespace?: string;

  @ValidateIf((o) => o.type === SecretPolicyTypes.VAULT_POLICY)
  @IsNumber()
  @Type(() => Number)
  vaultHistoryVersion?: number;

  @ValidateIf((o) => o.type === SecretPolicyTypes.VAULT_POLICY)
  @IsNumber()
  @Type(() => Number)
  secretVersion?: number;
}
