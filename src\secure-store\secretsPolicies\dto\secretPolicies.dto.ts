import {
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  Is<PERSON><PERSON>ber,
  IsO<PERSON>al,
  IsString,
  <PERSON>,
  Min,
  <PERSON><PERSON>teIf,
} from 'class-validator';
import { SecretPolicyTypes, Status } from '../types/secretsPolicies.enum';
import { Transform, Type } from 'class-transformer';

export class PolicyRulesDto {
  @IsNotEmpty()
  @IsString()
  passwordDescription: string;

  @IsNotEmpty()
  @IsString()
  acceptedSpecialCharacters: string;

  @IsNotEmpty()
  @IsNumber()
  @Min(12)
  @Max(30)
  totalCharactersLength: number;

  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  @Max(30)
  specialCharactersCount: number;

  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  @Max(30)
  lowerCaseLettersCount: number;

  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  @Max(30)
  upperCaseLettersCount: number;

  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  @Max(30)
  numericalCharactersCount: number;
}

export class SecretsPoliciesDto {
  @IsNotEmpty()
  @IsString()
  policyName: string;

  @IsNotEmpty()
  @IsEnum(SecretPolicyTypes)
  type: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsNotEmpty()
  policyRules: PolicyRulesDto;

  @IsString()
  @IsOptional()
  resourceId?: string;

  @IsEnum(Status)
  @IsOptional()
  status?: string;
}
