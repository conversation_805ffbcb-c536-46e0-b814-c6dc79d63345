import { BaseEntity } from '../../../naas/entities/serviceRequest.entity';
import { SecretType } from '../../secretsMetadata/types/secretsMetadata.enum';

export class SecretDeviceAssociationEntity extends BaseEntity {
  secretAssociationId: string;
  deviceId: number[];
  secretId: string[];
  type: SecretType;
  active: boolean;
  sourceSystem: boolean;
  createdBy: string;
  updatedBy: string;
  updatedAt: Date;
  createdAt: Date;
}
