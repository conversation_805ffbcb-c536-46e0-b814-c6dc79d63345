import {
  Controller,
  Get,
  Query,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { IntegrationNotificationService } from './integration.notification.service';
import { LoggerService } from '../loggers/logger.service';
import { ApiTags } from '@nestjs/swagger';

@ApiTags('Integration Notification')
@UsePipes(new ValidationPipe({ whitelist: true, stopAtFirstError: true }))
@Controller('integration-notification')
export class IntegrationNotificationController {
  constructor(
    private readonly integrationNotificationService: IntegrationNotificationService,
    private readonly logger: LoggerService,
  ) {}

  @Get('tufin-devices')
  async getTufinDevices(
    @Query('serviceid') serviceid: string,
    @Query('surequestid') surequestid: string,
  ) {
    this.logger.log(
      `Request received to get Tufin devices for serviceid: ${serviceid} and surequestid: ${surequestid}`,
    );
    return await this.integrationNotificationService.getAndLogTufinDevices(
      serviceid,
      surequestid,
    );
  }
}