import { IsEnum } from 'class-validator';

export enum FetchSecretType {
  ROTATABLE_SECRET = 'Rotatable',
  NORMAL_SECRET = 'Normal',
  VAULT_TOKEN = 'Vault-Token',
}

export class FetchSecretResponseDTO {
  key: string;
  value: string;
  @IsEnum(FetchSecretType)
  type: string;
  status?: boolean;
  rotationType?: string;
  secretTTLInHours?: number;
  nextRotationDate?: Date;
  updatedBy?: string;
  updatedAt?: Date;
}
