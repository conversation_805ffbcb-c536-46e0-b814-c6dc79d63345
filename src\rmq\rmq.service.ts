import { Inject, Injectable } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { EventPatterns, RequestType } from '../types';
import { LoggerService } from '../loggers/logger.service';
import { S3Dto } from '../storage/dto/s3.dto';
import { ConfigService } from '@nestjs/config';
import { ENVIRONMENT_VARS } from '../utils/constants';

@Injectable()
export class RmqService {
  constructor(
    private readonly configService: ConfigService,
    @Inject('IPAM_QUEUE') private readonly ipamQueueClient: ClientProxy,
    @Inject('MONITORING_QUEUE')
    private readonly monitoringQueueClient: ClientProxy,
    @Inject('DNP_QUEUE')
    private readonly dnpQueueClient: ClientProxy,
    @Inject('COMPUTE_QUEUE')
    private readonly computeQueueClient: ClientProxy,
    @Inject('BLUE_COMPUTE_QUEUE')
    private readonly blueComputeQueueClient: ClientProxy,
    @Inject('CHARTER_LAB_COMPUTE_QUEUE')
    private readonly charterLabComputeQueueClient: ClientProxy,
    @Inject('VMWARE_COMPUTE_QUEUE')
    private readonly vmwareComputeQueueClient: ClientProxy,
    @Inject('VMWARE_LAB_COMPUTE_QUEUE')
    private readonly vmwareLabComputeQueueClient: ClientProxy,
    @Inject('FIREWALL_QUEUE')
    private readonly firewallQueueClient: ClientProxy,
    @Inject('DELETE_VM_QUEUE')
    private readonly deleteVmQueueClient: ClientProxy,
    @Inject('DELETE_PACE_VM_QUEUE')
    private readonly deletePACEVmQueueClient: ClientProxy,
    @Inject('DELETE_VM_VMWARE_QUEUE')
    private readonly deleteVmWareQueueClient: ClientProxy,
    @Inject('DELETE_BLUE_VM_QUEUE')
    private readonly deleteBlueVmQueueClient: ClientProxy,
    @Inject('START_STOP_RESTART_VM_QUEUE')
    private readonly stopStartRestartVmQueueClient: ClientProxy,
    @Inject('CREATE_DB_QUEUE')
    private readonly createDbQueueClient: ClientProxy,
    @Inject('COMMON_FIREWALL_POLICY_QUEUE')
    private readonly commonFirewallPolicyQueueClient: ClientProxy,
    @Inject('FIREWALL_V2_QUEUE')
    private readonly firewallv2QueueClient: ClientProxy,
    @Inject('STORAGE_S3_QUEUE')
    private readonly storageS3QueueClient: ClientProxy,
    @Inject('REGENERATE_KEYS_QUEUE')
    private readonly regenerateKeysQueueClient: ClientProxy,
    @Inject('STORAGE_NFS_QUEUE')
    private readonly storageNFSQueueClient: ClientProxy,
    @Inject('DELETE_S3_QUEUE')
    private readonly deleteS3QueueClient: ClientProxy,
    @Inject('DELETE_NFS_QUEUE')
    private readonly deleteNFSQueueClient: ClientProxy,
    @Inject('MODIFY_NFS_QUEUE')
    private readonly modifyNFSQueueClient: ClientProxy,
    @Inject('STORAGE_MODIFY_QUEUE')
    private readonly storageModifyQueueClient: ClientProxy,
    @Inject('AWS_CREATE_SUBACCOUNT_QUEUE')
    private readonly awsCreateSubAccountQueue: ClientProxy,
    @Inject('AWS_CREATE_SUBACCOUNT_GROUP_QUEUE')
    private readonly awsCreateSubAccountGroupQueue: ClientProxy,
    @Inject('MANAGE_SUB_ACCOUNT_GROUP_PERMISSION_QUEUE')
    private readonly awsCreateGroupPermissionSetQueue: ClientProxy,
    @Inject('MANAGE_PERMISSION_SET_CREATE_QUEUE')
    private readonly managePermissionSetCreateQueue: ClientProxy,
    @Inject('MANAGE_PERMISSION_SET_REMOVE_QUEUE')
    private readonly managePermissionSetRemoveQueue: ClientProxy,
    @Inject('AWS_CREATE_USER_QUEUE')
    private readonly awsCreateUserQueue: ClientProxy,
    @Inject('F5_LOAD_BALANCE_QUEUE')
    private readonly loadBalancerQueue: ClientProxy,
    @Inject('INTERNAL_CERTIFICATE_QUEUE')
    private readonly internalCertificateQueue: ClientProxy,
    private readonly logger: LoggerService,
    @Inject('WORKFLOW_RETRY_QUEUE')
    private readonly workflowRetryQueue: ClientProxy,
    @Inject('WORKFLOW_RETRY_VMWARE_QUEUE')
    private readonly workflowRetryVmwareQueue: ClientProxy,
    @Inject('FIREWALL_MIGRATION_QUEUE')
    private readonly firwallMirgationQueue: ClientProxy,
    @Inject('DELETE_DB_QUEUE')
    private readonly deleteDbQueueClient: ClientProxy,
    @Inject('RMQ_ONBOARD_PROJECT_QUEUE')
    private readonly onBoardProjectQueueClient: ClientProxy,
    @Inject('RMQ_BULK_VM_IMPORT_QUEUE')
    private readonly bulkVMImportQueueClient: ClientProxy,
    @Inject('RMQ_CATALOG_ACCESS_REQUEST_QUEUE')
    private readonly catalogAccessRequestQueueClient: ClientProxy,
    @Inject('RMQ_RESUBMIT_FIREWALL_V2_REQUEST_QUEUE')
    private readonly resubmitFirewallV2QueueClient: ClientProxy,
    @Inject('RMQ_ONBOARD_GROUP_QUEUE')
    private readonly onBoardGroupQueueClient: ClientProxy,
    @Inject('RMQ_SPEC_FLOW_QUEUE')
    private readonly specFlowRequestQueueClient: ClientProxy,
    @Inject('RMQ_CREATE_PUBLIC_CLOUD_VPC_QUEUE')
    private readonly publicCloudRequestQueueClient: ClientProxy,
    @Inject('RMQ_DELETE_PUBLIC_CLOUD_QUEUE')
    private readonly deletePublicCloudRequestQueueClient: ClientProxy,
    @Inject('RMQ_CREATE_NAMESPACE_QUEUE')
    private readonly createNamespaceQueueClient: ClientProxy,
    @Inject('RECONFIGURE_VM_QUEUE')
    private readonly createReconfigureVmClient: ClientProxy,
    @Inject('RMQ_CREATE_ZTP_QUEUE')
    private readonly createZeroTouchProvisioning: ClientProxy,
    @Inject('PACE_RECONFIGURE_VM_QUEUE')
    private readonly createPaceReconfigureVmClient: ClientProxy,
    @Inject('EDIT_VM_VMWARE_QUEUE')
    private readonly createEditVmVmwareClient: ClientProxy,
    @Inject('RMQ_CREATE_SECRET_QUEUE')
    private readonly createSecretClient: ClientProxy,
    @Inject('EDIT_PACE_VM_VMWARE_QUEUE')
    private readonly createEditPACEVmVmwareClient: ClientProxy,
    @Inject('RMQ_UPDATE_SECRET_QUEUE')
    private readonly createUpdateSecretClient: ClientProxy,
  ) {}

  private getQueueClientFromRequestType = (
    requestType: RequestType,
    eventPattern: EventPatterns,
    domain?: string,
  ): [ClientProxy, string] => {
    const lowerCaseRequestType = requestType.toLowerCase();
    switch (requestType) {
      case RequestType.CREATE_AVAILABLE_NETWORK:
      case RequestType.RESERVE_IP:
      case RequestType.RELEASE_IP:
        return [this.ipamQueueClient, lowerCaseRequestType];
      case RequestType.ADD_DEVICE:
      case RequestType.CREATE_ORGANIZATION:
      case RequestType.REMOVE_DEVICE:
        return [this.monitoringQueueClient, lowerCaseRequestType];
      case RequestType.DNP:
        return [this.dnpQueueClient, lowerCaseRequestType];
      case RequestType.FIREWALL:
        return [this.firewallQueueClient, lowerCaseRequestType];
      case RequestType.CREATE_VM_LINUX7:
      case RequestType.CREATE_VM_LINUX89:
      case RequestType.CREATE_VM_UBUNTU:
      case RequestType.CREATE_VM_WINDOWS:
        return [this.computeQueueClient, 'provision_virtual_server'];
      case RequestType.CREATE_LINUX_CORPNET:
      case RequestType.CREATE_WINDOWS_CORPNET:
        return [this.blueComputeQueueClient, 'provision_virtual_server'];
      case RequestType.CREATE_VM_LINUX89_LAB:
      case RequestType.CREATE_VM_UBUNTU_LAB:
      case RequestType.CREATE_VM_WINDOWS_LAB:
        return [this.charterLabComputeQueueClient, 'provision_virtual_server'];
      case RequestType.CREATE_VM_LINUX89_VMWARE:
      case RequestType.CREATE_VM_LINUX89_ADMIN_VMWARE:
      case RequestType.CREATE_VM_UBUNTU_VMWARE:
      case RequestType.CREATE_VM_UBUNTU_ADMIN_VMWARE:
      case RequestType.CREATE_VM_WINDOWS_VMWARE:
      case RequestType.CREATE_VM_WINDOWS_ADMIN_VMWARE:
        switch (domain) {
          case this.configService.get(ENVIRONMENT_VARS.RED_VM_DOMAINS):
            return [this.vmwareComputeQueueClient, 'provision_virtual_server'];
          case this.configService.get(ENVIRONMENT_VARS.PACE_VM_DOMAINS):
            return [
              this.vmwareLabComputeQueueClient,
              'provision_virtual_server',
            ];
        }

      case RequestType.S3:
        return [this.storageS3QueueClient, lowerCaseRequestType];
      case RequestType.MODIFY_STORAGE_S3:
        return [this.storageModifyQueueClient, lowerCaseRequestType];
      case RequestType.REGENERATE_KEYS:
        return [this.regenerateKeysQueueClient, lowerCaseRequestType];
      case RequestType.NFS:
        return [this.storageNFSQueueClient, lowerCaseRequestType];
      case RequestType.MODIFY_NFS:
        return [this.modifyNFSQueueClient, lowerCaseRequestType];
      case RequestType.DELETE_S3:
        return [this.deleteS3QueueClient, lowerCaseRequestType];
      case RequestType.DELETE_NFS:
        return [this.deleteNFSQueueClient, lowerCaseRequestType];
      case RequestType.AWS_CREATE_SUBACCOUNT:
        return [this.awsCreateSubAccountQueue, lowerCaseRequestType];
      case RequestType.AWS_CREATE_USER:
        return [this.awsCreateUserQueue, lowerCaseRequestType];
      case RequestType.RETRY_VM:
        return [this.workflowRetryQueue, lowerCaseRequestType];
      case RequestType.RETRY_VM_VMWARE:
        return [this.workflowRetryVmwareQueue, lowerCaseRequestType];
      case RequestType.CREATE_DB_MONGODB7:
      case RequestType.CREATE_DB_MONGODB6:
      case RequestType.CREATE_DB_POSTGRESDB15:
      case RequestType.CREATE_DB_POSTGRESDB16:
      case RequestType.CREATE_DB_REDISDB6:
      case RequestType.CREATE_DB_ORACLEDB19C:
      case RequestType.CREATE_DB_ORACLEDB21C:
      case RequestType.CREATE_DB_REDISDB7:
        return [this.createDbQueueClient, 'provision_db'];
      case RequestType.DELETE_VM:
        return [this.deleteVmQueueClient, lowerCaseRequestType];
      case RequestType.DELETE_BLUE_VM:
        return [this.deleteBlueVmQueueClient, lowerCaseRequestType];
      case RequestType.STOP_VM:
      case RequestType.RESTART_VM:
      case RequestType.START_VM:
      case RequestType.SUSPEND_VM:
      case RequestType.RESET_VM:
        return [this.stopStartRestartVmQueueClient, lowerCaseRequestType];
      case RequestType.COMMON_FIREWALL_POLICY:
      case RequestType.DYNAMIC_ACCESS_POLICIES:
        return [this.commonFirewallPolicyQueueClient, eventPattern];
      case RequestType.FIREWALL_V2:
        return [this.firewallv2QueueClient, eventPattern];
      case RequestType.AWS_SUBACCOUNT_GROUPS:
        return [this.awsCreateSubAccountGroupQueue, lowerCaseRequestType];
      case RequestType.MANAGE_SUB_ACCOUNT_GROUP_PERMISSION:
        return [this.awsCreateGroupPermissionSetQueue, lowerCaseRequestType];
      case RequestType.MANAGE_PERMISSION_SET_CREATE:
        return [this.managePermissionSetCreateQueue, lowerCaseRequestType];
      case RequestType.MANAGE_PERMISSION_SET_REMOVE:
        return [this.managePermissionSetRemoveQueue, lowerCaseRequestType];
      case RequestType.CREATE_LB_F5:
        return [this.loadBalancerQueue, lowerCaseRequestType];
      case RequestType.CREATE_INTERNAL_CERTIFICATE:
        return [this.internalCertificateQueue, lowerCaseRequestType];
      case RequestType.FIREWALL_V1_MIGRATE:
        return [this.firwallMirgationQueue, lowerCaseRequestType];
      case RequestType.DELETE_DB:
        return [this.deleteDbQueueClient, lowerCaseRequestType];
      case RequestType.BULK_VM_IMPORT:
        return [this.bulkVMImportQueueClient, lowerCaseRequestType];
      case RequestType.CATALOG_ACCESS_REQUEST:
        return [this.catalogAccessRequestQueueClient, lowerCaseRequestType];
      case RequestType.ONBOARD_PROJECT:
        return [this.onBoardProjectQueueClient, lowerCaseRequestType];
      case RequestType.RESUBMIT_FIREWALL_V2:
        return [this.resubmitFirewallV2QueueClient, lowerCaseRequestType];
      case RequestType.ONBOARD_GROUP:
        return [this.onBoardGroupQueueClient, lowerCaseRequestType];
      case RequestType.SPEC_FLOW_S3:
      case RequestType.SPEC_FLOW_EC2:
      case RequestType.SPEC_FLOW_EKS:
        return [this.specFlowRequestQueueClient, lowerCaseRequestType];
      case RequestType.PUBLIC_CLOUD_VPC:
        return [this.publicCloudRequestQueueClient, lowerCaseRequestType];
      case RequestType.DELETE_PUBLIC_CLOUD:
        return [this.deletePublicCloudRequestQueueClient, lowerCaseRequestType];
      case RequestType.DELETE_VM_VMWARE:
        switch (domain) {
          case this.configService.get(ENVIRONMENT_VARS.RED_VM_DOMAINS):
            return [this.deleteVmWareQueueClient, lowerCaseRequestType];
          case this.configService.get(ENVIRONMENT_VARS.PACE_VM_DOMAINS):
            return [this.deletePACEVmQueueClient, lowerCaseRequestType];
        }
      case RequestType.CREATE_NAMESPACE:
        return [this.createNamespaceQueueClient, lowerCaseRequestType];
      case RequestType.RECONFIGURE_VM:
        switch (domain) {
          case this.configService.get(ENVIRONMENT_VARS.RED_VM_DOMAINS):
            return [this.createReconfigureVmClient, lowerCaseRequestType];
          case this.configService.get(ENVIRONMENT_VARS.PACE_VM_DOMAINS):
            return [this.createPaceReconfigureVmClient, lowerCaseRequestType];
        }
      case RequestType.EDIT_VM:
        switch (domain) {
          case this.configService.get(ENVIRONMENT_VARS.RED_VM_DOMAINS):
            return [this.createEditVmVmwareClient, lowerCaseRequestType];
          case this.configService.get(ENVIRONMENT_VARS.PACE_VM_DOMAINS):
            return [this.createEditPACEVmVmwareClient, lowerCaseRequestType];
        }
      case RequestType.ZERO_TOUCH_PROVISIONING:
        return [this.createZeroTouchProvisioning, lowerCaseRequestType];
      case RequestType.CREATE_SECRET:
        return [this.createSecretClient, lowerCaseRequestType];
      case RequestType.UPDATE_SECRET:
        return [this.createUpdateSecretClient, lowerCaseRequestType];
      default:
        throw new Error(
          `No queue configured for requests of type: ${requestType}`,
        );
    }
  };

  async pushMessage(
    requestType: RequestType,
    message:
      | {
          id: string;
          payload: object;
          serviceRequestId: string;
          retryCount?: number;
          requestorEmail?: string;
          requestorPID?: string;
          crqTicketId?: string;
          mopId?: number;
          crqStatus?: string;
          crqToggle?: boolean;
        }
      | {
          resourceId: string;
          vmId?: number;
          requestorEmail: string;
          requestorPID: string;
        }[]
      | {
          requestType: RequestType;
          bucketName?: string;
        }[]
      | S3Dto
      | {
          metadata: {
            serviceCatalog: {
              catalogName: string;
              catalogType: string;
            };
          };
          payload: object;
          requestType: string;
          status: string;
        }
      | {
          requestType: RequestType;
          fileSystemName: string;
        }[],
    eventPattern?: EventPatterns,
    domain?: string | '',
  ) {
    const [queueClient, pattern] = this.getQueueClientFromRequestType(
      requestType,
      eventPattern,
      domain,
    );
    queueClient.emit(pattern, message);
  }
}
