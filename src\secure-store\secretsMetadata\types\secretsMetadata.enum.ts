export enum Status {
  SUCCESS = 'SUCCESS',
  FAILURE = 'FAILURE',
  UNKNOWN = 'UNKNOWN',
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  DELETED = 'DELETED',
}

export enum SecretType {
  ROTATABLE_SECRET = 'ROTATABLE-SECRET',
  NORMAL_SECRET = 'NORMAL-SECRET',
  VAULT_TOKEN = 'VAULT-TOKEN',
  VAULT_POLICY = 'VAULT-POLICY',
  VAULT_PASSWORD_POLICY = 'VAULT-PASSWORD-POLICY',
  DEVICE_VAULT_SECRET_MAPPING = 'DEVICE-VAULT-SECRET-MAPPING',
}

export enum RotationType {
  AUTO = 'Auto',
  MANUAL = 'Manual',
}

export class Secrets {
  request_id: string;
  lease_id: string;
  renewable: Boolean;
  lease_duration: number;
  data: Object;
  wrap_info: any;
  warnings: any;
  auth: any;
}
