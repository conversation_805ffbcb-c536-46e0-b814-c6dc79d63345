import {
  IsString,
  <PERSON>Optional,
  Is<PERSON>rray,
  <PERSON><PERSON>teNested,
  IsN<PERSON>ber,
  IsBoolean,
} from 'class-validator';
import { Type } from 'class-transformer';

// Network DTO
class NetworkDto {
  @IsString()
  networkMor: string;

  @IsString()
  type: string;

  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  ipv4Subnet?: string;

  @IsOptional()
  @IsString()
  ipv6Subnet?: string;

  @IsBoolean()
  ipv4Enabled: boolean;

  @IsBoolean()
  ipv6Enabled: boolean;

  @IsBoolean()
  dhcp: boolean;

  @IsOptional()
  @IsString()
  dnsDomain?: string;

  @IsOptional()
  @IsString()
  ipv4Gateway?: string;

  @IsOptional()
  @IsString()
  ipv6Gateway?: string;

  @IsOptional()
  @IsString()
  ipv4DnsPrimary?: string;

  @IsOptional()
  @IsString()
  ipv4DnsSecondary?: string;

  @IsOptional()
  @IsString()
  ipv6DnsPrimary?: string;

  @IsOptional()
  @IsString()
  ipv6DnsSecondary?: string;

  @IsOptional()
  @IsString()
  ipv4DhcpServer?: string | null;

  @IsOptional()
  @IsString()
  ipv6DhcpServer?: string | null;

  @IsBoolean()
  disabled: boolean;
}

// Datastore DTO
class DatastoreDto {
  @IsString()
  datastoreMor: string;

  @IsString()
  name: string;

  @IsString()
  type: string;

  @IsNumber()
  freeSpace: number;

  @IsNumber()
  capacity: number;

  @IsBoolean()
  @IsOptional()
  disabled: boolean;
}

// Host DTO
class HostDto {
  @IsString()
  hostMor: string;

  @IsString()
  name: string;

  @IsString()
  connectionState: string;

  @IsOptional()
  @IsString()
  powerState?: string | null;

  @IsBoolean()
  @IsOptional()
  disabled: boolean;

  @IsString()
  @IsOptional()
  location: string;

  @IsString()
  @IsOptional()
  vendor: string;

  @IsString()
  @IsOptional()
  model: string;

  @IsNumber()
  @IsOptional()
  cpu: number;

  @IsNumber()
  @IsOptional()
  core: number;

  @IsNumber()
  @IsOptional()
  memory: number;
}

// Cluster DTO
class ClusterDto {
  @IsString()
  @IsOptional()
  clusterMor: string;

  @IsString()
  @IsOptional()
  name: string;

  @IsBoolean()
  haEnabled: boolean;

  @IsOptional()
  @IsBoolean()
  drsEnabled?: boolean | null;

  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  hosts?: string[];

  @IsOptional()
  @IsArray()
  projects?: [];

  @IsBoolean()
  @IsOptional()
  disabled: boolean;

  @IsBoolean()
  @IsOptional()
  restricted: boolean;

  @IsNumber()
  @IsOptional()
  hostCount: number;

  @IsNumber()
  @IsOptional()
  vmCount: number;

  @IsOptional()
  @IsString()
  clusterStatus?: string | null;
}

// OsLayout DTO
class OsLayoutDto {
  @IsString()
  osName: string;

  @IsString()
  layoutName: string;

  @IsString()
  layoutMor: string;

  @IsString()
  shortName: string;

  @IsString()
  imageName: string;

  @IsOptional()
  @IsString()
  imagePath?: string | null;

  @IsOptional()
  @IsBoolean()
  disabled?: boolean;

  @IsOptional()
  @IsBoolean()
  restricted?: boolean;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  groups?: string[];

  @IsOptional()
  @IsArray()
  projects?: [];
}

// CloudDetails DTO
class CloudDetailsDto {
  @IsString()
  domain: string;

  @IsString()
  datacenter: string;

  @IsString()
  cloudName: string;

  @IsString()
  hostname: string;
  @IsString()
  password: string;

  @IsNumber()
  port: number;

  @IsString()
  protocol: string;

  @IsString()
  username: string;

  @IsString()
  cloudId: string;

  @IsOptional()
  @IsString()
  vmFolder?: string;

  @IsOptional()
  @IsString()
  vcenterHost?: string | null;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => HostDto)
  hosts: HostDto[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ClusterDto)
  clusters: ClusterDto[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DatastoreDto)
  datastores: DatastoreDto[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => NetworkDto)
  networks: NetworkDto[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => OsLayoutDto)
  osLayouts: OsLayoutDto[];
}

// Main DTO
export class SaveVCenterLookupDto {
  @IsString()
  vcenterHost: string;

  @IsString()
  vcenterName: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CloudDetailsDto)
  cloudDetails: CloudDetailsDto[];
}

export class SaveVCenterLookupResponseDto {
  @IsString()
  message: string;
}
