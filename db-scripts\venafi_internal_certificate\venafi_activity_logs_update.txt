function runScript() {
  const catalog = db.menvcataloglevel04.findOne({
    shortName: 'internalcertificate',
  });

  if (!catalog) {
    print('Catalog document does not exists, aborting the transaction');
    throw new Error(
      'Catalog document does not exists, aborting the transaction',
    );
  }

  db.catalogsteps.findOneAndUpdate(
    { catalogLevel04Id: catalog._id, version: 1 },
    { $set: { active: false } },
  );

  const catalogSteps = {
    catalogLevel04Id: catalog._id,
    version: 2,
    active: true,
    activityLogSteps: [
      {
        name: 'Receive Certificate Request',
        eventCode: 'NEB-EVENT-VM-CREATE-1001',
        sequence: 1,
      },
      {
        name: 'Approve Request',
        eventCode: 'NEB-EVENT-APPROVAL-1001',
        sequence: 2,
      },
      {
        name: 'Update Service Request',
        eventCode: 'NEB-EVENT-IC-1001',
        sequence: 3,
      },
      {
        name: 'Submit Venafi Certificate Request',
        eventCode: 'NEB-EVENT-IC-2001',
        sequence: 4,
      },
      {
        name: 'Provision Certificate in Venafi',
        eventCode: 'NEB-EVENT-IC-3001',
        sequence: 5,
      },
      {
        name: 'Confirm Venafi Provisioning',
        eventCode: 'NEB-EVENT-IC-4001',
        sequence: 6,
      },
      {
        name: 'Fetch Venafi Certificate Details',
        eventCode: 'NEB-EVENT-IC-5001',
        sequence: 7,
      },
      {
        name: 'Update Details in Service Request',
        eventCode: 'NEB-EVENT-IC-6001',
        sequence: 8,
      },
      {
        name: 'Update Service Request Status',
        eventCode: 'NEB-EVENT-IC-7001',
        sequence: 9,
      }
    ],
  };

  db.catalogsteps.insertOne(catalogSteps);
  print(`Script ran successfully`);
}
let session;
try {
  session = db.getMongo().startSession();
  session.startTransaction();
  runScript();
  session.commitTransaction();
} catch (e) {
  session.abortTransaction();
  print('Transaction aborted due to error', e);
} finally {
  session.endSession();
}