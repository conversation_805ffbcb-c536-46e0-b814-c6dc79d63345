const session = db.getMongo().startSession();

session.startTransaction();

try {
  const ztp = {
    type: 'ZERO_TOUCH_PROVISIONING',
    catalogName: 'singledevice',
  };

  const ztpDoc = db.menvcataloglevel04.findOne({
    shortName: ztp.catalogName,
  });

  ztp.catalogLevel04Id = ztpDoc._id;

  db.requestcatalogmaps.insertOne(ztp);

  print(`Documents Created`);

  session.commitTransaction();
} catch (e) {
  session.abortTransaction();

  print('Transaction aborted due to error', e);
} finally {
  session.endSession();
}
