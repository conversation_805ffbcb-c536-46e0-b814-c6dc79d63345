import {
  BadRequestException,
  Body,
  Controller,
  Headers,
  HttpCode,
  HttpException,
  HttpStatus,
  Logger,
  Param,
  Post,
  Put,
  Res,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiSecurity,
  ApiTags,
  getSchemaPath,
} from '@nestjs/swagger';
import { Anonymous } from '../auth/anonymous.decorator';

import { WebhookRequestDto } from './dto/webhook.request.dto';
import { WebhookResponseDto } from './dto/webhook-response.dto';
import { WebhookHandlerFactory } from './webhook-handler.factory';
import { plainToInstance } from 'class-transformer';
import { DapUpdateRequestDto } from './dto/dap-update.request.dto';
import {
  API_RESPONSE_STATUS,
  RequestType,
  TufinTaskName,
  WEBHOOK_REQUEST_TYPE,
} from '../types';

import { Response } from 'express';
import { FirewallV2Service } from '../security/firewallv2/firewallv2.service';
import { CreateRiskAnalysisResultResponseDto } from '../security/dto/firewall.risk-analysis-result.response.dto';
import { CreateRiskAnalysisResultRequestDto } from '../security/dto/firewall.risk-analysis-result.request.dto';
import { GitUpdateRequestDto } from './dto/git-update.request.dto';
import { DapDeployementUpdateRequestDto } from './dto/dap-deployment-update.request.dto';
import { CatalogStepsService } from '../catalog-steps/catalog-steps.service';
import { CatalogStepsEntity } from '../catalog-steps/catalog-steps.entity';
import * as uuid from 'uuid';
import { AssetsService } from '../assets/assets.service';
import { ServiceRequestEntity } from '../naas/entities/serviceRequest.entity';
import { ActivityLoggerWrapperService } from '../activity-logs-wrapper/activity-logger-wrapper.service';
import { RiskAnalysisDTOMapper } from './risk-analysis-dto-mapper.service';
import { IncomingRiskAnalysisRequestDto } from '../security/dto/firewall.incoming.risk-analysis-result.request.dto';
import { ConfigService } from '@nestjs/config';
import { ENVIRONMENT_VARS } from '../utils/constants';
import { IncomingTaskResultRequestDto } from '../security/dto/firewall.incoming.designer-result.request.dto';
import { CreateDesignerResultResponseDto } from '../security/dto/firewall.designer-result.response.dto';
import { DesignerResultDtoMapper } from './designer-result-dto-mapper.service';
import { WorkflowService } from '../security/firewallv2/firewallv2.tufinworkflow.service';
import { ProcessStatus } from '../activity-logs/types/process-status.enum';
import {
  DapWebhookRequestDto,
  DapWebhookResponseDto,
} from './dto/dnp-dap-update.dto';
import { ActivityLogDto } from './dto/awx-activity-log.dto';

@ApiTags('WebhookUpdate')
@ApiSecurity('x-nebula-authorization')
@Controller('webhook')
@UsePipes(new ValidationPipe({ whitelist: true }))
@ApiExtraModels(
  WebhookRequestDto,
  DapUpdateRequestDto,
  GitUpdateRequestDto,
  DapDeployementUpdateRequestDto,
)
export class WebhookController {
  private readonly logger = new Logger(WebhookController.name);

  constructor(
    private readonly webhookHandlerFactory: WebhookHandlerFactory,
    private readonly firewallV2Service: FirewallV2Service,
    private readonly catalogStepsService: CatalogStepsService,
    private readonly assetsService: AssetsService,
    private readonly activityLoggerService: ActivityLoggerWrapperService,
    private readonly riskAnalysisDTOMapper: RiskAnalysisDTOMapper,
    private readonly designerResultDtoMapper: DesignerResultDtoMapper,
    private readonly configService: ConfigService,
    private readonly workflowService: WorkflowService,
  ) {}

  // @Anonymous()
  @Put(
    'risk-analysis-result/serviceRequest/:requestId/tufinTicket/:tufinTicketId',
  )
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Submit risk analysis results' })
  @ApiParam({
    name: 'tufinTicketId',
    type: 'string',
    description:
      'The tufin ticket id for which the risk analysis results are generated',
    required: true,
  })
  @ApiParam({
    name: 'requestId',
    type: 'string',
    description: 'The nebula request id',
    required: true,
  })
  @ApiBody({
    description: 'Incoming risk analysis request data',
    type: IncomingRiskAnalysisRequestDto,
  })
  @ApiResponse({
    status: 200,
    description: 'The request was processed successfully.',
    type: CreateRiskAnalysisResultResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'The requestId or tufinTicketId not found.',
    type: CreateRiskAnalysisResultResponseDto,
  })
  @ApiResponse({
    status: 500,
    description: 'An unexpected error occurred.',
    type: CreateRiskAnalysisResultResponseDto,
  })
  async createRiskAnalysisResult(
    @Param('requestId') requestId: string,
    @Param('tufinTicketId') tufinTicketId: string,
    @Body() incomingRiskAnalysisRequestDto: IncomingRiskAnalysisRequestDto,
    @Res() res: Response,
  ): Promise<Response> {
    this.logger.debug(
      `Received request to create risk analysis result for serviceRequest ${requestId},
      tufin ticket: ${tufinTicketId}`,
    );

    this.logger.debug(
      `IncomingRiskAnalysisRequestDto: ${JSON.stringify(incomingRiskAnalysisRequestDto)}`,
    );

    try {
      // Transform the incoming risk analysis result to RiskAnalysisDto[].
      const transformedRiskAnalysisResult =
        this.riskAnalysisDTOMapper.transformRiskAnalysis(
          incomingRiskAnalysisRequestDto.riskAnalysis,
        );

      this.logger.debug(
        `Transformed risk analysis result: ${JSON.stringify(transformedRiskAnalysisResult)}`,
      );

      const createRiskAnalysisResultRequestDto: CreateRiskAnalysisResultRequestDto =
        {
          requestId: incomingRiskAnalysisRequestDto.requestId,
          tufinTicketId: incomingRiskAnalysisRequestDto.tufinTicketId,
          riskAnalysis: transformedRiskAnalysisResult,
        };

      this.logger.debug(
        `CreateRiskAnalysisResultRequestDto: ${JSON.stringify(createRiskAnalysisResultRequestDto)}`,
      );

      const response = await this.firewallV2Service.createRiskAnalysisResults(
        createRiskAnalysisResultRequestDto,
      );
      const result: CreateRiskAnalysisResultResponseDto = {
        status: API_RESPONSE_STATUS.SUCCESS,
        message: response.message,
      };
      return res.status(HttpStatus.OK).json(result);
    } catch (error) {
      this.logger.debug(
        `Error while updating  risk analysis for ${tufinTicketId} ${error}`,
      );
      const status =
        error instanceof HttpException
          ? error.getStatus()
          : HttpStatus.INTERNAL_SERVER_ERROR;
      const message = error.message || 'An unexpected error occurred';

      const errorResponse: CreateRiskAnalysisResultResponseDto = {
        status: API_RESPONSE_STATUS.FAILED,
        message: message,
      };

      return res.status(status).json(errorResponse);
    }
  }

  // @Anonymous()
  @Put('task-result/serviceRequest/:requestId/tufinTicket/:tufinTicketId')
  @ApiOperation({ summary: 'Submit tufin task results' })
  @ApiParam({
    name: 'requestId',
    type: 'string',
    description: 'The nebula request id',
    required: true,
  })
  @ApiParam({
    name: 'tufinTicketId',
    type: 'string',
    description: 'The tufin ticket id for the task',
    required: true,
  })
  @ApiBody({
    description: 'Incoming task result data',
    type: IncomingTaskResultRequestDto,
  })
  @ApiResponse({
    status: 200,
    description: 'The request was processed successfully.',
    type: CreateDesignerResultResponseDto,
  })
  async createTaskResult(
    @Param('requestId') requestId: string,
    @Param('tufinTicketId') tufinTicketId: string,
    @Body() incomingTaskResultRequestDto: IncomingTaskResultRequestDto,
    @Res() res: Response,
  ): Promise<Response> {
    this.logger.debug(
      `Task result update received for task: ${incomingTaskResultRequestDto.taskName}, serviceRequest ${requestId}, tufin ticket: ${tufinTicketId}`,
    );

    this.logger.debug(
      `IncomingTaskResultRequestDto: ${JSON.stringify(incomingTaskResultRequestDto)}`,
    );

    try {
      const response: boolean =
        await this.workflowService.processTufinTaskResult(
          incomingTaskResultRequestDto,
        );

      if (response) {
        const result = {
          status: API_RESPONSE_STATUS.SUCCESS,
          message: `Task result for ${incomingTaskResultRequestDto.taskName} processed successfully for tufin ticket ${tufinTicketId}`,
        };
        return res.status(HttpStatus.OK).json(result);
      } else {
        const result = {
          status: API_RESPONSE_STATUS.FAILED,
          message: `Failed to process results for ${incomingTaskResultRequestDto.taskName} task for tufin ticket ${tufinTicketId}`,
        };
        return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json(result);
      }
    } catch (error) {
      this.logger.debug(
        `Error while processing task result for ${tufinTicketId}. ${error}`,
      );

      const result = {
        status: API_RESPONSE_STATUS.FAILED,
        message: `Failed to process results for ${incomingTaskResultRequestDto.taskName} task`,
      };
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json(result);
    }
  }

  // @Anonymous()
  @Put('job/:requestType')
  @ApiOperation({ summary: 'Handle webhook updates' })
  @ApiParam({
    name: 'requestType',
    enum: WEBHOOK_REQUEST_TYPE,
    description: 'The type of request (e.g., DAP_UPDATES, GIT_UPDATES)',
  })
  @ApiBody({
    description:
      'Generic Webhook Request. The structure of the "data" field depends on the requestType.',
    schema: {
      type: 'object',
      properties: {
        requestId: { type: 'string', example: '12345' },
        jobStatus: { type: 'string', example: 'SUCCESS' },
        requestType: {
          type: 'string',
          enum: Object.values(WEBHOOK_REQUEST_TYPE),
        },
        data: {
          type: 'object',
          oneOf: [
            { $ref: getSchemaPath(DapUpdateRequestDto) },
            { $ref: getSchemaPath(GitUpdateRequestDto) },
            { $ref: getSchemaPath(DapDeployementUpdateRequestDto) },
          ],
          discriminator: {
            propertyName: 'requestType',
            mapping: {
              DAP_UPDATES: getSchemaPath(DapUpdateRequestDto),
              GIT_UPDATES: getSchemaPath(GitUpdateRequestDto),
              DAP_DEPLOYMENT_UPDATE: getSchemaPath(DapUpdateRequestDto),
            },
          },
        },
        errors: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              errorCode: { type: 'string' },
              errorMessage: { type: 'string' },
            },
          },
        },
      },
      required: ['requestId', 'requestType', 'jobStatus', 'data'],
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Successful Response',
    type: WebhookResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  async handleWebhook(
    @Body() webhookRequestDto: WebhookRequestDto,
    @Param('requestType') requestType: WEBHOOK_REQUEST_TYPE,
  ): Promise<WebhookResponseDto> {
    const { data } = webhookRequestDto;

    this.logger.log(
      `Webhook request received for ${requestType} with data: ${JSON.stringify(webhookRequestDto)}`,
    );

    //fetching service request using payload nebularequestid
    const serviceRequest: ServiceRequestEntity =
      await this.assetsService.getByServiceRequestId(data?.nebulaRequestId);

    this.logger.log(`fetched service request ${serviceRequest}`);

    let catalogStepsObj: CatalogStepsEntity;
    let steps = [];
    let childSteps = [];
    let index;
    let childIndex;
    const traceId = uuid.v4();

    //fetch steps for catalog
    try {
      let type;
      if (
        requestType === WEBHOOK_REQUEST_TYPE.GIT_UPDATES ||
        requestType === WEBHOOK_REQUEST_TYPE.DAP_UPDATES ||
        requestType === WEBHOOK_REQUEST_TYPE.DAP_DEPLOYMENT_UPDATE
      ) {
        type = RequestType.COMMON_FIREWALL_POLICY;
      }
      if (requestType === WEBHOOK_REQUEST_TYPE.DAP_DEPLOYMENT_UPDATE) {
        const catalogSteps =
          await this.catalogStepsService.findActiveStepsByRequestType(
            RequestType.DYNAMIC_ACCESS_POLICIES,
          );
        childSteps = catalogSteps.activityLogSteps.sort(
          (a, b) => a.sequence - b.sequence,
        );
        this.logger.debug(`child steps for deployment updates ${childSteps}`);
      }
      catalogStepsObj =
        await this.catalogStepsService.findActiveStepsByRequestType(type);
      steps = catalogStepsObj.activityLogSteps.sort(
        (a, b) => a.sequence - b.sequence,
      );
      this.logger.debug(`steps for webhook ${steps}`);

      //hardcording the step number for activity log, similairly hard coded in n8n
      if (requestType === WEBHOOK_REQUEST_TYPE.GIT_UPDATES) {
        index = this.configService.get(ENVIRONMENT_VARS.GIT_PIPELINE_LOG_STEP);
      } else if (requestType === WEBHOOK_REQUEST_TYPE.DAP_DEPLOYMENT_UPDATE) {
        index = this.configService.get(
          ENVIRONMENT_VARS.DAP_DEPLOYMENT_LOG_STEP,
        );
        childIndex = this.configService.get(
          ENVIRONMENT_VARS.DAP_DEPLOYMENT_CHILD_LOG_STEP,
        );
      } else if (requestType === WEBHOOK_REQUEST_TYPE.DAP_UPDATES) {
        index = this.configService.get(ENVIRONMENT_VARS.DAP_UPDATE_LOG_STEP);
      }
    } catch (err) {
      this.logger.debug(
        'error while fetching catalog steps using request type',
        err,
      );
    }

    await this.activityLoggerService.sendActivity(
      ProcessStatus.STARTED,
      steps,
      webhookRequestDto?.data?.nebulaRequestId,
      webhookRequestDto?.data,
      traceId,
      index,
      serviceRequest,
      requestType,
      childSteps,
      childIndex,
      undefined,
      undefined,
    );
    try {
      // Get the appropriate handler and DTO type using the factory
      const { handler, dto } =
        this.webhookHandlerFactory.getHandlerAndDto(requestType);

      // Dynamically transform data to the specific DTO required by the handler
      const transformedData = plainToInstance(dto, data);

      // Execute the handler's operation with transformed data
      const specificResponse = await handler.handle(transformedData);

      // Convert the specific response DTO to a generic WebhookResponseDto
      const genericResponse: WebhookResponseDto<typeof specificResponse> = {
        status:
          specificResponse.status === API_RESPONSE_STATUS.SUCCESS
            ? API_RESPONSE_STATUS.SUCCESS
            : API_RESPONSE_STATUS.FAILED,
        message: specificResponse.message,
        data: specificResponse.data,
      };

      await this.activityLoggerService.sendActivity(
        ProcessStatus.COMPLETED,
        steps,
        webhookRequestDto?.data?.nebulaRequestId,
        webhookRequestDto.data,
        traceId,
        index,
        serviceRequest,
        requestType,
        childSteps,
        childIndex,
        genericResponse,
        undefined,
      );
      return genericResponse;
    } catch (error) {
      this.logger.debug('Error while calling webhook api', error);
      await this.activityLoggerService.sendActivity(
        ProcessStatus.FAILED,
        steps,
        webhookRequestDto?.data?.nebulaRequestId,
        webhookRequestDto?.data,
        traceId,
        index,
        serviceRequest,
        requestType,
        childSteps,
        childIndex,
        undefined,
        error,
      );
      if (error instanceof BadRequestException) {
        return {
          status: API_RESPONSE_STATUS.FAILED,
          message: error.message,
          data: undefined,
        };
      }
      throw error;
    }
  }
  @Put('awx-activty-log/:requestType')
  @ApiOperation({ summary: 'Handle webhook updates' })
  @ApiParam({
    name: 'requestType',
    enum: WEBHOOK_REQUEST_TYPE,
    description: 'The type of request (e.g., VM_CONFIGURE)',
  })
  @ApiResponse({
    status: 200,
    description: 'Successful Response',
    type: WebhookResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  async handleAwxWebhook(
    @Body() body: WebhookRequestDto,
    @Param('requestType') requestType: WEBHOOK_REQUEST_TYPE,
  ): Promise<WebhookResponseDto> {
    const { ...data } = body; // Destructure rest
    try {
      // Get the appropriate handler and DTO type using the factory
      const { handler, dto } =
        this.webhookHandlerFactory.getHandlerAndDto(requestType);

      // Dynamically transform data to the specific DTO required by the handler
      const transformedData = plainToInstance(dto, data);

      // Execute the handler's operation with transformed data
      const specificResponse = await handler.handle(transformedData);

      // Convert the specific response DTO to a generic WebhookResponseDto
      const genericResponse: WebhookResponseDto<typeof specificResponse> = {
        status:
          specificResponse.status === API_RESPONSE_STATUS.SUCCESS
            ? API_RESPONSE_STATUS.SUCCESS
            : API_RESPONSE_STATUS.FAILED,
        message: specificResponse.message,
        data: specificResponse.data,
      };

      return {
        message: `Received webhook for ${requestType}`,
        status: 'Processed',
      };
    } catch (error) {
      this.logger.debug('Error while calling webhook api', error);
      if (error instanceof BadRequestException) {
        return {
          status: API_RESPONSE_STATUS.FAILED,
          message: error.message,
          data: undefined,
        };
      }
      throw error;
    }
  }

  @Put('dap-dnp/interfaces/:DnpServiceRequestId')
  @ApiOperation({ summary: 'Handle DAP API webhook updates' })
  @ApiResponse({
    status: 200,
    description: 'DNP devices updated successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request. Please try again',
  })
  @ApiResponse({
    status: 401,
    description: 'UnAuthorized Access',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden Resource',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error.',
  })
  async handleDnpDapWebhook(
    @Param('DnpServiceRequestId') serviceRequestId: string,
    @Body() webhookRequestDto: DapWebhookRequestDto,
  ): Promise<DapWebhookResponseDto> {
    const { ...data } = webhookRequestDto;
    const requestType = WEBHOOK_REQUEST_TYPE.DNP_DAP_UPDATE;

    this.logger.log(
      `Webhook request received for ${requestType} with data: ${JSON.stringify(webhookRequestDto)}`,
    );
    try {
      // Get the appropriate handler and DTO type using the factory
      const { handler, dto } =
        this.webhookHandlerFactory.getHandlerAndDto(requestType);

      // Dynamically transform data to the specific DTO required by the handler
      const transformedData = plainToInstance(dto, data);

      // Execute the handler's operation with transformed data
      const specificResponse = await handler.handle(
        transformedData,
        serviceRequestId,
      );

      // Convert the specific response DTO to a generic WebhookResponseDto
      const genericResponse: WebhookResponseDto<typeof specificResponse> = {
        status:
          specificResponse.status === API_RESPONSE_STATUS.SUCCESS
            ? API_RESPONSE_STATUS.SUCCESS
            : specificResponse.status === API_RESPONSE_STATUS.FAILED
              ? API_RESPONSE_STATUS.FAILED
              : API_RESPONSE_STATUS.PARTIAL_COMPLETED,
        message: specificResponse.message,
        data: specificResponse.data,
      };
      return genericResponse;
    } catch (error) {
      this.logger.error(
        `Error while handling webhook (${requestType}): ${error}`,
        error.stack,
      );

      if (error instanceof BadRequestException) {
        return {
          status: API_RESPONSE_STATUS.FAILED,
          message: error.message,
          data: undefined,
        };
      }
      throw error;
    }
  }
}
