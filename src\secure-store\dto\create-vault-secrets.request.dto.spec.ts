import { validate } from 'class-validator';
import { CreateVaultSecretsRequestDto } from './create-vault-secrets.request.dto'; // Adjust path
import { FetchSecretType } from './fetch-secrets.response.dto';
import { RotationType } from '../secretsMetadata/types/secretsMetadata.enum';
import { SecretsIdsQueryDto } from './create-vault-secrets.request.dto';
describe('CreateVaultSecretsRequestDto', () => {
  it('should validate a valid ROTATABLE_SECRET DTO', async () => {
    const dto = new CreateVaultSecretsRequestDto();
    dto.type = FetchSecretType.ROTATABLE_SECRET;
    dto.vaultKey = 'vaultKey';
    dto.vaultPassword = 'vaultPass';
    dto.userNameKey = 'userKey';
    dto.userNamePassword = 'userPass';
    dto.secretTTLInHours = 24;
    dto.notifyBeforeTokenExpiry = true;
    dto.nextRotationDate = new Date().toISOString();
    dto.rotationType = RotationType.AUTO;

    const errors = await validate(dto);
    expect(errors.length).toBe(1);
  });

  it('should fail validation if required ROTATABLE_SECRET fields are missing', async () => {
    const dto = new CreateVaultSecretsRequestDto();
    dto.type = FetchSecretType.ROTATABLE_SECRET;
    dto.vaultKey = 'vaultKey';
    dto.vaultPassword = 'vaultPass';

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors.some((e) => e.property === 'userNameKey')).toBe(true);
    expect(errors.some((e) => e.property === 'userNamePassword')).toBe(true);
    expect(errors.some((e) => e.property === 'notifyBeforeTokenExpiry')).toBe(
      true,
    );
    expect(errors.some((e) => e.property === 'nextRotationDate')).toBe(true);
  });

  it('should validate a valid VAULT_TOKEN DTO', async () => {
    const dto = new CreateVaultSecretsRequestDto();
    dto.type = FetchSecretType.VAULT_TOKEN;
    dto.vaultKey = 'vaultKey';
    dto.vaultPassword = 'vaultPass';
    dto.expiryDate = new Date().toISOString();
    dto.tokenRenewByNebula = true;

    const errors = await validate(dto);
    expect(errors.length).toBe(0);
  });

  it('should fail validation if VAULT_TOKEN fields are missing', async () => {
    const dto = new CreateVaultSecretsRequestDto();
    dto.type = FetchSecretType.VAULT_TOKEN;
    dto.vaultKey = 'vaultKey';
    dto.vaultPassword = 'vaultPass';

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors.some((e) => e.property === 'expiryDate')).toBe(true);
    expect(errors.some((e) => e.property === 'tokenRenewByNebula')).toBe(true);
  });
});

describe('SecretsIdsQueryDto', () => {
  it('should validate a valid ids string', async () => {
    const dto = new SecretsIdsQueryDto();
    dto.ids = 'id1,id2,id3';

    const errors = await validate(dto);
    expect(errors.length).toBe(0);
  });

  it('should fail validation if ids is missing', async () => {
    const dto = new SecretsIdsQueryDto();

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors.some((e) => e.property === 'ids')).toBe(true);
  });
});
