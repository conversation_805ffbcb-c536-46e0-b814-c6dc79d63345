import { IGenericRepository } from '../../abstracts/generic-repository.abstract';
import { LoggerService } from '../../loggers/logger.service';
import mongoose, { ClientSession } from 'mongoose';
import {
  ApprovalNotification,
  FirewallRequestEntity,
  ReflowHistory,
} from '../entity/firewallRequest.entity';
import {
  FirewallRequest,
  FirewallRequestSchema,
} from '../schema/firewallRequest.schema';
import { transformDocumentToEntity } from '../../utils/helpers';
import { FirewallDbOperationResult } from './firewall.db-operation.result';
import {
  CreateRiskAnalysisResultRequestDto as RiskAnalysisResultDto,
  RiskAnalysisDto,
} from '../dto/firewall.risk-analysis-result.request.dto';
import {
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { Counters, CountersSchema } from '../../naas/schemas/counter.schema';
import {
  ApprovalStatus,
  CounterTypes,
  GenericResourceTypes,
  IpType,
  OrganizationName,
  RequestStatus,
} from '../../types';
import { FirewallRequestCreateDto } from '../firewallv2/dto/firewallRequest.request.dto';
import { UpdateJiraDetailsDto } from '../firewallv2/dto/updateFirewallRequest.dto';
import { TicketDetailsDto } from '../firewallv2/dto/firewall.ticket-details.response.dto';
import { TufinTaskRequestDto } from '../dto/firewall.tufin.job.request.dto';
import {
  TufinResponseDetails,
  TufinResponses,
} from '../firewallv2/dto/updateTufinDetails.dto';
import { DesignerResults } from '../dto/firewall.designer-result.request.dto';
import { IncomingTaskResultRequestDto } from '../dto/firewall.incoming.designer-result.request.dto';
import { setTimeout } from 'timers/promises';
import { ConfigService } from '@nestjs/config';
import { ENVIRONMENT_VARS } from '../../utils/constants';

const transformFirewallRequestDocumentToEntity = transformDocumentToEntity<
  FirewallRequest,
  FirewallRequestEntity
>;

@Injectable()
export class FirewallRequestDetailsRepository
  implements IGenericRepository<FirewallRequestEntity>
{
  constructor(
    private readonly logger: LoggerService,
    private readonly configService: ConfigService,
  ) {}

  private async connectToDB() {
    return await mongoose.connect(process.env.MONGODB_URI);
  }

  private async getModel() {
    const conn = await this.connectToDB();
    return conn.model<
      FirewallRequest & { createdAt: Date; updatedAt: Date; __v: number }
    >(FirewallRequest.name, FirewallRequestSchema);
  }

  async findById(id: string): Promise<FirewallRequestEntity> {
    this.logger.log(`Fetching data for ${id}`);
    const firewallRequestModel = await this.getModel();
    const request = await firewallRequestModel.findById({ id }).exec();
    if (!request) {
      return null;
    }
    return transformFirewallRequestDocumentToEntity(request);
  }

  async findBySubRequestId(
    subRequestId: string,
  ): Promise<FirewallRequestEntity> {
    this.logger.log(`Fetching data for ${subRequestId}`);
    const firewallRequestModel = await this.getModel();
    const subRequest = await firewallRequestModel
      .findOne({ subRequestId })
      .exec();
    if (!subRequest) {
      return null;
    }
    return transformFirewallRequestDocumentToEntity(subRequest);
  }

  async findByServiceRequestIdAndSubRequestId(
    requestId: string,
    subRequestId: string,
  ): Promise<FirewallRequestEntity> {
    this.logger.log(
      `Fetching data for serviceRequestId: ${requestId}, subRequestId: ${subRequestId}`,
    );
    const firewallRequestModel = await this.getModel();
    const request = await firewallRequestModel
      .findOne({ requestId, subRequestId })
      .exec();
    if (!request) {
      return null;
    }
    return transformFirewallRequestDocumentToEntity(request);
  }

  async findByFilter(filter) {
    this.logger.log(`fetching data using filter ${JSON.stringify(filter)}`);
    const firewallRequestModel = await this.getModel();
    const subRequests = await firewallRequestModel.find(filter);
    return subRequests.map((ele) => {
      return transformFirewallRequestDocumentToEntity(ele);
    });
  }

  async findByServiceRequestIdAndTufinId(
    serviceRequestId,
    tufinId,
  ): Promise<FirewallRequestEntity> {
    this.logger.log(`Fetching data for ${serviceRequestId} & ${tufinId}`);
    const firewallRequestModel = await this.getModel();
    let subRequest;
    const maxRetries =
      this.configService.get(ENVIRONMENT_VARS.MAX_RETRY_COUNT) || 3;
    let currentRetryCount = 0;
    while (currentRetryCount < maxRetries) {
      this.logger.log(
        `Retrying to find subrequest for ${currentRetryCount} with query ${JSON.stringify(
          {
            requestId: serviceRequestId,
            'ticketDetails.tufin.ticketId': tufinId,
          },
        )}`,
      );
      subRequest = await firewallRequestModel
        .findOne({
          requestId: serviceRequestId,
          'ticketDetails.tufin.ticketId': tufinId,
        })
        .exec();
      if (subRequest) {
        break;
      }
      await setTimeout(
        (this.configService.get(ENVIRONMENT_VARS.RETRY_TIME_DELAY) || 10000) *
          (currentRetryCount + 1),
      );
      currentRetryCount++;
    }
    if (!subRequest) {
      return null;
    }
    return transformFirewallRequestDocumentToEntity(subRequest);
  }

  async getFirewallTicketDetails(
    requestId?: string,
  ): Promise<Partial<FirewallRequestEntity>[]> {
    this.logger.log('Fetching firwall ticket details', requestId);
    const firewallRequestModel = await this.getModel();

    const query: any = {};

    if (requestId) {
      query['requestId'] = requestId;
    }

    this.logger.log(
      `Fetching firewal ticket details with query: ${JSON.stringify(query)}`,
    );

    const results = await firewallRequestModel
      .find(query)
      .select(
        'requestId subRequestId ipType ruleIds organizationName createdAt updatedAt status approvalStatus ticketDetails isSubRequestInValidated enableApproval reflowHistory approvalNotification',
      )
      .exec();

    if (!results || results.length === 0) {
      this.logger.warn(`No ticket details found for the given query.`);
      return [];
    }

    return results.map((result) => ({
      createdAt: result.createdAt,
      updatedAt: result.updatedAt,
      serviceRequestId: result.requestId,
      subRequestId: result.subRequestId,
      status: result.status,
      ipType: result.ipType,
      ruleIds: result.ruleIds,
      approvalStatus: result.approvalStatus,
      organizationName: result.organizationName,
      ticketDetails: result.ticketDetails as TicketDetailsDto,
      isSubRequestInValidated: result.isSubRequestInValidated,
      //assign enable approval only for rd_aps, corporate, un
      //assign false if enable approval is false
      //assign true if enable approval is true, undefined, null(for old requests enableApproval will be undefined)
      enableApproval:
        result.ipType === IpType.IPV4 &&
        [
          OrganizationName.RED_APS,
          OrganizationName.CORPORATE,
          OrganizationName.UNKNOWN,
        ].includes(result.organizationName as OrganizationName)
          ? result.enableApproval === false
            ? false
            : true
          : undefined,
      reflowHistory: result.reflowHistory,
      approvalNotification: result.approvalNotification,
    }));
  }

  async getRiskResults(searchCriteria: {
    tufinTicketId?: string;
    requestId?: string;
  }): Promise<RiskAnalysisResultDto[]> {
    this.logger.log(`Get risk results from DB ${searchCriteria}`);
    const firewallRequestModel = await this.getModel();

    try {
      const query: any = {};

      if (searchCriteria.tufinTicketId) {
        query['ticketDetials.tufin.ticketId'] = searchCriteria.tufinTicketId;
      }
      if (searchCriteria.requestId) {
        query['requestId'] = searchCriteria.requestId;
      }

      this.logger.log(
        `Fetching risk results with query: ${JSON.stringify(query)}`,
      );

      const results = await firewallRequestModel
        .find(query)
        .select('requestId ticketDetails')
        .exec();

      if (!results || results.length === 0) {
        this.logger.warn(`No risk results found for the given query.`);
        return [];
      }

      return results.map(
        (result) =>
          ({
            requestId: result?.requestId,
            tufinTicketId: result?.ticketDetails?.tufin?.ticketId,
            riskAnalysis: result?.ticketDetails?.tufin?.riskAnalysis,
          }) as RiskAnalysisResultDto,
      );
    } catch (error) {
      this.logger.error(
        error,
        `Failed to fetch risk results for the given query ${searchCriteria}`,
      );
      throw new InternalServerErrorException(
        'An error occurred while fetching risk analysis results. Please try again later.',
      );
    }
  }

  async updateWorkflowStepsResult(
    serviceRequestId: string,
    tufinTicketId: string,
    taskResult: IncomingTaskResultRequestDto,
  ): Promise<FirewallDbOperationResult<FirewallRequestEntity>> {
    this.logger.log(
      `Updating tufin workflow steps results in mongo collection for requestId: ${serviceRequestId}, 
      tufinTicketId: ${tufinTicketId}, taskResult: ${taskResult}`,
    );

    const firewallRequestModel = await this.getModel();

    const query = {
      requestId: serviceRequestId,
      'ticketDetails.tufin.ticketId': tufinTicketId,
    };

    try {
      const updatedDoc = await firewallRequestModel.findOneAndUpdate(
        query,
        {
          $push: {
            'ticketDetails.tufin.workflowStepsResults': {
              stepName: taskResult.taskName,
              taskResult: taskResult,
            },
          },
        },
        { new: true },
      );

      this.logger.log('workflowStepsResults updated in db');

      if (!updatedDoc) {
        return {
          success: false,
          message: `Failed to update workflow step result for serviceRequestId :${serviceRequestId}, tufinTicketId: ${tufinTicketId}`,
        };
      } else {
        return {
          success: true,
          data: transformFirewallRequestDocumentToEntity(updatedDoc),
          message: `Tufin workflow step result updated successfully`,
        };
      }
    } catch (error) {
      this.logger.error(
        `Failed to update workflow step result for serviceRequestId :${serviceRequestId}, tufinTicketId: ${tufinTicketId} in db: ${error.message}`,
        error.stack,
      );

      return {
        success: false,
        message: `Failed to update workflow step result for serviceRequestId :${serviceRequestId}, tufinTicketId: ${tufinTicketId} in db due to error : ${error.message}`,
      };
    }
  }

  async updateTufinTask(
    serviceRequestId: string,
    tufinTicketId: string,
    tufinTaskRequestDto: TufinTaskRequestDto,
  ): Promise<FirewallDbOperationResult<FirewallRequestEntity>> {
    this.logger.log(
      `Updating tufin task ${serviceRequestId} ${tufinTicketId} ${tufinTaskRequestDto}`,
    );
    const query = {
      requestId: serviceRequestId,
      'tufinTicketDetails.ticketId': tufinTicketId,
    };

    try {
      const firewallRequestModel = await this.getModel();

      const updatedDoc = await firewallRequestModel
        .findOneAndUpdate(
          query,
          { $push: { tufinTask: tufinTaskRequestDto } },
          { new: true },
        )
        .exec();

      if (!updatedDoc) {
        return {
          success: false,
          message: `No matching records found for serviceRequestId :${serviceRequestId} or tufinTicketId: ${tufinTicketId}`,
        };
      } else {
        return {
          success: true,
          data: transformFirewallRequestDocumentToEntity(updatedDoc),
          message: `Tufin task updated successfully`,
        };
      }
    } catch (error) {
      this.logger.error(
        error,
        `Tufin task update in db failed for ${serviceRequestId} ${tufinTicketId} ${tufinTaskRequestDto}`,
      );

      return {
        success: false,
        message: `Tufin task update failed for serviceRequestId :${serviceRequestId} or tufinTicketId: ${tufinTicketId}`,
      };
    }
  }

  async updateTufinStatus(
    serviceRequestId: string,
    tufinTicketId: string,
    tufinTaskRequestDto: TufinTaskRequestDto,
    tufinTicketStatus: string,
    serviceRequestStatus: string,
    taskName?: string,
  ): Promise<FirewallDbOperationResult<FirewallRequestEntity>> {
    this.logger.log(
      `Updating tufin ticket status: ${serviceRequestId} ${tufinTicketId} 
      ${tufinTaskRequestDto} ${tufinTicketStatus} ${serviceRequestStatus} ${taskName}`,
    );
    const query = {
      requestId: serviceRequestId,
      'ticketDetails.tufin.ticketId': tufinTicketId,
    };

    try {
      const firewallRequestModel = await this.getModel();
      let updateObj;
      if (taskName?.toLowerCase() === 'designer') {
        updateObj = {
          'ticketDetails.tufin.status': tufinTicketStatus,
          'ticketDetails.tufin.designerResultsUpdated': true,
          status: serviceRequestStatus,
        };
      } else if (taskName?.toLowerCase() === 'risk analysis') {
        updateObj = {
          'ticketDetails.tufin.status': tufinTicketStatus,
          status: serviceRequestStatus,
          'ticketDetails.tufin.riskAnalysisUpdated': true,
        };
      } else {
        updateObj = {
          'ticketDetails.tufin.status': tufinTicketStatus,
          status: serviceRequestStatus,
        };
      }

      const updatedDoc = await firewallRequestModel
        .findOneAndUpdate(
          query,
          {
            $push: { 'ticketDetails.tufin.jobUpdates': tufinTaskRequestDto },
            $set: updateObj,
          },
          { new: true },
        )
        .exec();

      if (!updatedDoc) {
        return {
          success: false,
          message: `No matching records found for serviceRequestId :${serviceRequestId} or tufinTicketId: ${tufinTicketId}`,
        };
      } else {
        return {
          success: true,
          data: transformFirewallRequestDocumentToEntity(updatedDoc),
          message: `Tufin task updated successfully`,
        };
      }
    } catch (error) {
      this.logger.error(
        error,
        `Tufin task update in db failed for ${serviceRequestId} ${tufinTicketId} 
      ${tufinTaskRequestDto} ${tufinTicketStatus} ${serviceRequestStatus} ${taskName}`,
      );

      return {
        success: false,
        message: `Tufin task update failed for serviceRequestId :${serviceRequestId} or tufinTicketId: ${tufinTicketId}`,
      };
    }
  }

  async updateRiskAnalysis(
    serviceRequestId: string,
    tufinTicketId: string,
    firewallRequestData: RiskAnalysisDto[],
  ): Promise<FirewallDbOperationResult<FirewallRequestEntity>> {
    this.logger.log(
      `Updating risk analysis ${serviceRequestId} ${tufinTicketId} ${firewallRequestData}`,
    );
    const firewallRequestModel = await this.getModel();
    const query = {
      requestId: serviceRequestId,
      'ticketDetails.tufin.ticketId': tufinTicketId,
    };

    //TODO: Check for valid value for approvalStatus.
    const update = {
      $set: {
        status: RequestStatus.PENDING_DESIGNER_RESULTS,
        'ticketDetails.tufin.riskAnalysisUpdated': true,
        'ticketDetails.tufin.riskAnalysis': firewallRequestData,
      },
    };

    this.logger.log(
      `Updating risk analysis result ${JSON.stringify(update)} for ${JSON.stringify(query)}`,
    );
    try {
      const updatedDoc = await firewallRequestModel
        .findOneAndUpdate(query, update, { new: true })
        .exec();
      if (!updatedDoc) {
        return {
          success: false,
          message: `No matching records found for serviceRequestId :${serviceRequestId} or tufinTicketId: ${tufinTicketId}`,
        };
      } else {
        return {
          success: true,
          data: transformFirewallRequestDocumentToEntity(updatedDoc),
          message: `Risk analysis result updated successfully`,
        };
      }
    } catch (error) {
      this.logger.error(
        error,
        `Risk analysis update in db failed for ${serviceRequestId} ${tufinTicketId} ${firewallRequestData}`,
      );

      return {
        success: false,
        message: `Risk analysis update failed for serviceRequestId :${serviceRequestId} or tufinTicketId: ${tufinTicketId}`,
      };
    }
  }

  async updateDesignerResult(
    serviceRequestId: string,
    tufinTicketId: string,
    designerResults: DesignerResults,
    enableApproval: boolean,
  ): Promise<FirewallDbOperationResult<FirewallRequestEntity>> {
    this.logger.log(
      `Updating designer result ${serviceRequestId} ${tufinTicketId} ${designerResults}`,
    );
    const firewallRequestModel = await this.getModel();
    const query = {
      requestId: serviceRequestId,
      'ticketDetails.tufin.ticketId': tufinTicketId,
    };

    const update = {
      $set: {
        status: RequestStatus.PENDING_APPROVAL,
        approvalStatus: ApprovalStatus.PENDING,
        'ticketDetails.tufin.designerResultsUpdated': true,
        'ticketDetails.tufin.designerResults': designerResults,
        enableApproval: enableApproval,
        approvalNotification: [],
      },
    };

    this.logger.log(
      `Updating designer result in db for ${JSON.stringify(query)}`,
    );

    try {
      const updatedDoc = await firewallRequestModel
        .findOneAndUpdate(query, update, { new: true })
        .exec();

      if (!updatedDoc) {
        return {
          success: false,
          message: `No matching records found for serviceRequestId :${serviceRequestId} or tufinTicketId: ${tufinTicketId}`,
        };
      } else {
        return {
          success: true,
          data: transformFirewallRequestDocumentToEntity(updatedDoc),
          message: `Designer result updated successfully`,
        };
      }
    } catch (error) {
      this.logger.error(
        error,
        `Designer result update in db failed for ${serviceRequestId} ${tufinTicketId} ${designerResults}`,
      );
      return {
        success: false,
        message: `Designer result update failed for serviceRequestId :${serviceRequestId} or tufinTicketId: ${tufinTicketId}: ${error.message}`,
      };
    }
  }

  async getFirewallRequestStatuses(
    serviceRequestId: string,
  ): Promise<string[]> {
    this.logger.log(`Get firewall request statuses for ${serviceRequestId}`);
    const firewallRequestModel = await this.getModel();
    const query = { requestId: serviceRequestId };

    try {
      const records = await firewallRequestModel.find(query);

      return records.map((record) => {
        return !record.status ||
          record.status === null ||
          record.status === undefined ||
          record.status.trim() === ''
          ? null
          : record.status;
      });
    } catch (error) {
      this.logger.error(
        error,
        `Exception while fetching status for all tufin tickets associated with service request ${serviceRequestId}`,
      );

      return [];
    }
  }

  private async getCountersModel() {
    const conn = await this.connectToDB();
    return conn.model<Counters>(Counters.name, CountersSchema);
  }

  async getCount(
    session: ClientSession,
    type: CounterTypes.FIREWALL_SUB_REQUEST,
  ) {
    const model = await this.getCountersModel();
    let count = 0;
    const updateResult = await model.findOneAndUpdate(
      { type: type },
      { $inc: { counter: 1 } },
      { session, new: true },
    );
    if (!updateResult) {
      await model.create({ type: type, counter: 1000 }, { session });
      count = 1;
    } else {
      count = updateResult.counter;
    }

    return count;
  }

  getGenericId(id: string, count: number): string {
    this.logger.log(`Get generic id for ${id} ${count}`);
    const requestType = GenericResourceTypes[id];
    if (!requestType) {
      this.logger.error(`The Request type "${id}" does not exist`);
      throw new Error(`No request type "${id}"`);
    } else {
      return `${requestType}-${count}`;
    }
  }

  async createRequest(
    data: FirewallRequestCreateDto,
  ): Promise<FirewallRequestEntity> {
    this.logger.log(`Create request ${data}`);
    const model = await this.getModel();
    const session = await model.startSession();

    try {
      const count = await this.getCount(
        session,
        CounterTypes.FIREWALL_SUB_REQUEST,
      );
      const genericId = this.getGenericId('Firewall-Sub-Request', count);
      const request = new model({
        ...data,
        subRequestId: genericId,
      });
      const updatedRequest = await request.save({ session });
      await session.endSession();
      return transformFirewallRequestDocumentToEntity(updatedRequest);
    } catch (error) {
      await session.endSession();
      this.logger.error(error);
      throw error;
    } finally {
      session.endSession();
    }
  }
  async updateJiraDetails(
    data: UpdateJiraDetailsDto,
    subRequestId: string,
  ): Promise<FirewallRequestEntity> {
    this.logger.log(`Update Jira details ${data} ${subRequestId}`);
    const firewallRequestModel = await this.getModel();
    const document = await firewallRequestModel.findOneAndUpdate(
      {
        subRequestId: subRequestId,
      },
      { jiraTicketDetails: data },
      { new: true },
    );
    if (!document) {
      throw new NotFoundException(
        `document doesn't exixts with subRequestId ${subRequestId}`,
      );
    }
    return transformFirewallRequestDocumentToEntity(document);
  }

  async update(
    subRequestId: string,
    updates: Partial<FirewallRequestEntity>,
  ): Promise<FirewallRequestEntity> {
    this.logger.log(`Update: ${JSON.stringify(updates)} ${subRequestId}`);
    const firewallRequestModel = await this.getModel();
    const document = await firewallRequestModel.findOneAndUpdate(
      {
        subRequestId: subRequestId,
      },
      { $set: updates },
      { new: true },
    );
    if (!document) {
      throw new NotFoundException();
    }
    return transformFirewallRequestDocumentToEntity(document);
  }

  async updateFirewallRequest(
    subRequestId: string,
    updates,
    condition,
  ): Promise<FirewallRequestEntity> {
    this.logger.log(
      `Update firewall request for ${subRequestId}`,
      updates,
      condition,
    );
    const firewallRequestModel = await this.getModel();
    const query = condition
      ? condition
      : {
          subRequestId: subRequestId,
        };
    const document = await firewallRequestModel.findOneAndUpdate(query, {
      $set: updates,
    });
    if (!document) {
      throw new NotFoundException('Firewall request (or) tufin data not found');
    }
    return transformFirewallRequestDocumentToEntity(document);
  }

  async updateTufinDetails(
    data: TufinResponseDetails,
    subRequestId: string,
    responseType,
  ): Promise<FirewallRequestEntity> {
    this.logger.log(
      `Update tufin details: ${data} ${subRequestId} ${responseType}`,
    );
    const firewallRequestModel = await this.getModel();
    let document;
    if (responseType == TufinResponses.TOPOLOGY) {
      document = await firewallRequestModel.findOneAndUpdate(
        {
          subRequestId: subRequestId,
        },
        { $set: { 'tufinResponseDetails.topologyResponse': data } },
        { new: true },
      );
    } else if (responseType == TufinResponses.ALETHIA) {
      document = await firewallRequestModel.findOneAndUpdate(
        {
          subRequestId: subRequestId,
        },
        { $set: { 'tufinResponseDetails.alethiaResponse': data } },
        { new: true },
      );
    } else if (responseType == TufinResponses.SECURECHANGE) {
      document = await firewallRequestModel.findOneAndUpdate(
        {
          subRequestId: subRequestId,
        },
        { $set: { 'tufinResponseDetails.secureChangeResponse': data } },
        { new: true },
      );
    }

    if (!document) {
      throw new NotFoundException(
        `document doesn't exixts with subRequestId ${subRequestId}`,
      );
    }
    return transformFirewallRequestDocumentToEntity(document);
  }

  /**
   * Updates approval status for all firewall requests filtered with serviceRequestId and organizationName when approval status is Approved
   * Updates approval status for all firewall requests filtered with only the serviceRequestId when approval status is Rejected
   * @param {string} serviceRequestId:string
   * @param {string} organizationName:string
   * @param {ApprovalStatus} status:ApprovalStatus
   * @returns {any}
   */
  async updateApprovalStatus(
    serviceRequestId: string,
    organizationName: string,
    status: ApprovalStatus,
  ) {
    this.logger.log(
      `Update approval status: ${serviceRequestId} ${organizationName} ${status}`,
    );
    const filterQuery = {
      requestId: serviceRequestId,
      ipType: IpType.IPV4,
      organizationName,
      status: RequestStatus.PENDING_APPROVAL,
    };
    const firewallRequestModel = await this.getModel();
    this.logger.debug('Updating approval status');
    const updateResult = await firewallRequestModel
      .updateMany(filterQuery, {
        $set: { approvalStatus: status, status, approvalNotification: [] },
      })
      .lean()
      .exec();
    this.logger.log('Approval status updated', updateResult);

    //writing find to return approved/ rejected subrequest to process further since updateMany doesnt retuen updated records
    const updatedSubRequests = this.findByFilter({
      requestId: serviceRequestId,
      organizationName,
      ipType: IpType.IPV4,
      status: status,
    });

    return updatedSubRequests;
  }

  async findPendingApprovalSubRequests(
    requestId: string,
    addApprovalNotificationCheck: boolean,
  ) {
    this.logger.log(`Fetching pending approval sub requests for ${requestId}`);
    const firewallRequestModel = await this.getModel();
    let query;
    if (addApprovalNotificationCheck) {
      query = {
        requestId,
        status: RequestStatus.PENDING_APPROVAL,
        $or: [
          { approvalNotification: { $size: 0 } },
          { approvalNotification: { $exists: false } },
        ],
      };
    } else {
      query = {
        requestId,
        status: RequestStatus.PENDING_APPROVAL,
      };
    }
    const subRequests = await firewallRequestModel.find(query).exec();
    return subRequests.map(transformFirewallRequestDocumentToEntity);
  }

  async updateReflowHistory(
    reflowHistoryObject: ReflowHistory,
    subRequestId: string,
  ) {
    this.logger.log(
      `updating reflow history for ${subRequestId} with ${JSON.stringify(reflowHistoryObject)}`,
    );
    const firewallRequestModel = await this.getModel();
    const subRequest = await firewallRequestModel
      .findOneAndUpdate(
        { subRequestId },
        { $push: { reflowHistory: reflowHistoryObject } },
      )
      .exec();
    return transformFirewallRequestDocumentToEntity(subRequest);
  }

  async updateApprovalNotifcation(
    notificationObject: ApprovalNotification,
    subRequestId: string,
  ) {
    this.logger.log(
      `updating approval notification for ${subRequestId} with ${JSON.stringify(notificationObject)}`,
    );
    const firewallRequestModel = await this.getModel();
    const subRequest = await firewallRequestModel
      .findOneAndUpdate(
        { subRequestId },
        { $push: { approvalNotification: notificationObject } },
      )
      .exec();
    return transformFirewallRequestDocumentToEntity(subRequest);
  }
}
