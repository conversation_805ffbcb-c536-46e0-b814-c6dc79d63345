import {
  Controller,
  Get,
  Post,
  Param,
  Query,
  Body,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
  Delete,
  Put,
  UseGuards,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiBearerAuth,
  ApiConsumes,
  ApiBody,
  ApiProduces,
  ApiOkResponse,
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiSecurity,
} from '@nestjs/swagger';
import { extname } from 'path';
import { SitemapService } from './sitemap.service';
import { LoggerService } from 'src/loggers/logger.service';
import { SitemapEntity } from './entity/sitemap.entity';
import { PaginationQueryDto } from 'src/utils/pagination/dto/pagination.dto';

@ApiTags('Sitemap')
@ApiSecurity('x-nebula-authorization')
@Controller('sitemap')
export class SitemapController {
  constructor(
    private readonly sitemapService: SitemapService,
    private readonly logger: LoggerService,
  ) {}

  @Post()
  @UseInterceptors(FileInterceptor('file'))
  @ApiBody({
    required: true,
    type: 'multipart/form-data',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @ApiConsumes('multipart/form-data')
  async processSitemapFile(@UploadedFile() file: Express.Multer.File) {
    if (!file || extname(file.originalname) != '.xlsx') {
      this.logger.debug('Invalid file or incorrect extension provide');
      throw new BadRequestException('Please provide file with xlsx extension');
    }
    this.logger.log('Processing file', extname(file.originalname));
    return await this.sitemapService.processSitemapFile(file.buffer);
  }

  @Post('create')
  async createSitemapRecords(
    @Body() sitemapData: SitemapEntity[],
  ): Promise<any> {
    return await this.sitemapService.createSitemapRecords(sitemapData);
  }

  @Put('/:id')
  async updateSiteMapData(
    @Param('id') id: string,
    @Body() siteMap: SitemapEntity,
  ): Promise<any> {
    return await this.sitemapService.updateSiteMapData(id, siteMap);
  }

  @Get('all')
  async getAllSiteMapData() {
    return await this.sitemapService.getAllSiteMapData();
  }

  @Get('')
  async getSiteMapData(@Query() pageQuery: PaginationQueryDto) {
    return await this.sitemapService.getSiteMapData(pageQuery);
  }

  @Delete('/:id')
  async deleteSiteMapData(@Param('id') id: string): Promise<any> {
    return await this.sitemapService.deleteSiteMapData(id);
  }
}
