import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { SecretPolicyTypes, Status } from '../types/secretsPolicies.enu';

export type PasswordPoliciesDocument = PasswordPolicies & Document;

@Schema({ timestamps: true })
export class PasswordPolicies {
  @Prop({ type: String })
  policyId?: string;

  @Prop({ type: String })
  policyName?: string;

  @Prop({
    required: true,
    enum: SecretPolicyTypes,
  })
  type: string;

  @Prop({ type: String })
  description?: string;

  @Prop({ type: String })
  resourceId?: string;

  @Prop({ enum: Status, default: 'ACTIVE' })
  status?: string;

  @Prop({ type: Number })
  secretTTLInHours?: number;

  @Prop({ default: true })
  active?: boolean;

  @Prop({ type: String })
  createdBy?: string;

  @Prop({ type: String })
  updatedBy?: string;

  @Prop({ type: String })
  namespace?: string;
}

export const PasswordPoliciesSchema =
  SchemaFactory.createForClass(PasswordPolicies);
