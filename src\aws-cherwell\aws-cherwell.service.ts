import { Injectable } from '@nestjs/common';
import { LoggerService } from '../loggers/logger.service';
import { TicketManagementWrapperService } from '../ticket-management-service-wrapper/ticket-management-wrapper-service';
import { IntegrationNotificationService } from '../naas/integration.notification.service';
import { TicketType } from '../utils/constants';

export interface AwsDeviceDetectionResult {
  hasAwsDevices: boolean;
  detectedDevices: string[];
  serviceRequestId: string;
  subRequestId?: string;
}

export interface CherwellWorkItemRequest {
  subject: string;
  description: string;
  assignmentGroup: string;
  serviceRequestId: string;
  subRequestId?: string;
}

export interface CherwellWorkItemResponse {
  workItemId: string;
  workItemNumber: string;
  status: string;
  ticketUrl?: string;
}

@Injectable()
export class AwsCherwellService {
  // AWS device patterns to match
  private readonly AWS_DEVICE_PATTERNS = [
    /.*izr01$/i,  // *IZR01 devices
    /.*izr02$/i,  // *IZR02 devices  
    /.*ivfa0$/i,  // *IVFA0 devices
    /.*ivfa1$/i,  // *IVFA1 devices
  ];

  // Specific AWS devices mentioned in requirements
  private readonly SPECIFIC_AWS_DEVICES = [
    'asbnvacz-izr01',
    'asbnvacz-izr02', 
    'dllstx97-izr01',
    'dllstx97-izr02',
    'chrcnctr-izr01',
    'chrcnctr-izr02', // Note: requirement shows 'iz02' but likely meant 'izr02'
    'enwdcocd-izr01',
    'enwdcocd-izr02',
    'chrcnctr-ivfa0',
    'chrcnctr-ivfa1'
  ];

  // Srihari's email for notifications
  private readonly SRIHARI_EMAIL = '<EMAIL>';

  constructor(
    private readonly logger: LoggerService,
    private readonly ticketManagementService: TicketManagementWrapperService,
    private readonly notificationService: IntegrationNotificationService,
  ) {}

  /**
   * Analyzes path analysis results to detect AWS devices
   * @param pathAnalysisData - Array of devices from path analysis
   * @returns AwsDeviceDetectionResult
   */
  analyzePathForAwsDevices(pathAnalysisData: any[]): AwsDeviceDetectionResult {
    this.logger.log('Analyzing path for AWS devices', { pathAnalysisData });
    
    const detectedDevices: string[] = [];
    
    if (!pathAnalysisData || !Array.isArray(pathAnalysisData)) {
      this.logger.warn('Invalid path analysis data provided');
      return {
        hasAwsDevices: false,
        detectedDevices: [],
        serviceRequestId: '',
      };
    }

    // Check each device in the path
    for (const pathData of pathAnalysisData) {
      if (pathData.target && Array.isArray(pathData.target)) {
        for (const device of pathData.target) {
          const deviceName = device.name?.toLowerCase();
          if (deviceName && this.isAwsDevice(deviceName)) {
            detectedDevices.push(device.name);
            this.logger.log(`AWS device detected: ${device.name}`);
          }
        }
      }
    }

    // Remove duplicates
    const uniqueDevices = [...new Set(detectedDevices)];
    
    return {
      hasAwsDevices: uniqueDevices.length > 0,
      detectedDevices: uniqueDevices,
      serviceRequestId: pathAnalysisData[0]?.serviceRequestId || '',
    };
  }

  /**
   * Checks if a device name matches AWS device patterns
   * @param deviceName - Device name to check
   * @returns boolean
   */
  private isAwsDevice(deviceName: string): boolean {
    const lowerDeviceName = deviceName.toLowerCase();
    
    // Check specific devices first
    if (this.SPECIFIC_AWS_DEVICES.some(device => 
      device.toLowerCase() === lowerDeviceName)) {
      return true;
    }

    // Check regex patterns
    return this.AWS_DEVICE_PATTERNS.some(pattern => 
      pattern.test(lowerDeviceName));
  }

  /**
   * Creates a Cherwell work item for AWS approval
   * @param request - CherwellWorkItemRequest
   * @returns CherwellWorkItemResponse
   */
  async createCherwellWorkItem(
    request: CherwellWorkItemRequest
  ): Promise<CherwellWorkItemResponse> {
    try {
      this.logger.log('Creating Cherwell work item for AWS approval', request);

      const cherwellPayload = {
        ticketingSystem: TicketType.CHERWELL,
        subject: request.subject,
        description: request.description,
        assignmentGroup: request.assignmentGroup,
        serviceRequestId: request.serviceRequestId,
        subRequestId: request.subRequestId,
        priority: 'Medium', // Default priority
        category: 'AWS Architecture Approval',
      };

      const response = await this.ticketManagementService.retryTicket({
        ...cherwellPayload,
        action: 'CREATED',
      });

      this.logger.log('Cherwell work item created successfully', response);

      return {
        workItemId: response.ticketId,
        workItemNumber: response.ticketNumber || response.ticketId,
        status: response.status || 'Created',
        ticketUrl: response.ticketUrl,
      };
    } catch (error) {
      this.logger.error('Failed to create Cherwell work item', error);
      throw error;
    }
  }

  /**
   * Sends email notification to Srihari about AWS approval needed
   * @param workItemNumber - Cherwell work item number
   * @param serviceRequestId - Service request ID
   */
  async sendAwsApprovalEmail(
    workItemNumber: string, 
    serviceRequestId: string
  ): Promise<void> {
    try {
      this.logger.log('Sending AWS approval email to Srihari', { 
        workItemNumber, 
        serviceRequestId 
      });

      const emailPayload = {
        requestId: serviceRequestId,
        type: 'AWS_APPROVAL_REQUEST',
        templateId: process.env.AWS_APPROVAL_TEMPLATE_ID || process.env.REQUESTER_TEMPLATE_ID,
        message: `Please approve AWS workflow for ${workItemNumber}`,
        userEmail: this.SRIHARI_EMAIL,
        notificationParams: [
          {
            WORK_ITEM_NUMBER: workItemNumber,
            SERVICE_REQUEST_ID: serviceRequestId,
            APPROVAL_TYPE: 'AWS Architecture Approval',
          }
        ],
      };

      await (this.notificationService as any).integrationApi.post('notification/', emailPayload);
      
      this.logger.log('AWS approval email sent successfully to Srihari');
    } catch (error) {
      this.logger.error('Failed to send AWS approval email', error);
      throw error;
    }
  }

  /**
   * Main method to handle AWS device detection and Cherwell work item creation
   * @param serviceRequestId - Service request ID
   * @param pathAnalysisData - Path analysis data
   * @param subRequestId - Optional sub request ID
   */
  async handleAwsDeviceDetection(
    serviceRequestId: string,
    pathAnalysisData: any[],
    subRequestId?: string
  ): Promise<CherwellWorkItemResponse | null> {
    try {
      this.logger.log('Handling AWS device detection', {
        serviceRequestId,
        subRequestId
      });

      // Analyze path for AWS devices
      const detectionResult = this.analyzePathForAwsDevices(pathAnalysisData);

      if (!detectionResult.hasAwsDevices) {
        this.logger.log('No AWS devices detected in path');
        return null;
      }

      this.logger.log('AWS devices detected, creating Cherwell work item', {
        detectedDevices: detectionResult.detectedDevices
      });

      // Create Cherwell work item
      const workItemRequest: CherwellWorkItemRequest = {
        subject: 'Approval needed for AWS related service request from SDIT Architecture Team',
        description: `Approval needed for AWS related service request from Srihari Siddabathula before implementing.\n\nDetected AWS devices in path: ${detectionResult.detectedDevices.join(', ')}\n\nService Request ID: ${serviceRequestId}${subRequestId ? `\nSub Request ID: ${subRequestId}` : ''}`,
        assignmentGroup: 'Architecture DFD Team',
        serviceRequestId,
        subRequestId,
      };

      const workItemResponse = await this.createCherwellWorkItem(workItemRequest);

      // Send email notification to Srihari
      await this.sendAwsApprovalEmail(
        workItemResponse.workItemNumber,
        serviceRequestId
      );

      this.logger.log('AWS device detection and Cherwell work item creation completed', {
        workItemNumber: workItemResponse.workItemNumber,
        detectedDevices: detectionResult.detectedDevices
      });

      return workItemResponse;
    } catch (error) {
      this.logger.error('Failed to handle AWS device detection', error);
      throw error;
    }
  }

  /**
   * Handles Cherwell work item approval/rejection workflow
   * @param workItemId - Cherwell work item ID
   * @param status - Work item status (approved/rejected)
   * @param serviceRequestId - Service request ID
   * @param subRequestId - Optional sub request ID
   */
  async handleCherwellWorkItemStatus(
    workItemId: string,
    status: 'approved' | 'rejected',
    serviceRequestId: string,
    subRequestId?: string
  ): Promise<void> {
    try {
      this.logger.log('Handling Cherwell work item status update', {
        workItemId,
        status,
        serviceRequestId,
        subRequestId
      });

      if (status === 'approved') {
        this.logger.log(`Cherwell work item ${workItemId} approved - enabling approval tab in Nebula`);
        // Here you would integrate with Nebula's approval system
        // This could involve updating the service request status or enabling approval workflows

      } else if (status === 'rejected') {
        this.logger.log(`Cherwell work item ${workItemId} rejected - rejecting Nebula request`);

        // Send rejection email to requester
        await this.sendRejectionEmailToRequester(serviceRequestId, subRequestId);

        // Here you would integrate with Nebula to reject the request
        // This could involve updating the service request status to rejected
      }

    } catch (error) {
      this.logger.error('Failed to handle Cherwell work item status', error);
      throw error;
    }
  }

  /**
   * Sends rejection email to the requester when SDIT Architecture team rejects the request
   * @param serviceRequestId - Service request ID
   * @param subRequestId - Optional sub request ID
   */
  private async sendRejectionEmailToRequester(
    serviceRequestId: string,
    subRequestId?: string
  ): Promise<void> {
    try {
      this.logger.log('Sending rejection email to requester', {
        serviceRequestId,
        subRequestId
      });

      // You would need to get the requester's email from the service request
      // For now, using a placeholder - this should be retrieved from the actual service request
      const requesterEmail = '<EMAIL>'; // This should be fetched from service request

      const emailPayload = {
        requestId: serviceRequestId,
        type: 'AWS_REQUEST_REJECTION',
        templateId: process.env.REQUESTER_TEMPLATE_ID,
        message: 'Your Request has been rejected by SDIT Architecture team so if you have any question then reach <NAME_EMAIL> or <EMAIL> for additional details.',
        userEmail: requesterEmail,
        notificationParams: [
          {
            SERVICE_REQUEST_ID: serviceRequestId,
            SUB_REQUEST_ID: subRequestId || 'N/A',
            REJECTION_REASON: 'SDIT Architecture Team Rejection',
            CONTACT_EMAIL_1: this.SRIHARI_EMAIL,
            CONTACT_EMAIL_2: '<EMAIL>',
          }
        ],
      };

      // Use a public method from the notification service instead of direct API access
      // This would need to be implemented in the IntegrationNotificationService
      // For now, using a workaround
      await (this.notificationService as any).integrationApi.post('notification/', emailPayload);

      this.logger.log('Rejection email sent successfully to requester');
    } catch (error) {
      this.logger.error('Failed to send rejection email to requester', error);
      throw error;
    }
  }
}
