import { Test, TestingModule } from '@nestjs/testing';
import { NaasController } from './naas.controller';
import { NaasService } from './naas.service';
import { IpmService } from '../ipm/ipm.service';
import { NetworkContainerDto } from './dto/ipam/networkContainer.request.dto';
import { ItentialService } from '../itential/itential.service';
import { ScienceLogicService } from '../scienceLogic/scienceLogic.service';
import { IPAddressType } from '../ipm/dto/ipAddressType';
import { AddDeviceDto } from './dto/monitoring/configureDevice.request.dto';
import { BadRequestException } from '@nestjs/common';
import { CreateOrganizationDto } from './dto/monitoring/createOrganization.request.dto';
import { readFile } from 'fs/promises';
import { Response } from 'express';
import { ConfigService } from '@nestjs/config';
import { IamService } from '../iam/iam.service';
import { LoggerService } from '../loggers/logger.service';
import { DnpDapService } from 'src/dnpdap/dnpdap.service';

type MockNaasService = Partial<Record<keyof NaasService, jest.Mock>>;
type MockIpmService = Partial<Record<keyof IpmService, jest.Mock>>;
type MockItentialService = Partial<Record<keyof ItentialService, jest.Mock>>;
type MockScienceLogicService = Partial<
  Record<keyof ScienceLogicService, jest.Mock>
>;
type MockIamService = Partial<Record<keyof IamService, jest.Mock>>;

describe('NaasController', () => {
  let controller: NaasController;
  let mockNaasService: MockNaasService;
  let mockIpmService: MockIpmService;
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [NaasController],
      providers: [
        {
          provide: NaasService,
          useFactory: () => {
            const mockNaasService: MockNaasService = {
              createAvailableNetwork: jest.fn(),
              releaseIp: jest.fn(),
              dnp: jest.fn(),
              updateDNPRequest: jest.fn(),
              addDevice: jest.fn(),
              removeDevice: jest.fn(),
              reserveIp: jest.fn(),
              createOrg: jest.fn(),
              getReservedIpFile: jest.fn(),
            };
            return mockNaasService;
          },
        },
        {
          provide: IpmService,
          useFactory: () => {
            const mockIpmService: MockIpmService = {
              getNetworkContainer: jest.fn(),
            };
            return mockIpmService;
          },
        },
        {
          provide: ItentialService,
          useFactory: () => {
            const mockItentialService: MockItentialService = {};
            return mockItentialService;
          },
        },
        {
          provide: ScienceLogicService,
          useValue: () => {
            const mockScienceLogicService: MockScienceLogicService = {};
            return mockScienceLogicService;
          },
        },
        {
          provide: IamService,
          useFactory: () => {
            const mockIamService: MockIamService = {};
            return mockIamService;
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(),
          },
        },
        {
          provide: DnpDapService,
          useValue: {},
        },
        {
          provide: LoggerService,
          useValue: {
            log: jest.fn(),
            error: jest.fn(),
            debug: jest.fn(),
            info: jest.fn(),
            fatal: jest.fn(),
            warn: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<NaasController>(NaasController);
    mockNaasService = module.get<MockNaasService>(NaasService);
    mockIpmService = module.get<MockIpmService>(IpmService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('createNextAvailableNetwork', () => {
    it('should create the next available network from infoblox', async () => {
      const mockCreateNetworkInput: NetworkContainerDto = {
        datacenter: 'Irvine - IRVOCAFL',
        company: 'LCHARTER',
        environment: 'RDC',
        addrReachability: 'Private',
        ipAllocType: 'Management',
        ipAddrType: IPAddressType.IPV4,
        cidrBlock: '/29',
        comment: 'unit testing',
        description: 'unit test case',
        deeplinkUrl: 'mockDeepLinkUrl',
      };
      const mockGetContainer = ['**********/29'];
      const mockCreateNetworkValue = {
        id: 'somemockid',
        message: 'Request submitted for approval',
      };
      mockIpmService.getNetworkContainer.mockImplementation(
        async () => mockGetContainer,
      );
      mockNaasService.createAvailableNetwork.mockImplementationOnce(
        async () => mockCreateNetworkValue,
      );
      expect(
        await controller.createNextAvailableNetwork(mockCreateNetworkInput),
      ).toBe(mockCreateNetworkValue);
    });
  });

  describe('reserveIp', () => {
    it('should allow to upload excel file to reserve Ip address', async () => {
      const filePath = `${__dirname}/mockData/validipv4-1.xlsx`;
      const mockFile = {
        fieldname: 'file',
        originalname: 'validipv4-1.xlsx',
        encoding: '7bit',
        mimetype: 'text/xlsx',
        buffer: Buffer.from(filePath, 'utf8'),
        size: 51828,
      } as Express.Multer.File;
      const payload = JSON.stringify({
        deeplinkUrl: 'mockDeepLinkUrl',
      });
      const mockreserveIpResponse = {
        id: '65700b9c6fe75ccff009cff1',
        message: 'Request submitted for approval',
      };
      mockNaasService.reserveIp.mockImplementationOnce(
        () => mockreserveIpResponse,
      );
      const apiResponse = await controller.reserveIp(payload, mockFile);
      expect(apiResponse).toStrictEqual(mockreserveIpResponse);
    });

    it('should throw exception for invalid file type', async () => {
      const filePath = `${__dirname}/mockData/validipv4-1.xlsx`;
      const mockFile = {
        fieldname: 'file',
        originalname: 'validipv4-1.csv',
        encoding: '7bit',
        mimetype: 'text/csv',
        buffer: Buffer.from(filePath, 'utf8'),
        size: 51828,
      } as Express.Multer.File;
      const payload = JSON.stringify({
        deeplinkUrl: 'mockDeepLinkUrl',
      });
      await controller.reserveIp(payload, mockFile).catch((error) => {
        expect(error).toBeInstanceOf(BadRequestException);
        expect(error.message).toStrictEqual(
          'Please provide file with correct extension',
        );
      });
    });
  });

  describe('releaseIp', () => {
    it('should call NaasService.releaseIp to release Ip address', async () => {
      const mockReleaseIpRequest = {
        ipAddress: ['***********', '***********'],
        reserveRequestId: [
          '65700b9c6fe75ccff009cff1',
          '65700b9c6fe75ccff009cff2',
        ],
        name: 'mockhost',
        deeplinkUrl: 'mockDeepLinkUrl',
      };
      await controller.releaseIp(mockReleaseIpRequest);
      expect(mockNaasService.releaseIp).toBeCalled();
      expect(mockNaasService.releaseIp).toBeCalledWith(mockReleaseIpRequest);
    });

    it('should return successfully submitted request value from NaasService.releaseIp if correct data passed', async () => {
      const mockReturnValue = {
        id: '65700b9c6fe75ccff009cff3',
        message: 'Request submitted for approval',
      };
      mockNaasService.releaseIp.mockImplementation(async () => {
        return mockReturnValue;
      });

      const mockReleaseIpRequest = {
        ipAddress: ['***********', '***********'],
        reserveRequestId: [
          '65700b9c6fe75ccff009cff1',
          '65700b9c6fe75ccff009cff2',
        ],
        name: 'mockhost',
        deeplinkUrl: 'mockDeepLinkUrl',
      };
      const mockResponse = await controller.releaseIp(mockReleaseIpRequest);
      expect(mockNaasService.releaseIp).toBeCalled();
      expect(mockNaasService.releaseIp).toBeCalledWith(mockReleaseIpRequest);
      expect(mockResponse).toBe(mockReturnValue);
    });

    it('should throw if NaasService.releaseIp throws error', async () => {
      mockNaasService.releaseIp.mockImplementation(async () => {
        throw new Error('Some error');
      });

      const mockReleaseIpRequest = {
        ipAddress: ['***********', '***********'],
        reserveRequestId: [
          '65700b9c6fe75ccff009cff1',
          '65700b9c6fe75ccff009cff2',
        ],
        name: 'mockhost',
        deeplinkUrl: 'mockDeepLinkUrl',
      };
      expect.assertions(3);
      await controller.releaseIp(mockReleaseIpRequest).catch((error) => {
        expect(error).toBeInstanceOf(Error);
      });
      expect(mockNaasService.releaseIp).toBeCalled();
      expect(mockNaasService.releaseIp).toBeCalledWith(mockReleaseIpRequest);
    });
  });

  describe('getReservedIpFile', () => {
    it('should return file stream from object storage service', async () => {
      const file = `${__dirname}/mockData/validipv4-1.xlsx`;
      const mockRequestId = '65700b9c6fe75ccff009cff3';
      const mockBuffer = await readFile(file);
      const mockres = {
        headers: {
          'content-type': 'application/json',
          'content-disposition': 'attachment; filename=validipv4-1.xlsx',
        },
        stream: mockBuffer,
        pipe: jest.fn().mockImplementation(() => mockBuffer),
      };

      const mockheader = {
        'content-type': 'application/json',
        'content-disposition': 'attachment; filename=validipv4-1.xlsx',
      };

      const response: Partial<Response> = {
        set: jest.fn(),
        writeHead: jest.fn().mockImplementation(() => mockheader),
        on: jest.fn().mockImplementation((event, cb) => {
          if (event === 'finish') {
            cb(mockBuffer);
          } else if (event === 'data') {
            cb(mockBuffer);
          }
        }),
      };

      mockNaasService.getReservedIpFile.mockImplementationOnce(() => mockres);
      await controller.getReservedIpFile(response as Response, mockRequestId);

      expect(mockNaasService.getReservedIpFile).toBeCalledTimes(1);
    });

    it('should throw exception for invalid id', async () => {
      await controller.getReservedIpFile(null, null).catch((error) => {
        expect(error).toBeInstanceOf(BadRequestException);
        expect(error.message).toStrictEqual('Invalid id');
      });
    });
  });
  describe('dnp', () => {
    it('should call NaasService.dnp with correct data', async () => {
      const mockDNPRequest = {
        hosts: [
          {
            hostname: 'some-host-name',
            interfaces: [{ description: 'd1', number: 'n1', type: 't1' }],
          },
        ],
        comment: '',
        deeplinkUrl: 'mockDeepLinkUrl',
      };

      await controller.dnp(mockDNPRequest);
      expect(mockNaasService.dnp).toBeCalled();
      expect(mockNaasService.dnp).toBeCalledWith(mockDNPRequest);
    });

    it('should return correct value from NaasService.dnp if correct data is passed', async () => {
      const mockReturnValue = {
        id: '65700b9c6fe75ccff009cff1',
        message: 'Request submitted for approval',
      };
      mockNaasService.dnp.mockImplementation(async () => {
        return mockReturnValue;
      });

      const mockDNPRequest = {
        hosts: [
          {
            hostname: 'some-host-name',
            interfaces: [{ description: 'd1', number: 'n1', type: 't1' }],
          },
        ],
        comment: '',
        deeplinkUrl: 'mockDeepLinkUrl',
      };

      const res = await controller.dnp(mockDNPRequest);
      expect(mockNaasService.dnp).toBeCalled();
      expect(mockNaasService.dnp).toBeCalledWith(mockDNPRequest);
      expect(res).toBe(mockReturnValue);
    });

    it('should throw if NaasService.dnp throws error', async () => {
      mockNaasService.dnp.mockImplementation(async () => {
        throw new Error('Some error');
      });

      const mockDNPRequest = {
        hosts: [
          {
            hostname: 'some-host-name',
            interfaces: [{ description: 'd1', number: 'n1', type: 't1' }],
          },
        ],
        comment: '',
        deeplinkUrl: 'mockDeepLinkUrl',
      };
      expect.assertions(3);
      await controller.dnp(mockDNPRequest).catch((error) => {
        expect(error).toBeInstanceOf(Error);
      });
      expect(mockNaasService.dnp).toBeCalled();
      expect(mockNaasService.dnp).toBeCalledWith(mockDNPRequest);
    });
  });

  describe('updateDNPRequest', () => {
    const mockId = 'sasas';
    const mockDNPRequestDto = {
      hosts: [
        {
          hostname: 'some-host-name',
          interfaces: [{ description: 'd1', number: 'n1', type: 't1' }],
        },
      ],
      comment: '',
      deeplinkUrl: 'mockDeepLinkUrl',
    };

    it('should call NaasService.updateDNPRequest with correct data', async () => {
      await controller.updateDNPRequest(mockId, mockDNPRequestDto);
      expect(mockNaasService.updateDNPRequest).toBeCalled();
      expect(mockNaasService.updateDNPRequest).toHaveBeenCalledWith(
        mockId,
        mockDNPRequestDto,
      );
    });

    it('should return correct value from NassService.dnp if valid data is supplied', async () => {
      const mockUpdatedRequest = { mockKey: 'mockValue' };
      mockNaasService.updateDNPRequest.mockImplementationOnce(async () => {
        return mockUpdatedRequest;
      });
      const res = await controller.updateDNPRequest(mockId, mockDNPRequestDto);
      expect(res).toStrictEqual(mockUpdatedRequest);
    });

    it('should throw if NaasService.updateDNPRequest throws error', async () => {
      mockNaasService.updateDNPRequest.mockImplementationOnce(async () => {
        throw new Error('Some error');
      });
      expect.assertions(1);
      await controller
        .updateDNPRequest(mockId, mockDNPRequestDto)
        .catch((error) => {
          expect(error).toBeInstanceOf(Error);
        });
    });
  });

  describe('Add Device', () => {
    it('should call NaasService.addDevice with correct data', async () => {
      const addDeviceDto: AddDeviceDto = {
        monitoring: {
          device: {
            hostName: 'nebula-dev.stage.charter.com',
            organizationId: '0',
            snmpCredentialId: '48',
            collectorGroupId: '3',
          },
        },
        deeplinkUrl: 'mockDeepLinkUrl',
      };
      await controller.addDevice(addDeviceDto);
      expect(mockNaasService.addDevice).toBeCalled();
      expect(mockNaasService.addDevice).toBeCalledWith(addDeviceDto);
    });

    it('should return correct value from NaasService.addDevice if correct data is passed', async () => {
      const mockReturnValue = {
        id: '66890b9c6fe75ccff009cfe1',
        message: 'Request submitted for approval',
      };
      mockNaasService.addDevice.mockImplementation(async () => {
        return mockReturnValue;
      });
      const mockaddDeviceDto: AddDeviceDto = {
        monitoring: {
          device: {
            hostName: 'nebula-dev.stage.charter.com',
            organizationId: '0',
            snmpCredentialId: '48',
            collectorGroupId: '3',
          },
        },
        deeplinkUrl: 'mockDeepLinkUrl',
      };
      const result = await controller.addDevice(mockaddDeviceDto);
      expect(mockNaasService.addDevice).toBeCalled();
      expect(mockNaasService.addDevice).toBeCalledWith(mockaddDeviceDto);
      expect(result).toBe(mockReturnValue);
    });
  });

  describe('Remove device', () => {
    it('should call NaasService.removeDevice with correct data', async () => {
      const removeDeviceDto = {
        hostName: 'nebula-dev.stage.charter.com',
        deeplinkUrl: 'mockDeepLinkUrl',
      };
      await controller.removeDevice(removeDeviceDto);
      expect(mockNaasService.removeDevice).toBeCalled();
      expect(mockNaasService.removeDevice).toBeCalledWith(removeDeviceDto);
    });

    it('should return correct value from NaasService.remove if correct data is passed', async () => {
      const mockReturnValue = {
        id: '66890b9c6fe75ccff009cfe1',
        message: 'Request submitted for approval',
      };
      mockNaasService.removeDevice.mockImplementation(async () => {
        return mockReturnValue;
      });
      const removeDeviceDto = {
        hostName: 'nebula-dev.stage.charter.com',
        deeplinkUrl: 'mockDeepLinkUrl',
      };
      const result = await controller.removeDevice(removeDeviceDto);
      expect(mockNaasService.removeDevice).toBeCalled();
      expect(mockNaasService.removeDevice).toBeCalledWith(removeDeviceDto);
      expect(result).toBe(mockReturnValue);
    });
  });

  describe('Create Organization', () => {
    const mockOrgdata: CreateOrganizationDto = {
      monitoring: {
        organization: {
          company: 'organization-name',
          address: 'address',
          city: 'city',
          state: 'state',
          country: 'country',
          firstname: 'firstname',
          lastname: 'lastname',
          title: 'title',
          dept: 'department',
          billingId: 'billingId',
          crmId: 'crmId',
          phone: 'phone',
          fax: 'fax',
          tollfree: 'tollfree',
          email: 'email',
          theme: 'theme',
          longitude: 'longitude',
          latitude: 'latitude',
        },
      },
      deeplinkUrl: 'mockDeepLinkUrl',
    };
    it('should call NaasService.createOrg with correct data', async () => {
      await controller.createOrganization(mockOrgdata);
      expect(mockNaasService.createOrg).toBeCalled();
      expect(mockNaasService.createOrg).toBeCalledWith(mockOrgdata);
    });

    it('should return correct value from NaasService.createOrg if correct data is passed', async () => {
      const mockReturnValue = {
        id: '66890b9c6fe75ccff009cfe1',
        message: 'Request submitted for approval',
      };
      mockNaasService.createOrg.mockImplementation(async () => {
        return mockReturnValue;
      });

      const result = await controller.createOrganization(mockOrgdata);
      expect(mockNaasService.createOrg).toBeCalled();
      expect(mockNaasService.createOrg).toBeCalledWith(mockOrgdata);
      expect(result).toBe(mockReturnValue);
    });
  });
});
