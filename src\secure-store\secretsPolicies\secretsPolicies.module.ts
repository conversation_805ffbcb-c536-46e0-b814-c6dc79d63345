import { Modu<PERSON> } from '@nestjs/common';
import { SecretsPoliciesController } from './secretsPolicies.controller';
import { SecretsPoliciesRepository } from './secretsPolicies.repository';
import { SecretsPoliciesService } from './secretsPolicies.service';

@Module({
  controllers: [SecretsPoliciesController],
  providers: [SecretsPoliciesRepository, SecretsPoliciesService],
  exports: [SecretsPoliciesService],
})
export class SecretsPoliciesModule {}
