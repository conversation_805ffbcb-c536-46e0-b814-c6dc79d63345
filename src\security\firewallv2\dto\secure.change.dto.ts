export class Target {
  id: number;
  name: string;
  owner?: string;
  organization?: string;
}

export class SecureChangeRequestDto {
  projectName: string;
  projectCreator: string;
  organization: string;
  ticketId: string;
  description: string;
  summary: string;
  accessRequest: accessRequestDto[];
}

export class accessRequestDto {
  target: Target[];
  source: string;
  destination: string;
  service: string;
}

export class AclRequestDto {
  ipAddress: string;
}
