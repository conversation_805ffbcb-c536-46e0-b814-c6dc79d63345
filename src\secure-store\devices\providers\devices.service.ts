import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import {
  CreateSecretDeviceAssociationDto,
  UpdateSecretDeviceAssociationDto,
} from '../dto/devices.dto';
import { SecretDeviceAssociationRepository } from '../repositories/secretDeviceAssociation.repository';
import {
  ICreateSecretDeviceAssociation,
  IUpdateSecretDeviceAssociation,
} from '../types/CreateSecretDeviceAssociation.interface';
import { getUserContext } from '../../../utils/helpers';
import { RequestContext } from 'nestjs-request-context';
import { SecretsMetadataRepository } from '../../secretsMetadata/secretsMetadata.repository';
import { SecretType } from '../../secretsMetadata/types/secretsMetadata.enum';
import { MetadataService } from '../../../metadata-service/metadata.service';
import { ResourcesRepository } from 'src/resources/resources.repository';
import { RequestType } from '../../../types';
import { IntegrationNotificationService } from '../../../naas/integration.notification.service';
import { MultiEnvIAMService } from '../../../multi-env/iam/iam.service';
import { ResourcesService } from '../../../resources/resources.service';
@Injectable()
export class DevicesService {
  constructor(
    private readonly secretDeviceAssociationRepository: SecretDeviceAssociationRepository,
    private readonly secretsMetadataRepository: SecretsMetadataRepository,
    private readonly metadataService: MetadataService,
    private readonly resourceRepository: ResourcesRepository,
    private readonly integrationNotificationService: IntegrationNotificationService,
    private readonly multiEnvIAMService: MultiEnvIAMService,
    private readonly resourcesService: ResourcesService,
  ) { }

  async validateInsertUpdatePayload(
    secretDevices: UpdateSecretDeviceAssociationDto[],
  ) {
    //Fetching and Validating SecretIds
    const fetchedSecrets =
      await this.secretsMetadataRepository.getAllSecretsMetaDataBySecretIds(
        secretDevices.map((obj) => obj.secretId),
      );

    if (fetchedSecrets.length !== secretDevices.length) {
      throw new BadRequestException('Please provide valid secretIds');
    }

    //Fetching and Validating DeviceIds
    const resource = await this.resourceRepository.findOneByResourceId(
      fetchedSecrets[0].resourceId,
    );

    const fetchedDevices = await this.metadataService.getDeviceSettingsByEnvId(
      resource.platformContext.envId,
    );

    const fetchedDeviceIds = fetchedDevices.map(
      (device) => device?.configuration?.id,
    );

    secretDevices.forEach((device) => {
      if (!fetchedDeviceIds.includes(device.deviceId)) {
        throw new BadRequestException('Please provide valid deviceIds');
      }
      const deviceDetails = fetchedDevices.find(
        (deviceDetail) => deviceDetail.configuration.id === device.deviceId,
      );

      if (deviceDetails.configuration.sourceSystem !== device.sourceSystem) {
        throw new BadRequestException(
          'Please provide valid sourceSystem for the device ' +
          deviceDetails.configuration.name,
        );
      }
    });

    //Fetching and Validating SecretIds for existence
    const result = await this.secretDeviceAssociationRepository.findBySecretIds(
      secretDevices
        .filter((obj) => !obj.secretAssociationId)
        .map((obj) => obj.secretId),
    );

    if (result.length) {
      throw new BadRequestException(
        'Secrets provided were already mapped to different devices',
      );
    }
  }

  async createSecretDeviceAssociations(
    secretDeviceAssociationData: CreateSecretDeviceAssociationDto[],
  ) {
    await this.validateInsertUpdatePayload(secretDeviceAssociationData);

    const secretDeviceAssociationDocuments: ICreateSecretDeviceAssociation[] =
      secretDeviceAssociationData.map((device) => ({
        deviceId: [device.deviceId],
        secretId: [device.secretId],
        sourceSystem: device.sourceSystem,
        createdBy: getUserContext(RequestContext)?.userId,
      }));

    const result = await this.secretDeviceAssociationRepository.create(
      secretDeviceAssociationDocuments,
    );

    if (result.length) {
      const secretDeviceAssociationPayload = result.map(device => {
        return {
          deviceId: device.deviceId[0],
          secretId: device.secretId[0]
        }
      });

      this.secretDeviceAssociationNotification(secretDeviceAssociationPayload, RequestType.ADD_DEVICE)
      return {
        message: 'Secret Device Association Succeeded',
      };
    } else {
      throw new UnprocessableEntityException({
        message: 'Failed to perform secret device association',
      });
    }
  }

  async bulkUpdate(payload): Promise<void> {
    await this.secretDeviceAssociationRepository.bulkUpdate(payload);
  }

  async updateSecretDeviceAssociations(
    secretDeviceAssociationsData: UpdateSecretDeviceAssociationDto[],
  ) {
    //Fetching Insertion and updation queries
    const insertionEntities = secretDeviceAssociationsData.filter(
      (entity) => !entity.secretAssociationId,
    );
    const updationEntities = secretDeviceAssociationsData.filter(
      (entity) => entity.secretAssociationId,
    );

    //Promise array to hold insert and update operation
    const promisesArray = [];

    if (insertionEntities.length) {
      promisesArray.push(
        this.createSecretDeviceAssociations(insertionEntities),
      );
    }

    if (updationEntities.length) {
      await this.validateInsertUpdatePayload(updationEntities);

      const updateSecretDeviceAssociatioEntities: IUpdateSecretDeviceAssociation[] =
        updationEntities.map((device) => ({
          secretAssociationId: device.secretAssociationId,
          deviceId: [device.deviceId],
          secretId: [device.secretId],
          sourceSystem: device.sourceSystem,
          updatedBy: getUserContext(RequestContext)?.userId,
        }));
      promisesArray.push(
        this.secretDeviceAssociationRepository.updateMany(
          updateSecretDeviceAssociatioEntities,
        ),
      );
    }

    const result = await Promise.all(promisesArray);

    if (result) {
      return {
        message: 'Secret Device Association Succeeded',
      };
    } else {
      throw new UnprocessableEntityException({
        message: 'Failed to perform secret device association',
      });
    }
  }

  async getAllRotatableSecretDeviceAssociation(
    namespace: string,
    path: string,
    envId: string,
  ) {
    const secretIds = (
      await this.secretsMetadataRepository.getSecretsMetaData({
        namespace: decodeURIComponent(namespace),
        path,
      })
    )
      .filter((sec) => sec.type === SecretType.ROTATABLE_SECRET)
      .map((sec) => sec?.secretId);

    const deviceIds = (
      await this.metadataService.getDeviceSettingsByEnvId(envId)
    ).map((device) => device?.configuration?.id);

    const result =
      await this.secretDeviceAssociationRepository.findBySecretIdsAndDeviceIds(
        secretIds,
        deviceIds,
      );

    return result;
  }

  async findBySecretIds(secretIds: string[]) {
    return this.secretDeviceAssociationRepository.findBySecretIds(secretIds);
  }

  async secretDeviceAssociationNotification(
    notificationPayload: { deviceId: number, secretId: string }[],
    action: RequestType.ADD_DEVICE | RequestType.REMOVE_DEVICE
  ) {
    const notificationPayload2 = await Promise.all(notificationPayload.map(async (device) => {
      const { deviceId, secretId } = device;
      const devicesDoc = await this.metadataService.getDeviceDetailsById(deviceId);

      const secretMetaData =
        await this.secretsMetadataRepository.getSecretsMetaDataBySecretId(
          secretId
        );
      if (!secretMetaData) {
        throw new NotFoundException(
          `Secret with id ${secretId} not found.`,
        );
      }

      return {
        devicename: devicesDoc[0].name ? devicesDoc[0].name : '',
        secretkey: secretMetaData.devicePasswordKey ? secretMetaData?.devicePasswordKey : '',
      }
    }));

    const secretId = notificationPayload[0].secretId;
    const secretMetaData =
      await this.secretsMetadataRepository.getSecretsMetaDataBySecretId(
        secretId
      );
    if (!secretMetaData) {
      throw new NotFoundException(
        `Secret with id ${secretId} not found.`,
      );
    }

    const {
      type,
      vaultNamespace,
      vaultPath,
      devicePasswordKey,
      resourceId, } = secretMetaData;

    const resources = await this.resourcesService.getResourceByResourceId(resourceId)
    if (!resources) {
      throw new NotFoundException(
        `Resource with id ${resourceId} not found.`,
      );
    }
    const projectData = await this.multiEnvIAMService.getProjectAppEnvByEnvId(
      resources?.platformContext?.envId,
    )
    if (!projectData) {
      throw new NotFoundException(
        `Project with Env id ${resources?.platformContext?.envId} not found.`,
      );
    }
    const { emailDistribution: emailList, projectName } = projectData;
    await this.integrationNotificationService.notifySecretDeviceAssociationChangesToNameSpaceGroup(
      secretId,
      SecretType.DEVICE_VAULT_SECRET_MAPPING,
      emailList,
      action,
      {
        project: projectName,
        namespace: vaultNamespace,
      },
      notificationPayload2
    );
  }
}
