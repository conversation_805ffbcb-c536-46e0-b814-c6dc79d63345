import {
  Injectable,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import {
  CreateSecretDeviceAssociationDto,
  UpdateSecretDeviceAssociationDto,
} from '../dto/devices.dto';
import { SecretDeviceAssociationRepository } from '../repositories/secretDeviceAssociation.repository';
import {
  ICreateSecretDeviceAssociation,
  IUpdateSecretDeviceAssociation,
} from '../types/CreateSecretDeviceAssociation.interface';
import { getUserContext } from '../../../utils/helpers';
import { RequestContext } from 'nestjs-request-context';
import { SecretsMetadataRepository } from '../../secretsMetadata/secretsMetadata.repository';
import { SecretType } from '../../secretsMetadata/types/secretsMetadata.enum';
import { IamService } from '../../../iam/iam.service';

@Injectable()
export class DevicesService {
  constructor(
    private readonly secretDeviceAssociationRepository: SecretDeviceAssociationRepository,
    private readonly secretsMetadataRepository: SecretsMetadataRepository,
    private readonly iamService: IamService,
  ) {}

  async createSecretDeviceAssociations(
    secretDeviceAssociationData: CreateSecretDeviceAssociationDto[],
  ) {
    const secretDeviceAssociationDocuments: ICreateSecretDeviceAssociation[] =
      secretDeviceAssociationData.map((device) => ({
        deviceId: [device.deviceId],
        secretId: [device.secretId],
        sourceSystem: device.sourceSystem,
        createdBy: getUserContext(RequestContext)?.userId,
      }));

    const result = await this.secretDeviceAssociationRepository.create(
      secretDeviceAssociationDocuments,
    );
    if (result.length) {
      return {
        message: 'Secret Device Association Succeeded',
      };
    } else {
      throw new UnprocessableEntityException({
        message: 'Failed to perform secret device association',
      });
    }
  }

  async bulkUpdate(payload): Promise<void> {
    await this.secretDeviceAssociationRepository.bulkUpdate(payload);
  }

  async updateSecretDeviceAssociations(
    secretDeviceAssociationsData: UpdateSecretDeviceAssociationDto[],
  ) {
    //Fetching Insertion and updation queries
    const insertionEntities = secretDeviceAssociationsData.filter(
      (entity) => !entity.secretAssociationId,
    );
    const updationEntities = secretDeviceAssociationsData.filter(
      (entity) => entity.secretAssociationId,
    );

    //Promise array to hold insert and update operation
    const promisesArray = [];

    if (insertionEntities.length) {
      promisesArray.push(
        this.createSecretDeviceAssociations(insertionEntities),
      );
    }

    if (updationEntities.length) {
      const updateSecretDeviceAssociatioEntities: IUpdateSecretDeviceAssociation[] =
        updationEntities.map((device) => ({
          secretAssociationId: device.secretAssociationId,
          deviceId: [device.deviceId],
          secretId: [device.secretId],
          sourceSystem: device.sourceSystem,
          updatedBy: getUserContext(RequestContext)?.userId,
        }));
      promisesArray.push(
        this.secretDeviceAssociationRepository.updateMany(
          updateSecretDeviceAssociatioEntities,
        ),
      );
    }

    const result = await Promise.all(promisesArray);

    if (result) {
      return {
        message: 'Secret Device Association Succeeded',
      };
    } else {
      throw new UnprocessableEntityException({
        message: 'Failed to perform secret device association',
      });
    }
  }

  async getAllRotatableSecretDeviceAssociation(
    namespace: string,
    path: string,
    envId: string,
  ) {
    const secretIds = (
      await this.secretsMetadataRepository.getSecretsMetaData({
        namespace,
        path,
      })
    )
      .filter((sec) => sec.type === SecretType.ROTATABLE_SECRET)
      .map((sec) => sec?.secretId);

    const deviceIds = (
      await this.iamService.getDeviceSettingsByEnvIds(envId)
    ).map((device) => device?.configration?.id);

    const result =
      await this.secretDeviceAssociationRepository.findBySecretIdsAndDeviceIds(
        secretIds,
        deviceIds,
      );

    if (result.length) return result;
    throw new NotFoundException(
      `Failed to find device asscoation for path '${path}' in '${namespace}' namespace`,
    );
  }
}
