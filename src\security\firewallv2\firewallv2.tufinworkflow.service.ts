import { IncomingTaskResultRequestDto } from '../dto/firewall.incoming.designer-result.request.dto';
import { Inject, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { AssetsService } from '../../assets/assets.service';
import { FirewallRequestDetailsRepository } from '../repository/firewallRequestDetails-repository';
import {
  EventPatterns,
  RequestStatus,
  RequestType,
  TufinTaskName,
  TufinTaskStatus,
  TufinWorkflowStepName,
  TufinWorkflowStepStatus,
} from '../../types';
import { TufinTaskRequestDto } from '../dto/firewall.tufin.job.request.dto';
import { RiskAnalysisDTOMapper } from '../../status/risk-analysis-dto-mapper.service';
import { DesignerResultDtoMapper } from '../../status/designer-result-dto-mapper.service';
import { FirewallV2Service } from './firewallv2.service';
import { CreateRiskAnalysisResultRequestDto } from '../dto/firewall.risk-analysis-result.request.dto';
import { AxiosInstance } from 'axios/index';
import { withResponseErrorHandler } from '../../utils/helpers';
import { FirewallDbOperationResult } from '../repository/firewall.db-operation.result';
import { FirewallRequestEntity } from '../entity/firewallRequest.entity';
import { RmqService } from '../../rmq/rmq.service';

import { TufinObjectRenameService } from './firewallv2.tufin.object-rename.service';
import { CatalogStepsService } from '../../catalog-steps/catalog-steps.service';
import { CatalogStepsEntity } from '../../catalog-steps/catalog-steps.entity';
import * as uuid from 'uuid';
import { ServiceRequestEntity } from '../../naas/entities/serviceRequest.entity';
import { ActivityLoggerWrapperService } from '../../activity-logs-wrapper/activity-logger-wrapper.service';
import { ENVIRONMENT_VARS, NO_ACL_DESIGNER_ERROR } from '../../utils/constants';
import { ConfigService } from '@nestjs/config';
import { ProcessStatus } from '../../activity-logs/types/process-status.enum';

@Injectable()
export class WorkflowService {
  constructor(
    private readonly assetsService: AssetsService,
    private readonly firewallRequestRepository: FirewallRequestDetailsRepository,
    private readonly riskAnalysisDTOMapper: RiskAnalysisDTOMapper,
    private readonly designerResultDtoMapper: DesignerResultDtoMapper,
    private readonly firewallV2Service: FirewallV2Service,
    private readonly rmqService: RmqService,
    private readonly tufinObjectRenameService: TufinObjectRenameService,
    @Inject('TUFIN_SERVICE_API')
    private readonly tufinServiceApi: AxiosInstance,
    private readonly catalogStepsService: CatalogStepsService,
    private readonly activityLoggerService: ActivityLoggerWrapperService,
    private readonly configService: ConfigService,
  ) {}

  private readonly logger = new Logger(WorkflowService.name);

  /**
   * Processes the result of a Tufin task based on the task's name and status, and takes appropriate actions to update the service request.
   *
   * The method follows these steps:
   *
   * 1. **Handle `FAILED` status**:
   *    - If the Tufin task has failed, updates the sub-request's status to `FAILED`, and also marks the Nebula request as
   *    `FAILED` if other tufin tickets are also 'Failed'
   *
   * 2. **Handle `SUCCESS` status**:
   *    - Based on the task name, the method processes the task results:
   *      - **Risk Analysis**: Calls `processRiskAnalysisStep` to handle the result of a risk analysis task.
   *      - **Designer**: Calls `processDesignerStep` to handle the result of a designer task.
   *      - For other tasks, we need to persist result in db which we will implement later.
   *      - We also attempt to move the workflow to the next step in Tufin except after designer step because
   *        we have approval step after designer.
   *
   * 3. **Final Task Check**:
   *    - If the task is the final step (`finalStep` is `true`), the method updates the Tufin ticket status to `SUCCESS`
   *      and updates the overall service request status.
   *
   */
  async processTufinTaskResult(
    taskResultRequestDto: IncomingTaskResultRequestDto,
  ): Promise<boolean> {
    this.logger.log(
      `Process tufin task result ${JSON.stringify(taskResultRequestDto)}`,
    );
    const { requestId, tufinTicketId, status, taskName } = taskResultRequestDto;

    const subRequestData =
      await this.firewallRequestRepository.findByServiceRequestIdAndTufinId(
        requestId,
        tufinTicketId,
      );
    this.logger.log(`Subrequest data : ${JSON.stringify(subRequestData)}`);
    if (!subRequestData) {
      this.logger.error(
        `Subrequest doesnt exists with ${requestId} and ${tufinTicketId}`,
      );
      throw new NotFoundException(
        `Subrequest doesnt exists with ${requestId} and ${tufinTicketId}`,
      );
    }

    //Persist task results in db.
    try {
      const taskResultUpdateResponse =
        await this.firewallV2Service.updateWorkflowStepsResult(
          requestId,
          tufinTicketId,
          taskResultRequestDto,
        );
      if (!taskResultUpdateResponse) {
        this.logger.error(
          `Failed to update workflow step result for serviceRequestId :${requestId}, tufinTicketId: ${tufinTicketId}`,
        );
      } else {
        this.logger.log(
          `Workflow step result updated for serviceRequestId :${requestId}, tufinTicketId: ${tufinTicketId}, taskResultUpdateResponse: ${taskResultUpdateResponse}`,
        );
      }
    } catch (error) {
      this.logger.error(
        error,
        `Failed to update workflow step result for serviceRequestId :${requestId}, tufinTicketId: ${tufinTicketId} in db`,
      );
    }

    const isNoAclErr = await this.checkIfNoAclDesigner(taskResultRequestDto);
    if (
      status === TufinWorkflowStepStatus.FAILED &&
      taskResultRequestDto?.taskName?.toLowerCase() !==
        TufinWorkflowStepName.DESIGNER
    ) {
      this.logger.log(
        `Tufin step ${taskName} failed to execute for ${taskResultRequestDto.tufinTicketId} due to the reason :${taskResultRequestDto.task_results}.`,
      );

      //Updating sub-request status to Failed
      this.logger.log(
        `Setting tufin status as Failed for tufin ticket : ${taskResultRequestDto.tufinTicketId}.`,
      );

      this.logger.log(
        `Identifying log step for activity log for ${taskResultRequestDto.taskName}`,
      );
      let step;
      let retryCount = 0;
      if (taskResultRequestDto.taskName?.toLowerCase() === 'designer') {
        step = ENVIRONMENT_VARS.DESIGNER_RESULTS_LOG_STEP;
        retryCount = this.getRetryCount(subRequestData, [
          'designer',
          TufinTaskName.DESIGNER,
        ]);
      } else if (
        taskResultRequestDto.taskName?.toLowerCase() === 'risk analysis'
      ) {
        step = ENVIRONMENT_VARS.RISK_ANALYSIS_LOG_STEP;
        retryCount = this.getRetryCount(subRequestData, [
          'risk analysis',
          TufinTaskName.RISK_ANALYSIS,
        ]);
      }
      this.logger.log(`Identified log step for activity log with step ${step}`);

      this.logger.log(
        `sending start activity for subrequest ${subRequestData.subRequestId}, request id ${subRequestData.requestId}`,
      );
      await this.sendStartActivity(
        subRequestData.requestId,
        subRequestData.subRequestId,
        step,
        retryCount,
      );
      this.logger.log(
        `sent start activity for subrequest ${subRequestData.subRequestId}, request id ${subRequestData.requestId}`,
      );
      await this.updateTufinTicketStatus(
        taskResultRequestDto.requestId,
        taskResultRequestDto.tufinTicketId,
        RequestStatus.FAILED,
        taskResultRequestDto.taskName,
      );
      this.logger.log(
        `sending failed activity for subrequest ${subRequestData.subRequestId}, request id ${subRequestData.requestId}`,
      );
      await this.sendFailedActivity(
        subRequestData.requestId,
        subRequestData.subRequestId,
        step,
        taskResultRequestDto.task_results,
        retryCount,
      );
      this.logger.log(
        `sent failed activity for subrequest ${subRequestData.subRequestId}, request id ${subRequestData.requestId}`,
      );

      this.logger.log(
        `check if nebula request status to be marked pending approval or pending designer result`,
      );
      if (taskResultRequestDto.taskName?.toLowerCase() === 'designer') {
        await this.firewallV2Service.updatePendingApprovalStatus(
          taskResultRequestDto.requestId,
        );
      } else if (
        taskResultRequestDto.taskName?.toLowerCase() === 'risk analysis'
      ) {
        await this.firewallV2Service.updatePendingDesignerResultStatus(
          taskResultRequestDto.requestId,
        );
      }

      //Update Nebula request.
      this.logger.log(
        `Check if nebula request status needs to be marked Failed.`,
      );

      this.logger.log(
        'Updating service request status from processTufinTaskResult-Tufin failed ',
        taskResultRequestDto,
      );
      await this.firewallV2Service.updateServiceRequestStatus(
        taskResultRequestDto.requestId,
        taskResultRequestDto.task_results,
      );
      return true;
    } else if (
      status.toLowerCase() == TufinWorkflowStepStatus.SUCCESS.toLowerCase() ||
      (taskName.toLowerCase() === TufinWorkflowStepName.DESIGNER &&
        status.toLowerCase() === TufinWorkflowStepStatus.FAILED.toLowerCase())
    ) {
      //Check the type of task for which we have received the result.
      if (taskName == TufinTaskName.RISK_ANALYSIS) {
        this.logger.log(
          `Sending start activity for ${taskResultRequestDto.tufinTicketId}.`,
        );
        let retryCount = this.getRetryCount(subRequestData, [
          'risk analysis',
          TufinTaskName.RISK_ANALYSIS,
        ]);
        await this.sendStartActivity(
          subRequestData.requestId,
          subRequestData.subRequestId,
          ENVIRONMENT_VARS.RISK_ANALYSIS_LOG_STEP,
          retryCount,
        );
        this.logger.log(
          `Processing risk analysis result for ${taskResultRequestDto.tufinTicketId}.`,
        );
        await this.processRiskAnalysisStep(taskResultRequestDto);
        this.logger.log(
          `Progress to next tufin step for ${taskResultRequestDto.tufinTicketId}.`,
        );
        await this.progressToNextTufinStep(taskResultRequestDto.tufinTicketId);
        this.logger.log(
          `Sending end activity for ${taskResultRequestDto.tufinTicketId}.`,
        );
        await this.sendEndActivity(
          subRequestData.requestId,
          subRequestData.subRequestId,
          ENVIRONMENT_VARS.RISK_ANALYSIS_LOG_STEP,
          retryCount,
        );
      } else if (
        taskName == TufinTaskName.DESIGNER ||
        taskName.toLowerCase() === TufinWorkflowStepName.DESIGNER
      ) {
        //Note: We do not move to next step after designer result step as we have to wait for the approval process.
        //Also, this is a last step in Tufin for some workflows like CBOE and Unkown.
        this.logger.log(
          `Sending start activity for ${taskResultRequestDto.tufinTicketId}.`,
        );
        const retryCount = this.getRetryCount(subRequestData, [
          'designer',
          TufinTaskName.DESIGNER,
        ]);
        await this.sendStartActivity(
          subRequestData.requestId,
          subRequestData.subRequestId,
          ENVIRONMENT_VARS.DESIGNER_RESULTS_LOG_STEP,
          retryCount,
        );
        this.logger.log(
          `Processing designer result for ${taskResultRequestDto.tufinTicketId}.`,
        );
        await this.processDesignerStep(taskResultRequestDto);
        this.logger.log(
          `Sending end activity for ${taskResultRequestDto.tufinTicketId}.`,
        );
        if (
          status.toLowerCase() === TufinWorkflowStepStatus.FAILED.toLowerCase()
        ) {
          await this.sendFailedActivity(
            subRequestData.requestId,
            subRequestData.subRequestId,
            ENVIRONMENT_VARS.DESIGNER_RESULTS_LOG_STEP,
            taskResultRequestDto.task_results,
            retryCount,
          );
        } else {
          await this.sendEndActivity(
            subRequestData.requestId,
            subRequestData.subRequestId,
            ENVIRONMENT_VARS.DESIGNER_RESULTS_LOG_STEP,
            retryCount,
          );
        }
      } else {
        this.logger.log(
          `Processing ${taskName} step for ${taskResultRequestDto.tufinTicketId}.`,
        );

        if (!taskResultRequestDto.finalStep) {
          this.logger.log(
            'Final step is not present for the tufin id',
            tufinTicketId,
          );
          await this.progressToNextTufinStep(
            taskResultRequestDto.tufinTicketId,
          );
        }
      }

      //Check if it's a final task for this ticket.
      if (taskResultRequestDto.finalStep) {
        this.logger.log(
          `Processing final step ${taskName} for ${taskResultRequestDto.tufinTicketId}.`,
        );

        //Special handling for Verification Review step.
        //If the Implementation step was not successful from Tufin side, it will send the task_results field as 'Not implemented'.
        //We need to mark sub-request as Failed in this case as the firewall was not implemented.

        this.logger.log(
          `Checking tufin is manually closed for tufin id :${tufinTicketId}`,
        );
        let requestStatusToBeUpdated: RequestStatus =
          subRequestData.status == RequestStatus.FAILED
            ? RequestStatus.FAILED
            : RequestStatus.SUCCESS;

        if (
          taskName === TufinTaskName.VERIFICATION &&
          typeof taskResultRequestDto.task_results === 'string' &&
          taskResultRequestDto.task_results.toLowerCase() ==
            TufinTaskStatus.NOT_IMPLEMENTED.toLowerCase()
        ) {
          this.logger.log(
            'Implementation step was not successful from Tufin side',
            tufinTicketId,
          );
          requestStatusToBeUpdated = RequestStatus.FAILED;
        } else {
          await this.progressToNextTufinStep(
            taskResultRequestDto.tufinTicketId,
          );
        }
        if (
          await this.updateTufinTicketStatus(
            taskResultRequestDto.requestId,
            taskResultRequestDto.tufinTicketId,
            requestStatusToBeUpdated,
          )
        ) {
          this.logger.log(
            'Updating service request status from processTufinTaskResult-Tufin success ',
            taskResultRequestDto,
          );
          const request =
            await this.firewallV2Service.updateServiceRequestStatus(
              taskResultRequestDto.requestId,
              taskResultRequestDto.task_results,
            );
          const subRequest =
            await this.firewallRequestRepository.findByServiceRequestIdAndTufinId(
              taskResultRequestDto.requestId,
              taskResultRequestDto.tufinTicketId,
            );
          this.logger.log(`Pushing subrequest ${subRequest.id} into rabbit MQ`);
          await this.rmqService.pushMessage(
            RequestType.FIREWALL_V2,
            {
              id: subRequest.id,
              payload: subRequest,
              serviceRequestId: subRequest.requestId,
            },
            EventPatterns.FIREWALL_V2_SUBREQUEST_CLOSED,
          );

          return request;
        } else {
          //Failed to update tufin ticket status.
          return false;
        }
      }
      return true;
    }
  }

  async checkIfNoAclDesigner(
    taskResultRequestDto: IncomingTaskResultRequestDto,
  ): Promise<boolean> {
    if (
      taskResultRequestDto.status === TufinWorkflowStepStatus.FAILED &&
      taskResultRequestDto.taskName?.toLowerCase() ===
        TufinWorkflowStepName.DESIGNER &&
      taskResultRequestDto.task_results?.includes(NO_ACL_DESIGNER_ERROR)
    ) {
      return true;
    }
    return false;
  }

  private async progressToNextTufinStep(
    tufinTicketId: string,
  ): Promise<boolean> {
    this.logger.log(
      `Attempting to move tufin ticket ${tufinTicketId} to the next step.`,
    );

    try {
      const tufinResponse = await withResponseErrorHandler(
        this.tufinServiceApi.put(
          `/firewall/tufin/process-change/${tufinTicketId}`,
        ),
      );

      this.logger.log(
        `Tufin api response for ticket ${tufinTicketId}: ${JSON.stringify(tufinResponse)}`,
      );

      return true;
    } catch (error) {
      this.logger.error(
        error,
        `Exception while moving workflow to next step for ${tufinTicketId}`,
      );
      return false;
    }
  }

  private async processDesignerStep(
    taskResultRequestDto: IncomingTaskResultRequestDto,
  ): Promise<boolean> {
    try {
      this.logger.debug(`Process designer step: ${taskResultRequestDto}`);
      const transformedTaskResultsRequestDto =
        this.designerResultDtoMapper.transformIncomingToDesignerResultsRequestDto(
          taskResultRequestDto,
        );

      this.logger.debug(
        `Transformed designer result dto: ${JSON.stringify(transformedTaskResultsRequestDto)}`,
      );

      this.logger.debug(`Process renaming`);
      await this.tufinObjectRenameService.processRenaming(
        transformedTaskResultsRequestDto,
      );

      this.logger.debug(`Creating designer results`);
      const enableApproval =
        taskResultRequestDto.status === TufinWorkflowStepStatus.FAILED
          ? taskResultRequestDto.task_results.includes(NO_ACL_DESIGNER_ERROR)
            ? true
            : false
          : true;
      await this.firewallV2Service.createDesignerResults(
        transformedTaskResultsRequestDto,
        enableApproval,
      );

      return true;
    } catch (error) {
      this.logger.error(
        error,
        `Exception while processing designer step for ${taskResultRequestDto}`,
      );
      return false;
    }
  }

  private async processRiskAnalysisStep(
    taskResultRequestDto: IncomingTaskResultRequestDto,
  ): Promise<boolean> {
    try {
      this.logger.debug(`Process risk analysis step: ${taskResultRequestDto}`);
      const transformedRiskAnalysisResult =
        this.riskAnalysisDTOMapper.transformRiskAnalysis(
          taskResultRequestDto.task_results,
        );

      this.logger.debug(
        `Transformed risk analysis result: ${JSON.stringify(transformedRiskAnalysisResult)}`,
      );

      const createRiskAnalysisResultRequestDto: CreateRiskAnalysisResultRequestDto =
        {
          requestId: taskResultRequestDto.requestId,
          tufinTicketId: taskResultRequestDto.tufinTicketId,
          riskAnalysis: transformedRiskAnalysisResult,
        };

      this.logger.debug(
        `CreateRiskAnalysisResultRequestDto: ${JSON.stringify(createRiskAnalysisResultRequestDto)}`,
      );

      await this.firewallV2Service.createRiskAnalysisResults(
        createRiskAnalysisResultRequestDto,
      );

      return true;
    } catch (error) {
      this.logger.error(
        error,
        `Exception while processing risk analysis step for ${taskResultRequestDto}`,
      );
      return false;
    }
  }

  async updateTufinTicketStatus(
    serviceRequestId: string,
    tufinTicketId: string,
    status: RequestStatus,
    taskname?: string,
  ): Promise<boolean> {
    //Initialize tufin ticket status to be same as service request status.
    //Just in case we face any issue getting tufin staus from the api, we will fall back to the request status.
    let tufinTicketStatus = status;

    try {
      this.logger.log(
        `Update tufin ticket status: ${serviceRequestId} ${tufinTicketId} ${status} ${taskname}`,
      );
      this.logger.log('Get tufin ticket status from tufin service.');
      const tufinTicketStatusResponse = await withResponseErrorHandler(
        this.tufinServiceApi.get(`firewall/ticket/${tufinTicketId}`),
      );

      this.logger.log(
        `Tufin api response for ticket details: ${JSON.stringify(tufinTicketStatusResponse)}`,
      );

      if (!tufinTicketStatusResponse || !tufinTicketStatusResponse.status) {
        this.logger.error(
          `Invalid tufin ticket response. Unable to update tufin ticket status.`,
        );
      } else {
        tufinTicketStatus = tufinTicketStatusResponse.status;
      }
    } catch (error) {
      this.logger.error(
        error.stack,
        `Exception while fetching tufin ticket status for ${serviceRequestId} ${tufinTicketId} ${status}`,
      );
    }

    try {
      const dbResponse: FirewallDbOperationResult<FirewallRequestEntity> =
        await this.firewallRequestRepository.updateTufinStatus(
          serviceRequestId,
          tufinTicketId,
          new TufinTaskRequestDto(),
          tufinTicketStatus,
          status,
          taskname,
        );
      this.logger.log(`Update tufin status DB response: ${dbResponse}`);

      return dbResponse.success;
    } catch (error) {
      this.logger.error(
        error,
        `Exception while updating tufin ticket status for ${serviceRequestId} ${tufinTicketId} ${status}`,
      );
      return false;
    }
  }

  async sendStartActivity(requestId, subRequestId, logStep, retryCount) {
    this.logger.log(
      `Send start activity: ${requestId} ${subRequestId} ${logStep}`,
    );
    if (logStep) {
      let catalogStepsObj;
      let steps = [];
      const childSteps = [];
      let index;
      let childIndex;
      const traceId = uuid.v4();

      //fetching service request using payload nebularequestid
      const serviceRequest: ServiceRequestEntity =
        await this.assetsService.getByServiceRequestId(requestId);

      this.logger.log(`fetched service request ${serviceRequest}`);
      //fetch steps for catalog

      steps = await this.getCatalogSteps();

      index = this.configService.get(logStep);
      await this.activityLoggerService.sendActivity(
        ProcessStatus.STARTED,
        steps,
        requestId,
        {},
        traceId,
        index,
        serviceRequest,
        '',
        childSteps,
        childIndex,
        undefined,
        undefined,
        subRequestId,
        retryCount,
      );
    }
  }

  async sendEndActivity(requestId, subRequestId, logStep, retryCount) {
    this.logger.log(
      `Send end activity: ${requestId} ${subRequestId} ${logStep}`,
    );
    if (logStep) {
      let catalogStepsObj;
      let steps = [];
      const childSteps = [];
      let index;
      let childIndex;
      const traceId = uuid.v4();
      const serviceRequest: ServiceRequestEntity =
        await this.assetsService.getByServiceRequestId(requestId);

      this.logger.log(`fetched service request ${serviceRequest}`);
      //fetch steps for catalog

      steps = await this.getCatalogSteps();
      index = this.configService.get(logStep);
      await this.activityLoggerService.sendActivity(
        ProcessStatus.COMPLETED,
        steps,
        requestId,
        {},
        traceId,
        index,
        serviceRequest,
        '',
        childSteps,
        childIndex,
        'Service request updated successfully',
        undefined,
        subRequestId,
        retryCount,
      );
    }
  }

  async sendFailedActivity(
    requestId,
    subRequestId,
    logStep,
    errorMessage: string = 'step failed at tufin',
    retryCount,
  ) {
    this.logger.log(
      `Send failed activity: ${requestId} ${subRequestId} ${logStep}`,
    );
    if (logStep) {
      let catalogStepsObj;
      let steps = [];
      const childSteps = [];
      let index;
      let childIndex;
      const traceId = uuid.v4();
      const serviceRequest: ServiceRequestEntity =
        await this.assetsService.getByServiceRequestId(requestId);

      this.logger.log(`fetched service request ${serviceRequest}`);
      //fetch steps for catalog

      steps = await this.getCatalogSteps();
      index = this.configService.get(logStep);
      await this.activityLoggerService.sendActivity(
        ProcessStatus.FAILED,
        steps,
        requestId,
        {},
        traceId,
        index,
        serviceRequest,
        '',
        childSteps,
        childIndex,
        undefined,
        { statusCode: '500', message: errorMessage },
        subRequestId,
        retryCount,
      );
    }
  }

  async getCatalogSteps() {
    try {
      const type = RequestType.FIREWALL_V2;
      const catalogStepsObj =
        await this.catalogStepsService.findActiveStepsByRequestType(type);
      const steps = catalogStepsObj.activityLogSteps.sort(
        (a, b) => a.sequence - b.sequence,
      );
      this.logger.debug(`steps for webhook ${steps}`);

      return steps;
    } catch (err) {
      this.logger.error(
        err,
        'error while fetching catalog steps using request type',
      );
    }
  }

  getRetryCount(subrequest: FirewallRequestEntity, taskNameArray: string[]) {
    const filteredTasks =
      subrequest?.ticketDetails?.tufin?.workflowStepsResults?.filter((task) =>
        taskNameArray.includes(task.stepName),
      );
    if (filteredTasks?.length) {
      return filteredTasks.length - 1;
    }
    return 0;
  }
}
