// secrets-metadata.entity.spec.ts
import { SecretsMetadataEntity } from './secretsMetadata.entity';

describe('SecretsMetadataEntity', () => {
  it('should create an instance with required fields', () => {
    const entity = new SecretsMetadataEntity();
    entity.secretId = 'secret123';
    entity.type = 'ROTATABLE_SECRET';
    entity.status = 'ACTIVE';

    expect(entity.secretId).toBe('secret123');
    expect(entity.type).toBe('ROTATABLE_SECRET');
    expect(entity.status).toBe('ACTIVE');
  });

  it('should allow optional fields to be undefined', () => {
    const entity = new SecretsMetadataEntity();
    expect(entity.description).toBeUndefined();
    expect(entity.tokenTTLInHours).toBeUndefined();
    expect(entity.expiryDate).toBeUndefined();
    expect(entity.resourcesDetails).toBeUndefined();
    expect(entity.notificationEnabled).toBeUndefined();
    expect(entity.tokenRenewByNebula).toBeUndefined();
    expect(entity.rotationReason).toBeUndefined();
    expect(entity.rotationType).toBeUndefined();
    expect(entity.nextRotationDate).toBeUndefined();
    expect(entity.vaultPath).toBeUndefined();
    expect(entity.vaultHistoryVersion).toBeUndefined();
    expect(entity.secretVersion).toBeUndefined();
    expect(entity.error).toBeUndefined();
  });

  it('should accept and store all fields correctly', () => {
    const entity = new SecretsMetadataEntity();
    entity.secretId = 'secret456';
    entity.type = 'VAULT_TOKEN';
    entity.status = 'INACTIVE';
    entity.tokenTTLInHours = 12;
    entity.expiryDate = new Date('2025-07-01T00:00:00Z');
    entity.resourcesDetails = { key: 'value' };
    entity.notificationEnabled = true;
    entity.tokenRenewByNebula = false;
    entity.rotationReason = 'Scheduled';
    entity.rotationType = 'AUTOMATIC';
    entity.nextRotationDate = new Date('2025-07-10T00:00:00Z');
    entity.vaultPath = '/vault/token';
    entity.vaultHistoryVersion = 1;
    entity.secretVersion = 2;
    entity.error = [{ code: 500, message: 'Internal error' }];

    expect(entity.secretId).toBe('secret456');
    expect(entity.resourcesDetails).toEqual({ key: 'value' });
    expect(entity.error).toEqual([{ code: 500, message: 'Internal error' }]);
  });

  it('should inherit from BaseEntity', () => {
    const entity = new SecretsMetadataEntity();
    expect(entity).toBeInstanceOf(Object); // Replace with BaseEntity if it's a class
  });
});
