import { Test } from '@nestjs/testing';
import { SecureStoreController } from './secure-store.controller';
import { AuthenticationGuard } from '../auth/authentication.guard';
import { SecureStoreService } from './secure-store.service';
import { RotateSecretRequestDto } from './dto/rotate-secret.request.dto';
import { RotateSecretResponseDto } from './dto/rotate-secret.response.dto';
import { UpdateVaultSecretRequestDto } from './dto/update-vault-secrets.request.dto';
import { HttpStatus, InternalServerErrorException } from '@nestjs/common';
import { PermissionGuard } from '../iam/permission.guard';
import { LoggerService } from '../loggers/logger.service';
import { NameSpaceType } from './enums/NamespaceType';
import { FetchSecretType } from './dto/fetch-secrets.response.dto';
import { CreateVaultSecretsRequestDto } from './dto/create-vault-secrets.request.dto';

describe('SecureStoreController', () => {
  let controller: SecureStoreController;

  const mockService = {
    updateVaultSecretByPath: jest.fn(),
    createRotateSecretRequest: jest.fn(),
    deleteSecretsByIds: jest.fn(),
    fetchAuthorizedNamespaces: jest.fn(),
    fetchPasswordPolicies: jest.fn(),
    generateUniquePassword: jest.fn(),
    fetchNamespaceResources: jest.fn(),
    fetchNamespaceData: jest.fn(),
    fetchVaultSecrets: jest.fn(),
    fetchVaultSecretsPaginated: jest.fn(),
    fetchSecretsDetails: jest.fn(),
    generatePassword: jest.fn(),
    encryptPassword: jest.fn(),
    decryptPassword: jest.fn(),
    createVaultSecrets: jest.fn(),
    renewBulkTokens: jest.fn(),
  };

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      controllers: [SecureStoreController],
      providers: [
        { provide: SecureStoreService, useValue: mockService },
        {
          provide: LoggerService,
          useValue: {
            warn: jest.fn(),
            debug: jest.fn(),
            error: jest.fn(),
            fatal: jest.fn(),
          },
        },
      ],
    })
      .overrideGuard(AuthenticationGuard)
      .useValue({ canActivate: jest.fn(() => true) })
      .overrideGuard(PermissionGuard)
      .useValue({ canActivate: jest.fn(() => true) })
      .compile();
    controller = module.get<SecureStoreController>(SecureStoreController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('updateVaultSecretByPath should return 200 with success message', async () => {
    const secretId = 'NEB-VAULT-TOKEN-12234';
    const dto: UpdateVaultSecretRequestDto = {
      vaultKey: 'p9ol8icyik9lp0p50',
      vaultPassword: 'tyuo',
      userNameKey: 'userName3',
      userNamePassword: 'string',
      notifyBeforeTokenExpiry: false,
    };
    const responseMock = {
      message: 'Secret is updated successfully.',
    };
    mockService.updateVaultSecretByPath.mockResolvedValue(responseMock);

    const result = await controller.updateVaultSecretByPath(secretId, dto);
    expect(result).toEqual(responseMock);
    expect(mockService.updateVaultSecretByPath).toHaveBeenCalledWith(
      secretId,
      dto,
    );
  });

  it('deleteSecretsByIds should return 200 with success message', async () => {
    const secretId = 'NEB-VAULT-TOKEN-12234';
    const responseMock = {
      status: HttpStatus.OK,
      message: 'Secrets deleted successfully.',
    };
    mockService.deleteSecretsByIds.mockResolvedValue(responseMock);

    const result = await mockService.deleteSecretsByIds(secretId);
    expect(result).toEqual(responseMock);
  });

  it('rotateSecrets should return rotated secret response', async () => {
    const rotateRequest: RotateSecretRequestDto[] = [
      {
        secretId: 'NEB-VAULT-ROT-SECRET-001',
        newPassword: 'TestnewPassword',
      } as RotateSecretRequestDto,
    ];

    const responseMock: RotateSecretResponseDto = {
      message: 'Secrets rotated successfully',
    };

    mockService.createRotateSecretRequest.mockResolvedValue(responseMock);

    const result = await controller.rotateSecretRequest(rotateRequest);
    expect(result).toEqual(responseMock);
    expect(mockService.createRotateSecretRequest).toHaveBeenCalledWith(
      rotateRequest,
    );
  });

  describe('fetchPasswordPolicy', () => {
    it('should return password policies from service', async () => {
      const mockNamespace = 'test-namespace';
      const mockPolicies = {
        policies: [
          {
            policyId: 'NEB-VAULT-PASSWORD-POLICY-12234',
            policyName: 'sample-dev-passwordmay201',
            description: 'test',
          },
        ],
      };

      mockService.fetchPasswordPolicies.mockResolvedValue(mockPolicies);

      const result = await controller.fetchPasswordPolicy(mockNamespace);

      expect(mockService.fetchPasswordPolicies).toHaveBeenCalledWith(
        mockNamespace,
      );
      expect(result).toEqual(mockPolicies);
    });

    it('should handle empty namespace gracefully', async () => {
      const mockPolicies = { policies: [] };

      mockService.fetchPasswordPolicies.mockResolvedValue(mockPolicies);

      const result = await controller.fetchPasswordPolicy('');

      expect(mockService.fetchPasswordPolicies).toHaveBeenCalledWith('');
      expect(result).toEqual(mockPolicies);
    });

    describe('generateUniquePassword', () => {
      it('should return result in object array', async () => {
        let mockIds = 'NEB-VAULT-ROT-SECRET-1063,NEB-VAULT-NOR-SECRET-1062';
        let mockResponse = [
          {
            secretId: 'NEB-VAULT-NOR-SECRET-1062',
            mockUserKey: '1XvuJ^wRz*z$yc%CBdzA',
          },
          {
            secretId: 'NEB-VAULT-ROT-SECRET-1063',
            mockkey: 'N*#u2V^tGuwtCjflz1d5',
          },
        ];
        mockService.generateUniquePassword.mockResolvedValue(mockResponse);
        const result = await controller.generateUniquePassword({
          ids: mockIds,
        });
        expect(result).toEqual(mockResponse);
      });
    });

    it('should fetch namespace data', async () => {
      mockService.fetchNamespaceData.mockResolvedValue([
        { label: 'path/test', value: 'path/test' },
      ]);
      const result = await controller.fetchNamespaceData(
        'nebula-stamp',
        NameSpaceType.PATH,
      );
      expect(result[0].label).toEqual('path/test');
    });

    it('should fetchVaultSecrets', async () => {
      const mockValue = {
        key: 'mySecretKey',
        value: 'mySecretValue',
        type: 'API_KEY',
        status: true,
        rotationType: 'daily',
        secretTTLInHours: 24,
        nextRotationDate: new Date(),
        updatedBy: 'system',
        updatedAt: new Date(),
      };

      mockService.fetchVaultSecretsPaginated.mockResolvedValue(mockValue);
      const result = await controller.fetchVaultSecrets('', '');
      expect(result).toEqual(mockValue);
    });

    it('should fetchSecretsDetails', async () => {
      const mockValue = {
        vaultKey: 'myVaultKey',
        vaultPassword: 'myVaultPassword',
        userNameKey: 'myUserNameKey',
        userNamePassword: 'myUserNamePassword',
        resourceId: 'myResourceId',
        policyId: 'myPolicyId',
        policyName: 'myPolicyName',
        rotationType: 'daily',
        expiryDate: new Date(Date.now() + 86400000),
        notificationEnabled: true,
        tokenTTLInHours: 12,
        nextRotationDate: new Date(Date.now() + 43200000),
        namespace: 'myNamespace',
        vaultPath: 'myVaultPath',
        type: 'someType',
      };

      mockService.fetchSecretsDetails.mockResolvedValue(mockValue);
      const result = await controller.fetchSecretsDetails('NEB_SEC_1000');
      expect(result).toEqual(mockValue);
    });

    it('should deleteSecretsByIds', async () => {
      mockService.deleteSecretsByIds.mockResolvedValue({
        status: HttpStatus.OK,
        message: 'Secrets deleted successfully.',
      });
      const result = await controller.deleteSecretsByIds({ ids: 'NEB_SEC_1' });
      expect(result).toEqual({
        status: HttpStatus.OK,
        message: 'Secrets deleted successfully.',
      });
    });

    it('should generatePassword', async () => {
      const result = await controller.generatePassword('nebula-stamp', {
        policyName: 'test',
      });
      expect(mockService.generatePassword).toHaveBeenCalled();
    });

    it('should encryptPassword', async () => {
      const result = await controller.encryptPassword('dgerwg@ger');
      expect(mockService.encryptPassword).toHaveBeenCalled();
    });
    it('should decryptPassword', async () => {
      const result = await controller.decryptPassword('#%$#T^hdfw');
      expect(mockService.decryptPassword).toHaveBeenCalled();
    });
  });
  // fetch namespace resource
  describe('fetchNamespaceResources', function () {
    it('should return all namespace resources user have access to', async function () {
      let mockResponse = [
        {
          id: '682b519e3388bf6af95954e1',
          resourcesName: 'test policy vault',
          requestId: 'NEB-SECRET-VAULT-12567',
          resourceId: 'NEB-VAULT-TOKEN-8382',
          status: 'ACTIVE',
          catalogType: 'vault-namespace',
          catalogLevel03: 'SharedServices-Secrets-Vault-Token',
          createdBy: '<EMAIL>',
          resourcesDetails: {
            request_id: '10f1e532-0f7a-39b9-8e78-2a0e4f25e9ba',
            lease_id: '',
            renewable: false,
            lease_duration: 0,
            data: {
              key_info: {
                'nebula-dev/': {
                  id: '1GbQ3',
                  path: 'nebula-stamp/nebula-dev/',
                },
                'nebula-qa/': {
                  id: 'Cn2zs',
                  path: 'nebula-stamp/nebula-qa/',
                },
                'nebula-stamp/': {
                  id: 'XJu3u',
                  path: 'nebula-stamp/nebula-stamp/',
                },
                'nebula-stg/': {
                  id: 'NmkZN',
                  path: 'nebula-stamp/nebula-stg/',
                },
              },
              keys: [
                'nebula-dev/',
                'nebula-qa/',
                'nebula-stamp/',
                'nebula-stg/',
              ],
            },
            wrap_info: null,
            warnings: null,
            auth: null,
          },
          projectId: '66967d8d75564197a15ea214',
          childId: [],
          platformContext: {
            catalogId: '660408f18fd3d3bc24ac58d8',
            domainId: '660408f18fd3d3bc24ac58d8',
            envId: '01966c73-b401-7285-bec1-b122f7118def',
          },
          tokenAccessor: 'string',
          parentID: 'NEB-RES-VAULT-NAMESPACE-1000',
          resourceType: 'Vault-Token',
          vaultTokenRequestId: 'asd',
          expiryDate: '2025-05-19T15:36:20.323Z',
          ttlInHours: 24,
          tokenRenewByNebula: true,
          namespace: 'nebula-stamp',
          createdAt: '2025-05-19T15:43:26.042Z',
          updatedAt: '2025-05-19T15:43:26.042Z',
        },
      ];
      mockService.fetchNamespaceResources.mockResolvedValue(mockResponse);
      const result = await controller.fetchNamespaceResources();
      expect(result).toEqual(mockResponse);
    });
  });

  describe('getAuthorizedNamespaces', function () {
    it('getAuthorizedNamespaces should return filtered namespaces', async () => {
      const mockNamespaces = {
        items: [
          {
            namespaceName: 'Test',
            resourceId: 'NEB-RES-VAULT-NAMESPACE-8572',
            status: 'ACTIVE',
            catalogType: 'Vault',
            catalogLevel03: 'Create-Namespace',
            platformContext: {
              catalogId: '68119d4cc8f7a6598328a116',
              envId: '01966c73-bf70-76ef-ab84-3ca2c5ed441d',
              domainId: '67ebc4fd49be555801fec525',
            },
            requestType: 'CREATE_NAMESPACE',
            projectName: 'Nebula BLUE 2886',
          },
        ],
      };

      mockService.fetchAuthorizedNamespaces.mockResolvedValue(mockNamespaces);

      const result = await controller.getAuthorizedNamespaces();
      expect(mockService.fetchAuthorizedNamespaces).toHaveBeenCalled();
      expect(result).toEqual(mockNamespaces);
    });
  });

  it('should call secureStoreService.createVaultSecrets and return result', async () => {
    const namespaceName = 'test-namespace';
    const dto: CreateVaultSecretsRequestDto = {
      type: 'VAULT_TOKEN',
      vaultKey: 'key',
      vaultPassword: 'pass',
      expiryDate: new Date(),
      tokenRenewByNebula: true,
    } as any;

    const mockResponse = { message: 'Secret created' };
    mockService.createVaultSecrets.mockResolvedValue(mockResponse);

    const result = await controller.createValutTokenSecrets(namespaceName, dto);

    expect(mockService.createVaultSecrets).toHaveBeenCalledWith(
      namespaceName,
      dto,
    );
    expect(result).toEqual(mockResponse);
  });

  it('should call secureStoreService.renewBulkTokens and return result', async () => {
    const secretIds = ['id1', 'id2'];
    const mockResponse = { success: true };

    mockService.renewBulkTokens.mockResolvedValue(mockResponse);

    const result = await controller.renewBulkTokens(secretIds);

    expect(mockService.renewBulkTokens).toHaveBeenCalledWith(secretIds);
    expect(result).toEqual(mockResponse);
  });
});
