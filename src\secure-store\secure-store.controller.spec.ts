import { Test } from '@nestjs/testing';
import { SecureStoreController } from './secure-store.controller';
import { AuthenticationGuard } from '../auth/authentication.guard';
import { SecureStoreService } from './secure-store.service';
import { RotateSecretRequestDto } from './dto/rotate-secret.request.dto';
import { RotateSecretResponseDto } from './dto/rotate-secret.response.dto';
import { UpdateVaultSecretRequestDto } from './dto/update-vault-secrets.request.dto';
import { HttpStatus, InternalServerErrorException, NotFoundException } from '@nestjs/common';
import { PermissionGuard } from '../iam/permission.guard';
import { LoggerService } from '../loggers/logger.service';
import { NameSpaceType } from './enums/NamespaceType';
import { FetchSecretType } from './dto/fetch-secrets.response.dto';
import { RotationType } from '../../src/secure-store/secretsMetadata/types/secretsMetadata.enum';
import { IamService } from '../../src/iam/iam.service';
import { CreateVaultSecretsRequestDto } from './dto/create-vault-secrets.request.dto';
import {
  ROTATION_STATUS
} from './enums/secret_rotation_request_type';


type MockIamService = Partial<Record<keyof IamService, jest.Mock>>;
describe('SecureStoreController', () => {
  let controller: SecureStoreController;
  let mockIamService: MockIamService;

  const mockService = {
    fetchSecretsHistory: jest.fn(),
    createVaultSecrets: jest.fn(),
    updateVaultSecretByPath: jest.fn(),
    createRotateSecretRequest: jest.fn(),
    deleteSecretsByIds: jest.fn(),
    fetchAuthorizedNamespaces: jest.fn(),
    fetchPasswordPolicies: jest.fn(),
    generateUniquePassword: jest.fn(),
    fetchNamespaceResources: jest.fn(),
    fetchNamespaceData: jest.fn(),
    fetchVaultSecrets: jest.fn(),
    fetchVaultSecretsPaginated: jest.fn(),
    fetchSecretsDetails: jest.fn(),
    generatePassword: jest.fn(),
    encryptPassword: jest.fn(),
    decryptPassword: jest.fn(),
    renewBulkTokens: jest.fn(),
    createNameSpaceRequest: jest.fn(),
    secretsRotationResult: jest.fn(),
    createPasswordPolicy: jest.fn()
  };

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      controllers: [SecureStoreController],
      providers: [
        { provide: SecureStoreService, useValue: mockService },
        {
          provide: LoggerService,
          useValue: {
            warn: jest.fn(),
            debug: jest.fn(),
            error: jest.fn(),
            fatal: jest.fn(),
          },
        },
        {
          provide: IamService,
          useValue: {
            findCatalogL4Permissions: jest.fn(),
            findCatalogLevel4ByRequestType: jest.fn(),
            findAllowedRequestTypeByPermissionKey: jest.fn(),
            findUserProjects: jest.fn(),
          },
        },
      ],
    })
      .overrideGuard(AuthenticationGuard)
      .useValue({ canActivate: jest.fn(() => true) })
      .overrideGuard(PermissionGuard)
      .useValue({ canActivate: jest.fn(() => true) })
      .compile();
    controller = module.get<SecureStoreController>(SecureStoreController);
    mockIamService = module.get<MockIamService>(IamService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('updateVaultSecretByPath should return 200 with success message', async () => {
    const secretId = 'NEB-VAULT-TOKEN-12234';
    const dto: UpdateVaultSecretRequestDto = {
      vaultKey: 'p9ol8icyik9lp0p50',
      vaultPassword: 'tyuo',
      userNameKey: 'userName3',
      userNamePassword: 'string',
      notifyBeforeTokenExpiry: false,
    };
    const responseMock = {
      message: 'Secret is updated successfully.',
    };
    mockService.updateVaultSecretByPath.mockResolvedValue(responseMock);

    const result = await controller.updateVaultSecretByPath(secretId, dto);
    expect(result).toEqual(responseMock);
    expect(mockService.updateVaultSecretByPath).toHaveBeenCalledWith(
      secretId,
      dto,
    );
  });

  it('deleteSecretsByIds should return 200 with success message', async () => {
    const secretId = 'NEB-VAULT-TOKEN-12234';
    const responseMock = {
      status: HttpStatus.OK,
      message: 'Secrets deleted successfully.',
    };
    mockService.deleteSecretsByIds.mockResolvedValue(responseMock);

    const result = await mockService.deleteSecretsByIds(secretId);
    expect(result).toEqual(responseMock);
  });

  it('rotateSecrets should return rotated secret response', async () => {
    const rotateRequest: RotateSecretRequestDto[] = [
      {
        secretId: 'NEB-VAULT-ROT-SECRET-001',
        newPassword: 'TestnewPassword',
      } as RotateSecretRequestDto,
    ];

    const responseMock: RotateSecretResponseDto = {
      message: 'Secrets rotated successfully',
    };

    mockService.createRotateSecretRequest.mockResolvedValue(responseMock);

    const result = await controller.rotateSecretRequest(rotateRequest);
    expect(result).toEqual(responseMock);
    expect(mockService.createRotateSecretRequest).toHaveBeenCalledWith(
      rotateRequest,
    );
  });

  // fetch namespace resource
  describe('fetchNamespaceResources', function () {
    it('should return all namespace resources user have access to', async function () {
      let mockResponse = [
        {
          id: '682b519e3388bf6af95954e1',
          resourcesName: 'test policy vault',
          requestId: 'NEB-SECRET-VAULT-12567',
          resourceId: 'NEB-VAULT-TOKEN-8382',
          status: 'ACTIVE',
          catalogType: 'vault-namespace',
          catalogLevel03: 'SharedServices-Secrets-Vault-Token',
          createdBy: '<EMAIL>',
          resourcesDetails: {
            request_id: '10f1e532-0f7a-39b9-8e78-2a0e4f25e9ba',
            lease_id: '',
            renewable: false,
            lease_duration: 0,
            data: {
              key_info: {
                'nebula-dev/': {
                  id: '1GbQ3',
                  path: 'nebula-stamp/nebula-dev/',
                },
                'nebula-qa/': {
                  id: 'Cn2zs',
                  path: 'nebula-stamp/nebula-qa/',
                },
                'nebula-stamp/': {
                  id: 'XJu3u',
                  path: 'nebula-stamp/nebula-stamp/',
                },
                'nebula-stg/': {
                  id: 'NmkZN',
                  path: 'nebula-stamp/nebula-stg/',
                },
              },
              keys: [
                'nebula-dev/',
                'nebula-qa/',
                'nebula-stamp/',
                'nebula-stg/',
              ],
            },
            wrap_info: null,
            warnings: null,
            auth: null,
          },
          projectId: '66967d8d75564197a15ea214',
          childId: [],
          platformContext: {
            catalogId: '660408f18fd3d3bc24ac58d8',
            domainId: '660408f18fd3d3bc24ac58d8',
            envId: '01966c73-b401-7285-bec1-b122f7118def',
          },
          tokenAccessor: 'string',
          parentID: 'NEB-RES-VAULT-NAMESPACE-1000',
          resourceType: 'Vault-Token',
          vaultTokenRequestId: 'asd',
          expiryDate: '2025-05-19T15:36:20.323Z',
          ttlInHours: 24,
          tokenRenewByNebula: true,
          namespace: 'nebula-stamp',
          createdAt: '2025-05-19T15:43:26.042Z',
          updatedAt: '2025-05-19T15:43:26.042Z',
        },
      ];
      mockService.fetchNamespaceResources.mockResolvedValue(mockResponse);
      const result = await controller.fetchNamespaceResources();
      expect(result).toEqual(mockResponse);
    });
  });

  describe('fetchNamespaceData', function () {
    it('should return all namespace data ', async function () {
      let mockResponse = [{
        namespace:'nebula-stamp',
        path:'path'
      }]
      mockService.fetchNamespaceData.mockResolvedValue(mockResponse);
      const result = await controller.fetchNamespaceData('nebula-stamp');
      expect(result).toEqual(mockResponse);
    });
  });

  
    describe('fetchVaultSecrets', function () {
    it('should return fetchVaultSecrets ', async function () {
      let mockResponse = [
        {
          key: "key",
          value: "value",
          type: "rotatable",
          status: true,
          rotationType: "auto",
          secretTTLInHours: 0,
          nextRotationDate: "2025-07-24T12:07:35.789Z",
          updatedBy: "2025-07-24T12:07:35.789Z",
          updatedAt: "2025-07-24T12:07:35.789Z"
        }
      ]
      
      mockService.fetchVaultSecrets.mockResolvedValue(mockResponse);
      mockService.fetchVaultSecretsPaginated.mockResolvedValue(mockResponse);
      const result = await controller.fetchVaultSecrets('path','nebula-stamp','',false);
      expect(result).toEqual(mockResponse);
    });
  });
  
  describe('secretsRotationResult', function () {
    it('should check secretsRotationResult ', async function () {
      let mockPayload ={
        nebulaSecretId: "secret1",
        status:  ROTATION_STATUS.COMPLETED,
        completedAt: "2025-07-24T12:07:35.789Z",
        // "error": {
        //   "code": 0,
        //   "message": "string"
        // },
        nebulaActionId: "123"
      }
      let mockResponse = { success:true, message:'secretRotationResult validated'}
      
      mockService.secretsRotationResult.mockResolvedValue(mockResponse);

      const result = await controller.secretsRotationResult(mockPayload);
      expect(result).toEqual(mockResponse);
    });
  });

  
  describe('fetchSecretsDetails', function () {
    it('should get SecretsDetails', async function () {

      let mockResponse =
        {
          id: "6874e0909a0158ca0752c916",
          secretId: "NEB-VAULT-ROT-SECRET-1556",
          path: "CO/CISCO/123",
          key: "sss",
          value: "de91c60ae8861388529a79ac92bb9feb2313c980b9eaf9bc9dd0cc776dd0f9bb",
          type: "Rotatable",
          status: "SUCCESS",
          rotationType: "Manual",
          secretTTLInHours: 720,
          policyId: "NEB-VAULT-PASSWORD-POLICY-12",
          expiryDate: "2025-07-14T11:31:11.713Z",
          nextRotationDate: "2025-07-14T18:30:00.000Z",
          updatedAt: "2025-07-14T10:48:48.334Z",
          lastDeviceSyncStatus: "NOT-STARTED",
          active: true,
          notifyBeforeTokenExpiry: true
        }
      
      mockService.fetchSecretsDetails.mockResolvedValue(mockResponse);
      const result = await controller.fetchSecretsDetails('secid123');
      expect(result).toEqual(mockResponse);
    });
  });

  
  describe('generatePassword', function () {
    it('should get generatedPassword', async function () {

      let mockResponse =
        {
          password:'HSGaasdj123@#'
        }
      
      mockService.generatePassword.mockResolvedValue(mockResponse);
      const result = await controller.generatePassword('namespace1', 'policy1');
      expect(result).toEqual(mockResponse);
    });
  });

  //TODO
  // describe.only('encryptPassword', function () {
  //   it.only('should get encryptPassword', async function () {

  //     let mockResponse = {encryptedPassword:{encryptedPassword: "HSGaasdj123@#"}}
        
      
  //     mockService.encryptPassword.mockResolvedValue(mockResponse);
  //     const result = await controller.encryptPassword('HSGaasdj123@#');
  //     expect(result).toEqual(mockResponse);
  //   });
  // });

  describe('generateUniquePassword', function () {
    it('should get generateUniquePassword', async function () {

      let mockResponse =[{password: "HSGaasdj123@#"}]
        
      
      mockService.generateUniquePassword.mockResolvedValue(mockResponse);
      const result = await controller.generateUniquePassword({ids:'1234'});
      expect(result).toEqual(mockResponse);
    });
  });

  describe('deleteSecretsByIds', function () {
    it('should deleteSecretsByIds', async function () {

      let mockResponse =[{success: true, message:'Secret deleted successfully'}]
        
      
      mockService.deleteSecretsByIds.mockResolvedValue(mockResponse);
      const result = await controller.deleteSecretsByIds({ids:'1234'});
      expect(result).toEqual(mockResponse);
    });
  });

  describe('createPasswordPolicy', function () {
    it('should createPasswordPolicy', async function () {

      let mockResponse =[{
        success: true,
        message: "PasswordPolicy is created successfully."
      }]
      let mockpayload = {
        policyname: "new-policy",
        totalchars: 12,
        smallAlphabets: 4,
        bigAlphabets: 4,
        numbers: 2,
        noOfSplChars: 2,
        splChars: "$#",
        description: "test",
        namespace: "nebula-stamp"
      }
        
      
      mockService.createPasswordPolicy.mockResolvedValue(mockResponse);
      const result = await controller.createPasswordPolicy(mockpayload);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('createNameSpaceRequest', function () {
    it('should return createNameSpaceRequest ', async function () {
      let dto = {
        namespaceName: "/bCWbG3mhKGaUTFRznD44QD5zXydwA3FnCC86446lozmavRx7qga0qM676KH00exfYWIcmhC1jE2fslEdri9PfN6rDaF7Uq/",
        adminGroup: [
          "group"
        ],
        platformContext: {
          catalogId: "cat1",
          domainId: "dm1",
          envId: "env1"
        },
        vaultPolicies: [
          {
            enable: true,
            policyName: "policy",
            role: "role1"
          }
        ],
        vaultAppRoles: [
          {
            policyName: "policy",
            appRoleName: "Role 1"
          }
        ],
        tokenTTL: 31,
        autoRenewTokenOnExpiry: true,
        notifyBeforeTokenExpiry: true,
        disks: [
          "HDD"
        ],
        deeplinkUrl: "link/to/nebula"
      }
      
      let mockResponse = {
        id: "123",
        serviceRequestId: "123456",
        message: "Request raised successfully"
      }
      mockService.createNameSpaceRequest.mockResolvedValue(mockResponse);
      const result = await controller.createNameSpaceRequest(dto);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('getAuthorizedNamespaces', function () {
    it('getAuthorizedNamespaces should return filtered namespaces', async () => {
      const mockNamespaces = {
        items: [
          {
            namespaceName: 'Test',
            resourceId: 'NEB-RES-VAULT-NAMESPACE-8572',
            status: 'ACTIVE',
            catalogType: 'Vault',
            catalogLevel03: 'Create-Namespace',
            platformContext: {
              catalogId: '68119d4cc8f7a6598328a116',
              envId: '01966c73-bf70-76ef-ab84-3ca2c5ed441d',
              domainId: '67ebc4fd49be555801fec525',
            },
            requestType: 'CREATE_NAMESPACE',
            projectName: 'Nebula BLUE 2886',
          },
        ],
      };

      mockService.fetchAuthorizedNamespaces.mockResolvedValue(mockNamespaces);

      const result = await controller.getAuthorizedNamespaces({
        envId: '01966c73-bf70-76ef-ab84-3ca2c5ed441d',
      });
      expect(mockService.fetchAuthorizedNamespaces).toHaveBeenCalled();
      expect(result).toEqual(mockNamespaces);
    });
  });
  describe('create VaultSecrets', () => {
    it('should create vaultsecrets', async () => {
      const mockResponse = {
        successCode: 200,
        message: 'vaultScreat craeted successfully',
      };
      jest
        .spyOn(mockService, 'createVaultSecrets')
        .mockImplementation(() => Promise.resolve(mockResponse));

      const createVaultSecretsRequestDto = {
        type: FetchSecretType.NORMAL_SECRET,
        vaultKey: 'Nebula-test-6',
        vaultPassword: 'Nebula-test-6-password',
        userNameKey: 'Nebula-test-6',
        userNamePassword: 'Nebula-test-6-passwordKey',
        rotationType: RotationType.MANUAL,
        nextRotationDate: '123432343',
        notifyBeforeTokenExpiry: false,
        secretTTLInHours: 123978123,
        description: 'Example Desc',
      } as CreateVaultSecretsRequestDto;

      const response = await controller.createVaultSecrets(
        'Nebula-test-6',
        'test/user',
        createVaultSecretsRequestDto,
      );
      expect(mockService.createVaultSecrets).toHaveBeenCalled();
      expect(response).toEqual(mockResponse);
    });
  });

  it('should call secureStoreService.createVaultSecrets and return result', async () => {
    const namespaceName = 'test-namespace';
    const dto: CreateVaultSecretsRequestDto = {
      type: 'VAULT_TOKEN',
      vaultKey: 'key',
      vaultPassword: 'pass',
      expiryDate: new Date(),
      tokenRenewByNebula: true,
    } as any;

    const mockResponse = { message: 'Secret created' };
    mockService.createVaultSecrets.mockResolvedValue(mockResponse);

    const result = await controller.createValutTokenSecrets(namespaceName, dto);

    expect(mockService.createVaultSecrets).toHaveBeenCalledWith(
      namespaceName,
      dto,
    );
    expect(result).toEqual(mockResponse);
  });

  it('should call secureStoreService.renewBulkTokens and return result', async () => {
    const secretIds = ['id1', 'id2'];
    const mockResponse = { success: true };

    mockService.renewBulkTokens.mockResolvedValue(mockResponse);

    const result = await controller.renewBulkTokens(secretIds);

    expect(mockService.renewBulkTokens).toHaveBeenCalledWith(secretIds);
    expect(result).toEqual(mockResponse);
  });

  describe('fetchSecretsHistory', () => {
    it('should fetch history for secrets with the provided secretId', async () => {
      let mockResponse = [
        {
          action: 'Update Secret',
          lastUpdatedBy: 'P3271329',
          lastUpdatedAt: '2025-06-25T05:25:24.979Z',
          newValue: {
            secretTTLInHours: 10,
            version: 2,
            password: 'password-CHANGED',
          },
          originalValue: {
            secretTTLInHours: 3,
            version: 1,
            password: 'password',
          },
        },
        {
          action: 'Update Secret',
          lastUpdatedBy: 'P3271329',
          lastUpdatedAt: '2025-06-25T05:24:56.260Z',
          newValue: {
            nextRotationDate: '2025-06-30T00:00:00.000Z',
          },
          originalValue: {
            nextRotationDate: '2025-06-26T00:00:00.000Z',
          },
        },
        {
          action: 'Update Secret',
          lastUpdatedBy: 'P3271329',
          lastUpdatedAt: '2025-06-25T05:24:37.609Z',
          newValue: {
            secretTTLInHours: 3,
          },
          originalValue: {
            secretTTLInHours: 5,
          },
        },
        {
          action: 'Update Secret',
          lastUpdatedBy: 'P3271329',
          lastUpdatedAt: '2025-06-25T05:23:49.774Z',
          newValue: {
            secretTTLInHours: 5,
            policyId: '647484',
          },
          originalValue: {
            secretTTLInHours: 1,
            policyId: 'Policy123',
          },
        },
        {
          action: 'Create Secret',
          lastUpdatedAt: '2025-06-25T05:23:00.361Z',
          lastUpdatedBy: 'P3271329',
          newValue: {
            type: 'ROTATABLE-SECRET',
            secretTTLInHours: 1,
            rotationType: 'Manual',
            nextRotationDate: '2025-06-26T00:00:00.000Z',
            vaultNamespace: 'nebula-stamp/suriyavault',
            vaultPath: 'test',
            devicePasswordKey: 'test',
            policyId: 'Policy123',
            status: 'SUCCESS',
            active: true,
            version: 1,
            password: 'password',
          },
          originalValue: null,
        },
      ];
      let mockSecretId = 'NEB-VAULT-ROT-SECRET-1267';
      mockService.fetchSecretsHistory.mockResolvedValueOnce(mockResponse);
      const result = await controller.fetchSecretsHistory(mockSecretId);
      expect(result).toBe(result);
    });
  });
});
