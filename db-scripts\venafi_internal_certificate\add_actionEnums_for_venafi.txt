function runScript() {
  const session = db.getMongo().startSession();
 
  session.startTransaction();
  try {
    db.actionEnums.insertMany([
      {
        "actionTypeId": "RENEW_CERTIFICATE",
        "action": "Renew Certificate"
      },
      {
        "actionTypeId": "DOWNLOAD_CERTIFICATE",
        "action": "Download Certificate"
      }
    ])
    print(`Document was updated`);
  } catch (e) {
    print('Transaction aborted due to error', e);
    throw e;
  }
}
let session;
try {
  session = db.getMongo().startSession();
  session.startTransaction();
  runScript();
  session.commitTransaction();
} catch (e) {
  session.abortTransaction();
  print('Transaction aborted due to error', e);
} finally {
  session.endSession();
}