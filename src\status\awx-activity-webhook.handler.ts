import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { WebhookHandlerInterface } from './webhook-handler.interface';
import {
  API_RESPONSE_STATUS,
  ApprovalStatus,
  RequestStatus,
  ResultType,
  WEBHOOK_REQUEST_TYPE,
} from '../types';
import { AssetsService } from '../assets/assets.service';
import { ServiceRequestEntity } from '../naas/entities/serviceRequest.entity';
import { ActivityLogDto } from './dto/awx-activity-log.dto';
import { AwxActivityResponseDto } from './dto/awx-activity-response.dto';
import { WebhookRequestDto } from './dto/webhook.request.dto';
import { WebhookResponseDto } from './dto/webhook-response.dto';
import { ProcessStatus } from 'src/activity-logs/types';
import { ActivityLoggerWrapperService } from 'src/activity-logs-wrapper/activity-logger-wrapper.service';
import * as uuid from 'uuid';

@Injectable()
export class AWXActivityWebhookHandler
  implements WebhookHandlerInterface<WebhookRequestDto, WebhookResponseDto>
{
  private readonly logger = new Logger(AWXActivityWebhookHandler.name);

  constructor(
    private readonly assetsService: AssetsService,
    private readonly activityLoggerService: ActivityLoggerWrapperService,
  ) {}

  async handle(requestVal: WebhookRequestDto): Promise<WebhookResponseDto> {
    const { requestId, jobStatus, data, errors } = requestVal;
    const activityLogs: ActivityLogDto = requestVal.data as ActivityLogDto; // Destructure data (Activity logs)
    const traceId = uuid.v4();
    const payload = {};
    const serviceRequest = requestVal.requestId;
    const error = requestVal.errors || null;
    const childSteps = [];
    const childIndex = 0;
    const requestType = WEBHOOK_REQUEST_TYPE.VM_CONFIGURE;

    // Perform update logic (e.g., database update, business logic processing)
    this.logger.log(
      `Updating VM Config task: ${requestId} for ${jobStatus} with data: ${requestVal}`,
    );

    for (let index = 0; index < requestVal.data.ActivityLogs.length; index++) {
      const steps = requestVal.data.ActivityLogs;
      const activityToLog = this.mapStatusToProcessStatus(steps[index].Status);

      const modifiedError = {
        statusCode: error[0].errorCode,
        message: error[0].errorMessage,
      };

      try {
        await this.activityLoggerService.sendActivity(
          activityToLog,
          steps,
          requestId,
          payload,
          traceId,
          index,
          serviceRequest,
          requestType,
          childSteps,
          childIndex,
          undefined,
          modifiedError,
          `${steps[index].vmHostName}-${steps[index].name}`,
          0,
          steps[index].Starttime,
          steps[index].Endtime,
        );
        return {
          status: API_RESPONSE_STATUS.SUCCESS,
          message: 'VM Config request submitted successfully',
          data: requestVal.data,
        };
      } catch (err) {
        this.logger.error(
          err,
          'Error while handleing the activity ',
          activityToLog,
          requestId,
        );
        if (error instanceof BadRequestException) {
          return {
            status: API_RESPONSE_STATUS.FAILED,
            message: error.message,
            data: undefined,
          };
        }
        throw error;
      }
    }
  }

  private mapStatusToProcessStatus(status: string): ProcessStatus {
    switch (status.toUpperCase()) {
      case ProcessStatus.COMPLETED:
        return ProcessStatus.COMPLETED;
      case ProcessStatus.FAILED:
        return ProcessStatus.FAILED;
      case ProcessStatus.SKIPPED:
        return ProcessStatus.SKIPPED;
      default:
        return ProcessStatus.STARTED;
    }
  }
}
