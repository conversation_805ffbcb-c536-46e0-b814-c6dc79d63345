import {
  Body,
  Controller,
  Get,
  Headers,
  Post,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { ApiSecurity, ApiTags } from '@nestjs/swagger';
import { Anonymous } from '../auth/anonymous.decorator';
import { BaseConfigRequestDto } from './dto/baseConfig.request.dto';
import { BaseConfigResponseDto } from './dto/baseConfig.response.dto';
import { ComplianceConfigRequestDto } from './dto/complianceConfig.request.dto';
import { ComplianceConfigResponseDto } from './dto/complianceConfig.response.dto';
import { FirewallPolicyDto } from './dto/firewallPolicy.request.dto';
import { GoldenConfigHeaderDto } from './dto/goldenConfig.header.request.dto';
import { GoldenConfigService } from './golden-config.service';

@ApiTags('Golden Config')
@ApiSecurity('x-nebula-authorization')
@Controller('golden-config')
@UsePipes(new ValidationPipe({ whitelist: true }))
// @Anonymous()
export class GoldenConfigController {
  constructor(private readonly service: GoldenConfigService) {}

  @Get('config')
  async getBaseConfig(
  ): Promise<BaseConfigResponseDto> {
    return await this.service.getBaseConfig();
  }

  @Post('config')
  async postBaseConfig(
    @Headers() headers: GoldenConfigHeaderDto,
    @Body() payload: BaseConfigRequestDto,
  ) {
    return await this.service.postBaseConfig(payload);
  }

  @Get('config-template')
  async getBaseConfigTemplate(
    @Headers() headers: GoldenConfigHeaderDto,
  ): Promise<BaseConfigResponseDto> {
    return await this.service.getBaseConfigTemplate(headers);
  }

  @Post('config-template')
  async postBaseConfigTemplate(
    @Headers() headers: GoldenConfigHeaderDto,
    @Body() payload: BaseConfigRequestDto,
  ) {
    return await this.service.postBaseConfigTemplate(payload);
  }

  @Get('compliance-config')
  async getComplianceConfig(
    @Headers() headers: GoldenConfigHeaderDto,
  ): Promise<ComplianceConfigResponseDto> {
    return await this.service.getComplianceConfig(headers);
  }

  @Post('compliance-config')
  async postComplianceConfig(
    @Headers() headers: GoldenConfigHeaderDto,
    @Body() payload: ComplianceConfigRequestDto,
  ) {
    return await this.service.postComplianceConfig(payload);
  }

  @Post('compliance-template')
  async postComplianceTemplate(
    @Headers() headers: GoldenConfigHeaderDto,
    @Body() payload: ComplianceConfigRequestDto,
  ) {
    return await this.service.postComplianceTemplate(payload);
  }

  @Get('common-firewall-policy')
  async getCommonFirewallPolicy(@Headers() headers: GoldenConfigHeaderDto) {
    return await this.service.getCommonFirewallPolicy(headers);
  }

  @Post('common-firewall-policy')
  async postCommonFirewallPolicy(
    @Headers() headers: GoldenConfigHeaderDto,
    @Body() payload: FirewallPolicyDto,
  ) {
    return await this.service.postCommonFirewallPolicy(payload);
  }

  @Get('common-firewall-policy-component')
  async getCommonFirewallComponent(@Headers() headers: GoldenConfigHeaderDto) {
    return await this.service.getCommonFirewallComponent(headers);
  }

  @Post('common-firewall-policy-component')
  async postCommonFirewallComponent(
    @Headers() headers: GoldenConfigHeaderDto,
    @Body() payload: FirewallPolicyDto,
  ) {
    return await this.service.postCommonFirewallComponent(payload);
  }
}
