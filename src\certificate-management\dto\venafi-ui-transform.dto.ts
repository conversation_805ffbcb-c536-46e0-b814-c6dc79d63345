import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsArray,
  IsOptional,
  IsNumber,
  IsBoolean,
} from 'class-validator';

export class VenafiCertificateInfo {
  @ApiProperty({
    example: 'nebula-test-jun27.charter.com',
    description:
      'The common name (CN) of the certificate, usually the primary domain.',
  })
  @IsString()
  commonName: string;

  @ApiProperty({
    example: 'US',
    description: 'Country of the certificate subject.',
  })
  @IsString()
  country: string;

  @ApiProperty({
    example: 'Missouri',
    description: 'State/Province of the certificate subject.',
  })
  @IsString()
  stateProvince: string;

  @ApiProperty({
    example: 'St. Louis',
    description: 'Locality/City of the certificate subject.',
  })
  @IsString()
  locality: string;

  @ApiProperty({
    example: 'Charter Communications Operating, LLC',
    description: 'Organization of the certificate subject.',
  })
  @IsString()
  organization: string;

  @ApiProperty({
    example: '2025-06-18T07:44:30.0000000Z',
    description: 'The date and time when the certificate becomes valid.',
  })
  @IsString()
  validFromDate: string;

  @ApiProperty({
    example: '2025-09-16T07:44:30.0000000Z',
    description: 'The date and time when the certificate expires.',
  })
  @IsString()
  validToDate: string;

  @ApiProperty({
    example: '834E9140D6A38853760765730FA53FA036D2549E',
    description: 'A unique hash that identifies the certificate.',
  })
  @IsString()
  fingerprint?: string;

  @ApiProperty({
    example: 'RSA',
    description: 'The algorithm used for the public key (e.g., RSA, ECC).',
  })
  @IsString()
  keyAlgorithmType: string;

  @ApiProperty({
    example: 2048,
    description: 'The bit length of the cryptographic key.',
  })
  @IsNumber()
  keyBitSize: number;

  @ApiProperty({
    example: [
      'nebula-test-jun27.charter.com',
      'nebula-test2-may24.charter.com',
    ],
    isArray: true,
    type: String,
    description: 'Alternative domain names covered by this certificate.',
  })
  @IsArray()
  @IsString({ each: true })
  alternateDnsNames: string[];

  @ApiProperty({
    example:
      'CN=Charter Communications Issuing CA1, DC=corp, DC=chartercom, DC=com',
    description: 'The issuer of the certificate.',
  })
  @IsString()
  certificateIssuer: string;

  @ApiProperty({
    example: 'WebServers-Venafi-UAT',
    description: 'The Venafi template name used for issuance.',
  })
  @IsString()
  venafiTemplateName: string;

  @ApiProperty({
    example: 'sha256RSA',
    description: 'The algorithm used to sign the certificate.',
  })
  @IsString()
  signatureHashingAlgorithm: string;
}

export class VenafiCustomField {
  @ApiProperty({
    example: 'Cherwell Application ID',
    description: 'Human-readable name of the custom field.',
  })
  @IsString()
  fieldName: string;

  @ApiProperty({
    example: ['APP0494'],
    isArray: true,
    type: String,
    description: 'Values associated with the custom field.',
  })
  @IsArray()
  @IsString({ each: true })
  fieldValues: string[];
}

export class VenafiCertificateTransformedDetailsDto {
  @ApiProperty({
    example: '764b7da1-2a70-4533-ac3f-8d1e0b7af808',
    description: 'The unique identifier for this certificate record in Venafi.',
  })
  @IsString()
  certificateIdentifier: string;

  @ApiProperty({
    example: 'nebula-test-jun27.charter.com',
    description: 'The primary display name of the certificate.',
  })
  @IsString()
  @IsOptional()
  certificateName?: string;

  @ApiProperty({
    example: 'X509 Server Certificate',
    description: 'The classification or type of the certificate.',
  })
  @IsString()
  @IsOptional()
  certificateSchemaClass?: string;

  @ApiProperty({
    example: '2025-06-18T07:40:47.301Z',
    description:
      'The timestamp when this certificate record was created in Venafi.',
  })
  @IsString()
  @IsOptional()
  creationTimestamp?: string;

  @ApiProperty({
    type: () => VenafiCertificateInfo,
    description: 'Detailed information about the certificate itself.',
  })
  @IsOptional()
  certificateInformation?: VenafiCertificateInfo;

  @ApiProperty({
    type: () => [VenafiCustomField],
    isArray: true,
    description: 'Important custom fields associated with the certificate.',
  })
  @IsArray()
  @IsOptional()
  applicationSpecificFields?: VenafiCustomField[];

  @ApiProperty({
    example: 'Aperture',
    description: 'The system that manages this certificate.',
  })
  @IsString()
  @IsOptional()
  managedBySystem?: string;

  @ApiProperty({
    example: 'Enrollment',
    description:
      'The type of management lifecycle (e.g., Enrollment, Discovery).',
  })
  @IsString()
  @IsOptional()
  managementLifecycleType?: string;

  @ApiProperty({
    example: 'Nebula',
    description: 'The source system where the certificate originated.',
  })
  @IsString()
  @IsOptional()
  originalSourceSystem?: string;

  @ApiProperty({
    example:
      '\\VED\\Policy\\INT\\Automation\\Nebula\\APP4940 Nebula\\INT\\nebula-test-jun27.charter.com',
    description:
      'The distinguished name (DN) of the certificate within Venafi policy tree.',
  })
  @IsString()
  @IsOptional()
  distinguishedName?: string;

  @ApiProperty({
    example: 'Submission script failed -- check logs for details',
    description: 'Status message from Cherwell, if applicable.',
  })
  @IsString()
  @IsOptional()
  cherwellStatusMessage?: string;

  @ApiProperty({
    example: true,
    description:
      'Indicates if the certificate is currently in the process of being provisioned or renewed.',
  })
  @IsBoolean()
  isInProcess: boolean;

  @ApiProperty({
    example: 'Retrieve Certificate',
    description: 'The current stage of certificate processing, if in progress.',
  })
  @IsString()
  @IsOptional()
  processingCurrentStage?: string;

  @ApiProperty({
    example: 700,
    description:
      'The numeric stage code of certificate processing, if in progress.',
  })
  @IsNumber()
  @IsOptional()
  processingStageCode?: number;
}
