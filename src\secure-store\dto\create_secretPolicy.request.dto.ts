import {
  <PERSON>NotEmpty,
  <PERSON><PERSON><PERSON>ber,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IsMongoId,
} from 'class-validator';

export class createSecretPoliciesDto {
  @IsNotEmpty()
  @IsString()
  policyname: string;

  @IsNotEmpty()
  @IsNumber()
  @Min(12)
  @Max(30)
  totalchars: number;

  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  @Max(30)
  smallAlphabets: number;

  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  @Max(30)
  bigAlphabets: number;

  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  @Max(30)
  numbers: number;

  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  @Max(30)
  noOfSplChars: number;

  @IsNotEmpty()
  @IsString()
  splChars: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsNotEmpty()
  @IsString()
  namespace: string;
}
