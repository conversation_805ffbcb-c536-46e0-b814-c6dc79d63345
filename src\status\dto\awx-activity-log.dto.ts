import { IsString, IsEnum, IsOptional, IsNotEmpty } from 'class-validator';

enum StatusEnum {
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  SKIPPED = 'SKIPPED',
}

export class ActivityLogDto {
  @IsString()
  @IsNotEmpty()
  eventCode: string;

  @IsEnum(StatusEnum)
  Status: StatusEnum;

  @IsOptional()
  @IsString()
  Starttime?: string;

  @IsOptional()
  @IsString()
  Endtime?: string;

  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  vmHostName: string;
}
