import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { LoggerService } from '../loggers/logger.service';
import { TufinWrapperService } from '../tufin-wrapper/tufin-wrapper.service';
import { GetAclConfigRequestDto } from './dto/tufin.request.dto';
import { GetAclConfigResponseDto } from './dto/tufin.response.dto';
import * as IpAddress from 'ip-address';
import { SecureChangeRequestDto } from '../security/firewallv2/dto/secure.change.dto';
export const ipv4 = IpAddress.Address4;
export const ipv6 = IpAddress.Address6;

@Injectable()
export class TufinService {
  constructor(
    private readonly tufinWrapperService: TufinWrapperService,
    private readonly logger: LoggerService,
  ) {}

  async getAclConfig(
    getAclConfigRequestDto: GetAclConfigRequestDto,
  ): Promise<GetAclConfigResponseDto> {
    const requestIp = getAclConfigRequestDto.ipAddress?.trim();
    if (!(ipv4.isValid(requestIp) || ipv6.isValid(requestIp))) {
      this.logger.error(`Invalid ipAddress: ${requestIp}`);
      throw new BadRequestException('Invalid ipAddress');
    }
    if (requestIp.includes('/')) {
      this.logger.error(
        `Subnet mask is not allowed for IP address: ${requestIp}`,
      );
      throw new BadRequestException(
        'Subnet mask is not allowed for IP address',
      );
    }
    let tufinRes = null;
    try {
      this.logger.log(
        `Calling tufin wrapper service to fetch acl config for ipAddress: ${requestIp}`,
      );
      tufinRes = await this.tufinWrapperService.getAclConfig(requestIp);
      this.logger.log('Fetched acl config from tufin service');
    } catch (err) {
      this.logger.error(
        err,
        `Error while fetching acl config for ipAddress: ${requestIp}`,
      );
      throw new InternalServerErrorException(
        `Error while fetching acl config for given ipAddress. ${err}`,
      );
    }
    if (!tufinRes) {
      this.logger.error(`Acl config not obtained for ipAddress: ${requestIp}`);
      throw new NotFoundException(
        'Acl config not obtained for given ipAddress',
      );
    }
    return { ipAddress: requestIp, aclConfig: tufinRes };
  }

  async cancelTufinTicket(tufinTicketId) {
    this.logger.log(
      `Received request to cancel tufin ticket for ${tufinTicketId}`,
    );
    return await this.tufinWrapperService.cancelTufinTicket(tufinTicketId);
  }

  async createSecureChange(data: SecureChangeRequestDto) {
    return await this.tufinWrapperService.createSecureChange(data);
  }
}
