import { validate } from 'class-validator';
import { CreateSecretDeviceAssociationDto } from './devices.dto';

describe('CreateSecretDeviceAssociationDto', () => {
  it('should pass validation with valid input', async () => {
    const dto = new CreateSecretDeviceAssociationDto();
    dto.deviceId = 123;
    dto.secretId = 'secret-123';
    dto.sourceSystem = 'keyguard';

    const errors = await validate(dto);
    expect(errors.length).toBe(0);
  });

  it('should fail validation with missing fields', async () => {
    const dto = new CreateSecretDeviceAssociationDto();
    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
  });
});
