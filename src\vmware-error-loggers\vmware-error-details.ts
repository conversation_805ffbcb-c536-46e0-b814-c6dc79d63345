export const ERROR_DETAILS = {
  // VMware Fetch Ref service
  VMWARE_FETCH_REFERNCE_ERROR_CODE: 'VMWARE_FETCH_REFERENCE_ERROR_1001',
  VMWARE_FETCH_REFERNCE_IAM_SERVICE_ERROR_MESSAGE:
    'Failed fetching catalog 04 by shortname from IAM service',
  VMWARE_FETCH_REFERNCE_TEMPLATE_CONFIG_ERROR_MESSAGE:
    'Failed fetching VM template config by catalog ID',
  VMWARE_FETCH_REFERNCE_LAYOUT_SERVICE_ERROR_MESSAGE:
    'Failed fetching VMware layouts from compute wrapper',
  VMWARE_FETCH_REF_ERROR_MESSAGE: 'Failed fetching VMware reference data',

  //Provison Service
  VMWARE_PROVISION_ERROR_CODE: 'VMWARE_PROVISION_ERROR_1002',
  VMWARE_PROVISION_ERROR: 'Failed provison error',
  VMWARE_PROVISION_IP_AVAILABILITY_ERROR_MESSAGE: 'Failed find available IPs',
  VMWARE_PROVISION_VALIDATE_STATIC_IP_ERROR_MESSAGE:
    'Failed validate static IPs',
  VMWARE_PROVISION_ENCRYPTION_ERROR_MESSAGE: 'Failed encrypt VM',
  VMWARE_PROVISION_IAM_SERVICE_ERROR_MESSAGE:
    'Failed fetching catalog 04 by shortname from IAM service',
  VMWARE_PROVISION_TEMPLATE_CONFIG_ERROR_MESSAGE:
    'Failed fetching VM template config by catalog ID',
  VMWARE_PROVISION_REPO_ERROR_MESSAGE:
    'Failed fetching datacenter from repository',
  VMWARE_PROVISION_CATALOG_L4_ERROR_MESSAGE: 'Failed to find catalog L4 group',
  VMWARE_PROVISION_ASSET_CREATE_ERROR_MESSAGE: 'Failed creating asset request',
  VMWARE_PROVISION_ASSET_QUEUE_ERROR_MESSAGE:
    'Failed queueing asset request if approval not required',

  //ValidateHostname
  VMWARE_VALIDATE_HOSTNAME_ERROR_CODE: 'VMWARE_VALIDATE_HOSTNAME_ERROR_1003',
  VMWARE_VALIDATE_HOSTNAME_ERROR_MESSAGE: 'Failed validate VM hostname',
  VMWARE_PROVISION_VCENTER_VALIDITY_WITHOUT_SEQUENCE_ERROR_MESSAGE:
    'Failed validate Vcenter without sequence number',
  VMWARE_PROVISION_GET_SEQUENCE_ERROR_MESSAGE: 'Failed to get sequence number',

  //Fetch Resource
  VMWARE_FETCH_RESOURCE_ERROR_CODE: 'VMWARE_FETCH_RESOURCE_ERROR_1004',
  VMWARE_FETCH_RESOURCE_ERROR_MESSAGE: 'Failed fetch VM resources',

  //FetchRsourceByNetwork
  VMWARE_FETCH_RESOURCE_NETWORK_ERROR_CODE: 'VMWARE_FETCH_NETWORK_ERROR_1005',
  VMWARE_FETCH_RESOURCE_NETWORK_ERROR_MESSAGE: 'Failed fetch VM network',

  //FetchResouce Network Details
  VMWARE_FETCH_RESOURCE_BY_NETWORK_ERROR_CODE:
    'VMWARE_FETCH_RESOURCE_BY_NETWORK_ERROR_1006',
  VMWARE_FETCH_RESOURCE_BY_NETWORK_ERROR_MESSAGE:
    'Failed fetch VM resources by network id',

  //DNS
  VMWARE_DNS_DETAILS_ERROR_CODE: 'VMWARE_DNS_DETAILS_ERROR_1007',
  VMWARE_DNS_DETAILS_ERROR_MESSAGE: 'Failed get VM Dns details',

  //CONSOLE VMs
  VMWARE_CONSOLE_DETAILS_ERROR_CODE: 'VMWARE_CONSOLE_DETAILS_ERROR_1008',
  VMWARE_CONSOLE_DETAILS_ERROR_MESSAGE: 'Failed to fetch VMs console details',

  //Edit VMs
  VMWARE_EDIT_VM_ERROR_CODE: 'VMWARE_CONSOLE_DETAILS_ERROR_1009',
  VMWARE_EDIT_VM_ERROR_MESSAGE: 'Failed to edit VMs',
  VMWARE_EDIT_VM_RMQ_MESSAGE_QUEUE_ERROR_MESSAGE:
    'Failed to push edit message rmq service',
  VMWARE_EDIT_VM_SYSTEM_UPDATE_ERROR_MESSAGE:
    'Failed system update for edit VMs',
  VMWARE_EDIT_VM_CATALOG_L4_ERROR_MESSAGE: 'Failed to find catalog L4 group',
  VMWARE_EDIT_VM_UPDATE_RESOURCE_ERROR_MESSAGE: 'Failed to update resources',
  VMWARE_EDIT_VM_FETCH_RESOURCE_ERROR_MESSAGE:
    'Failed to fetch resources by Id',
};
