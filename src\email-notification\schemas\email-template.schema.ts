import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type EmailTemplateDocument = EmailTemplate & Document;

@Schema({ collection: 'emailTemplates' })
export class EmailTemplate {
  @Prop({ required: true })
  templateId: string;

  @Prop({ default: '' })
  templateLocation: string;

  @Prop({
    type: [{
      type: {
        type: String,
        required: true
      },
      address: {
        type: String,
        default: ''
      },
      message: {
        type: String,
        required: true
      },
      contentType: {
        type: String,
        required: true
      },
      title: {
        type: String,
        required: true
      }
    }],
    required: true
  })
  notifications: Array<{
    type: string;
    address: string;
    message: string;
    contentType: string;
    title: string;
  }>;
}

export const EmailTemplateSchema = SchemaFactory.createForClass(EmailTemplate);
