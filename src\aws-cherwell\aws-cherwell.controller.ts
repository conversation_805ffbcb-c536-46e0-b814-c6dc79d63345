import { 
  Controller, 
  Post, 
  Body, 
  UseGuards, 
  UsePipes, 
  ValidationPipe 
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiBearerAuth, 
  ApiSecurity 
} from '@nestjs/swagger';
import { AwsCherwellService } from './aws-cherwell.service';
import { AuthenticationGuard } from '../auth/authentication.guard';
import { LoggerService } from '../loggers/logger.service';
import {
  AwsDeviceDetectionRequestDto,
  AwsDeviceDetectionResponseDto,
  CherwellWorkItemRequestDto,
  CherwellWorkItemResponseDto,
  AwsApprovalEmailRequestDto,
  AwsDeviceHandlingResponseDto,
} from './dto/aws-cherwell.dto';

@Controller('aws-cherwell')
@ApiTags('AWS Cherwell Integration')
@ApiBearerAuth()
@ApiSecurity('x-nebula-authorization')
@UseGuards(AuthenticationGuard)
@UsePipes(new ValidationPipe({ whitelist: true, stopAtFirstError: true }))
export class AwsCherwellController {
  constructor(
    private readonly awsCherwellService: AwsCherwellService,
    private readonly logger: LoggerService,
  ) {}

  @Post('detect-aws-devices')
  @ApiOperation({ 
    summary: 'Detect AWS devices in path analysis data',
    description: 'Analyzes path analysis data to detect AWS devices that require SDIT approval'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'AWS device detection completed',
    type: AwsDeviceDetectionResponseDto 
  })
  async detectAwsDevices(
    @Body() request: AwsDeviceDetectionRequestDto
  ): Promise<AwsDeviceDetectionResponseDto> {
    this.logger.log('AWS device detection request received', request);

    const result = this.awsCherwellService.analyzePathForAwsDevices(
      request.pathAnalysisData
    );

    return {
      hasAwsDevices: result.hasAwsDevices,
      detectedDevices: result.detectedDevices,
      serviceRequestId: request.serviceRequestId,
      subRequestId: request.subRequestId,
    };
  }

  @Post('create-work-item')
  @ApiOperation({ 
    summary: 'Create Cherwell work item for AWS approval',
    description: 'Creates a Cherwell work item for SDIT Architecture team approval'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Cherwell work item created successfully',
    type: CherwellWorkItemResponseDto 
  })
  async createCherwellWorkItem(
    @Body() request: CherwellWorkItemRequestDto
  ): Promise<CherwellWorkItemResponseDto> {
    this.logger.log('Cherwell work item creation request received', request);

    return await this.awsCherwellService.createCherwellWorkItem(request);
  }

  @Post('send-approval-email')
  @ApiOperation({ 
    summary: 'Send AWS approval email to Srihari',
    description: 'Sends email notification to Srihari about AWS approval needed'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Email sent successfully'
  })
  async sendApprovalEmail(
    @Body() request: AwsApprovalEmailRequestDto
  ): Promise<{ message: string }> {
    this.logger.log('AWS approval email request received', request);

    await this.awsCherwellService.sendAwsApprovalEmail(
      request.workItemNumber,
      request.serviceRequestId
    );

    return { message: 'AWS approval email sent successfully' };
  }

  @Post('handle-aws-detection')
  @ApiOperation({ 
    summary: 'Complete AWS device detection and handling workflow',
    description: 'Detects AWS devices, creates Cherwell work item if needed, and sends email notification'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'AWS device handling completed',
    type: AwsDeviceHandlingResponseDto 
  })
  async handleAwsDeviceDetection(
    @Body() request: AwsDeviceDetectionRequestDto
  ): Promise<AwsDeviceHandlingResponseDto> {
    this.logger.log('Complete AWS device handling request received', request);

    const workItemResponse = await this.awsCherwellService.handleAwsDeviceDetection(
      request.serviceRequestId,
      request.pathAnalysisData,
      request.subRequestId
    );

    const detectionResult = this.awsCherwellService.analyzePathForAwsDevices(
      request.pathAnalysisData
    );

    return {
      awsDevicesDetected: detectionResult.hasAwsDevices,
      detectedDevices: detectionResult.detectedDevices,
      cherwellWorkItem: workItemResponse || undefined,
      emailSent: !!workItemResponse,
      message: workItemResponse 
        ? `AWS devices detected. Cherwell work item ${workItemResponse.workItemNumber} created and email sent to Srihari.`
        : 'No AWS devices detected in the path analysis.'
    };
  }
}
