db.n8nconfig.insertOne({
  "workflow": "Create-Namespace",
  "baseUrl": "https://nebula.stage.charter.com/",
  "generateUUIDWorkflow": "0i8p5DF44BTbrQwG",
  "catalogStepsWorkflow": "92vU920KUcRRVe0a",
  "eventSourceHostName": "cdptpabb04-caas-mgmt-v3.stage.charter.com",
  "cloudApiUrl": "http://cloud-api-service.nebula.svc.cluster.local:80/nebula-api/",
  "secretsUrl": "http://secrets-management-service.nebula.svc.cluster.local:80/secrets-management-service/",
  "rootNamespace": "nebula-namespaces"
})
