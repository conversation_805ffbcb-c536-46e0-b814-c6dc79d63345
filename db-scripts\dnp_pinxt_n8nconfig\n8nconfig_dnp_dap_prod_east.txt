db.n8nconfig.insertOne({
  "workflow": "dnp-dap-PROD-EAST",
  "baseUrl": "https://nebula-east.charter.com/",
  "generateUUIDWorkflow": "NbVb60znpXgXlOeM",
  "catalogStepsWorkflow": "j63a8zHvgg5NTRey",
  "eventSourceHostName": "chdcnc03-caas-mgmt-v3.cloud.charter.com",
  "activityLogUrl":"http://activity-log-producer-service.nebula.svc.cluster.local:80/activity-log-producer/v1/log",
  "cloudApiUrl": "http://cloud-api-service.nebula.svc.cluster.local:80/nebula-api/",
  "updateInterfaceDelay":"20"
})