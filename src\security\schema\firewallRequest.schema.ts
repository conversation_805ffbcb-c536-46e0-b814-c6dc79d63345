import { <PERSON><PERSON>, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import {
  ApprovalStatus,
  IpType,
  RequestStatus,
  RequestType,
} from '../../types';
import { TufinResponseDetails } from '../firewallv2/dto/updateTufinDetails.dto';

export type MongoDocument<T> = HydratedDocument<
  T & {
    createdAt: Date;
    updatedAt: Date;
    __v: number;
  }
>;

export type FirewallRequestDocument = MongoDocument<FirewallRequest>;

export enum JobStatusEnum {
  SUCCESS = 'success',
  FAILURE = 'failure',
  IN_PROGRESS = 'in_progress',
  PENDING = 'pending',
}
export class ErrorDetailDto {
  errorCode: string;
  errorMessage: string;
}

export class WarningsDetailDto {
  code: string;
  message: string;
}

class TufinTask {
  taskId: string;

  status: JobStatusEnum;

  message: string;

  timestamp: Date;

  errors?: ErrorDetailDto[];
}

class RiskViolations {
  @Prop({ required: false })
  sources: string[];

  @Prop({ required: false })
  destinations: string[];

  @Prop({ required: false })
  violatingServices: string[];
}

class SecurityRequirements {
  @Prop({ required: false })
  policy: string;

  @Prop({ required: false })
  fromZone: string;

  @Prop({ required: false })
  toZone: string;

  @Prop({ required: false })
  allowedServices: string[];
}

class RiskAnalysis {
  @Prop({ required: false })
  severity: string;

  @Prop({ required: false, type: RiskViolations, _id: false })
  violations: RiskViolations;

  @Prop({ required: false, type: SecurityRequirements, _id: false })
  securityRequirements: SecurityRequirements;
}

class TufinTicket {
  ticketId: string;
  href: string;
  status: string;
}

class JiraTicket {
  ticketId: string;
  href: string;
  status: string;
}

class Jira {
  @Prop({ required: false })
  ticketId: string;

  @Prop({ required: false })
  status: string;

  @Prop({ required: false })
  ticketUrl: string;

  @Prop({ required: false, default: [] })
  error: ErrorDetailDto[];

  @Prop({ required: false, default: [] })
  warnings: WarningsDetailDto[];
}

class Cherwell {
  @Prop({ required: false })
  ticketId: string;

  @Prop({ required: false })
  status: string;

  @Prop({ required: false })
  ticketUrl: string;

  @Prop({ required: false, default: [] })
  error: ErrorDetailDto[];

  @Prop({ required: false, default: [] })
  warnings: WarningsDetailDto[];
}

class Remedy {
  @Prop({ required: false })
  ticketId: string;

  @Prop({ required: false })
  status: string;

  @Prop({ required: false })
  ticketUrl: string;

  @Prop({ required: false })
  mopId: string;

  @Prop({ required: false, default: [] })
  error: ErrorDetailDto[];

  @Prop({ required: false, default: [] })
  warnings: WarningsDetailDto[];
}

class WorkflowStepResult {
  @Prop({ required: true })
  stepName: string;
  @Prop({ type: Object, required: true })
  taskResult: Record<string, any>;
}

class Tufin {
  @Prop({ required: false })
  ticketId: string;

  @Prop({ required: false })
  status: string;

  @Prop({ required: false })
  ticketUrl: string;

  @Prop({ required: false })
  riskAnalysis: RiskAnalysis[];

  @Prop({ type: [WorkflowStepResult], required: false, default: [] })
  workflowStepsResults?: WorkflowStepResult[];

  @Prop({ required: false, default: [] })
  error: ErrorDetailDto[];

  @Prop({ required: false, default: [] })
  warnings: WarningsDetailDto[];
}

class TicketDetails {
  @Prop({ required: false, type: Tufin, _id: false })
  tufin?: Tufin;

  @Prop({ required: false, type: Cherwell, _id: false })
  cherwell?: Cherwell;

  @Prop({ required: false, type: Jira, _id: false })
  jira?: Jira;

  @Prop({ required: false, type: Remedy, _id: false })
  remedy?: Remedy;
}

class ReflowHistory {
  @Prop({ required: true, type: Date, default: null })
  reflowedAt: Date;

  @Prop({ required: true })
  reflowedBy: string;

  @Prop({ required: true, type: JobStatusEnum })
  reflowedStatus: JobStatusEnum;
}

class ApprovalNotification {
  @Prop({ required: true, type: Date })
  notifiedAt: Date;

  @Prop({ required: true })
  notifiedGroup: string[];

  @Prop({ required: true })
  notifiedLevel: number;

  @Prop({ required: true })
  notificationStatus: JobStatusEnum;
}

@Schema({ timestamps: true })
export class FirewallRequest {
  @Prop({ required: false, enum: RequestType })
  requestType: RequestType;

  @Prop({ required: false, enum: RequestStatus })
  status: RequestStatus;

  @Prop({ required: false, enum: ApprovalStatus })
  approvalStatus: ApprovalStatus;

  @Prop({ required: false })
  organizationName: string;

  @Prop({ required: false })
  requestId: string;

  @Prop({ required: false, enum: IpType })
  ipType: IpType;

  @Prop({ required: false })
  ruleIds: Number[];

  @Prop({ required: false })
  subRequestId: string;

  @Prop({ required: false, type: Date, default: null })
  startedAt?: Date;

  @Prop({ required: false, type: Date, default: null })
  completedAt?: Date;

  @Prop({ required: false, type: Date, default: null })
  updatedAt?: Date;

  @Prop({ required: false, type: TicketDetails, _id: false })
  ticketDetails?: TicketDetails;

  @Prop({ required: false, default: false })
  isSubRequestInValidated?: boolean;

  @Prop({ required: false })
  enableApproval?: boolean;

  @Prop({ required: false, type: ReflowHistory, default: [] })
  reflowHistory?: ReflowHistory[];

  @Prop({ required: false, type: ApprovalNotification, default: [] })
  approvalNotification?: ApprovalNotification[];
}

export const FirewallRequestSchema =
  SchemaFactory.createForClass(FirewallRequest);
