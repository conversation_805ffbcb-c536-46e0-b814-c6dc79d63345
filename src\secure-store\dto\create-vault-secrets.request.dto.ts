import {
  IsA<PERSON>y,
  IsBoolean,
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Matches,
  ValidateIf,
} from 'class-validator';
import { FetchSecretType } from './fetch-secrets.response.dto';
import { RotationType } from '../secretsMetadata/types/secretsMetadata.enum';

export class CreateVaultSecretsRequestDto {
  @IsEnum(FetchSecretType)
  type: FetchSecretType;

  @IsOptional()
  @IsString()
  description?: string;

  @ValidateIf((o) => o.type === FetchSecretType.ROTATABLE_SECRET)
  @IsString()
  policyId?: string;

  @IsString()
  @IsNotEmpty()
  vaultKey: string;

  @IsString()
  @IsNotEmpty()
  vaultPassword: string;

  @ValidateIf((o) => o.type === FetchSecretType.ROTATABLE_SECRET)
  @IsString()
  @IsNotEmpty()
  userNameKey: string;

  @ValidateIf((o) => o.type === FetchSecretType.ROTATABLE_SECRET)
  @IsString()
  @IsNotEmpty()
  userNamePassword: string;

  @ValidateIf((o) => o.type === FetchSecretType.ROTATABLE_SECRET)
  @IsNumber()
  secretTTLInHours?: number;

  @ValidateIf((o) => o.type === FetchSecretType.ROTATABLE_SECRET)
  @IsBoolean()
  notifyBeforeTokenExpiry: boolean;

  @ValidateIf((o) => o.type === FetchSecretType.ROTATABLE_SECRET)
  @IsDateString()
  nextRotationDate: string;

  @ValidateIf((o) => o.type === FetchSecretType.ROTATABLE_SECRET)
  @IsOptional()
  @IsEnum(RotationType)
  rotationType?: RotationType;

  @ValidateIf((o) => o.type === FetchSecretType.VAULT_TOKEN)
  @IsDateString()
  expiryDate: Date;

  @ValidateIf((o) => o.type === FetchSecretType.VAULT_TOKEN)
  @IsBoolean()
  tokenRenewByNebula: boolean;
}

export class SecretsIdsQueryDto {
  @IsString()
  ids: string;
}
