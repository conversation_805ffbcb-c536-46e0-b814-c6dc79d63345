import { ApiHideProperty } from '@nestjs/swagger';
import { BadRequestException } from '@nestjs/common';
import { Transform, Type } from 'class-transformer';
import { LoggerService } from '../../loggers/logger.service';

import {
  IsNotEmpty,
  IsNumber,
  IsString,
  ValidateNested,
  IsObject,
  IsDefined,
  IsNotEmptyObject,
  IsBoolean,
  ArrayMinSize,
  IsArray,
  IsOptional,
  Matches,
  IsIP,
  Min,
  Max,
  IsEnum,
  MaxLength,
  ArrayUnique,
} from 'class-validator';
import { PlatformContext } from 'src/utils/dto/platform-context.dto';
class WindowsConfig {
  @IsNotEmpty()
  @IsNumber()
  @Min(4)
  @Max(64)
  customMemory: number;

  @IsNotEmpty()
  @IsNumber()
  @Min(4)
  @Max(32)
  customCores: number;

  @IsNotEmpty()
  @IsNumber()
  @Min(80)
  @Max(1000)
  customVolume: number;
  total?: number;
}

class LinuxConfig {
  @IsNotEmpty()
  @IsNumber()
  @Min(4)
  @Max(64)
  customMemory: number;

  @IsNotEmpty()
  @IsNumber()
  @Min(4)
  @Max(32)
  customCores: number;

  @IsNotEmpty()
  @IsNumber()
  @Min(20)
  @Max(900)
  root: number;

  @IsNotEmpty()
  @IsNumber()
  @Min(2)
  @Max(900)
  home: number;

  @IsNotEmpty()
  @IsNumber()
  @Min(8)
  @Max(900)
  opt: number;

  @IsNotEmpty()
  @IsNumber()
  @Min(4)
  @Max(900)
  var: number;

  @IsNotEmpty()
  @IsNumber()
  @Min(4)
  @Max(900)
  var_log: number;

  @IsNotEmpty()
  @IsNumber()
  @Min(4)
  @Max(900)
  var_log_audit: number;

  @IsNotEmpty()
  @IsNumber()
  @Min(2)
  @Max(900)
  var_tmp: number;

  @IsNotEmpty()
  @IsNumber()
  @Min(2)
  @Max(900)
  tmp: number;
  total?: number;
}

class NetworkDto {
  @IsNotEmpty()
  @IsNumber()
  id: number;

  @IsString()
  @IsNotEmpty()
  @IsOptional()
  subnetIpv4?: string;

  @IsString()
  @IsNotEmpty()
  @IsOptional()
  subnetIpv6?: string;

  @IsString()
  @IsNotEmpty()
  displayName: string;

  @IsString()
  @IsNotEmpty()
  name: string;
}

class VmwareNetworkDto {
  @IsNotEmpty()
  @IsString()
  id: string;

  @IsString()
  @IsNotEmpty()
  @IsOptional()
  subnetIpv4?: string;

  @IsString()
  @IsNotEmpty()
  @IsOptional()
  subnetIpv6?: string;

  @IsString()
  @IsNotEmpty()
  displayName: string;

  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsOptional()
  ipv4Gateway?: string;

  @IsString()
  @IsOptional()
  ipv6Gateway?: string;
}

export class SecondaryNetworkDto {
  @IsObject({ each: true })
  @IsNotEmptyObject()
  @ValidateNested()
  @Type(() => NetworkDto)
  @IsDefined()
  network: NetworkDto;

  @IsNotEmpty()
  @IsString()
  ipModes: string;

  @IsIP('4', { each: true })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  ipv4Addresses: string[];

  @IsIP('6', { each: true })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  ipv6Addresses: string[];

  @IsNotEmpty()
  @IsBoolean()
  ipv4: boolean;

  @IsNotEmpty()
  @IsBoolean()
  ipv6: boolean;
}

export class VmwareSecondaryNetworkDto {
  @IsObject({ each: true })
  @IsNotEmptyObject()
  @ValidateNested()
  @Type(() => VmwareNetworkDto)
  @IsDefined()
  network: VmwareNetworkDto;

  @IsNotEmpty()
  @IsString()
  ipModes: string;

  @IsIP('4', { each: true })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  ipv4Addresses: string[];

  @IsIP('6', { each: true })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  ipv6Addresses: string[];

  @IsNotEmpty()
  @IsBoolean()
  ipv4: boolean;

  @IsNotEmpty()
  @IsBoolean()
  ipv6: boolean;
}

export class Layout {
  @IsNotEmpty()
  @IsNumber()
  id: number;

  @IsNotEmpty()
  @IsString()
  code: string;

  @IsNotEmpty()
  @IsString()
  shortName: string;
}

export class VmwareLayout {
  @IsNotEmpty()
  @IsString()
  id: string;

  @IsNotEmpty()
  @IsString()
  code: string;

  @IsNotEmpty()
  @IsString()
  shortName: string;
}

class diskConfig {
  @IsString()
  @IsNotEmpty()
  @Matches(/^[a-zA-Z0-9\s][a-zA-Z0-9\s-_/]*$/, {
    message:
      'diskName must be a string. It can contain alphanumeric, underscore, hyphen',
  })
  @MaxLength(255, { message: 'diskName length should be less than 255 char' })
  diskName: string;

  @Min(10)
  @IsNumber()
  @IsNotEmpty()
  diskValue: number;
}

export enum DiskFileSystem {
  XFS = 'xfs',
  EXT4 = 'ext4',
}

export enum RubrikSLA {
  UNPROTECTED = 'Unprotected',
  RPO6_RSC = 'RPO6-RSC',
  RPO24_RSC = 'RPO24-RSC',
}

class TagsDto {
  @IsString()
  @IsNotEmpty()
  tagKey: string;

  @IsString()
  @IsNotEmpty()
  tagValue: string;
}

class ResourceDto {
  @IsNotEmpty()
  @IsNumber()
  id: number;

  @IsNotEmpty()
  @IsString()
  name: string;
}

class VmwareResourceDto {
  @IsNotEmpty()
  @IsString()
  id: string;

  @IsNotEmpty()
  @IsString()
  name: string;
}

class CloudDto {
  @IsNotEmpty()
  @IsString()
  id: string;

  @IsNotEmpty()
  @IsString()
  name: string;
}
class GroupDto extends ResourceDto {}

export class ProvisionVmRequestDto {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsString()
  @IsOptional()
  catalogName?: string;

  @ValidateNested()
  @IsNotEmpty()
  @Type(() => PlatformContext)
  platformContext: PlatformContext;

  @IsNotEmpty()
  @IsString()
  shortName: string;

  @IsNotEmpty()
  @IsString()
  size: string;

  @ValidateNested({ each: true })
  @Type((options) => {
    LoggerService.log('custom Opions', options);
    if (options?.object && 'customVolume' in options.object.customSizeOption) {
      return WindowsConfig;
    }
    if (options?.object && 'root' in options.object.customSizeOption) {
      return LinuxConfig;
    }
    throw new BadRequestException(
      'customSizeOption object in the payload does not have all the required fields!',
    );
  })
  customSizeOption?: WindowsConfig | LinuxConfig;

  @IsNotEmpty()
  targetLayout: Layout;

  @IsObject({ each: true })
  @IsNotEmptyObject()
  @ValidateNested()
  @Type(() => GroupDto)
  group: GroupDto;

  @IsNotEmpty()
  @IsString()
  datacenter: string;

  @IsNotEmpty()
  @IsString()
  dirrtAction: string;

  @IsNumber()
  @IsNotEmpty()
  cloudId: number;

  @IsNotEmpty()
  @Matches(/^[a-zA-Z0-9]+(?:-[a-zA-Z0-9]+)*$/, {
    message: 'hostname does not match the required pattern',
  })
  hostname: string;

  @IsArray()
  @IsNotEmpty()
  @ArrayMinSize(1)
  availableHostNames: string[];

  @IsNotEmpty()
  @IsNumber()
  vmCount: number;

  @IsOptional()
  @IsNumber()
  @Min(1)
  sequenceNumber: number;

  @IsObject({ each: true })
  @IsNotEmptyObject()
  @ValidateNested()
  @Type(() => ResourceDto)
  resource: ResourceDto;

  @IsObject({ each: true })
  @IsNotEmptyObject()
  @ValidateNested()
  @Type(() => NetworkDto)
  @IsDefined()
  network: NetworkDto;

  @IsObject({ each: true })
  @IsOptional()
  @ValidateNested()
  @Type(() => SecondaryNetworkDto)
  @IsDefined()
  secondaryNetwork?: SecondaryNetworkDto;

  @IsNotEmpty()
  @IsString()
  @IsOptional()
  ipModes: string;

  @IsIP('4', { each: true })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  ipv4Addresses: string[];

  @IsIP('6', { each: true })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  ipv6Addresses: string[];

  @IsNotEmpty()
  @IsBoolean()
  ipv4: boolean;

  @IsNotEmpty()
  @IsBoolean()
  ipv6: boolean;

  @IsString()
  @IsOptional()
  description: string;

  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(0)
  securityTools: string[];

  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(0)
  complianceTools: string[];

  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(0)
  observabilityTools: string[];

  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(0)
  inventoryTools: string[];

  @IsNotEmpty()
  @IsString()
  projectName: string;

  @IsString({
    message: `Password must be at least 12 characters with uppercase, lowercase, numbers and shouldn't be existing default password. $ and \ not allowed in passwords`,
  })
  password: string;

  @IsArray()
  @IsOptional()
  @ArrayUnique((disk: diskConfig) => disk.diskName, {
    message: 'diskName must be unique',
  })
  @ValidateNested({ each: true })
  @Type(() => diskConfig)
  addDisks?: diskConfig[];

  @Transform((params) => (params.value === '' ? null : params.value))
  @IsOptional()
  @IsEnum(DiskFileSystem, {
    message: 'diskFileSystem must be either xfs or ext4',
  })
  diskFileSystem?: DiskFileSystem;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(4)
  diskCount?: number;

  @IsOptional()
  @IsString()
  deeplinkUrl: string;

  @Transform((params) => (params.value === '' ? null : params.value))
  @IsOptional()
  @IsEnum(RubrikSLA)
  rubrikSLA?: RubrikSLA;

  @IsString()
  @IsOptional()
  @Matches(/^(ssh-rsa).*/, {
    message: 'ssh public key must start with "ssh-rsa".',
  })
  sshpubkey?: string;

  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => TagsDto)
  resourceTags?: TagsDto[];

  @IsString()
  @IsOptional()
  requestorPID?: string;

  @IsOptional()
  @IsString()
  inventoryAsn?: string;

  @IsString()
  projectDL: string;

  @IsString()
  projectId: string;

  @Min(4)
  @IsOptional()
  @IsNumber()
  swap?: number;
}

class GroupsDTO {
  @IsNotEmpty()
  @IsString()
  id: string;

  @IsNotEmpty()
  @IsString()
  name: string;
}

class DefaultQuota {
  @IsNotEmpty()
  @IsNumber()
  storage: number;

  @IsNotEmpty()
  @IsNumber()
  memory: number;

  @IsNotEmpty()
  @IsNumber()
  cpu: number;
}

class NetworkDomainDto {
  @IsString()
  dnsDomainName: string;

  @IsString()
  description: string;

  @IsString()
  dnsPrimaryServerIpv4: string;

  @IsString()
  dnsSecondaryServerIpv4: string;

  @IsString()
  dnsSecondaryServerIpv6: string;

  @IsString()
  dnsPrimaryServerIpv6: string;
}
export class ProvisionVmwareVmRequestDto {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsString()
  @IsOptional()
  catalogName?: string;

  @IsString()
  @IsOptional()
  applicationName?: string;

  @ValidateNested()
  @IsNotEmpty()
  @Type(() => PlatformContext)
  platformContext: PlatformContext;

  @IsNotEmpty()
  @IsString()
  shortName: string;

  @IsNotEmpty()
  @IsString()
  size: string;

  @ValidateNested({ each: true })
  @Type((options) => {
    LoggerService.log('custom Options', options);
    if (options?.object && 'customVolume' in options.object.customSizeOption) {
      return WindowsConfig;
    }
    if (options?.object && 'root' in options.object.customSizeOption) {
      return LinuxConfig;
    }
    throw new BadRequestException(
      'customSizeOption object in the payload does not have all the required fields!',
    );
  })
  customSizeOption?: WindowsConfig | LinuxConfig;

  @IsNotEmpty()
  targetLayout: VmwareLayout;

  @IsNotEmpty()
  @IsString()
  datacenter: string;

  @IsNotEmpty()
  @IsString()
  dirrtAction: string;

  @IsObject({ each: true })
  @IsNotEmptyObject()
  @ValidateNested()
  @Type(() => CloudDto)
  cloud: CloudDto;

  @Min(4)
  @IsOptional()
  @IsNumber()
  swap?: number;

  @IsNotEmpty()
  @Matches(/^[a-zA-Z0-9]+(?:-[a-zA-Z0-9]+)*$/, {
    message: 'hostname does not match the required pattern',
  })
  hostname: string;

  @IsArray()
  @IsNotEmpty()
  availableHostNames: string[];

  @IsNotEmpty()
  @IsNumber()
  vmCount: number;

  @IsOptional()
  @IsNumber()
  @Min(1)
  sequenceNumber: number;

  @IsObject({ each: true })
  @IsOptional()
  @ValidateNested()
  @Type(() => VmwareResourceDto)
  resource?: VmwareResourceDto;

  @IsObject({ each: true })
  @IsNotEmptyObject()
  @ValidateNested()
  @Type(() => VmwareNetworkDto)
  @IsDefined()
  network: VmwareNetworkDto;

  @IsObject({ each: true })
  @IsOptional()
  @ValidateNested()
  @Type(() => VmwareSecondaryNetworkDto)
  @IsDefined()
  secondaryNetwork?: VmwareSecondaryNetworkDto;

  @IsNotEmpty()
  @IsString()
  @IsOptional()
  ipModes: string;

  @IsIP('4', { each: true })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  ipv4Addresses: string[];

  @IsIP('6', { each: true })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  ipv6Addresses: string[];

  @IsNotEmpty()
  @IsBoolean()
  ipv4: boolean;

  @IsNotEmpty()
  @IsBoolean()
  ipv6: boolean;

  @IsString()
  @IsOptional()
  description: string;

  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(0)
  securityTools: string[];

  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(0)
  complianceTools: string[];

  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(0)
  observabilityTools: string[];

  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(0)
  inventoryTools: string[];

  @IsNotEmpty()
  @IsString()
  projectName: string;

  @IsString({
    message: `Password must be at least 12 characters with uppercase, lowercase, numbers and shouldn't be existing default password. $ and \ not allowed in passwords`,
  })
  password: string;

  @IsArray()
  @IsOptional()
  @ArrayUnique((disk: diskConfig) => disk.diskName, {
    message: 'diskName must be unique',
  })
  @ValidateNested({ each: true })
  @Type(() => diskConfig)
  addDisks?: diskConfig[];

  @Transform((params) => (params.value === '' ? null : params.value))
  @IsOptional()
  @IsEnum(DiskFileSystem, {
    message: 'diskFileSystem must be either xfs or ext4',
  })
  diskFileSystem?: DiskFileSystem;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(4)
  diskCount?: number;

  @IsOptional()
  @IsString()
  deeplinkUrl: string;

  @Transform((params) => (params.value === '' ? null : params.value))
  @IsOptional()
  @IsEnum(RubrikSLA)
  rubrikSLA?: RubrikSLA;

  @IsString()
  @IsOptional()
  @Matches(/^(ssh-rsa).*/, {
    message: 'ssh public key must start with "ssh-rsa".',
  })
  sshpubkey?: string;

  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => TagsDto)
  resourceTags?: TagsDto[];

  @IsString()
  @IsOptional()
  requestorPID?: string;

  @IsOptional()
  @IsString()
  inventoryAsn?: string;

  @IsString()
  projectDL: string;

  @IsString()
  projectId: string;

  @IsString()
  catalogId: string;

  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => GroupsDTO)
  groups: GroupsDTO[];

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => DefaultQuota)
  defaultQuota: DefaultQuota;

  //networkDomain is mandatory field 
  //to unblock testing in dev made it optional
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => NetworkDomainDto)
  networkDomain?: NetworkDomainDto;
}

class IpAddressesDto {
  @IsOptional()
  @IsString()
  ipv4Address?: string;

  @IsOptional()
  @IsString()
  ipv6Address?: string;
}

class IpReservationDto {
  @IsString()
  hostName: string;

  @ValidateNested()
  @Type(() => IpAddressesDto)
  ipAddress: IpAddressesDto;
}

export class IpReservationDownstreamResponseDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => IpReservationDto)
  ipReservation: IpReservationDto[];
}

export class VirtualMachineDto {
  @IsString()
  hostName: string;

  @IsOptional()
  @IsString()
  resourceId?: string;

  @IsBoolean()
  existsInNebula: boolean;

  @IsOptional()
  @ValidateNested()
  @Type(() => IpAddressesDto)
  ipAddress?: IpAddressesDto;
}

export class VmRetryRequestDto {
  @IsString()
  requestId: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => VirtualMachineDto)
  virtualMachines: VirtualMachineDto[];
}
