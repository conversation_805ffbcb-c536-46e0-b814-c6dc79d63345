import { Type } from 'class-transformer';
import {
  ArrayMinSize,
  ArrayNotEmpty,
  IsArray,
  IsEmail,
  IsMongoId,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';

class RoleDTO {
  @IsMongoId({ message: '$property must be a valid role id' })
  @IsString()
  @IsNotEmpty()
  roleId: string;
}

class DomainDTO {
  @IsMongoId({ message: '$property must be a valid domain id' })
  @IsString()
  @IsOptional()
  domainId: string = null;

  @IsArray()
  @ValidateNested({ each: true })
  @ArrayMinSize(1)
  @Type(() => RoleDTO)
  roles: RoleDTO[];
}
class CatalogPermissionsDTO {
  @IsMongoId({ message: '$property must be a valid catalog id' })
  @IsString()
  @IsNotEmpty()
  catalogId: string;

  @IsArray()
  @ValidateNested({ each: true })
  @ArrayMinSize(1, {
    message: '$property must contain at least one domain detail.',
  })
  @Type(() => DomainDTO)
  domain: DomainDTO[];
}
export class OnboardGroupRequest {
  @IsString()
  @IsNotEmpty()
  groupName: string;

  @IsString()
  @IsNotEmpty({ message: 'Description is required and cannot be empty' })
  description: string;

  @IsEmail(
    { host_whitelist: ['charter.com'] },
    {
      message: 'Invalid email domain, allowed email domains are: charter.com',
      each: true,
    },
  )
  @ArrayNotEmpty()
  @IsArray()
  emailDistribution: string[];

  @IsString()
  @IsOptional()
  nebulaClientId: string;

  @IsString()
  @IsNotEmpty({ message: 'Organization ID is required and cannot be empty' })
  organizationId: string;

  @IsString()
  @IsNotEmpty({ message: 'Vertical ID is required and cannot be empty' })
  verticalId: string;

  @IsString()
  @IsNotEmpty({ message: 'Department ID is required and cannot be empty' })
  departmentId: string;

  @IsArray()
  @ValidateNested({ each: true })
  @ArrayMinSize(1, {
    message: '$property must contain at least one catalog permission.',
  })
  @Type(() => CatalogPermissionsDTO)
  catalogPermissions: CatalogPermissionsDTO[];

  @IsString()
  @IsNotEmpty()
  deeplinkUrl: string;
}
