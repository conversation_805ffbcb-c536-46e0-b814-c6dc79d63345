// create-secret-metadata.dto.spec.ts
import { validate } from 'class-validator';
import { CreateSecretMetaDataDto } from './secretsMetadata.dto';
import {
  SecretType,
  Status,
  RotationType,
} from '../types/secretsMetadata.enum';

describe('CreateSecretMetaDataDto', () => {
  it('should fail validation if vaultPath is missing for ROTATABLE_SECRET', async () => {
    const dto = new CreateSecretMetaDataDto();
    dto.type = SecretType.ROTATABLE_SECRET;
    dto.resourceId = 'res123';
    dto.status = Status.ACTIVE;
    dto.active = true;

    const errors = await validate(dto);
    const errorProps = errors.map((e) => e.property);
    expect(errorProps).toContain('vaultPath');
  });

  it('should validate VAULT_TOKEN type with optional fields', async () => {
    const dto = new CreateSecretMetaDataDto();
    dto.type = SecretType.VAULT_TOKEN;
    dto.resourceId = 'res456';
    dto.status = Status.ACTIVE;
    dto.active = true;
    dto.vaultPath = '/vault/token';
    dto.tokenTTLInHours = 12;
    dto.expiryDate = new Date().toISOString();
    dto.resourcesDetails = { key: 'value' };
    dto.notificationEnabled = true;
    dto.tokenRenewByNebula = false;
    dto.actionId = 'NEB_ACTION_1';

    const errors = await validate(dto);
    expect(errors.length).toBe(2);
  });

  it('should validate NORMAL_SECRET type with secretId and vaultPath', async () => {
    const dto = new CreateSecretMetaDataDto();
    dto.type = SecretType.NORMAL_SECRET;
    dto.resourceId = 'res789';
    dto.status = Status.ACTIVE;
    dto.active = true;
    dto.secretId = 'secret123';
    dto.vaultPath = '/vault/normal';
    dto.actionId = 'NEB_ACTION_1';

    const errors = await validate(dto);
    expect(errors.length).toBe(1);
  });

  it('should fail validation if required fields are missing', async () => {
    const dto = new CreateSecretMetaDataDto(); // missing required fields
    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    const errorProps = errors.map((e) => e.property);
    expect(errorProps).toContain('type');
    expect(errorProps).toContain('resourceId');
    expect(errorProps).toContain('status');
    expect(errorProps).toContain('active');
  });
});
