import { Modu<PERSON> } from '@nestjs/common';
import axios, { AxiosInstance } from 'axios';
import * as https from 'https';
import { VmwareAdapterService } from './vmware-adapter.service';

import { ConfigService } from '@nestjs/config';
import { ENVIRONMENT_VARS } from '../utils/constants';
import { LoggerModule } from '../loggers/logger.module';
import { ErrorDetails } from '../vmware-error-loggers/vmware-error-logger.service';

@Module({
  imports: [LoggerModule],
  controllers: [],
  providers: [
    VmwareAdapterService,
    ErrorDetails,
    {
      provide: 'VMWARE_ADAPTER_SERVICE_API',
      inject: [ConfigService],
      useFactory: (configService: ConfigService): AxiosInstance => {
        return axios.create({
          baseURL: configService.get(
            ENVIRONMENT_VARS.VMWARE_ADAPTER_SERVICE_API_BASE_URL,
          ),
          httpsAgent: new https.Agent({ rejectUnauthorized: false }),
        });
      },
    },
  ],
  exports: [VmwareAdapterService],
})
export class VmwareAdapterServiceModule {}
