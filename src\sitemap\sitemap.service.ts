import {
  Injectable,
  InternalServerErrorException,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { Workbook } from 'exceljs';
import { SitemapEntity } from './entity/sitemap.entity';
import { SitemapRepository } from './sitemap.repository';
import { PaginationQueryDto } from 'src/utils/pagination/dto/pagination.dto';
import { LoggerService } from 'src/loggers/logger.service';

@Injectable()
export class SitemapService {
  constructor(
    private readonly sitemapRepository: SitemapRepository,
    private logger: LoggerService,
  ) {}

  async processSitemapFile(file: Buffer): Promise<any> {
    const workbook = new Workbook();
    try {
      await workbook.xlsx.load(file);
    } catch (err) {
      throw new HttpException(
        'Upload proper sitemap file, with NETOPS+FOPS sheet!',
        HttpStatus.BAD_REQUEST,
      );
    }
    const worksheet = workbook.getWorksheet('NETOPS+FOPS');
    const sitemapData = [];
    if (worksheet != undefined) {
      worksheet.spliceRows(0, 2);
      try {
        worksheet.eachRow((row) => {
          var soldToInfoObj = {
            soldToId: row.getCell(10).value,
            CLLI: row.getCell(7).value,
            address: row.getCell(11).value,
            city: row.getCell(12).value,
            state: row.getCell(13).value,
            zip: row.getCell(14).value,
            siteContactPID: row.getCell(15).value,
            manager: row.getCell(16).value,
            phone: row.getCell(17).value,
            soldToAddress: row.getCell(37).value,
          };
          var shipToInfoObj = {
            shipToId: row.getCell(18).value,
            address: row.getCell(19).value,
            city: row.getCell(20).value,
            state: row.getCell(21).value,
            zip: row.getCell(22).value,
            siteContactPID: row.getCell(23).value,
            siteContactPerson: row.getCell(24).value,
            phone: row.getCell(25).value,
            shipToAddress: row.getCell(38).value,
            palletJack: row.getCell(26).value,
            liftGate: row.getCell(27).value,
          };
          var sitemapDataObj = {
            siteName: row.getCell(9).value,
            SAPFunctionalName: row.getCell(8).value,
            regionOrigin: row.getCell(1).value,
            marketOrigin: row.getCell(2).value,
            regionISP: row.getCell(3).value,
            marketISP: row.getCell(4).value,
            regionFOPS: row.getCell(5).value,
            marketFOPS: row.getCell(6).value,
            facilityType: row.getCell(28).value,
            colocationCageInfo: row.getCell(29).value,
            sparingDepots: row.getCell(30).value,
            spareCLLI: row.getCell(31).value,
            latitude: row.getCell(32).value,
            longitude: row.getCell(33).value,
            vendorAddressHeader: row.getCell(34).value,
            soldToInfo: soldToInfoObj,
            shipToInfo: shipToInfoObj,
            entity: row.getCell(39).value,
            siteCreatedDate: row.getCell(35).value,
            siteModifiedDate: row.getCell(36).value,
          };
          sitemapData.push(sitemapDataObj);
        });
      } catch (err) {
        this.logger.error(
          'Error with processing the uploaded sitemap data',
          err,
        );
        throw new HttpException(
          'Unable to process uploaded sitemap data!',
          HttpStatus.BAD_REQUEST,
        );
      }
    } else {
      throw new HttpException(
        'Upload proper sitemap file, with NETOPS+FOPS sheet!',
        HttpStatus.BAD_REQUEST,
      );
    }
    return await sitemapData;
  }

  async createSitemapRecords(sitemapData: SitemapEntity[]):Promise<any> {
    if (sitemapData.length) {
      return await this.sitemapRepository.createManySitemap(sitemapData);
    } else
      throw new InternalServerErrorException(
        'No records found to be inserted!',
      );
  }

  async getSiteMapData(pageQuery: PaginationQueryDto) {
    return await this.sitemapRepository.getSiteMapData(pageQuery);
  }

  async getAllSiteMapData() {
    return await this.sitemapRepository.getAllSiteMapData();
  }

  async updateSiteMapData(id: string, siteMap: SitemapEntity) {
    return await this.sitemapRepository.updateSiteMapData(id, siteMap);
  }

  async deleteSiteMapData(id: string) {
    return await this.sitemapRepository.deleteSiteMapData(id);
  }
}
