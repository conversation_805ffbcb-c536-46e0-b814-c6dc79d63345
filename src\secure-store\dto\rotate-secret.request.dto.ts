import { IsNotEmpty, IsString, Matches } from 'class-validator';
import { IsEncrypted } from '../validators/is-encrypted.decorator';

export class RotateSecretRequestDto {
  @IsString()
  @IsNotEmpty()

  @Matches(/^NEB-VAULT-ROT-SECRET-\d+$/, {
    message: 'secretId must follow the format NEB-VAULT-ROT-SECRET-<number>',
  })
  secretId: string;

  @IsString()
  @IsNotEmpty()
  @IsEncrypted({ message: 'newPassword must be an encrypted string.' })
  newPassword: string;
}
