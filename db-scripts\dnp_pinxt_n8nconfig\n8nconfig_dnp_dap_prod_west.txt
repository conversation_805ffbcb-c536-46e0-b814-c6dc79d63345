db.n8nconfig.insertOne({
  "workflow": "dnp-dap-PROD-WEST",
  "baseUrl": "https://nebula-west.charter.com/",
  "generateUUIDWorkflow": "kfFP7XZEJffaQpe5",
  "catalogStepsWorkflow": "8SqOYagRiArDrgfH",
  "eventSourceHostName": "chdcnc03-caas-mgmt-v3.cloud.charter.com",
  "activityLogUrl":"http://activity-log-producer-service.nebula.svc.cluster.local:80/activity-log-producer/v1/log",
  "cloudApiUrl": "http://cloud-api-service.nebula.svc.cluster.local:80/nebula-api/",
  "updateInterfaceDelay":"20"
})