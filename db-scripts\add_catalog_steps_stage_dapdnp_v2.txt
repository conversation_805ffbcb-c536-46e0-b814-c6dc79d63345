function runScript() {
  const catalog = db.menvcataloglevel04.findOne({
    shortName: 'doNotPage',
  });

  if (!catalog) {
    print('Catalog document does not exist, aborting the transaction');
    throw new Error('Catalog document does not exist, aborting the transaction');
  }

  db.catalogsteps.updateMany(
    { catalogLevel04Id: catalog._id, version: 1 },
    { $set: { active: false } }
  );

  const existingV2 = db.catalogsteps.findOne({
    catalogLevel04Id: catalog._id,
    version: 2,
  });

  if (existingV2) {
    print('Version 2 catalog steps already exist. Skipping insert.');
    return;
  }

  const catalogSteps = {
    catalogLevel04Id: catalog._id,
    version: 2,
    active: true,
    activityLogSteps: [
      {
        name: 'Receive DNP Request',
        eventCode: 'NEB-EVENT-VM-CREATE-1001',
        sequence: 1,
      },
      {
        name: 'Approve Request',
        eventCode: 'NEB-EVENT-APPROVAL-1001',
        sequence: 2,
      },
      {
        name: 'Update Nebula Interface',
        eventCode: 'NEB-EVENT-DNP-UPDATE-INTERFACE-ID-3001',
        sequence: 3,
      },
      {
        name: 'Publish DAP Status to Nebula',
        eventCode: 'NEB-EVENT-WEBHOOK-CALL-ID-4001',
        sequence: 4,
      },
      {
        name: 'Update Service Request',
        eventCode: 'NEB-EVENT-DNP-UPDATE-SERVICE-REQUEST-5001',
        sequence: 5,
      },
    ],
  };

  db.catalogsteps.insertOne(catalogSteps);
  print('Version 2 catalog steps inserted successfully');
}

let session;
try {
  session = db.getMongo().startSession();
  session.startTransaction();
  runScript();
  session.commitTransaction();
} catch (e) {
  session.abortTransaction();
  print('Transaction aborted due to error', e);
} finally {
  session.endSession();
}
