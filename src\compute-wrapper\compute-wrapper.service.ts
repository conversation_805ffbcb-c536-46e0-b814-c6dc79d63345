import { Inject, Injectable } from '@nestjs/common';
import { AxiosInstance } from 'axios';
import {
  Groups,
  Layouts,
  MorpheusData,
  ReferenceData,
  ValidateHostNamesRequestDto,
  ValidateHostNamesRequestDtoV2,
  VmwareCluster,
  VMWareConsoleDetailsResponse,
  VmwareData,
  VmwareReferenceData,
} from '../compute/dto/compute.dto';
import { withResponseErrorHandler } from '../utils/helpers';
import { LoggerService } from '../loggers/logger.service';
import { ResourceUpdateSuccessDto } from '../resources/dto/resourceUpdateSuccess.dto';
import { VmwareAdapterService } from '../vmware-adapter-service/vmware-adapter.service';
import { ErrorDetails } from 'src/vmware-error-loggers/vmware-error-logger.service';
import { ERROR_DETAILS } from 'src/vmware-error-loggers/vmware-error-details';

@Injectable()
export class ComputeWrapperService {
  constructor(
    @Inject('COMPUTE_SERVICE_API')
    private readonly computeServiceApi: AxiosInstance,
    private readonly logger: LoggerService,
    private readonly vmwareAdapterService: VmwareAdapterService,
    private readonly vmwareErrorlogService: ErrorDetails,
  ) {}

  async fetchRefernceData(email: string): Promise<ReferenceData> {
    let userEmail = email;
    if (!userEmail && process.env.MORPHEUS_EMAIL !== undefined) {
      this.logger.debug(`userEmail doesn't exists in JWT: ${userEmail}`);
      userEmail = process.env.MORPHEUS_EMAIL;
    }
    return withResponseErrorHandler(
      this.computeServiceApi.get(`/v1/reference-data?email=${userEmail}`),
    );
  }

  async validateHostNames(
    validateHostNamesRequestDto: ValidateHostNamesRequestDto,
  ) {
    this.logger.debug(
      'Calling compute service for hostname validation from cloud-api',
      JSON.stringify(validateHostNamesRequestDto),
    );
    return await withResponseErrorHandler(
      this.computeServiceApi.post(
        `/v1/validate-hostnames`,
        validateHostNamesRequestDto,
      ),
    );
  }

  async validateHostNamesV2(
    validateHostNamesRequestDto: ValidateHostNamesRequestDtoV2,
  ) {
    this.logger.debug(
      'Calling compute service for hostname validation v2 from cloud-api',
      JSON.stringify(validateHostNamesRequestDto),
    );
    return await withResponseErrorHandler(
      this.computeServiceApi.post(
        `/v2/validate-hostnames`,
        validateHostNamesRequestDto,
      ),
    );
  }

  async fetchResources(
    cloudId: number,
    email: string,
  ): Promise<MorpheusData[]> {
    let userEmail = email;
    // for svc accounts userEmail wont be present in the jwt, in that case use an email configured in env var
    if (!userEmail && process.env.MORPHEUS_EMAIL !== undefined) {
      this.logger.debug(`userEmail doesn't exists in JWT: ${userEmail}`);
      userEmail = process.env.MORPHEUS_EMAIL;
    }
    return withResponseErrorHandler(
      this.computeServiceApi.get(`/v1/resources/${cloudId}?email=${userEmail}`),
    );
  }
  async fetchBlueResources(
    cloudId: number,
    network: string,
  ): Promise<MorpheusData[]> {
    return withResponseErrorHandler(
      this.computeServiceApi.get(
        `/v1/blue/resources?cloudId=${cloudId}&network=${network}`,
      ),
    );
  }
  async fetchBlueGroups(): Promise<Groups[]> {
    return withResponseErrorHandler(
      this.computeServiceApi.get(`/v1/blue/groups`),
    );
  }

  async fetchLayouts(layoutInstanceId: number): Promise<Layouts[]> {
    return withResponseErrorHandler(
      this.computeServiceApi.get(`/v1/layouts/${layoutInstanceId}`),
    );
  }
  async fetchBlueLayouts(layoutInstanceId: number): Promise<Layouts[]> {
    console.log('BLUE layouts calling');
    return withResponseErrorHandler(
      this.computeServiceApi.get(`/v1/blue/layouts/${layoutInstanceId}`),
    );
  }

  async fetchOptionTypeList() {
    return withResponseErrorHandler(
      this.computeServiceApi.get(`/v1/generic/Blue/optionTypeLists`),
    );
  }
  async fetchOptionTypeListItems(id): Promise<MorpheusData[]> {
    const data = await withResponseErrorHandler(
      this.computeServiceApi.get(
        `/v1/generic/Blue/optionTypeLists/items?id=${id}`,
      ),
    );
    return data?.listItems;
  }

  async fetchBlueDatacenter(): Promise<MorpheusData[]> {
    return withResponseErrorHandler(
      this.computeServiceApi.get(`/v1/generic/Blue/datacenter`),
    );
  }

  async fetchOptionTypeListItemsDb() {
    const data = await withResponseErrorHandler(
      this.computeServiceApi.get(`/v1/generic/Blue/optionTypeLists/DB`),
    );
    return data;
  }

  async fetchRefernceDataGeneric(
    environment: string,
    email: string,
  ): Promise<ReferenceData> {
    let userEmail = email;
    if (!userEmail && process.env.MORPHEUS_EMAIL !== undefined) {
      this.logger.debug(`userEmail doesn't exists in JWT: ${userEmail}`);
      userEmail = process.env.MORPHEUS_EMAIL;
    }
    return withResponseErrorHandler(
      this.computeServiceApi.get(
        `/v1/generic/${environment}/reference-data?email=${userEmail}`,
      ),
    );
  }

  async fetchResourcesGeneric(
    environment: string,
    cloudId: number,
    email: string,
  ): Promise<MorpheusData[]> {
    let userEmail = email;
    // for svc accounts userEmail wont be present in the jwt, in that case use an email configured in env var
    if (!userEmail && process.env.MORPHEUS_EMAIL !== undefined) {
      this.logger.debug(`userEmail doesn't exists in JWT: ${userEmail}`);
      userEmail = process.env.MORPHEUS_EMAIL;
    }
    return withResponseErrorHandler(
      this.computeServiceApi.get(
        `/v1/generic/${environment}/resources/${cloudId}?email=${userEmail}`,
      ),
    );
  }

  async fetchLayoutsGeneric(
    environment: string,
    layoutInstanceId: number,
  ): Promise<Layouts[]> {
    this.logger.log('Generic layouts calling');
    return withResponseErrorHandler(
      this.computeServiceApi.get(
        `/v1/generic/${environment}/layouts/${layoutInstanceId}`,
      ),
    );
  }

  async validateHostNamesGeneric(
    environment: string,
    validateHostNamesRequestDto: ValidateHostNamesRequestDto,
  ) {
    this.logger.debug(
      'Calling compute service for hostname validation from cloud-api',
      JSON.stringify(validateHostNamesRequestDto),
    );
    return await withResponseErrorHandler(
      this.computeServiceApi.post(
        `/v1/generic/${environment}/validate-hostnames`,
        validateHostNamesRequestDto,
      ),
    );
  }

  async getGenericNetwork(environment: string, cloudId, catalogName: string) {
    this.logger.log('calling compute-service for getting network', cloudId);

    return await withResponseErrorHandler(
      this.computeServiceApi.get(
        `/v1/generic/${environment}/network/catalogName?catalogName=${catalogName}&zoneId=${cloudId}`,
      ),
    );
  }
  async getGenericADGroup(environment: string, adGroup: string) {
    this.logger.log('calling compute-service for getting adGroup', adGroup);

    return await withResponseErrorHandler(
      this.computeServiceApi.get(
        `/v1/generic/${environment}/group/groupName?groupName=${adGroup}`,
      ),
    );
  }
  async getGenericAppRef(environment: string, appRef: string) {
    this.logger.log('calling compute-service for getting appRef', appRef);

    return await withResponseErrorHandler(
      this.computeServiceApi.get(
        `/v1/generic/${environment}/app/appName?appName=${appRef}`,
      ),
    );
  }

  async deleteVm(id: number) {
    return withResponseErrorHandler(
      this.computeServiceApi.delete(`/v1/virtual-machine/${id}`),
    );
  }

  async getVMStatus(instanceId: number) {
    this.logger.log(
      `calling compute service api to refresh vm with id ${instanceId}`,
    );

    return withResponseErrorHandler(
      this.computeServiceApi.get(`/v1/vm/instance?instanceId=${instanceId}`),
    );
  }

  async getBlueVMStatus(instanceId: number) {
    this.logger.log(
      `calling compute service api to refresh blue vm with id ${instanceId}`,
    );

    return withResponseErrorHandler(
      this.computeServiceApi.get(
        `/v1/blue/vm/instance?instanceId=${instanceId}`,
      ),
    );
  }

  async stopVm(vmId): Promise<ResourceUpdateSuccessDto> {
    this.logger.log(`Calling compute service api to stop VM with id ${vmId}`);
    return withResponseErrorHandler(
      this.computeServiceApi.put(`/v1/virtual-machine/stop?instanceId=${vmId}`),
    );
  }

  async startVm(vmId): Promise<ResourceUpdateSuccessDto> {
    this.logger.log(`Calling compute service api to start VM with id ${vmId}`);
    return withResponseErrorHandler(
      this.computeServiceApi.put(
        `/v1/virtual-machine/start?instanceId=${vmId}`,
      ),
    );
  }

  async restartVm(vmId): Promise<ResourceUpdateSuccessDto> {
    this.logger.log(
      `Calling compute service api to restart VM with id ${vmId}`,
    );
    return withResponseErrorHandler(
      this.computeServiceApi.put(
        `/v1/virtual-machine/restart?instanceId=${vmId}`,
      ),
    );
  }

  async searchVmDetails(searchVMQueryDTO, environment) {
    this.logger.log('passing search vm details params');
    const { page, limit, group, datacenterName, network } = searchVMQueryDTO;
    return withResponseErrorHandler(
      this.computeServiceApi.get(`/v1/virtual-machine/${environment}/search`, {
        params: {
          page,
          limit,
          group,
          datacenterName,
          network,
        },
      }),
    );
  }

  async fetchUsersRoles(email: string) {
    this.logger.log('passing email to get user roles from compute-service');
    return withResponseErrorHandler(
      this.computeServiceApi.get(`/v1/userRoles?email=${email}`),
    );
  }

  async getNetworkByNetworkId(id: number) {
    this.logger.log(
      `passing network id : ${id} to compute-service to get network details`,
    );
    return withResponseErrorHandler(
      this.computeServiceApi.get(`/v1/network/${id}`),
    );
  }

  async fetchResourcesVmware(
    domain: string,
    datacenter: string,
    cloudId: string,
    email: string,
  ): Promise<VmwareCluster[]> {
    try {
      let userEmail = email;
      // for svc accounts userEmail wont be present in the jwt, in that case use an email configured in env var
      if (!userEmail && process.env.MORPHEUS_EMAIL !== undefined) {
        this.logger.debug(`userEmail doesn't exists in JWT: ${userEmail}`);
        userEmail = process.env.MORPHEUS_EMAIL;
      }
      const response = await this.vmwareAdapterService.fetchResources(
        domain,
        datacenter,
        cloudId,
      );
      return response;
    } catch (error) {
      this.vmwareErrorlogService.logError(
        error,
        'fetchResourcesVmware',
        ERROR_DETAILS.VMWARE_FETCH_RESOURCE_ERROR_CODE,
        ERROR_DETAILS.VMWARE_FETCH_RESOURCE_ERROR_MESSAGE,
      );
    }
  }
  async fetchResourcesVmwareByNetworkId(
    domain: string,
    datacenter: string,
    cloudId: string,
    networkId: string,
  ): Promise<VmwareCluster[]> {
    try {
      const response =
        await this.vmwareAdapterService.fetchResourcesByNetworkId(
          domain,
          datacenter,
          cloudId,
          networkId,
        );
      return response;
    } catch (error) {
      this.vmwareErrorlogService.logError(
        error,
        'fetchResourcesByNetworkId',
        ERROR_DETAILS.VMWARE_FETCH_RESOURCE_NETWORK_ERROR_CODE,
        ERROR_DETAILS.VMWARE_FETCH_RESOURCE_NETWORK_ERROR_MESSAGE,
      );
    }
  }

  async fetchVMwareNetwork(
    domain: string,
    datacenter: string,
    networkId: string,
  ) {
    try {
      const response = await this.vmwareAdapterService.fetchVMwareNetwork(
        domain,
        datacenter,
        networkId,
      );
      return response;
    } catch (error) {
      this.vmwareErrorlogService.logError(
        error,
        'fetchVMwareNetwork',
        ERROR_DETAILS.VMWARE_FETCH_RESOURCE_BY_NETWORK_ERROR_CODE,
        ERROR_DETAILS.VMWARE_FETCH_RESOURCE_BY_NETWORK_ERROR_MESSAGE,
      );
    }
  }

  async fetchLayoutsVmware(
    domain: string,
    datacenter: string,
    catalog: string,
  ): Promise<VmwareReferenceData> {
    console.log('Vmware layouts calling');
    return await this.vmwareAdapterService.fetchLayouts(
      domain,
      datacenter,
      catalog,
    );
  }

  async validateHostNamesVmware(
    domain: string,
    datacenter: string,
    validateHostNamesRequestDto: ValidateHostNamesRequestDto,
  ) {
    try {
      this.logger.debug(
        'Calling compute service for hostname validation from cloud-api',
        JSON.stringify(validateHostNamesRequestDto),
      );
      this.logger.log('Calling Morpheus for hostnames validation');
      return await this.vmwareAdapterService.validateHostNames(
        domain,
        datacenter,
        validateHostNamesRequestDto,
      );
    } catch (error) {
      this.vmwareErrorlogService.logError(
        error,
        'validateHostNamesVmware',
        ERROR_DETAILS.VMWARE_VALIDATE_HOSTNAME_ERROR_CODE,
        ERROR_DETAILS.VMWARE_VALIDATE_HOSTNAME_ERROR_MESSAGE,
      );
    }
  }

  async getGenericCloudResourcesByCloud(environment, optionTypeListDTOByCloud) {
    const { cloudId, shortName, optionType } = optionTypeListDTOByCloud;
    this.logger.log(
      `calling compute-service for getting cloud resources for a cloudID : ${optionTypeListDTOByCloud.cloudId} , Env : ${environment}`,
    );
    return await withResponseErrorHandler(
      this.computeServiceApi.get(
        `/v1/generic/${environment}/optionTypeLists/cloud?cloudId=${cloudId}&PlatformName=${shortName}&optionType=${optionType}`,
      ),
    );
  }

  async getLatestStatusFromVMWare(
    domain: string,
    datacenter: string,
    cloudId: string,
    hostName: string,
  ): Promise<any> {
    this.logger.log('fetching latest status of VMWare Vms');
    return await this.vmwareAdapterService.getCurrentStatus(
      domain,
      datacenter,
      cloudId,
      hostName,
    );
  }

  async getVMWareConsoleDetails(domain, datacenter, cloudId, vmwareConsoleDTO) {
    try {
      this.logger.log('fetching console details of VMWare Vms');
      const response: VMWareConsoleDetailsResponse =
        await this.vmwareAdapterService.getVMWareConsoleDetails(
          domain,
          datacenter,
          cloudId,
          vmwareConsoleDTO,
        );
      const newResponse = response;
      const trimmedUrl = response.webMksTicket.url.replace(/^wss:\/\//, '');
      const newUrl = `${process.env.APPEND_CONSOLE_URL}${trimmedUrl}`;
      newResponse.webMksTicket.url = newUrl;
      return newResponse;
    } catch (error) {
      this.vmwareErrorlogService.logError(
        error,
        'getVMWareConsoleDetails',
        ERROR_DETAILS.VMWARE_CONSOLE_DETAILS_ERROR_CODE,
        ERROR_DETAILS.VMWARE_CONSOLE_DETAILS_ERROR_MESSAGE,
      );
    }
  }

  async getDnsDetailsFromVMWare(domain: string, datacenter: string) {
    try {
      this.logger.log(
        'calling vmware adapter service to get dns details from vmware',
      );
      const dnsDetails = await this.vmwareAdapterService.getDnsDetails(
        domain,
        datacenter,
      );
      this.logger.log(
        'dns details received from vmware in compute-wrapper',
        dnsDetails,
      );
      return dnsDetails;
    } catch (error) {
      this.vmwareErrorlogService.logError(
        error,
        'getDnsDetailsFromVMWare',
        ERROR_DETAILS.VMWARE_DNS_DETAILS_ERROR_CODE,
        ERROR_DETAILS.VMWARE_DNS_DETAILS_ERROR_MESSAGE,
      );
    }
  }
}
