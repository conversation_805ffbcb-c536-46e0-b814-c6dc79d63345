import {
  Controller,
  UseGuards,
  ValidationPipe,
  Post,
  Body,
  Get,
  Query,
  Param,
  Patch,
  ParseArrayPipe,
  Delete,
  ParseBoolPipe,
} from '@nestjs/common';
import { SecureStoreService } from './secure-store.service';
import {
  <PERSON>pi<PERSON>ody,
  ApiParam,
  ApiQuery,
  ApiSecurity,
  ApiTags,
} from '@nestjs/swagger';
import { LoggerService } from '../loggers/logger.service';
import { PermissionGuard } from '../iam/permission.guard';
import { AuthenticationGuard } from '../auth/authentication.guard';
import {
  NamespaceResponseDTO,
  PathDTO,
} from './dto/namespace-child.response.dto';
import { CreateNameSpaceRequestDto } from './dto/create_namespace.request.dto';
import { FetchSecretResponseDTO } from './dto/fetch-secrets.response.dto';
import {
  CreateVaultSecretsRequestDto,
  SecretsIdsQueryDto,
} from './dto/create-vault-secrets.request.dto';
import { UpdateVaultSecretRequestDto } from './dto/update-vault-secrets.request.dto';
import { NameSpaceType } from './enums/NamespaceType';
import { RotateSecretRequestDto } from './dto/rotate-secret.request.dto';
import { RotateSecretResponseDto } from './dto/rotate-secret.response.dto';
import { SecretRotationUpdateRequestDto } from './dto/secret_rotation_update.request.dto';
import { FetchSecretRequestDto } from './dto/fetch-secrets.request.dto';
import { PaginationQueryDto } from '../utils/pagination/dto/pagination.dto';
import { createSecretPoliciesDto } from './dto/create_secretPolicy.request.dto';

import { EnvPermission } from '../iam/envPermission.decorator';
import { SecureStoreRequestResolver } from './utils';
import { PermissionKey } from '../iam/types';
import { DecryptPasswordRequestDto } from './dto/decrypt.request.dto';
import { FetchNamespacesQuery } from './dto/fetcha-namespaces.dto';

@ApiTags('Secure Store')
@ApiSecurity('x-nebula-authorization')
@UseGuards(AuthenticationGuard, PermissionGuard)
@Controller('secure-store')
export class SecureStoreController {
  constructor(
    private readonly secureStoreService: SecureStoreService,
    private readonly logger: LoggerService,
  ) {}

  @Get('namespaces/:namespaceName')
  @ApiQuery({
    name: 'type',
    enum: NameSpaceType,
    example: NameSpaceType.PATH,
  })
  async fetchNamespaceData(
    @Param('namespaceName', ValidationPipe) namespaceName: string,
    @Query('type', ValidationPipe) type?: NameSpaceType,
  ): Promise<NamespaceResponseDTO | PathDTO[]> {
    const metaData = await this.secureStoreService.fetchNamespaceData(
      decodeURIComponent(namespaceName),
      type,
    );
    return metaData;
  }

  @Get('namespaces/:namespaceName/path/:paths')
  @ApiQuery({
    name: 'paginate',
    required: false,
    type: Boolean,
    description: 'Optional flag to enable/disable pagination',
    example: false,
  })
  @ApiQuery({
    name: 'filter',
    required: false,
    type: String,
    description: 'Optional property to filter Rotatable/Norrmal Secret',
    example: { type: 'Rotatable' },
  })
  async fetchVaultSecrets(
    @Param('paths', ValidationPipe) path: string,
    @Param('namespaceName', ValidationPipe) namespaceName: string,
    @Query('version') version?: FetchSecretRequestDto['version'],
    @Query('paginate', ParseBoolPipe) paginate: boolean = false,
    @Query() paginationParams?: PaginationQueryDto,
  ): Promise<FetchSecretResponseDTO[]> {
    try {
      const response: FetchSecretResponseDTO[] =
        await this.secureStoreService.fetchVaultSecretsPaginated(
          {
            path,
            namespace: decodeURIComponent(namespaceName),
            version,
          },
          {
            ...paginationParams,
          },
          paginate,
        );
      return response;
    } catch (error) {
      this.logger.error(`error while fetch secrets`);
      this.logger.error(error);
      throw error;
    }
  }

  @Post('vault-token/namespaces/:namespaceName')
  async createValutTokenSecrets(
    @Param('namespaceName', ValidationPipe) namespaceName: string,
    @Body() createVaultSecretsRequestDto: CreateVaultSecretsRequestDto,
  ) {
    try {
      return await this.secureStoreService.createVaultSecrets(
        decodeURIComponent(namespaceName),
        createVaultSecretsRequestDto,
      );
    } catch (error) {
      this.logger.error(`error while fetch namespace: ${error}`);
      this.logger.error(error);
      throw error;
    }
  }

  @Post('namespaces/:namespaceName/path/:paths')
  @EnvPermission(SecureStoreRequestResolver, PermissionKey.CREATE)
  async createVaultSecrets(
    @Param('namespaceName', ValidationPipe) namespaceName: string,
    @Param('paths', ValidationPipe) path: string,
    @Body() createVaultSecretsRequestDto: CreateVaultSecretsRequestDto,
  ) {
    try {
      return await this.secureStoreService.createVaultSecrets(
        decodeURIComponent(namespaceName),
        createVaultSecretsRequestDto,
        path,
      );
    } catch (error) {
      this.logger.error(`error while fetch namespace: ${error}`);
      this.logger.error(error);
      throw error;
    }
  }

  @Post('namespaces')
  async createNameSpaceRequest(
    @Body() createNameSpaceRequestDto: CreateNameSpaceRequestDto,
  ) {
    return this.secureStoreService.createNameSpaceRequest(
      createNameSpaceRequestDto,
    );
  }

  @Post('renew-tokens')
  async renewBulkTokens(secretIds: string[]) {
    return await this.secureStoreService.renewBulkTokens(secretIds);
  }

  @Post('secret-rotate')
  @ApiBody({
    isArray: true,
    type: RotateSecretRequestDto,
  })
  async rotateSecretRequest(
    @Body(new ParseArrayPipe({ items: RotateSecretRequestDto }))
    rotateSecretRequest: RotateSecretRequestDto[],
  ): Promise<RotateSecretResponseDto> {
    return await this.secureStoreService.createRotateSecretRequest(
      rotateSecretRequest,
    );
  }

  @Post('secrets/rotation/result')
  async secretsRotationResult(
    @Body() secretRotationUpdateRequest: SecretRotationUpdateRequestDto,
  ): Promise<any> {
    return await this.secureStoreService.secretsRotationResult(
      secretRotationUpdateRequest,
    );
  }

  @Get('secretId/:secretId')
  async fetchSecretsDetails(
    @Param('secretId', ValidationPipe) secretId: string,
  ) {
    return await this.secureStoreService.fetchSecretsDetails(secretId);
  }

  @Patch('secretId/:secretId')
  async updateVaultSecretByPath(
    @Param('secretId') secretId: string,
    @Body() updateVaultSecretRequestDto: UpdateVaultSecretRequestDto,
  ) {
    return await this.secureStoreService.updateVaultSecretByPath(
      secretId,
      updateVaultSecretRequestDto,
    );
  }

  @ApiParam({
    name: 'policyName',
    type: String,
    description: 'Name of the password policy',
    required: true,
  })
  @Get('namespaces/:namespaceName/password/generate/:policyName')
  async generatePassword(
    @Param('namespaceName', ValidationPipe) namespaceName: string,
    @Param('policyName', ValidationPipe) policyName: string,
  ) {
    return await this.secureStoreService.generatePassword(
      decodeURIComponent(namespaceName),
      policyName,
    );
  }

  @Post('encrypt-password')
  async encryptPassword(@Body('password') password: string) {
    const encryptedPassword =
      await this.secureStoreService.encryptPassword(password);
    return {
      encryptedPassword,
    };
  }

  @Post('decrypt-password')
  async decryptPassword(@Body() DecryptPasswordRequest: DecryptPasswordRequestDto) {
    const decryptedPassword =
      await this.secureStoreService.decryptPassword(DecryptPasswordRequest.encryptedPassword);
    return {
      decryptedPassword,
    };
  }
  @Get('namespaces')
  async getAuthorizedNamespaces(@Query() query: FetchNamespacesQuery) {
    const response = await this.secureStoreService.fetchAuthorizedNamespaces(
      query.envId,
    );
    return response;
  }

  @Get('secrets/unique-password')
  async generateUniquePassword(@Query() query: SecretsIdsQueryDto) {
    const { ids } = query;
    return await this.secureStoreService.generateUniquePassword(ids);
  }
  @Delete('secrets')
  async deleteSecretsByIds(@Query() query: SecretsIdsQueryDto) {
    const { ids } = query;
    return await this.secureStoreService.deleteSecretsByIds(ids);
  }

  @Get('namespace/resources')
  async fetchNamespaceResources() {
    return await this.secureStoreService.fetchNamespaceResources();
  }

  @Get('history/secretId/:secretId')
  @ApiQuery({
    name: 'paginate',
    required: false,
    type: Boolean,
    description: 'Optional flag to enable/disable pagination',
    example: false,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
  })
  async fetchSecretsHistory(
    @Param('secretId') secretId: string,
    @Query('page') page?: number,
    @Query('paginate', ParseBoolPipe) paginate: boolean = false,
    @Query('limit') limit?: number,
    @Query('sortBy') sortBy?: string,
  ) {
    return await this.secureStoreService.fetchSecretsHistory(secretId, {
      page,
      paginate,
      limit,
      sortBy,
    });
  }
  @Post('policies/password-policy')
  async createPasswordPolicy(
    @Body() passwordPolicies: createSecretPoliciesDto,
  ) {
    return await this.secureStoreService.createPasswordPolicy(passwordPolicies);
  }

  @Get('policies/password-policy')
  async fetchPasswordPolicy(
   @Query('namespaceName') namespaceName: string,
  ) {
    return await this.secureStoreService.fetchPasswordPolicies(namespaceName);
  }
  
}
