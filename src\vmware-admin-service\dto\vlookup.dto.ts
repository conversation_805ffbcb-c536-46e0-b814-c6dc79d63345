import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ber,
  IsOptional,
  <PERSON><PERSON>teNested,
  IsNotEmpty,
} from 'class-validator';
import { Type } from 'class-transformer';
export class VlookupDTO {
  @IsString()
  @IsOptional()
  domain: string;

  @IsString()
  @IsOptional()
  cloudName: string;

  @IsString()
  @IsOptional()
  datacenter: string;

  @IsString()
  @IsNotEmpty()
  hostname: string;

  @IsString()
  @IsOptional()
  password: string;

  @IsNumber()
  @IsOptional()
  port: number;

  @IsString()
  @IsOptional()
  protocol: string;

  @IsString()
  @IsNotEmpty()
  username: string;

  @IsString()
  @IsNotEmpty()
  vcenterName: string;
}

class HostDto {
  @IsString()
  hostMor: string;

  @IsString()
  name: string;

  @IsString()
  connectionState: string;

  @IsOptional()
  @IsString()
  powerState?: string | null;
}

class ClusterDto {
  @IsString()
  clusterMor: string;

  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  haEnabled?: string | boolean;

  @IsOptional()
  @IsString()
  drsEnabled?: string | boolean | null;

  @IsArray()
  @IsString({ each: true })
  hosts: string[];
}

class DatastoreDto {
  @IsString()
  datastoreMor: string;

  @IsString()
  name: string;

  @IsString()
  type: string;

  @IsNumber()
  freeSpace: number;

  @IsNumber()
  capacity: number;
}

class VcenterNetworkDto {
  @IsString()
  networkMor: string;

  @IsString()
  type: string;

  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  ipv4Subnet?: string | null;

  @IsOptional()
  @IsString()
  ipv6Subnet?: string | null;
}

class OsLayoutDto {
  @IsString()
  osName: string;

  @IsString()
  layoutName: string;

  @IsString()
  layoutMor: string;

  @IsString()
  shortName: string;

  @IsString()
  imageName: string;

  @IsOptional()
  @IsString()
  imagePath?: string | null;
}

class CloudDetailsDto {
  @IsString()
  cloudId: string;

  @IsString()
  cloudName: string;

  @IsOptional()
  @IsString()
  vcenterHost?: string | null;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => HostDto)
  hosts: HostDto[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ClusterDto)
  clusters: ClusterDto[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DatastoreDto)
  datastores: DatastoreDto[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => VcenterNetworkDto)
  networks: VcenterNetworkDto[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => OsLayoutDto)
  osLayouts: OsLayoutDto[];
}

export class VlookupDTOResponseDto {
  @IsString()
  vcenterHost: string;

  @IsString()
  vcenterName: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CloudDetailsDto)
  cloudDetails: CloudDetailsDto[];
}
