import { Injectable } from '@nestjs/common';
import { SecretsMetadataRepository } from './secretsMetadata.repository';
import { CreateSecretMetaDataDto } from './dto/secretsMetadata.dto';
import { getUserContext } from '../../utils/helpers';
import { RequestContext } from 'nestjs-request-context';

@Injectable()
export class SecretsMetadataService {
  constructor(
    private readonly secretsMetadataRepo: SecretsMetadataRepository,
  ) {}

  async create(createSecretMetaDataDto: CreateSecretMetaDataDto) {
    createSecretMetaDataDto['createdBy'] =
      getUserContext(RequestContext)?.userId;
    return await this.secretsMetadataRepo.create(createSecretMetaDataDto);
  }

  async getSecretsMetadataListForRenewal(divisor: number = 2) {
    return await this.secretsMetadataRepo.getSecretsMetadataListForRenewal(
      divisor,
    );
  }

  async reactivateSecrets(reactivateSecretsDuration) {
    const date = new Date();
    date.setHours(date.getHours() - reactivateSecretsDuration);
    return await this.secretsMetadataRepo.reactivateSecretsMetaData(date);
  }

  async getAllAutoRenewVaultTokens() {
    return await this.secretsMetadataRepo.getAllAutoRenewVaultTokens();
  }

  async updateRenewedTokenMetaData(
    secretId: string,
    renewalDate: Date,
    expiryDate: Date,
  ) {
    return await this.secretsMetadataRepo.updateRenewedTokenMetaData(
      secretId,
      renewalDate,
      expiryDate,
    );
  }
  async fetchSecretsMetadataExpiringIn2mins(gteDate: Date, lteDate: Date) {
    return await this.secretsMetadataRepo.fetchSecretsMetadataExpiringIn2mins(
      gteDate,
      lteDate,
    );
  }
  async getSecretsMetaDataBySecretId(secretId: string | string[]) {
    return await this.secretsMetadataRepo.getSecretsMetaDataBySecretId(
      secretId,
    );
  }

  async bulkUpdateSecretsMetaDataBySecretId(updateMetaData) {
    return await this.secretsMetadataRepo.bulkUpdateSecretsMetaDataBySecretId(
      updateMetaData,
    );
  }

  async changeActiveStatusByIds(secretIds: string[], active: boolean = false) {
    await this.secretsMetadataRepo.changeActiveStatusByIds(secretIds, active);
  }
}
