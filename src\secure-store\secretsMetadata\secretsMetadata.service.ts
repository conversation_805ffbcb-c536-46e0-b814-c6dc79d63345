import { Injectable } from '@nestjs/common';
import { SecretsMetadataRepository } from './secretsMetadata.repository';
import { CreateSecretMetaDataDto } from './dto/secretsMetadata.dto';
import { getUserContext } from '../../utils/helpers';
import { RequestContext } from 'nestjs-request-context';
import { LoggerService } from '../../loggers/logger.service';

@Injectable()
export class SecretsMetadataService {
  constructor(
    private readonly secretsMetadataRepo: SecretsMetadataRepository,
    private readonly logger: LoggerService,
  ) {}

  async create(createSecretMetaDataDto: CreateSecretMetaDataDto) {
    createSecretMetaDataDto['createdBy'] =
      getUserContext(RequestContext)?.userId;
    return await this.secretsMetadataRepo.create(createSecretMetaDataDto);
  }

  async getSecretsMetadataListForRenewal(divisor: number = 2) {
    return await this.secretsMetadataRepo.getSecretsMetadataListForRenewal(
      divisor,
    );
  }

  async reactivateSecrets(reactivateSecretsDuration) {
    const date = new Date();
    date.setHours(date.getHours() - reactivateSecretsDuration);
    return await this.secretsMetadataRepo.reactivateSecretsMetaData(date);
  }

  async getAllAutoRenewVaultTokens() {
    return await this.secretsMetadataRepo.getAllAutoRenewVaultTokens();
  }

  async updateRenewedTokenMetaData(
    secretId: string,
    renewalDate: Date,
    expiryDate: Date,
  ) {
    return await this.secretsMetadataRepo.updateRenewedTokenMetaData(
      secretId,
      renewalDate,
      expiryDate,
    );
  }
  async fetchSecretsForRotationDue(rotationInterval: number) {
    return await this.secretsMetadataRepo.fetchSecretsForRotationDue(
      rotationInterval,
    );
  }
  async getSecretsMetaDataBySecretId(secretId: string | string[]) {
    return await this.secretsMetadataRepo.getSecretsMetaDataBySecretId(
      secretId,
    );
  }

  async bulkUpdateSecretsMetaDataBySecretId(updateMetaData) {
    return await this.secretsMetadataRepo.bulkUpdateSecretsMetaDataBySecretId(
      updateMetaData,
    );
  }

  async changeActiveStatusByIds(secretIds: string[], active: boolean = false) {
    this.logger.log(
      'Updating active status for secretsIds',
      secretIds.join(', '),
    );
    await this.secretsMetadataRepo.changeActiveStatusByIds(
      secretIds.map((secretId) => ({ secretId, active })),
    );
  }
}
