import { SecretDeviceAssociationEntity } from './secretDeviceAssociation.entity';
import { SecretType } from '../../secretsMetadata/types/secretsMetadata.enum';

describe('SecretDeviceAssociationEntity', () => {
  const mockData = {
    secretAssociationId: 'assoc-123',
    deviceId: [1, 2],
    secretId: ['secret-001', 'secret-002'],
    type: SecretType.ROTATABLE_SECRET,
    sourceSystem: 'keyguard',
    active: true,
    createdBy: 'user1',
    updatedBy: 'user2',
    updatedAt: new Date('2023-01-01T00:00:00Z'),
    createdAt: new Date('2022-01-01T00:00:00Z'),
  };

  it('should create an instance with correct properties', () => {
    const entity = new SecretDeviceAssociationEntity();
    Object.assign(entity, mockData);

    expect(entity.secretAssociationId).toBe(mockData.secretAssociationId);
    expect(entity.deviceId).toEqual(mockData.deviceId);
    expect(entity.secretId).toEqual(mockData.secretId);
    expect(entity.type).toBe(mockData.type);
    expect(entity.active).toBe(true);
    expect(entity.createdBy).toBe('user1');
    expect(entity.updatedBy).toBe('user2');
    expect(entity.updatedAt).toEqual(mockData.updatedAt);
    expect(entity.createdAt).toEqual(mockData.createdAt);
  });

  it('should handle empty deviceId and secretId arrays', () => {
    const entity = new SecretDeviceAssociationEntity();
    entity.deviceId = [];
    entity.secretId = [];

    expect(entity.deviceId).toEqual([]);
    expect(entity.secretId).toEqual([]);
  });

  it('should have correct enum type', () => {
    const entity = new SecretDeviceAssociationEntity();
    entity.type = SecretType.DEVICE_VAULT_SECRET_MAPPING;

    expect(Object.values(SecretType)).toContain(entity.type);
  });
});
