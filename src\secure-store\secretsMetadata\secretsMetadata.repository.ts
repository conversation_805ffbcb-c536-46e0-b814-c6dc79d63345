import { Injectable } from '@nestjs/common';
import {
  SecretsMetaData,
  SecretsMetaDataSchema,
} from './schemas/secretsMetadata.schema';
import { SecretsMetadataEntity } from './entities/secretsMetadata.entity';
import { deepSanitize, transformDocumentToEntity } from '../../utils/helpers';
import {
  Mongoose,
  connect,
  Model,
  ClientSession,
  AnyKeys,
  AnyObject,
} from 'mongoose';
import { CreateSecretMetaDataDto } from './dto/secretsMetadata.dto';
import { SecretType } from './types/secretsMetadata.enum';
import { CounterTypes, GenericRequestTypes } from '../../types';
import { UpdateVaultSecretRequestDto } from '../dto/update-vault-secrets.request.dto';
import { Counters, CountersSchema } from '../../naas/schemas/counter.schema';
import { paginateAggregateResults } from '../../utils/pagination/pagination';
import {
  PaginatedResponseDto,
  PaginationQueryDto,
} from '../../utils/pagination/dto/pagination.dto';
import { FetchSecretType } from '../dto/fetch-secrets.response.dto';
import { ActionStatus } from '../../action/enums/ActionStatus.enum';
import { ConfigService } from '@nestjs/config';
import { ENVIRONMENT_VARS } from '../../utils/constants';

const transformSecretsMetadataDocumentToEntity = transformDocumentToEntity<
  SecretsMetaData,
  SecretsMetadataEntity
>;

@Injectable()
export class SecretsMetadataRepository {
  constructor(private configService: ConfigService) {}
  private async connectToDB(): Promise<Mongoose> {
    return await connect(process.env.MONGODB_URI);
  }

  private async getSecretsMetadataModel(): Promise<
    Model<SecretsMetaData & { createdAt: Date; updatedAt: Date; __v: number }>
  > {
    const conn = await this.connectToDB();
    return conn.model<
      SecretsMetaData & { createdAt: Date; updatedAt: Date; __v: number }
    >(SecretsMetaData.name, SecretsMetaDataSchema);
  }

  private async getPaginatedModel() {
    const conn = await this.connectToDB();
    interface ISecretMetadataModel extends Model<SecretsMetaData> {
      aggregatePaginate: (agg: any, options: any) => Promise<any>;
    }
    return conn.model<SecretsMetaData, ISecretMetadataModel>(
      SecretsMetaData.name,
      SecretsMetaDataSchema,
    );
  }

  async create(
    createSecretMetaDataDto: CreateSecretMetaDataDto,
  ): Promise<SecretsMetadataEntity> {
    const model = await this.getSecretsMetadataModel();
    const secretMetadata = await model.create(
      deepSanitize(createSecretMetaDataDto),
    );
    return transformSecretsMetadataDocumentToEntity(secretMetadata);
  }

  async getSecretsMetaData(params: {
    namespace: string;
    path: string;
    type?: string;
  }): Promise<SecretsMetaData[]> {
    const { namespace, path, type } = params;
    const model = await this.getSecretsMetadataModel();
    const regexExp = decodeURIComponent(path).endsWith('/')
      ? { $regex: `^${decodeURIComponent(path)}` }
      : decodeURIComponent(path);
    const findQuery: {
      vaultNamespace: string;
      vaultPath: string | { $regex: string };
      isDeleted?: { $ne: boolean };
      type?: string;
    } = {
      vaultNamespace: namespace,
      vaultPath: regexExp,
      isDeleted: { $ne: true },};
      
    if (type) {
      findQuery.type = type;
    }
    const secretMetadata = await model.find(findQuery);
    return secretMetadata;
  }

  async getSecretsMetaDataPaginated(
    params: {
      namespace: string;
      path: string;
    },
    paginationOptions: PaginationQueryDto,
  ): Promise<PaginatedResponseDto<SecretsMetaData>> {
    const filter = paginationOptions.filter
      ? JSON.parse(paginationOptions.filter)
      : '';

    const { namespace, path } = params;
    const regexExp = decodeURIComponent(path).endsWith('/')
      ? { $regex: `^${decodeURIComponent(path)}` }
      : decodeURIComponent(path);
    const type = paginationOptions.filter
      ? JSON.parse(paginationOptions.filter)['type']
      : null;
    const model = await this.getPaginatedModel();
    const aggregateQuery = [
      {
        $match: {
          isDeleted: { $ne: true },
          vaultNamespace: namespace,
          vaultPath: regexExp,
          ...(type && {
            type:
              type === FetchSecretType.ROTATABLE_SECRET
                ? SecretType.ROTATABLE_SECRET
                : type === FetchSecretType.NORMAL_SECRET
                  ? SecretType.NORMAL_SECRET
                  : type,
          }),
        },
      },
      { $sort: { createdAt: -1 } },
    ];
    delete filter.type;
    paginationOptions.filter = JSON.stringify(filter);
    const secretMetadata = await paginateAggregateResults<SecretsMetaData>(
      aggregateQuery,
      model,
      paginationOptions,
    );
    return { items: secretMetadata.items, pageInfo: secretMetadata.pageInfo };
  }

  async reactivateSecretsMetaData(reactivateDurationDate: Date) {
    const model = await this.getSecretsMetadataModel();
    const deleteTemporarySecretsFromVault = await model.find({
      type: SecretType.ROTATABLE_SECRET,
      active: false,
      deactivatedAt: { $lte: reactivateDurationDate },
    });
    const expiryDate = new Date(
      Date.now() +
        this.configService.get(
          ENVIRONMENT_VARS.DELAY_FAILED_SECRET_ROTATION_HOURS,
        ) *
          60 *
          60 *
          1000,
    );
    await model.updateMany(
      {
        type: SecretType.ROTATABLE_SECRET,
        active: false,
        deactivatedAt: { $lte: reactivateDurationDate },
      },
      {
        active: true,
        lastDeviceSyncStatus: ActionStatus.STATUS_UNKNOWN,
        expiryDate: expiryDate,
        nextRotationDate: expiryDate,
      },
    );
    return deleteTemporarySecretsFromVault;
  }

  private async getCountersModel() {
    const conn = await this.connectToDB();
    return conn.model<Counters>(Counters.name, CountersSchema);
  }

  async getCount(session: ClientSession, type: CounterTypes.SECRETS_METADATA) {
    const model = await this.getCountersModel();
    let count = 0;
    const updateResult = await model.findOneAndUpdate(
      { type: type },
      { $inc: { counter: 1 } },
      { session, new: true },
    );
    if (!updateResult) {
      await model.create({ type: type, counter: 1000 }, { session });
      count = 1;
    } else {
      count = updateResult.counter;
    }

    return count;
  }

  async generateSecretId(type: SecretType): Promise<string> {
    const model = await this.getSecretsMetadataModel();
    const session = await model.startSession();
    const count = await this.getCount(session, CounterTypes.SECRETS_METADATA);
    let secretType;
    switch (type) {
      case SecretType.ROTATABLE_SECRET:
        secretType = GenericRequestTypes['VAULT-ROTATABLE-SECRET'];
        break;
      case SecretType.NORMAL_SECRET:
        secretType = GenericRequestTypes['VAULT-NORMAL-SECRET'];
        break;
      case SecretType.VAULT_TOKEN:
        secretType = GenericRequestTypes['VAULT-TOKEN-SECRET'];
        break;
    }
    const genericId = `${secretType}-${count}`;
    return genericId;
  }

  async getSecretsMetaDataBySecretId(secretId: string | string[]) {
    const model = await this.getSecretsMetadataModel();
    let secretMetadata;
    if (Array.isArray(secretId)) {
      secretMetadata = await model.find({
        secretId: { $in: secretId },
        active: true,
      });
    } else {
      secretMetadata = await model.findOne({
        secretId,
      });
    }
    return secretMetadata;
  }

  async getAllSecretsMetaDataBySecretIds(secretIds: string[]) {
    const model = await this.getSecretsMetadataModel();

    return model.aggregate([
      {
        $match: {
          secretId: { $in: secretIds },
        },
      },
      {
        $lookup: {
          from: model.collection.name,
          localField: 'deviceUserNameSecretId',
          foreignField: 'secretId',
          as: 'linkedSecret',
        },
      },
      {
        $addFields: {
          linkedSecret: { $arrayElemAt: ['$linkedSecret', 0] },
          userNamePasswordKey: '$linkedSecret.devicePasswordKey',
        },
      },
      {
        $lookup: {
          from: 'secretdeviceassociations',
          localField: 'secretId',
          foreignField: 'secretId',
          as: 'deviceAssociations',
        },
      },
      {
        $addFields: {
          deviceId: {
            $arrayElemAt: ['$deviceAssociations.deviceId', 0],
          },
          sourceSystem: '$deviceAssociations.sourceSystem',
        },
      },
      {
        $project: {
          deviceAssociations: 0,
        },
      },
    ]);
  }

  async updateSecretsMetadataById(
    secretId: string,
    data: AnyKeys<
      SecretsMetaData & { createdAt: Date; updatedAt: Date; __v: number }
    > &
      AnyObject,
  ): Promise<void> {
    const model = await this.getSecretsMetadataModel();
    await model.updateOne({ secretId }, { $set: { ...data } });
  }

  async changeActiveStatusByIds(
    documents: {
      secretId: string;
      active?: boolean;
      rotationStatus?: ActionStatus;
      actionId?: string;
      updatedBy?: string;
    }[],
  ): Promise<void> {
    const model = await this.getSecretsMetadataModel();

    const updateQueries = documents.map((doc) => {
      const update: Partial<SecretsMetadataEntity> = {};
      update.updatedBy = doc.updatedBy;
      update.active = doc.active || false;
      if (doc.actionId) {
        update.actionId = doc.actionId;
      }
      if (!doc.active) {
        update.deactivatedAt = new Date();
      }
      if (doc.rotationStatus) {
        update.lastDeviceSyncStatus = doc.rotationStatus;
      }
      if (ActionStatus.STARTED === doc.rotationStatus) {
        update.error = null;
      }

      return {
        updateOne: {
          filter: { secretId: doc.secretId },
          update: update,
        },
      };
    });

    await model.bulkWrite(updateQueries);
  }

  async bulkUpdateSecretsMetaDataBySecretId(updateMetaData): Promise<void> {
    const model = await this.getSecretsMetadataModel();
    const secretMetadata = await model.bulkWrite(updateMetaData);
  }

  async createQueryForUpdateMetaData(payload: {
    secretMetaData: Record<string, any>;
    secretResponse: Record<string, any>;
    updateVaultSecretRequest: UpdateVaultSecretRequestDto;
    secretId: string;
    actionId: string;
    currentVersion: number;
    userId: string;
  }) {
    const {
      secretMetaData,
      secretResponse,
      updateVaultSecretRequest,
      secretId,
      actionId,
      currentVersion,
      userId,
    } = payload;
    let secretData = {};
    const updateMetaData = [];
    if (secretMetaData.type === SecretType.ROTATABLE_SECRET) {
      const deviceUserNameMetaData = await this.getSecretsMetaDataBySecretId(
        secretMetaData.deviceUserNameSecretId,
      );

      let roatableUpdateFields:
        | {
            version?: number;
            devicePasswordKey?: string;
          }
        | undefined;

      if (
        secretResponse[secretMetaData.devicePasswordKey] !==
          updateVaultSecretRequest.vaultPassword ||
        secretMetaData.devicePasswordKey !== updateVaultSecretRequest.vaultKey
      ) {
        const keyUpdated =
          secretMetaData.devicePasswordKey !==
          updateVaultSecretRequest.vaultKey;
        roatableUpdateFields = {
          version: currentVersion + 1,
        };
        if (keyUpdated) {
          roatableUpdateFields.devicePasswordKey =
            updateVaultSecretRequest.vaultKey;
        }
        delete secretResponse[secretMetaData.devicePasswordKey];
        secretData = {
          ...secretResponse,
          [updateVaultSecretRequest.vaultKey]:
            updateVaultSecretRequest.vaultPassword,
        };
      }
      updateMetaData.push({
        updateOne: {
          filter: { secretId: secretId },
          update: {
            $set: {
              actionId,
              devicePasswordKey: updateVaultSecretRequest.vaultKey,
              ...(updateVaultSecretRequest.policyId !== undefined && {
                policyId: updateVaultSecretRequest.policyId,
              }),
              ...(updateVaultSecretRequest.secretTTLInHours !== undefined && {
                secretTTLInHours: updateVaultSecretRequest.secretTTLInHours,
              }),
              ...(updateVaultSecretRequest.nextRotationDate !== undefined && {
                nextRotationDate: updateVaultSecretRequest.nextRotationDate,
                expiryDate: updateVaultSecretRequest.nextRotationDate,
              }),
              ...(updateVaultSecretRequest.notifyBeforeTokenExpiry !==
                undefined && {
                notifyBeforeTokenExpiry:
                  updateVaultSecretRequest.notifyBeforeTokenExpiry,
              }),
              ...roatableUpdateFields,
              updatedBy: userId,
            },
          },
        },
      });

      if (
        secretResponse[deviceUserNameMetaData.devicePasswordKey] !==
          updateVaultSecretRequest.userNamePassword ||
        deviceUserNameMetaData.devicePasswordKey !==
          updateVaultSecretRequest.userNameKey
      ) {
        const keyUpdated =
          deviceUserNameMetaData.devicePasswordKey !==
          updateVaultSecretRequest.userNameKey;
        const updateFields: {
          actionId: string;
          version: number;
          devicePasswordKey?: string;
          updatedBy: string;
        } = {
          actionId,
          version: currentVersion + 1,
          updatedBy: userId,
        };
        if (keyUpdated) {
          updateFields.devicePasswordKey = updateVaultSecretRequest.userNameKey;
        }
        delete secretResponse[deviceUserNameMetaData.devicePasswordKey];
        delete secretData[deviceUserNameMetaData.devicePasswordKey];
        secretData = {
          ...secretResponse,
          ...secretData,
          [updateVaultSecretRequest.userNameKey]:
            updateVaultSecretRequest.userNamePassword,
        };

        updateMetaData.push({
          updateOne: {
            filter: { secretId: secretMetaData.deviceUserNameSecretId },
            update: {
              $set: updateFields,
            },
          },
        });
      }
    } else {
      if (
        secretResponse[secretMetaData.devicePasswordKey] !==
          updateVaultSecretRequest.vaultPassword ||
        secretMetaData.devicePasswordKey !== updateVaultSecretRequest.vaultKey
      ) {
        const keyUpdated =
          secretMetaData.devicePasswordKey !==
          updateVaultSecretRequest.vaultKey;
        const updateFields: {
          actionId: string;
          version: number;
          devicePasswordKey?: string;
          policyId?: string;
          updatedBy: string;
        } = {
          actionId,
          version: currentVersion + 1,
          updatedBy: userId,
        };
        if (keyUpdated) {
          updateFields.devicePasswordKey = updateVaultSecretRequest.vaultKey;
        }
        if (updateVaultSecretRequest.policyId !== undefined) {
          updateFields.policyId = updateVaultSecretRequest.policyId;
        }
        delete secretResponse[secretMetaData.devicePasswordKey];
        secretData = {
          ...secretResponse,
          [updateVaultSecretRequest.vaultKey]:
            updateVaultSecretRequest.vaultPassword,
        };

        updateMetaData.push({
          updateOne: {
            filter: { secretId: secretId },
            update: {
              $set: updateFields,
            },
          },
        });
      } else if (updateVaultSecretRequest.policyId !== undefined) {
        updateMetaData.push({
          updateOne: {
            filter: { secretId: secretId },
            update: {
              $set: {
                policyId: updateVaultSecretRequest.policyId,
                updatedBy: userId,
              },
            },
          },
        });
      }
    }
    return { updateMetaData, secretData };
  }

  async fetchSecretsForRotationDue(
    rotationInterval: number,
  ): Promise<SecretsMetadataEntity[]> {
    const model = await this.getSecretsMetadataModel();
    const now = new Date();
    const timeToCompare = new Date(
      now.getTime() + rotationInterval * 60 * 1000 - 1,
    );
    const secretsMetadataList = await model.find({
      expiryDate: {
        $lte: timeToCompare,
      },
      type: SecretType.ROTATABLE_SECRET,
      active: true,
    });
    return secretsMetadataList.map((secretsMetadata) =>
      transformSecretsMetadataDocumentToEntity(secretsMetadata),
    );
  }

  async getSecretsMetadataListForRenewal(divisor: number = 2) {
    const model = await this.getSecretsMetadataModel();
    const secretsMetaDataList: SecretsMetaData[] = await model.aggregate([
      {
        $match: {
          active: true,
        },
      },
      {
        $addFields: {
          baseDate: {
            $cond: {
              if: { $gt: ['$updatedAt', null] },
              then: '$updatedAt',
              else: '$createdAt',
            },
          },
        },
      },
      {
        $addFields: {
          currentDate: '$$NOW',
          thresholdDate: {
            $toDate: {
              $divide: [
                {
                  $add: [{ $toLong: '$expiryDate' }, { $toLong: '$baseDate' }],
                },
                divisor,
              ],
            },
          },
        },
      },
      {
        $match: {
          $expr: {
            $and: [
              { $ne: ['$thresholdDate', null] },
              {
                $gte: ['$currentDate', '$thresholdDate'],
              },
            ],
          },
        },
      },
    ]);

    return secretsMetaDataList;
  }

  async getAllAutoRenewVaultTokens() {
    const model = await this.getSecretsMetadataModel();
    const vaultTokenList = model.find({
      type: SecretType.VAULT_TOKEN,
      tokenRenewByNebula: true,
    });
    return vaultTokenList;
  }

  async updateRenewedTokenMetaData(
    secretId: string,
    renewalDate: Date,
    expiryDate: Date,
  ) {
    const model = await this.getSecretsMetadataModel();
    return await model.updateOne(
      { secretId: secretId },
      { $set: { expiryDate: expiryDate, renewedAt: renewalDate } },
    );
  }
}
