import { Injectable } from '@nestjs/common';
import {
  SecretsMetaData,
  SecretsMetaDataSchema,
} from './schemas/secretsMetadata.schema';
import { SecretsMetadataEntity } from './entities/secretsMetadata.entity';
import { deepSanitize, transformDocumentToEntity } from '../../utils/helpers';
import { Mongoose, connect, Model, ClientSession } from 'mongoose';
import { CreateSecretMetaDataDto } from './dto/secretsMetadata.dto';
import { SecretType } from './types/secretsMetadata.enum';
import { CounterTypes, GenericRequestTypes } from '../../types';
import { UpdateVaultSecretRequestDto } from '../dto/update-vault-secrets.request.dto';
import { Counters, CountersSchema } from '../../naas/schemas/counter.schema';
import { paginateAggregateResults } from '../../utils/pagination/pagination';
import {
  PaginatedResponseDto,
  PaginationQueryDto,
} from '../../utils/pagination/dto/pagination.dto';
import { FetchSecretType } from '../dto/fetch-secrets.response.dto';

const transformSecretsMetadataDocumentToEntity = transformDocumentToEntity<
  SecretsMetaData,
  SecretsMetadataEntity
>;

@Injectable()
export class SecretsMetadataRepository {
  private async connectToDB(): Promise<Mongoose> {
    return await connect(process.env.MONGODB_URI);
  }

  private async getSecretsMetadataModel(): Promise<
    Model<SecretsMetaData & { createdAt: Date; updatedAt: Date; __v: number }>
  > {
    const conn = await this.connectToDB();
    return conn.model<
      SecretsMetaData & { createdAt: Date; updatedAt: Date; __v: number }
    >(SecretsMetaData.name, SecretsMetaDataSchema);
  }

  private async getPaginatedModel() {
    const conn = await this.connectToDB();
    interface ISecretMetadataModel extends Model<SecretsMetaData> {
      aggregatePaginate: (agg: any, options: any) => Promise<any>;
    }
    return conn.model<SecretsMetaData, ISecretMetadataModel>(
      SecretsMetaData.name,
      SecretsMetaDataSchema,
    );
  }

  async create(
    createSecretMetaDataDto: CreateSecretMetaDataDto,
  ): Promise<SecretsMetadataEntity> {
    const model = await this.getSecretsMetadataModel();
    const secretMetadata = await model.create(
      deepSanitize(createSecretMetaDataDto),
    );
    return transformSecretsMetadataDocumentToEntity(secretMetadata);
  }

  async getSecretsMetaData(params: {
    namespace: string;
    path: string;
  }): Promise<SecretsMetaData[]> {
    const { namespace, path } = params;
    const model = await this.getSecretsMetadataModel();
    const regexExp = decodeURIComponent(path).endsWith('/')
      ? { $regex: `^${decodeURIComponent(path)}` }
      : decodeURIComponent(path);
    const secretMetadata = await model.find({
      vaultNamespace: namespace,
      vaultPath: regexExp,
      isDeleted: { $ne: true },
    });
    return secretMetadata;
  }

  async getSecretsMetaDataPaginated(
    params: {
      namespace: string;
      path: string;
    },
    paginationOptions: PaginationQueryDto,
  ): Promise<PaginatedResponseDto<SecretsMetaData>> {
    const { filter, ...pageOptions } = paginationOptions;
    const { namespace, path } = params;
    const regexExp = decodeURIComponent(path).endsWith('/')
      ? { $regex: `^${decodeURIComponent(path)}` }
      : decodeURIComponent(path);
    const type = paginationOptions.filter
      ? JSON.parse(paginationOptions.filter)['type']
      : null;
    const model = await this.getPaginatedModel();
    const aggregateQuery = [
      {
        $match: {
          isDeleted: { $ne: true },
          vaultNamespace: namespace,
          vaultPath: regexExp,
          ...(type && {
            type:
              type === FetchSecretType.ROTATABLE_SECRET
                ? SecretType.ROTATABLE_SECRET
                : SecretType.NORMAL_SECRET,
          }),
        },
      },
      { $sort: { createdAt: -1 } },
    ];

    const secretMetadata = await paginateAggregateResults<SecretsMetaData>(
      aggregateQuery,
      model,
      pageOptions,
    );
    return { items: secretMetadata.items, pageInfo: secretMetadata.pageInfo };
  }

  async reactivateSecretsMetaData(reactivateDurationDate: Date) {
    const model = await this.getSecretsMetadataModel();
    const deleteTemporarySecretsFromVault = model.find({
      type: SecretType.ROTATABLE_SECRET,
      active: false,
      deactivatedAt: { $lte: reactivateDurationDate },
    });
    await model.updateMany(
      {
        type: SecretType.ROTATABLE_SECRET,
        active: false,
        deactivatedAt: { $lte: reactivateDurationDate },
      },
      { active: true },
    );
    return deleteTemporarySecretsFromVault;
  }

  private async getCountersModel() {
    const conn = await this.connectToDB();
    return conn.model<Counters>(Counters.name, CountersSchema);
  }

  async getCount(session: ClientSession, type: CounterTypes.SECRETS_METADATA) {
    const model = await this.getCountersModel();
    let count = 0;
    const updateResult = await model.findOneAndUpdate(
      { type: type },
      { $inc: { counter: 1 } },
      { session, new: true },
    );
    if (!updateResult) {
      await model.create({ type: type, counter: 1000 }, { session });
      count = 1;
    } else {
      count = updateResult.counter;
    }

    return count;
  }

  async generateSecretId(type: SecretType): Promise<string> {
    const model = await this.getSecretsMetadataModel();
    const session = await model.startSession();
    const count = await this.getCount(session, CounterTypes.SECRETS_METADATA);
    const secretType =
      type === SecretType.ROTATABLE_SECRET
        ? GenericRequestTypes['VAULT-ROTATABLE-SECRET']
        : GenericRequestTypes['VAULT-NORMAL-SECRET'];
    const genericId = `${secretType}-${count}`;
    return genericId;
  }

  async getSecretsMetaDataBySecretId(secretId: string | string[]) {
    const model = await this.getSecretsMetadataModel();
    let secretMetadata;
    if (Array.isArray(secretId)) {
      secretMetadata = await model.find({
        secretId: { $in: secretId },
        active: true,
      });
    } else {
      secretMetadata = await model.findOne({
        secretId,
      });
    }
    return secretMetadata;
  }

  async getAllSecretsMetaDataBySecretIds(secretIds: string[]) {
    const model = await this.getSecretsMetadataModel();

    return model.aggregate([
      {
        $match: {
          secretId: { $in: secretIds },
        },
      },
      {
        $lookup: {
          from: model.collection.name,
          localField: 'deviceUserNameSecretId',
          foreignField: 'secretId',
          as: 'linkedSecret',
        },
      },
      {
        $addFields: {
          linkedSecret: { $arrayElemAt: ['$linkedSecret', 0] },
          userNamePasswordKey: '$linkedSecret.devicePasswordKey',
        },
      },
      {
        $lookup: {
          from: 'secretdeviceassociations',
          localField: 'secretId',
          foreignField: 'secretId',
          as: 'deviceAssociations',
        },
      },
      {
        $addFields: {
          deviceId: {
            $arrayElemAt: ['$deviceAssociations.deviceId', 0],
          },
          sourceSystem: '$deviceAssociations.sourceSystem',
        },
      },
      {
        $project: {
          deviceAssociations: 0,
        },
      },
    ]);
  }

  async changeActiveStatusByIds(
    secretIds: string[],
    active: boolean = false,
  ): Promise<void> {
    const model = await this.getSecretsMetadataModel();

    await model.updateMany(
      { secretId: { $in: secretIds } },
      { $set: { active } },
    );
  }

  async bulkUpdateSecretsMetaDataBySecretId(updateMetaData): Promise<void> {
    const model = await this.getSecretsMetadataModel();
    const secretMetadata = await model.bulkWrite(updateMetaData);
  }

  async createQueryForUpdateMetaData(payload: {
    secretMetaData: Record<string, any>;
    secretResponse: Record<string, any>;
    updateVaultSecretRequest: UpdateVaultSecretRequestDto;
    secretId: string;
  }) {
    const {
      secretMetaData,
      secretResponse,
      updateVaultSecretRequest,
      secretId,
    } = payload;
    let secretData = {};
    const updateMetaData = [];
    if (secretMetaData.type === SecretType.ROTATABLE_SECRET) {
      const deviceUserNameMetaData = await this.getSecretsMetaDataBySecretId(
        secretMetaData.deviceUserNameSecretId,
      );

      delete secretResponse[secretMetaData.devicePasswordKey];
      delete secretResponse[deviceUserNameMetaData.devicePasswordKey];
      secretData = {
        ...secretResponse,
        [updateVaultSecretRequest.vaultKey]:
          updateVaultSecretRequest.vaultPassword,
        [updateVaultSecretRequest.userNameKey]:
          updateVaultSecretRequest.userNamePassword,
      };
      updateMetaData.push(
        {
          updateOne: {
            filter: { secretId: secretId },
            update: {
              $set: {
                devicePasswordKey: updateVaultSecretRequest.vaultKey,
                ...(updateVaultSecretRequest.policyId !== undefined && {
                  policyId: updateVaultSecretRequest.policyId,
                }),
                ...(updateVaultSecretRequest.secretTTLInHours !== undefined && {
                  secretTTLInHours: updateVaultSecretRequest.secretTTLInHours,
                }),
                ...(updateVaultSecretRequest.nextRotationDate !== undefined && {
                  nextRotationDate: updateVaultSecretRequest.nextRotationDate,
                }),
                ...(updateVaultSecretRequest.notifyBeforeTokenExpiry !==
                  undefined && {
                  notifyBeforeTokenExpiry:
                    updateVaultSecretRequest.notifyBeforeTokenExpiry,
                }),
              },
            },
          },
        },
        {
          updateOne: {
            filter: { secretId: secretMetaData.deviceUserNameSecretId },
            update: {
              $set: { devicePasswordKey: updateVaultSecretRequest.userNameKey },
            },
          },
        },
      );
    } else {
      delete secretResponse[secretMetaData.devicePasswordKey];
      secretData = {
        ...secretResponse,
        [updateVaultSecretRequest.vaultKey]:
          updateVaultSecretRequest.vaultPassword,
      };
      secretMetaData.devicePasswordKey;
      updateMetaData.push({
        updateOne: {
          filter: { secretId: secretId },
          update: {
            $set: { devicePasswordKey: updateVaultSecretRequest.vaultKey },
          },
        },
      });
    }
    return { updateMetaData, secretData };
  }

  async fetchSecretsMetadataExpiringIn2mins(
    gteDate: Date,
    lteDate: Date,
  ): Promise<SecretsMetadataEntity[]> {
    const model = await this.getSecretsMetadataModel();
    const secretsMetadataList = await model.find({
      expiryDate: {
        $lte: lteDate,
        $gte: gteDate,
      },
    });
    return secretsMetadataList.map((secretsMetadata) =>
      transformSecretsMetadataDocumentToEntity(secretsMetadata),
    );
  }

  async getSecretsMetadataListForRenewal(divisor: number = 2) {
    const model = await this.getSecretsMetadataModel();
    const secretsMetaDataList: SecretsMetaData[] = await model.aggregate([
      {
        $match: {
          active: true,
        },
      },
      {
        $addFields: {
          baseDate: {
            $cond: {
              if: { $gt: ['$updatedAt', null] },
              then: '$updatedAt',
              else: '$createdAt',
            },
          },
        },
      },
      {
        $addFields: {
          currentDate: '$$NOW',
          thresholdDate: {
            $toDate: {
              $divide: [
                {
                  $add: [{ $toLong: '$expiryDate' }, { $toLong: '$baseDate' }],
                },
                divisor,
              ],
            },
          },
        },
      },
      {
        $match: {
          $expr: {
            $and: [
              { $ne: ['$thresholdDate', null] },
              {
                $gte: ['$currentDate', '$thresholdDate'],
              },
            ],
          },
        },
      },
    ]);

    return secretsMetaDataList;
  }

  async getAllAutoRenewVaultTokens() {
    const model = await this.getSecretsMetadataModel();
    const vaultTokenList = model.find({
      type: SecretType.VAULT_TOKEN,
      tokenRenewByNebula: true,
    });
    return vaultTokenList;
  }

  async updateRenewedTokenMetaData(
    secretId: string,
    renewalDate: Date,
    expiryDate: Date,
  ) {
    const model = await this.getSecretsMetadataModel();
    return await model.updateOne(
      { secretId: secretId },
      { $set: { expiryDate: expiryDate, renewedAt: renewalDate } },
    );
  }
}
