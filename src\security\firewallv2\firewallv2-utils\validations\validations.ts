import { HttpException, HttpStatus } from '@nestjs/common';
import { IPv4CidrRange, IPv6CidrRange } from 'ip-num/IPRange';
import {
  AllowedProtocols,
  FirewallRule,
  IpVersion,
  Protocols,
  ValidateFieldResponse,
  ValidatedFirewallRequest,
} from '../../dto/firewallv2.request.dto';
import { ipv4, ipv6 } from '../../firewallv2.service';
import { ipv4ToInt, ipv6ToInt } from '../../../../utils/helpers';
import { LoggerService } from '../../../../loggers/logger.service';
const logger = new LoggerService();

export function validateFirewallRules(
  firewallRules: FirewallRule[],
  config,
  bulkImport?: boolean,
): ValidatedFirewallRequest[] {
  logger.debug('check firewall rule', firewallRules);
  const transformedRules = transformFirewallRules(firewallRules);
  transformedRules.forEach((rule, index) => {
    const sourceFields = ['location', 'hostName', 'ipAddress', 'port'];

    const destinationFields = ['location', 'hostName', 'ipAddress', 'port'];
    const otherFields = ['protocol'];
    const error = {
      source: {},
      destination: {},
    };
    const errorCount = {
      source: 0,
      destination: 0,
      generic: 0,
    };

    //check for mandatory fields
    if (
      rule.source.ipAddress == undefined ||
      rule.destination.ipAddress == undefined ||
      rule.protocol == undefined
    ) {
      logger.debug('invalid rule - missing required fields ', rule);
      throw new HttpException(
        'Please make sure to add the required fields for all the rules',
        HttpStatus.BAD_REQUEST,
      );
    }

    //Protocol conversion
    const incomingProtocol = rule.protocol;
    if (config && config.protocolConversion) {
      config.protocolConversion.forEach((conversion) => {
        if (conversion.from?.includes(rule.protocol.trim().toUpperCase())) {
          logger.debug(
            `Incoming protocol: ${rule.protocol}. Converting to ${conversion.to}`,
          );
          rule.protocol = conversion.to;
        }
      });
    }

    //Check for ports based on the protocol selected
    if (rule.protocol.trim().toUpperCase() == AllowedProtocols.ICMP) {
      logger.debug(
        `destination port present:
        ${!(rule?.destination?.port == undefined || rule?.destination?.port == '')},
        ${rule?.destination?.port}`,
      );
      if (
        !(rule.destination.port == undefined || rule.destination.port == '')
      ) {
        logger.debug(
          'invalid rule - destination port is not needed if selected/converted protocol is ICMP',
        );
        error.destination['port'] =
          'destination port is not needed if selected/converted protocol is ICMP';
        errorCount.destination += 1;
      }
      if (!(rule.source.port == undefined || rule.source.port == '')) {
        logger.debug(
          'invalid rule - source port is not needed if selected/converted protocol is ICMP',
        );
        error.source['port'] =
          'source port is not needed if selected/converted protocol is ICMP';
        errorCount.source += 1;
      }
    } else if (
      rule.protocol.trim().toUpperCase() == AllowedProtocols.TCP ||
      rule.protocol.trim().toUpperCase() == AllowedProtocols.UDP
    ) {
      if (rule.destination.port == undefined || rule.destination.port == '') {
        logger.debug(
          `invalid rule - destination port is required when selected/converted protocol is ${rule.protocol}`,
        );
        error.destination['port'] =
          `destination port is required when selected/converted protocol is ${rule.protocol}`;
        errorCount.destination += 1;
      }
    }
    //check for multiple source and destination ip address
    const ipValidationResponse = validateSourceDestination(
      rule.source.ipAddress,
      rule.destination.ipAddress,
    );
    logger.log('ip type validation response ', ipValidationResponse);
    if (ipValidationResponse.valid != true) {
      logger.debug(
        'invalid rule - with ip mismatch ',
        ipValidationResponse.msg,
      );
      throw new HttpException(
        `Invalid Rule: ${ipValidationResponse.msg}`,
        HttpStatus.BAD_REQUEST,
      );
    }
    //Check for source and destination ip address to be of same type
    if (
      (ipv4.isValid(rule.source.ipAddress.trim()) &&
        ipv6.isValid(rule.destination.ipAddress.trim())) ||
      (ipv6.isValid(rule.source.ipAddress.trim()) &&
        ipv4.isValid(rule.destination.ipAddress.trim()))
    ) {
      logger.debug(
        'invalid rule - source and destination ip address not of same type',
      );

      error.source['ipAddress'] =
        'source and destination should have same type of ip address';
      error.destination['ipAddress'] =
        'source and destination should have same type of ip address';
      errorCount.source += 1;
      errorCount.destination += 1;
    }
    //check for source and destination ip address not to be same
    if (rule.source.ipAddress == rule.destination.ipAddress) {
      logger.debug(
        `invalid rule - source and destination ip address not to be same: 
        ${rule?.source?.ipAddress}`,
      );
      error.source['ipAddress'] = 'source and destination ip cannot be same';
      error.destination['ipAddress'] =
        'source and destination ip cannot be same';
      errorCount.source += 1;
      errorCount.destination += 1;
    }
    for (const field of sourceFields) {
      const validators = {
        location: validateLocation,
        hostName: validateHostName,
        ipAddress: validateIpAddresses,
        port: validatePort,
      };

      const response: ValidateFieldResponse = validators[field](
        rule['source'][field],
      );
      if (response.valid !== true) {
        logger.debug(`source invalid ${response.msg}`);
        error.source[field] = response.msg;
        errorCount.source += 1;
      }
      if (response.ipVersion) {
        rule['ipVersion'] = response.ipVersion;
      }
    }
    for (const field of destinationFields) {
      const validators = {
        location: validateLocation,
        hostName: validateHostName,
        ipAddress: validateIpAddresses,
        port: validatePort,
      };
      const response: ValidateFieldResponse = validators[field](
        rule['destination'][field],
      );
      if (response.valid !== true) {
        logger.debug(`Destination invalid ${response.msg}`);
        error.destination[field] = response.msg;
        errorCount.destination += 1;
      }
      if (response.ipVersion) {
        rule['ipVersion'] = response.ipVersion;
      }
    }
    for (const field of otherFields) {
      const validators = {
        protocol: validateProtocol,
      };
      const response = validators[field](rule[field], config?.allowedProtocols);
      if (response.valid !== true) {
        error[field] = response.msg;
        errorCount.generic += 1;
      }
    }
    if (errorCount.destination == 0) {
      delete error.destination;
    }
    if (errorCount.source == 0) {
      delete error.source;
    }
    if (errorCount.destination + errorCount.generic + errorCount.source > 0) {
      rule['errors'] = error;
      rule['valid'] = false;
    } else {
      rule['valid'] = true;
    }
    rule['ruleId'] = index + 1;

    /*To avoid inconsistency in UI between bulk import and new rule,
    incoming protocol is sent in response after validating with converted protocol*/
    if (bulkImport === true) {
      rule.protocol = incomingProtocol;
    }
  });
  return firewallRules as ValidatedFirewallRequest[];
}

export function validateIpAddresses(ipAddress: string): ValidateFieldResponse {
  logger.debug(`FirewallRules ${ipAddress}`);
  ipAddress = String(ipAddress);
  ipAddress = ipAddress.replace(/\r?\n|\r/g, '');
  const parts = ipAddress.split(',');
  const parts_valid = [];
  const errors = [];
  const ret_obj = { ip: IpVersion.ipv4, errors: '' };

  if (/^,|,$/.test(ipAddress)) {
    errors.push('cannot start or end with a comma');
    ret_obj['ip'] = IpVersion.ipv4;
  }
  if (/,,/.test(ipAddress)) {
    errors.push('cannot have two commas in a row');
    ret_obj['ip'] = IpVersion.ipv4;
  }
  logger.debug(`parts ${parts}`);
  for (const part of parts) {
    if (/^-/.test(part)) {
      errors.push('cannot start with a hyphen');
      ret_obj['ip'] = IpVersion.ipv4;
    } else if (part !== '*') {
      const myerrors = [];
      //checking if an ip range is provided
      const ip_range = part.split('-');
      logger.debug(`ip_range ${ip_range}`);
      if (ip_range.length == 1) {
        // if input is a single ip address
        for (const ip of ip_range) {
          logger.debug(`ip: ${ip}`);
          if (ipv4.isValid(ip.trim())) {
            if (!/\/(9|[1-2][0-9]|3[0-2])$/.test(ip.trim())) {
              myerrors.push(
                `${ip} should have a subnet mask or prefix, between /9 to /32 for ipv4`,
              );
              ret_obj['ip'] = IpVersion.ipv4;
            } else {
              const ipv4Range = IPv4CidrRange.fromCidr(ip);
              if (ipv4Range.getFirst().toString() != ip.split('/')[0]) {
                logger.debug(`Invalid IPV4 Network Address`);
                myerrors.push(`${ip} Invalid network Address`);
                ret_obj['ip'] = IpVersion.ipv4;
              } else {
                logger.debug('Valid IPV4');
                ret_obj['ip'] = IpVersion.ipv4;
              }
            }
          } else if (
            ip.trim() == '::0/0' ||
            ip.trim() == '0::0/0' ||
            ip.trim() == '::/0'
          ) {
            //ipv6 default route is entered
            logger.debug('default ipv6 address : ', ip);
            ret_obj['ip'] = IpVersion.ipv6;
          } else if (ipv6.isValid(ip.trim())) {
            const newIPV6 = new ipv6(ip);
            const newIPV6Address = newIPV6.parsedAddress.join(':');
            logger.debug(`Parsed IPV6 string: ${newIPV6Address}`);
            if (!/\/\d{1,3}$/.test(ip.trim())) {
              myerrors.push(
                `${ip} is missing a subnet mask or prefix, eg. /32 or /128`,
              );
              ret_obj['ip'] = IpVersion.ipv6;
            }
            const ipv6Range = IPv6CidrRange.fromCidr(ip);
            if (ipv6Range.getFirst().toString() != newIPV6Address) {
              logger.debug(`Invalid IPV6 Network Address`);
              myerrors.push(`${ip} : Invalid network Address`);
              ret_obj['ip'] = IpVersion.ipv6;
            } else {
              logger.debug('Valid IPV6');
              ret_obj['ip'] = IpVersion.ipv6;
            }
          } else {
            logger.debug(`Invalid IP Address Type`);
            myerrors.push(`${ip} Invalid IP Address Type`);
            ret_obj['ip'] = IpVersion.ipv4;
          }

          if (myerrors.length > 0) {
            errors.push(...myerrors);
          } else {
            parts_valid.push(part);
          }
        }
      } else if (ip_range.length == 2) {
        //if input is multiple ip addresses
        const startIp = ip_range[0];
        const endIp = ip_range[1];
        if (startIp == endIp) {
          errors.push(`start and end Ip address of a range cannot be same`);
          ret_obj['ip'] = IpVersion.ipv4;
        } else if (
          /\/\d{1,3}$/.test(startIp.trim()) ||
          /\/\d{1,3}$/.test(endIp.trim())
        ) {
          errors.push(
            `subnet mask or prefix is entered, eg. /32 or /128, which is not required when input is a range`,
          );
          ret_obj['ip'] = IpVersion.ipv4;
        } else if (ipv4.isValid(startIp) && ipv4.isValid(endIp)) {
          logger.debug(`valid ipv4 address ${startIp}`);
          //converting ip addresses to integer to check the range
          const startInt = ipv4ToInt(startIp);
          const endInt = ipv4ToInt(endIp);
          logger.debug(`Start-end ${startInt}, ${endInt}`);
          //check if end ip is greater than start ip in the range
          if (!(endInt > startInt)) {
            errors.push('end ip should be greater than start ip');
            ret_obj['ip'] = IpVersion.ipv4;
          }
          //check that the range is not greater than 64
          if (endInt - startInt > 64) {
            errors.push('The IPV4 range must not exceed 64 addresses');
            ret_obj['ip'] = IpVersion.ipv4;
            continue;
          }
        } else if (ipv6.isValid(startIp) && ipv6.isValid(endIp)) {
          logger.debug('valid ipv6 address');
          //converting ip addresses to integer to check the range
          const startInt = ipv6ToInt(startIp);
          const endInt = ipv6ToInt(endIp);
          logger.debug(`Start-end ${startInt}, ${endInt}`);
          //check if end ip is greater than start ip in the range
          if (!(endInt > startInt)) {
            errors.push('end ip should be greater than stop ip');
            ret_obj['ip'] = IpVersion.ipv6;
          }
          //check that the range is not greater than 64
          if (endInt - startInt > 64) {
            errors.push(`The IPV6 range must not exceed 64 addresses`);
            ret_obj['ip'] = IpVersion.ipv6;
            continue;
          }
        } else {
          errors.push('please provide correct ip range with same type');
          ret_obj['ip'] = IpVersion.ipv4;
        }
      } else {
        logger.debug('invalid ip format provided');
        errors.push(` invalid ip format`);
        ret_obj['ip'] = IpVersion.ipv4;
      }
    }
  }
  if (parts_valid.length > 0) {
    //ret_obj['ip'] = parts_valid.join(',');
  }
  if (errors.length > 0) {
    ret_obj['errors'] = ipAddress + '=> ' + errors.join(';');
  }
  if (ipAddress == undefined || ipAddress == '' || ret_obj.errors == '') {
    logger.debug('rety', ret_obj.ip);
    return { valid: true, ipVersion: ret_obj.ip };
  } else {
    return { msg: ret_obj.errors, ipVersion: ret_obj.ip };
  }
}

export function validateSourceDestination(source, destination) {
  let sourceValidation = validateIpAddresses(source);
  let destinationValidation = validateIpAddresses(destination);

  if (sourceValidation.ipVersion !== destinationValidation.ipVersion) {
    return {
      valid: false,
      msg: 'Source and Destination IP must be the same IP type.',
    };
  }

  return { valid: true, msg: 'valid' };
}

export function validateLocation(location: string): ValidateFieldResponse {
  logger.debug(`inside validate loc ${location}`);
  if (location == undefined || location == '') {
    logger.debug('location not provided');
    return { valid: true };
  }
  if (location.length > 255) {
    return { msg: 'location max length is 255 characters' };
  } else if (location.length >= 2 && location.length <= 255) {
    const validLocationPattern = /^[a-zA-Z0-9\s-_.?/\\]+$/;
    if (!validLocationPattern.test(location)) {
      return {
        msg: 'location contains invalid characters, Allowed special characters are _ - ? / \ .',
      };
    }
    return { valid: true };
  } else if (location.length < 2) {
    return { msg: 'location less than 2 characters' };
  } else {
    return { msg: 'location not provided correctly' };
  }
}
export function validateHostName(hostName: string): ValidateFieldResponse {
  if (
    hostName == undefined ||
    hostName == '' ||
    /(?=^.{4,253}\.?$)(^((?!-)[a-zA-Z0-9-]{1,63}(?<!-)\.)+[a-zA-Z]{2,63}\.?$)/.test(
      hostName ? hostName.trim() : hostName,
    )
  ) {
    return { valid: true };
  } else {
    return { msg: 'Hostname provided is not correct' };
  }
}

export function validatePort(value: string): ValidateFieldResponse {
  if (
    value == undefined ||
    value == '' ||
    String(value).toUpperCase() == 'ANY'
  ) {
    return { valid: true };
  }
  value = String(value);
  const parts = value.split(',');
  const parts_valid = [];
  const errors = [];
  const ret_obj = { port: '', errors: '' };

  if (/^,|,$/.test(value)) {
    errors.push('cannot start or end with a comma');
  }
  if (/,,/.test(value)) {
    errors.push('cannot have two commas in a row');
  }
  for (const part of parts) {
    if (/^-/.test(part)) {
      errors.push('cannot start with a hyphen');
    } else if (part !== '*') {
      const myerrors = [];
      const port_range = part.split('-');

      for (const port of port_range) {
        const badchars = port.match(/[^0-9,\s-]+/g);
        if (badchars && badchars.length > 0) {
          myerrors.push(
            `non numeric characters not allowed (${badchars.join(',')}) `,
          );
        } else if (parseInt(port) < 1 || parseInt(port) > 65535) {
          myerrors.push('valid port should be between 1 and 65535');
        }
      }
      if (port_range.length > 2) {
        errors.push(
          'valid port ramnge should be two numbers separated by a hyphen',
        );
      }
      if (
        port_range.length === 2 &&
        parseInt(port_range[0]) >= parseInt(port_range[1])
      ) {
        errors.push('first no of port range should be less than second no');
      }
      if (myerrors.length > 0) {
        errors.push(...myerrors);
      } else {
        parts_valid.push(part);
      }
    }
  }
  if (parts_valid.length > 0) {
    ret_obj['ports'] = parts_valid.join(',');
  }
  if (errors.length > 0) {
    ret_obj['errors'] = value + ' ' + errors.join(';');
  }
  if (value == undefined || value == '' || ret_obj.errors == '') {
    return { valid: true };
  } else {
    return { msg: ret_obj.errors };
  }
}

export function validateProtocol(
  protocol: string,
  allowedProtocols,
): ValidateFieldResponse {
  if (protocol == null || undefined || '') {
    return { msg: 'protocol is required' };
  } else if (allowedProtocols?.includes(protocol.trim().toUpperCase())) {
    return { valid: true };
  } else {
    return { msg: `must be one of ${allowedProtocols}` };
  }
}

export function transformFirewallRules(firewallRules): FirewallRule[] {
  logger.debug(
    'transforming FirewallRules ie, removing whitespaces and changing type to string if required',
  );
  for (let i = 0; i < firewallRules.length; i++) {
    firewallRules[i].source.location = firewallRules[i].source.location
      ? firewallRules[i].source.location.trim()
      : firewallRules[i].source.location;
    firewallRules[i].source.hostName = firewallRules[i].source.hostName
      ? firewallRules[i].source.hostName.trim()
      : firewallRules[i].source.hostName;
    firewallRules[i].source.ipAddress = firewallRules[i].source.ipAddress
      ? String(firewallRules[i].source.ipAddress)
          .trim()
          .replace(/\r?\n|\r/g, '')
          .replace(/\s*,\s*/g, ',')
          .replace(/\s*\/\s*/g, '/')
      : firewallRules[i].source.ipAddress;
    //transforming ip address
    firewallRules[i].source.ipAddress = firewallRules[i].source.ipAddress
      ? transformIpAddressesWithDefaultMask(firewallRules[i].source.ipAddress)
      : firewallRules[i].source.ipAddress;
    //if type of port field is other than undefined or string we are converting it to string and triming
    //else value is string we trim the whitespaces and if the value is undefined we pass it without any change
    if (
      firewallRules[i].source.port != undefined &&
      typeof firewallRules[i].source.port != 'string'
    ) {
      firewallRules[i].source.port = String(
        firewallRules[i].source.port,
      ).trim();
    } else {
      firewallRules[i].source.port = firewallRules[i].source.port
        ? firewallRules[i].source.port
            .split(',')
            .map((p) => p.trim().replace(/\s*-\s*/g, '-'))
            .join(',')
        : firewallRules[i].source.port;
    }
    firewallRules[i].destination.location = firewallRules[i].destination
      .location
      ? firewallRules[i].destination.location.trim()
      : firewallRules[i].destination.location;
    firewallRules[i].destination.hostName = firewallRules[i].destination
      .hostName
      ? firewallRules[i].destination.hostName.trim()
      : firewallRules[i].destination.hostName;
    firewallRules[i].destination.ipAddress = firewallRules[i].destination
      .ipAddress
      ? String(firewallRules[i].destination.ipAddress)
          .trim()
          .replace(/\r?\n|\r/g, '')
          .replace(/\s*,\s*/g, ',')
          .replace(/\s*\/\s*/g, '/')
      : firewallRules[i].destination.ipAddress;
    //transforming ip address
    firewallRules[i].destination.ipAddress = firewallRules[i].destination
      .ipAddress
      ? transformIpAddressesWithDefaultMask(
          firewallRules[i].destination.ipAddress,
        )
      : firewallRules[i].destination.ipAddress;
    //if type of port field is other than undefined or string we are converting it to string and triming
    //else value is string we trim the whitespaces and if the value is undefined we pass it without any change
    if (
      firewallRules[i].destination.port != undefined &&
      typeof firewallRules[i].destination.port != 'string'
    ) {
      firewallRules[i].destination.port = String(
        firewallRules[i].destination.port,
      ).trim();
    } else {
      firewallRules[i].destination.port = firewallRules[i].destination.port
        ? firewallRules[i].destination.port
            .split(',')
            .map((p) => p.trim().replace(/\s*-\s*/g, '-'))
            .join(',')
        : firewallRules[i].destination.port;
    }
    firewallRules[i].destination.port = firewallRules[i].destination.port
      ? firewallRules[i].destination.port.trim()
      : firewallRules[i].destination.port;
    firewallRules[i].protocol = firewallRules[i].protocol
      ? firewallRules[i].protocol.trim()
      : firewallRules[i].protocol;
  }
  return firewallRules;
}

export function transformIpAddressesWithDefaultMask(ipAddress: string) {
  try {
    logger.log(`transforming ${ipAddress}`);
    ipAddress = String(ipAddress);
    ipAddress = ipAddress.replace(/\r?\n|\r/g, '');
    const parts = ipAddress.split(',');
    let partsArray = [];
    let finalIp;
    logger.debug(`parts ${parts}`);
    for (let part of parts) {
      if (part && /^-/.test(part)) {
        logger.log(`cannot start with a hyphen ${part}`);
      } else if (part !== '*') {
        //checking if an ip range is provided
        const ip_range = part.split('-');
        logger.debug(`ip_range ${ip_range}`);
        if (ip_range.length == 1) {
          // if input is a single ip address
          logger.debug(`ip: ${part}`);
          if (ipv4.isValid(part.trim())) {
            if (!/\/\d{1,3}$/.test(part.trim())) {
              logger.log(`${part} is missing a subnet mask or prefix, eg. /32`);
              part = `${part}/32`;
              partsArray.push(part);
            } else {
              logger.log(
                `no transformation req if ip already has a mask: ${part}`,
              );
              partsArray.push(part);
            }
          } else {
            logger.log(`no transformation req if ip is v6: ${part}`);
            partsArray.push(part);
          }
        } else {
          //if input is a range
          logger.log(`no transformation req if ip is a range: ${part}`);
          partsArray.push(part);
        }
      }
    }
    if (partsArray.length > 0) {
      finalIp = partsArray.join(',');
    }
    logger.log('final value after transformation', finalIp);
    return finalIp;
  } catch (error) {
    logger.error(error, `error while transforming ip address ${ipAddress}`);
    return ipAddress;
  }
}
