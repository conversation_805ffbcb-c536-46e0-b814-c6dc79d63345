import { Test, TestingModule } from '@nestjs/testing';
import { DevicesController } from './devices.controller';
import { DevicesService } from './providers/devices.service';
import { AuthenticationGuard } from '../../auth/authentication.guard';
import {
  CreateSecretDeviceAssociationDto,
  UpdateSecretDeviceAssociationDto,
} from './dto/devices.dto';

describe('DevicesController', () => {
  let controller: DevicesController;
  const mockService = {
    createSecretDeviceAssociations: jest.fn(),
    getAllRotatableSecretDeviceAssociation: jest.fn(),
    updateSecretDeviceAssociations: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [DevicesController],
      providers: [{ provide: DevicesService, useValue: mockService }],
    })
      .overrideGuard(AuthenticationGuard)
      .useValue({ canActivate: jest.fn(() => true) })
      .compile();

    controller = module.get<DevicesController>(DevicesController);
  });
  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should call service with correct data', async () => {
    const dto = [{ deviceId: 1, secretId: 'abc' }];
    const response = { message: 'Success' };
    mockService.createSecretDeviceAssociations.mockResolvedValue(response);

    const result = await controller.createSecreDeviceAssociations(
      dto as CreateSecretDeviceAssociationDto[],
    );
    expect(result).toEqual(response);
    expect(mockService.createSecretDeviceAssociations).toHaveBeenCalledWith(
      dto,
    );
  });

  it('should return mock secret device association data', async () => {
    const mockData = [
      {
        _id: '6846b20de6c9155f3cf74cc7',
        secretAssociationId: 'NEB-SEC-LINK-1005',
        deviceId: [0],
        secretId: ['suriya-concurrency-test'],
        type: 'DEVICE-VAULT-SECRET-MAPPING',
        active: true,
        createdBy: 'P3271329',
        updatedBy: null,
        createdAt: '2025-06-09T10:06:05.937Z',
        updatedAt: '2025-06-09T10:06:05.937Z',
        __v: 0,
      },
    ];
    mockService.getAllRotatableSecretDeviceAssociation.mockResolvedValue(
      mockData,
    );
    const result = await controller.getAllRotatableSecretDeviceAssociation(
      'nebula-stamp',
      'dev/rc',
      '01966c73-b401-7285-bec1-b122f7118def',
    );
    expect(result).toEqual(mockData);
  });

  it('should call updateSecretDeviceAssociations service with correct data', async () => {
    const dto = [{ deviceId: 1, secretId: 'abc' }];
    const response = { message: 'Success' };
    mockService.updateSecretDeviceAssociations.mockResolvedValue(response);

    const result = await controller.updateSecretDeviceAssociations(
      dto as UpdateSecretDeviceAssociationDto[],
    );
    expect(result).toEqual(response);
    expect(mockService.createSecretDeviceAssociations).toHaveBeenCalledWith(
      dto,
    );
  });

  it('should call updateSecretDeviceAssociations service with correct data', async () => {
    const dto = [{ deviceId: 1, secretId: 'abc' }];
    const response = { message: 'Success' };
    mockService.updateSecretDeviceAssociations.mockResolvedValue(response);

    const result = await controller.updateSecretDeviceAssociations(
      dto as UpdateSecretDeviceAssociationDto[],
    );
    expect(result).toEqual(response);
    expect(mockService.createSecretDeviceAssociations).toHaveBeenCalledWith(
      dto,
    );
  });

  it('should return mock secret device association data', async () => {
    const mockData = [
      {
        _id: '6846b20de6c9155f3cf74cc7',
        secretAssociationId: 'NEB-SEC-LINK-1005',
        deviceId: [0],
        secretId: ['suriya-concurrency-test'],
        type: 'DEVICE-VAULT-SECRET-MAPPING',
        active: true,
        createdBy: 'P3271329',
        updatedBy: null,
        createdAt: '2025-06-09T10:06:05.937Z',
        updatedAt: '2025-06-09T10:06:05.937Z',
        __v: 0,
      },
    ];
    mockService.getAllRotatableSecretDeviceAssociation.mockResolvedValue(
      mockData,
    );
    const result = await controller.getAllRotatableSecretDeviceAssociation(
      'nebula-stamp',
      'dev/rc',
      '01966c73-b401-7285-bec1-b122f7118def',
    );
    expect(result).toEqual(mockData);
  });

  it('should call updateSecretDeviceAssociations service with correct data', async () => {
    const dto = [{ deviceId: 1, secretId: 'abc' }];
    const response = { message: 'Success' };
    mockService.updateSecretDeviceAssociations.mockResolvedValue(response);

    const result = await controller.updateSecretDeviceAssociations(
      dto as UpdateSecretDeviceAssociationDto[],
    );
    expect(result).toEqual(response);
    expect(mockService.createSecretDeviceAssociations).toHaveBeenCalledWith(
      dto,
    );
  });
});
