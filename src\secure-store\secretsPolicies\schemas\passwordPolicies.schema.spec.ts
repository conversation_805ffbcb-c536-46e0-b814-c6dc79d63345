import { Test, TestingModule } from '@nestjs/testing';
import { MongooseModule, getModelToken } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  PasswordPolicies,
  PasswordPoliciesSchema,
} from './passwordPolicies.schema';
import { SecretPolicyTypes, Status } from '../types/secretsPolicies.enu';
import { MongoMemoryServer } from 'mongodb-memory-server';
import mongoose from 'mongoose';

describe('PasswordPolicies Schema', () => {
  let mongoServer: MongoMemoryServer;
  let passwordPoliciesModel: Model<PasswordPolicies>;

  beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();

    const module: TestingModule = await Test.createTestingModule({
      imports: [
        MongooseModule.forRoot(uri),
        MongooseModule.forFeature([
          { name: PasswordPolicies.name, schema: PasswordPoliciesSchema },
        ]),
      ],
    }).compile();

    passwordPoliciesModel = module.get<Model<PasswordPolicies>>(
      getModelToken(PasswordPolicies.name),
    );
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  it('should create and save a PasswordPolicy document', async () => {
    const policy = new passwordPoliciesModel({
      policyId: 'policy123',
      policyName: 'Strong Policy',
      type: SecretPolicyTypes.VAULT_POLICY,
      description: 'Requires strong passwords',
      resourceId: 'res123',
      status: Status.ACTIVE,
      secretTTLInHours: 24,
      active: true,
      createdBy: 'admin',
      updatedBy: 'admin',
      namespace: 'default',
    });

    const savedPolicy = await policy.save();

    expect(savedPolicy._id).toBeDefined();
    expect(savedPolicy.policyName).toBe('Strong Policy');
    expect(savedPolicy.status).toBe(Status.ACTIVE);
    expect(savedPolicy.active).toBe(true);
  });

  it('should fail validation if required "type" is missing', async () => {
    const policy = new passwordPoliciesModel({
      policyName: 'Missing Type',
    });

    let error;
    try {
      await policy.save();
    } catch (err) {
      error = err;
    }

    expect(error).toBeDefined();
    expect(error.name).toBe('ValidationError');
    expect(error.errors.type).toBeDefined();
  });

  it('should default status to ACTIVE if not provided', async () => {
    const policy = new passwordPoliciesModel({
      policyId: 'policy456',
      policyName: 'Default Status Policy',
      type: SecretPolicyTypes.VAULT_POLICY,
    });

    const savedPolicy = await policy.save();
    expect(savedPolicy.status).toBe(Status.ACTIVE);
  });

  it('should default active to true if not provided', async () => {
    const policy = new passwordPoliciesModel({
      policyId: 'policy789',
      policyName: 'Default Active Policy',
      type: SecretPolicyTypes.VAULT_POLICY,
    });

    const savedPolicy = await policy.save();
    expect(savedPolicy.active).toBe(true);
  });
});
