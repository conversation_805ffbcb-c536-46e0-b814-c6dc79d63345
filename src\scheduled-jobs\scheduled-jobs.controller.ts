import {
  Controller,
  Get,
  HttpStatus,
  Post,
  Res,
  UseGuards,
} from '@nestjs/common';
import { ScheduledJobsService } from './scheduled-jobs.service';
import { AuthenticationGuard } from '../auth/authentication.guard';
import { ApiSecurity, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';

@ApiTags('Jobs')
@ApiSecurity('x-nebula-authorization')
@UseGuards(AuthenticationGuard)
@Controller('jobs')
export class ScheduledJobsController {
  constructor(private readonly scheduledJobService: ScheduledJobsService) {}

  @Post('rotate-secrets')
  async rotateSecretsJob(@Res() res: Response) {
    res.status(HttpStatus.OK).json({});
    await this.scheduledJobService.rotateSecretsJob();
  }

  @Post('reactivate-secrets')
  async reactivateSecrets(@Res() res: Response) {
    res.status(HttpStatus.OK).json({});
    await this.scheduledJobService.reactivateSecrets();
  }

  @Post('renew-vault-tokens')
  async renewVaultTokens(@Res() res: Response) {
    res.status(HttpStatus.OK).json({});
    await this.scheduledJobService.renewVaultTokens();
  }
}
