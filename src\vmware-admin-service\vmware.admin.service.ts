import { Inject, Injectable } from '@nestjs/common';
import { VlookupDTO } from './dto/vlookup.dto';
import { LoggerService } from '../loggers/logger.service';
import { AxiosInstance } from 'axios';
import { withResponseErrorHandler } from 'src/utils/helpers';
import { SaveVCenterLookupDto } from './dto/save.vlookup.dto';
import { GetAllVcenterResponseDto } from './dto/getvCenterResponse.dto';
import { UpdateVmwareCloudDto } from './dto/updatevCenter.dto';

@Injectable()
export class VmwareAdminService {
  constructor(
    @Inject('VMWARE_ADMIN_SERVICE_API')
    private readonly vmwareServiceApi: AxiosInstance,
    private readonly logger: LoggerService,
  ) {}

  async vLookup(vlookupDTO: VlookupDTO) {
    this.logger.log('searching vLookup data for', vlookupDTO);
    return withResponseErrorHandler(
      this.vmwareServiceApi.post(`vmware/vCenter/lookup`, vlookupDTO),
    );
  }

  async saveVlookUp(saveVlookupDTO: SaveVCenterLookupDto) {
    this.logger.log('save vLookup data for', saveVlookupDTO);
    return withResponseErrorHandler(
      this.vmwareServiceApi.post(`vmware/vCenter/lookup/save`, saveVlookupDTO),
    );
  }

  async getAllVcenter(): Promise<GetAllVcenterResponseDto[]> {
    this.logger.log('calling vmware admin service  to get all the  vCenter');
    return withResponseErrorHandler(
      this.vmwareServiceApi.get(`vmware/vCenters`),
    );
  }

  async getvCenterClustersHostsDatastoreOSLayouts(cloudId: string) {
    this.logger.log(
      'calling vmware admin service  to get all the  vCenter for cloudId - ',
      cloudId,
    );
    return withResponseErrorHandler(
      this.vmwareServiceApi.get(`vmware/vCenters/${cloudId}`),
    );
  }

  async deletevCenters(deletevCentersIds: string[]) {
    this.logger.log(
      'getting the vcenter cloudId(s) to delete ',
      deletevCentersIds,
    );
    return withResponseErrorHandler(
      this.vmwareServiceApi.delete(`vmware/vCenters`, {
        data: deletevCentersIds,
      }),
    );
  }

  async updatevCenter(updateVmwareCloudDto: UpdateVmwareCloudDto[]) {
    this.logger.log(
      'calling vmware admin service to update vCenters with payload:',
      updateVmwareCloudDto,
    );
    return withResponseErrorHandler(
      this.vmwareServiceApi.put(`vmware/vCenters/update`, updateVmwareCloudDto),
    );
  }
}
