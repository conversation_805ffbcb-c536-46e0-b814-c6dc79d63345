import { Inject, Injectable } from '@nestjs/common';
import { VlookupDTO } from './dto/vlookup.dto';
import { LoggerService } from '../loggers/logger.service';
import { AxiosInstance } from 'axios';
import { withResponseErrorHandler } from 'src/utils/helpers';
import { SaveVCenterLookupDto } from './dto/save.vlookup.dto';
import { GetAllVcenterResponseDto } from './dto/getvCenterResponse.dto';
import { UpdateVmwareCloudDto } from './dto/updatevCenter.dto';
import { ErrorDetails } from '../vmware-error-loggers/vmware-error-logger.service';
import { ERROR_DETAILS } from 'src/vmware-error-loggers/vmware-error-details';

@Injectable()
export class VmwareAdminService {
  private serviceRequestId: string | null = null;
  constructor(
    @Inject('VMWARE_ADMIN_SERVICE_API')
    private readonly vmwareServiceApi: AxiosInstance,
    private readonly logger: LoggerService,
    private readonly vmwareErrorlogService: ErrorDetails,
  ) {}

  async vLookup(vlookupDTO: VlookupDTO) {
    try {
      this.logger.log('searching vLookup data for', vlookupDTO);
      return withResponseErrorHandler(
        this.vmwareServiceApi.post(`vmware/vCenter/lookup`, vlookupDTO),
      );  
    }
    catch (err) {
      this.vmwareErrorlogService.logError(
        err,
        'vmwareServiceApi.vLookup',
        ERROR_DETAILS.VCENTER_LOOKUP_ERROR_CODE,
        ERROR_DETAILS.VCENTER_LOOKUP_ERROR_MESSAGE,
        this.serviceRequestId
      );
    }    
  }

  async vLookupRefresh(vlookupDTO: VlookupDTO) {
    try {
      this.logger.log('searching vLookup data for', vlookupDTO);
      return withResponseErrorHandler(
        this.vmwareServiceApi.post(`vmware/vCenters/lookup/refresh`, vlookupDTO),
      );
    }
    catch (err) {
      this.vmwareErrorlogService.logError(
        err,
        'vmwareServiceApi.vLookup',
        ERROR_DETAILS.VCENTER_LOOKUP_ERROR_CODE,
        ERROR_DETAILS.VCENTER_LOOKUP_ERROR_MESSAGE,
        this.serviceRequestId
      );
    }  
  }

  async saveVlookUp(saveVlookupDTO: SaveVCenterLookupDto) {
    try {
      this.logger.log('save vLookup data for', saveVlookupDTO);
      return withResponseErrorHandler(
        this.vmwareServiceApi.post(`vmware/vCenter/lookup/save`, saveVlookupDTO),
      );
    }
     catch (err) {
      this.vmwareErrorlogService.logError(
        err,
        "vmwareServiceApi.saveVlookUp",
        ERROR_DETAILS.VCENTER_SAVE_ERROR_CODE,
        ERROR_DETAILS.VCENTER_SAVE_ERROR_MESSAGE,
        this.serviceRequestId
      );
    }
  }

  async getAllVcenter(): Promise<GetAllVcenterResponseDto[]> {
    try {
      this.logger.log('calling vmware admin service  to get all the  vCenter');
      return withResponseErrorHandler(
        this.vmwareServiceApi.get(`vmware/vCenters`),
      );
    } 
    catch (err) {
      this.vmwareErrorlogService.logError(
        err,
        "vmwareServiceApi.getAllVcenter",
        ERROR_DETAILS.VCENTER_READ_ITEMS_ERROR_CODE,
        ERROR_DETAILS.VCENTER_READ_ALL_ITEMS_ERROR_MESSAGE,
        this.serviceRequestId
      );
    }
  }

  async getvCenterClustersHostsDatastoreOSLayouts(cloudId: string) {
    try {
      this.logger.log(
        'calling vmware admin service  to get all the  vCenter for cloudId - ',
        cloudId,
      );
      return withResponseErrorHandler(
        this.vmwareServiceApi.get(`vmware/vCenters/${cloudId}`),
      );
    }
     catch (err) {
      this.vmwareErrorlogService.logError(
        err,
        "vmwareServiceApi.getvCenterClustersHostsDatastoreOSLayouts",
        ERROR_DETAILS.VCENTER_READ_ITEMS_ERROR_CODE,
        ERROR_DETAILS.VCENTER_READ_ITEM_ERROR_MESSAGE,
        this.serviceRequestId
      );
    }
  }

    async deletevCenters(deletevCentersIds: string[]) {
      try {
        this.logger.log(
          'getting the vcenter cloudId(s) to delete ',
          deletevCentersIds,
        );
        return withResponseErrorHandler(
          this.vmwareServiceApi.delete(`vmware/vCenters`, {
            data: deletevCentersIds,
          }),
        );
      }
      catch (err) {
        this.vmwareErrorlogService.logError(
          err,
          "vmwareServiceApi.softDeletedvCenterCloud",
          ERROR_DETAILS.VCENTER_DELETE_ERROR_CODE,
          ERROR_DETAILS.VCENTER_DELETE_ERROR_MESSAGE,
          this.serviceRequestId
        );
      }
    }

  async updatevCenter(updateVmwareCloudDto: UpdateVmwareCloudDto[]) {
    try {
      this.logger.log(
        'calling vmware admin service to update vCenters with payload:',
        updateVmwareCloudDto,
      );
      return withResponseErrorHandler(
        this.vmwareServiceApi.put(`vmware/vCenters/update`, updateVmwareCloudDto),
      );
    } catch (err) {
      this.vmwareErrorlogService.logError(
        err,
        "vmwareServiceApi.updateVcenterResponse",
        ERROR_DETAILS.VCENTER_UPDATE_ERROR_CODE,
        ERROR_DETAILS.VCENTER_UPDATE_ERROR_MESSAGE,
        this.serviceRequestId
      );
    }
  }
}
