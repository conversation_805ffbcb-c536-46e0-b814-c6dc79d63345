import { SecretsMetadataRepository } from './secretsMetadata.repository';
import { SecretType, Status } from './types/secretsMetadata.enum';
import { CounterTypes, GenericRequestTypes } from '../../types';
import { CreateSecretMetaDataDto } from './dto/secretsMetadata.dto';
import { ActionStatus } from '../../action/enums/ActionStatus.enum';
import { ConfigService } from '@nestjs/config';
import { ENVIRONMENT_VARS } from '../../utils/constants';

const configService = new ConfigService();

describe('SecretsMetadataRepository', () => {
  let repository: SecretsMetadataRepository;
  let mockModel: any;
  let mockCountersModel: any;
  let mockSession: any;
  let mockConfigService: any;
  beforeEach(() => {
    
    mockConfigService = {
      get: jest.fn(),
    } as unknown as ConfigService;

    repository = new SecretsMetadataRepository(mockConfigService);
    
    mockModel = {
      create: jest.fn(),
      fetchSecretsForRotationDue: jest.fn(),
      find: jest.fn(),
      findOne: jest.fn(),
      updateMany: jest.fn(),
      updateOne: jest.fn(),
      bulkWrite: jest.fn(),
      aggregate: jest.fn(),
      startSession: jest.fn().mockResolvedValue(mockSession),
      findOneAndUpdate: jest.fn(),
      collection: { name: 'secretsmetadatas' },
    };

    mockCountersModel = {
      findOneAndUpdate: jest.fn(),
      create: jest.fn(),
    };

    mockSession = {};

    jest
      .spyOn(repository as any, 'getSecretsMetadataModel')
      .mockResolvedValue(mockModel);
    jest
      .spyOn(repository as any, 'getCountersModel')
      .mockResolvedValue(mockCountersModel);
  });

  it('should create secret metadata', async () => {
    const dto: CreateSecretMetaDataDto = {
      type: SecretType.ROTATABLE_SECRET,
      resourceId: '',
      status: Status.SUCCESS,
      active: false,
      vaultPath: '',
      actionId: '',
    };
    mockModel.create.mockResolvedValue({
      _id: 'mock-id',
      secretId: 's1',
      vaultNamespace: 'ns',
      vaultPath: 'path',
      toJSON: () => ({
        _id: 'mock-id',
        secretId: 's1',
        vaultNamespace: 'ns',
        vaultPath: 'path',
      }),
    });

    const result = await repository.create(dto);
    expect(mockModel.create).toHaveBeenCalled();
    expect(result.secretId).toBe('s1');
  });

  it('should fetch Secrets ForRotationDue', async () => {
  
      mockModel.find.mockResolvedValue([{
        _id: 'mock-id',
        secretId: 's1',
        vaultNamespace: 'ns',
        vaultPath: 'path',
        toJSON: () => ({
          _id: 'mock-id',
          secretId: 's1',
          vaultNamespace: 'ns',
          vaultPath: 'path',
        }),
      }]);
  
      const result = await repository.fetchSecretsForRotationDue(1);
  
      expect(mockModel.find).toHaveBeenCalled();
      expect(result[0].secretId).toBe('s1');
  });

  it('should get secrets metadata by namespace and path', async () => {
    mockModel.find.mockResolvedValue([
      {
        _id: 'mock-id-1',
        secretId: 's1',
        toJSON: () => ({
          _id: 'mock-id-1',
          secretId: 's1',
        }),
      },
    ]);
    const result = await repository.getSecretsMetaData({
      namespace: 'ns',
      path: 'path',
    });
    expect(mockModel.find).toHaveBeenCalled();
    expect(result.length).toBe(1);
  });

    it('should get secrets metadata by namespace, path and type', async () => {
    mockModel.find.mockResolvedValue([
      {
        _id: 'mock-id-1',
        secretId: 's1',
        toJSON: () => ({
          _id: 'mock-id-1',
          secretId: 's1',
        }),
      },
    ]);
    const result = await repository.getSecretsMetaData({
      namespace: 'ns',
      path: 'path/',
      type: 'Normal'
    });
    expect(mockModel.find).toHaveBeenCalled();
    expect(result.length).toBe(1);
  });

  it('should get secrets metadata by single secretId', async () => {
    mockModel.findOne.mockResolvedValue({
      _id: 'mock-id-1',
      secretId: 's1',
      toJSON: () => ({
        _id: 'mock-id-1',
        secretId: 's1',
      }),
    });
    const result = await repository.getSecretsMetaDataBySecretId('s1');
    expect(mockModel.findOne).toHaveBeenCalled();
    expect(result.secretId).toBe('s1');
  });

  it('should get secrets metadata by multiple secretIds', async () => {
    mockModel.find.mockResolvedValue([
      {
        _id: 'mock-id-1',
        secretId: 's1',
        toJSON: () => ({
          _id: 'mock-id-1',
          secretId: 's1',
        }),
      },
      {
        _id: 'mock-id-2',
        secretId: 's2',
        toJSON: () => ({
          _id: 'mock-id-2',
          secretId: 's2',
        }),
      },
    ]);
    const result = await repository.getSecretsMetaDataBySecretId(['s1', 's2']);
    expect(mockModel.find).toHaveBeenCalled();
    expect(result.length).toBe(2);
  });

  it('should aggregate secrets metadata with linked secrets and associations', async () => {
    mockModel.aggregate.mockResolvedValue([{ secretId: 's1' }]);
    const result = await repository.getAllSecretsMetaDataBySecretIds(['s1']);
    expect(mockModel.aggregate).toHaveBeenCalled();
    expect(result.length).toBe(1);
  });

  it('should change active status by secretIds', async () => {
    await repository.changeActiveStatusByIds(
      ['s1', 's2'].map((secretId) => ({ secretId, active: false, actionId: 'act-1', rotationStatus: ActionStatus.STARTED })),
    );
    expect(mockModel.bulkWrite).toHaveBeenCalled();
  });

  it('should bulk update secrets metadata', async () => {
    await repository.bulkUpdateSecretsMetaDataBySecretId([
      { updateOne: { filter: {}, update: {} } },
    ]);
    expect(mockModel.bulkWrite).toHaveBeenCalled();
  });

  it('should generate secret ID', async () => {
    jest.spyOn(repository, 'getCount').mockResolvedValue(1001);
    const result = await repository.generateSecretId(
      SecretType.ROTATABLE_SECRET,
    );
    expect(result).toBe(
      `${GenericRequestTypes['VAULT-ROTATABLE-SECRET']}-1001`,
    );
  });

    it('should generate Token secret ID', async () => {
    jest.spyOn(repository, 'getCount').mockResolvedValue(1001);
    const result = await repository.generateSecretId(
      SecretType.VAULT_TOKEN,
    );
    expect(result).toBe(
      `${GenericRequestTypes['VAULT-TOKEN-SECRET']}-1001`,
    );
  });

    it('should generate Normal secret ID', async () => {
    jest.spyOn(repository, 'getCount').mockResolvedValue(1001);
    const result = await repository.generateSecretId(
      SecretType.NORMAL_SECRET,
    );
    expect(result).toBe(
      `${GenericRequestTypes['VAULT-NORMAL-SECRET']}-1001`,
    );
  });

  it('should get count and increment counter with undefined.', async () => {
    mockCountersModel.findOneAndUpdate.mockResolvedValue();
    mockCountersModel.create.mockResolvedValue({});
    const result = await repository.getCount(
      mockSession,
      CounterTypes.SECRETS_METADATA,
    );
    expect(result).toBe(1);
  });

    it('update Secrets Metadata By Id.', async () => {
    mockModel.updateOne.mockResolvedValue({});
    const result = await repository.updateSecretsMetadataById(
      'Vault-secret-id',
      {secretId: 'Vault-secret-id'},
    );
    expect(mockModel.updateOne).toHaveBeenCalledWith(
      { secretId: 'Vault-secret-id' },
      { $set: { secretId: 'Vault-secret-id'  } },
    );
});
  
  it('should get count and increment counter', async () => {
    mockCountersModel.findOneAndUpdate.mockResolvedValue({ counter: 1001 });
    const result = await repository.getCount(
      mockSession,
      CounterTypes.SECRETS_METADATA,
    );
    expect(result).toBe(1001);
  });

  it('should build update metadata for rotatable secret', async () => {
    jest
      .spyOn(repository, 'getSecretsMetaDataBySecretId')
      .mockResolvedValue({ devicePasswordKey: 'usernameKey' });

    const payload: any = {
      secretMetaData: {
        type: SecretType.ROTATABLE_SECRET,
        deviceUserNameSecretId: 'linked-id',
        devicePasswordKey: 'passwordKey',
      },
      secretResponse: {
        passwordKey: 'oldPassword',
        usernameKey: 'oldUsername',
      },
      updateVaultSecretRequest: {
        vaultKey: 'newPasswordKey',
        vaultPassword: 'newPassword',
        userNameKey: 'newUsernameKey',
        userNamePassword: 'newUsername',
      },
      secretId: 'secret-123',
    };

    const result = await repository.createQueryForUpdateMetaData(payload);
    expect(result.updateMetaData.length).toBe(2);
    expect(result.secretData).toHaveProperty('newPasswordKey');
    expect(result.secretData).toHaveProperty('newUsernameKey');
  });

  it('should reactivate secrets metadata correctly', async () => {
    const reactivateDate = new Date('2025-06-01');
    const mockSecrets = [{ id: 'secret1' }, { id: 'secret2' }];

    const fixedNow = new Date('2025-06-01T00:00:00Z').getTime();
    jest.spyOn(Date, 'now').mockReturnValue(fixedNow);

    mockModel.find.mockResolvedValue(mockSecrets);
    mockModel.updateMany.mockResolvedValue({ modifiedCount: 2 });

    mockConfigService.get.mockReturnValue(24); // 24 hours
    const expiryDate = new Date(fixedNow + 24 * 60 * 60 * 1000);

    const result = await repository.reactivateSecretsMetaData(reactivateDate);
    expect(mockModel.find).toHaveBeenCalledWith({
      type: SecretType.ROTATABLE_SECRET,
      active: false,
      deactivatedAt: { $lte: reactivateDate },
    });
    expect(mockModel.updateMany).toHaveBeenCalledWith(
      {
        type: SecretType.ROTATABLE_SECRET,
        active: false,
        deactivatedAt: { $lte: reactivateDate },
      },
      {
        active: true,
        lastDeviceSyncStatus: ActionStatus.STATUS_UNKNOWN,
        expiryDate: expiryDate,
        nextRotationDate: expiryDate,
      }
    );

    expect(result).toEqual(mockSecrets);
  });

  it('should return secrets metadata list for renewal', async () => {
    const mockResult = [{ id: 'secret1' }, { id: 'secret2' }];
    mockModel.aggregate.mockResolvedValue(mockResult);

    const result = await repository.getSecretsMetadataListForRenewal(2);

    expect(mockModel.aggregate).toHaveBeenCalledWith(expect.any(Array));
    expect(result).toEqual(mockResult);
  });

  it('should return all auto-renew vault tokens', async () => {
    const mockTokens = [{ id: 'token1' }, { id: 'token2' }];
    mockModel.find.mockResolvedValue(mockTokens);

    const result = await repository.getAllAutoRenewVaultTokens();

    expect(mockModel.find).toHaveBeenCalledWith({
      type: SecretType.VAULT_TOKEN,
      tokenRenewByNebula: true,
    });
    expect(result).toEqual(mockTokens);
  });

  it('should update renewed token metadata', async () => {
    const secretId = 'secret123';
    const renewalDate = new Date('2025-06-20');
    const expiryDate = new Date('2025-12-20');

    const result = await repository.updateRenewedTokenMetaData(
      secretId,
      renewalDate,
      expiryDate,
    );

    expect(mockModel.updateOne).toHaveBeenCalledWith(
      { secretId },
      { $set: { expiryDate, renewedAt: renewalDate } },
    );
  });
});
