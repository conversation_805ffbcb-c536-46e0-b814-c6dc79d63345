import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { SecretType } from '../../secretsMetadata/types/secretsMetadata.enum';

@Schema({ timestamps: true })
export class SecretDeviceAssociation extends Document {
  @Prop({
    type: String,
    required: true,
    unique: true,
  })
  secretAssociationId: string;

  @Prop({ type: [Number], required: true })
  deviceId: Array<number>;

  @Prop({ type: [String], ref: 'secretsmetadata', required: true })
  secretId: Array<string>;

  @Prop({
    type: String,
    required: true,
    default: SecretType.DEVICE_VAULT_SECRET_MAPPING,
  })
  type: string;

  @Prop({
    type: Boolean,
    required: true,
    default: true,
  })
  active: Boolean;

  @Prop({
    type: String,
    required: true,
  })
  createdBy: string;

  @Prop({
    type: String,
    default: null,
  })
  updatedBy?: string;

  @Prop({
    type: String,
    required: true,
  })
  sourceSystem?: string;
}

export const SecretDeviceAssociationSchema = SchemaFactory.createForClass(
  SecretDeviceAssociation,
);
