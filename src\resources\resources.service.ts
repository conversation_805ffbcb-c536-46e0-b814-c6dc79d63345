import { BadRequestException, HttpStatus, Injectable } from '@nestjs/common';
import { ResourcesRepository } from './resources.repository';
import {
  ResourcesEntity,
  ResourcesRequestDto,
} from '../naas/entities/resources.entity';
import { IamService } from '../iam/iam.service';
import { ProjectRepository } from '../projects/projects.repository';
import { ProjectEntity } from '../naas/entities/project.entity';
import {
  CATALOGTYPE,
  RequestStatus,
  RequestType,
  ServiceCatalogName,
  ServiceCatalogType,
  VM_COMPUTE,
} from '../types';
import { RmqService } from '../rmq/rmq.service';
import { corpnet, PermissionKey, resourcesStatus } from '../iam/types';
import { IntegrationNotificationService } from '../naas/integration.notification.service';
import { LoggerService } from '../loggers/logger.service';
import { ExtendedResource, ResourceType } from './dto/extendedResource.dto';
import { RequestContext } from 'nestjs-request-context';
import { AssetsService } from '../assets/assets.service';
import { ResourceUpdateSuccessDto } from './dto/resourceUpdateSuccess.dto';
import {
  ComplianceValidations,
  ParsedComplianceResult,
  RawComplianceResult,
  ResourceWithComplianceResult,
} from './types/resource-with-compliance';
import { ConfigService } from '@nestjs/config';
import {
  CATALOGV3,
  COLLECTIONS,
  DeleteDB,
  DeleteVM,
  IS_COPIED_FIELD,
  IS_REGENERATED_FIELD,
  RequiredStatus,
  SecretAccessKey,
  VMAction,
} from '../utils/constants';
import {
  PaginatedResponseDto,
  PaginationQueryDto,
} from '../utils/pagination/dto/pagination.dto';
import { filteringByProjectName } from '../utils/helpers';
import {
  IpAddressesDto,
  Message,
  RequestedVMsDataDto,
  ResourcesDetailsDto,
} from './dto/restartVmResources.dto';
import { DbRequestTypes } from '../dbaas/util';
import { StorageS3Service } from '../storage/s3.service';
import { EncryptionService } from '../encryption/encryption.service';
import { ModifyResourceDto } from './dto/modifyResource.dto';
import { CatalogTypes, StorageStatus } from './utils';
import { StorageNFSService } from '../storage/nfs.service';
import { RestoreResourceDto } from './dto/restoreStorage.dto';
import { ComputeWrapperService } from '../compute-wrapper/compute-wrapper.service';
import { quotaResourceDTO } from './dto/quotaResource.dto';
import { DeleteSpecMessage } from './dto/deleteSpecResource.dto';
import { ActionService } from '../action/providers/action.service';
import { ActionType } from '../action/enums/ActionType.enum';
import { ActionStatus } from '../action/enums/ActionStatus.enum';

@Injectable()
export class ResourcesService {
  constructor(
    private readonly encryptionService: EncryptionService,
    private readonly resourcesRepository: ResourcesRepository,
    private readonly assetsService: AssetsService,
    private readonly iamService: IamService,
    private readonly computeWrapperService: ComputeWrapperService,
    private readonly projectRepository: ProjectRepository,
    private readonly rmqService: RmqService,
    private readonly integrationNotificationService: IntegrationNotificationService,
    private readonly logger: LoggerService,
    private configService: ConfigService,
    private readonly storageS3Service: StorageS3Service,
    private readonly storageNFSService: StorageNFSService,
    private readonly actionService: ActionService,
  ) {}

  private getComplianceValue(value: string): boolean {
    try {
      const parsedValue = value.trim().toLowerCase();
      switch (parsedValue) {
        case 'true':
          return true;
        case 'false':
          return false;
        default:
          return null;
      }
    } catch (error) {
      this.logger.log(`Error in converting value: ${value} to string`, error);
      return false;
    }
  }

  private parseComplianceResult(
    rawComplianceResult: RawComplianceResult,
  ): ParsedComplianceResult {
    try {
      const createdAt = rawComplianceResult.audit?.createdAt;
      const lastResultDate = createdAt ? new Date(createdAt) : null;
      const validations: ComplianceValidations = <ComplianceValidations>{};
      let isCompliant: boolean = true;
      if (rawComplianceResult.compliance) {
        for (const [check, value] of Object.entries(
          rawComplianceResult?.compliance,
        )) {
          const parsedValue = this.getComplianceValue(value);
          validations[check] = parsedValue;
          isCompliant &&= parsedValue;
        }
      }
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { audit, compliance: _, ...rest } = rawComplianceResult;
      if (rawComplianceResult.compliance && isCompliant != null) {
        return { isCompliant, lastResultDate, validations, ...rest };
      } else {
        return { lastResultDate, ...rest };
      }
    } catch (error) {
      this.logger.log(
        `Error in parsing compliance result for resource: ${rawComplianceResult.resourceId}`,
        error,
      );
      return null;
    }
  }

  private isDeleted(status) {
    return (
      status !== resourcesStatus.DELETED &&
      status !== resourcesStatus.PARTIALLYDELETED &&
      status !== resourcesStatus.DELETEFAILED &&
      status !== resourcesStatus.DELETING &&
      status !== resourcesStatus.SOFTDELETED &&
      status !== resourcesStatus.ERADICATED
    );
  }

  // To be deprecated, Make code changes in the paginated resources
  async getResourcesList(): Promise<
    ResourceWithComplianceResult<ParsedComplianceResult>[]
  > {
    const userEnvsWithPermissions =
      await this.iamService.findUserEnvironmentsWithPermissions();
    const envIds = userEnvsWithPermissions.map((envPerm) => envPerm.envId);
    const resourcesWithRawCompliance =
      await this.resourcesRepository.findByEnvId(envIds);

    // Parse compliance result
    const resources: ResourceWithComplianceResult<ParsedComplianceResult>[] =
      resourcesWithRawCompliance.map((resource) => {
        return {
          ...resource,
          complianceResult: resource.complianceResult
            ? this.parseComplianceResult(resource.complianceResult)
            : null,
        };
      });

    resources.forEach((resource) => {
      const env = userEnvsWithPermissions.find(
        (envPerm) => envPerm.envId === resource.platformContext.envId,
      );
      const deleteFlag =
        env?.permissions?.includes(PermissionKey.DELETE) &&
        this.isDeleted(resource['status']) &&
        resource.catalogType.toLowerCase() !== corpnet.LINUX_CORPNET &&
        resource.catalogType.toLowerCase() !== corpnet.WINDOWS_CORPNET;
      resource['projectName'] = env?.projectName || '';
      resource.catalogLevel03 === ServiceCatalogName.PROVISION_VIRTUAL_SERVER ||
      ServiceCatalogName.S3 ||
      ServiceCatalogName.NFS
        ? (resource['enableDelete'] = deleteFlag)
        : null;

      const updateFlag = this.isUpdateEnable(resource, env);
      resource['projectName'] = env?.projectName || '';
      resource.catalogLevel03 === ServiceCatalogName.PROVISION_VIRTUAL_SERVER ||
      ServiceCatalogName.S3 ||
      ServiceCatalogName.NFS
        ? (resource['enableUpdate'] = updateFlag)
        : null;
    });
    return resources;
  }

  private isUpdateEnable(resource, environment) {
    return (
      environment?.permissions?.includes(PermissionKey.UPDATE) &&
      resource['status'] !== resourcesStatus.DELETED &&
      resource['status'] !== resourcesStatus.PARTIALLYDELETED &&
      resource['status'] !== resourcesStatus.DELETEFAILED &&
      resource['status'] !== resourcesStatus.DELETING &&
      resource['status'] !== resourcesStatus.ERADICATED
    );
  }

  async getPaginatedResourcesList(
    paginationOptions: PaginationQueryDto,
  ): Promise<
    PaginatedResponseDto<ResourceWithComplianceResult<ParsedComplianceResult>>
  > {
    try {
      const userEnvsWithPermissions =
        await this.iamService.findUserEnvironmentsWithPermissions();
      const userEnvWithPermissionsMap = new Map(
        userEnvsWithPermissions.map((envPerm) => [envPerm.envId, envPerm]),
      );
      const envIds = filteringByProjectName(
        userEnvsWithPermissions,
        paginationOptions,
      );
      const resourcesWithRawCompliance =
        await this.resourcesRepository.findByEnvIdsPaginated(
          envIds,
          paginationOptions,
        );

      // Parse compliance result
      const resourceItems: ResourceWithComplianceResult<ParsedComplianceResult>[] =
        resourcesWithRawCompliance.items.map((resource) => {
          return {
            ...resource,
            complianceResult: resource.complianceResult
              ? this.parseComplianceResult(resource.complianceResult)
              : null,
          };
        });
      resourceItems.forEach((resource) => {
        const env = userEnvWithPermissionsMap.get(
          resource.platformContext.envId,
        );
        const deleteFlag =
          env?.permissions?.includes(PermissionKey.DELETE) &&
          this.isDeleted(resource['status']) &&
          resource.catalogType.toLowerCase() !== corpnet.LINUX_CORPNET &&
          resource.catalogType.toLowerCase() !== corpnet.WINDOWS_CORPNET;
        // resource['projectName'] = project?.projectName || '';

        if (
          resource.catalogLevel03 === ServiceCatalogName.PROVISION_MONGO_DB ||
          resource.catalogLevel03 ===
            ServiceCatalogName.PROVISION_POSTGRES_DB ||
          resource.catalogLevel03 === ServiceCatalogName.PROVISION_REDIS_DB ||
          resource.catalogLevel03 === ServiceCatalogName.PROVISION_ORACLE_DB
        ) {
          if (!!DbRequestTypes[resource?.catalogType]) {
            resource['enableDelete'] = deleteFlag;
          }
        } else {
          resource.catalogLevel03 ===
            ServiceCatalogName.PROVISION_VIRTUAL_SERVER ||
          ServiceCatalogName.S3 ||
          ServiceCatalogName.NFS
            ? (resource['enableDelete'] = deleteFlag)
            : null;
        }

        const updateFlag = this.isUpdateEnable(resource, env);
        resource['projectName'] = env?.projectName || '';
        resource.catalogLevel03 ===
          ServiceCatalogName.PROVISION_VIRTUAL_SERVER ||
        ServiceCatalogName.S3 ||
        ServiceCatalogName.NFS
          ? (resource['enableUpdate'] = updateFlag)
          : null;
      });
      return {
        items: resourceItems,
        pageInfo: resourcesWithRawCompliance.pageInfo,
      };
    } catch (err) {
      this.logger.error(err, 'Failed to fetch');
      throw err;
    }
  }

  async updateResourcesSystemUpdate(
    resourcesName: string,
    systemUpdate: object,
  ) {
    const udpatedDoc =
      await this.resourcesRepository.updateResourcesSystemUpdate(
        resourcesName,
        systemUpdate,
      );
    return {
      id: udpatedDoc.id,
      message: 'Resources updated successfully!',
    };
  }

  async updateResourcesSystemUpdateByResourceId(
    resourceId: string,
    systemUpdate: object,
  ) {
    const udpatedDoc =
      await this.resourcesRepository.updateResourcesSystemUpdateByResourceId(
        resourceId,
        systemUpdate,
      );
    return {
      id: udpatedDoc.id,
      message: 'Resources updated successfully!',
    };
  }

  async createResource(res: ResourcesRequestDto) {
    if (res.catalogType === CatalogTypes.NAMESPACE) {
      if (!/^[a-zA-Z0-9\-]+$/.test(res.resourcesName)) {
        throw new BadRequestException(
          'namespace resourceName can only contain alphanumeric characters and hyphens',
        );
      }
      let existingResource;
      try {
        existingResource =
          await this.resourcesRepository.findOneByResourcesName(
            res?.resourcesName,
          );
      } catch (error) {}
      if (existingResource) {
        throw new BadRequestException(
          `Namespace resource ${res?.resourcesName} already exists.`,
        );
      }
    }

    try {
      const serviceRequest: any =
        await this.assetsService.findByServiceRequestObjectId(res.requestId);

      res.requestId = serviceRequest.serviceRequestId
        ? serviceRequest.serviceRequestId
        : res.requestId;
      res.createdBy = serviceRequest.createdBy;
      res.catalogLevel03 = serviceRequest.metadata.serviceCatalog.catalogName;

      if (serviceRequest?.payload?.projectName) {
        const serviceRequestProjectName = serviceRequest.payload.projectName;
        try {
          const [projectEntity]: ProjectEntity[] =
            await this.iamService.getMultiEnvProjectByName(
              serviceRequestProjectName,
            );
          res.projectId = projectEntity.id;
        } catch (error) {
          this.logger.error(error, 'project name error');
          throw error;
        }
      }
      const updatedDoc = await this.resourcesRepository.createResource(res);

      // Notification email should be sent only for the DB creation request
      if (
        updatedDoc.status.toUpperCase() !== 'FAILED' &&
        DbRequestTypes[updatedDoc.catalogType]
      ) {
        try {
          await this.integrationNotificationService.notifyResourceCreationToIntegrationApi(
            updatedDoc,
            'Your request has been created',
            {
              userEmail: serviceRequest.requesterEmail,
              id: serviceRequest.id,
              deeplinkUrl: serviceRequest.payload.deeplinkUrl,
            },
          );
        } catch (error) {
          this.logger.error(error, 'Failed to send notification');
        }
      }
      return {
        id: updatedDoc.id,
        resourceId: updatedDoc.resourceId,
        message: 'Resources created successfully!',
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async getResourcesByInstanceIds(instanceIds: number[]) {
    this.logger.log(
      'instanceIds passing to get resource details',
      JSON.stringify(instanceIds),
    );
    const resource =
      await this.resourcesRepository.findResourcesByInstanceIds(instanceIds);
    this.logger.log('DB response of instance Ids', resource);
    return resource;
  }

  async getResource(resourcesName: string): Promise<ResourcesEntity> {
    return await this.resourcesRepository.findOneByResourcesName(resourcesName);
  }

  async getLatestResourceStatus(
    resourceIds: string[],
  ): Promise<ResourcesEntity[]> {
    const validResourceIds = [];
    const resources =
      await this.resourcesRepository.findResourcesByResourceIds(resourceIds);
    this.logger.log(
      'filitering the resources to get the resource with not ending status',
    );

    this.logger.log('resources received after filitering with status');
    for (const resource of resources) {
      if (this.checkCatalogLevel3(resource)) {
        await this.processResource(resource, validResourceIds);
      }
    }

    return this.updateArray(resources, validResourceIds);
  }
  private updateArray(array1, array2) {
    array2.forEach((obj2) => {
      const index = array1.findIndex((obj1) => obj1.id === obj2.id);
      if (index !== -1) {
        array1.splice(index, 1, obj2);
      }
    });
    return array1;
  }

  private async processResource(
    resource: any,
    validResourceIds: any[],
  ): Promise<void> {
    try {
      const timeDiff = this.getTimeDifference(resource.updatedAt);
      const timeThreshold = +process.env.UPDATED_TIME_MINUTES;

      if (timeDiff > timeThreshold) {
        this.logger.log(
          'getting latest status from compute service for instance:',
          resource?.resourcesDetails?.id,
        );
        await this.refreshResourceStatus(resource, validResourceIds);
      }
    } catch (err) {
      await this.handleResourceError(err, resource, validResourceIds);
    }
  }

  private getTimeDifference(updatedAt: string): number {
    const updatedTime = new Date(updatedAt).getTime();
    const currentTime = new Date().getTime();
    return (currentTime - updatedTime) / (1000 * 60); // Convert to minutes
  }

  private async refreshResourceStatus(
    resource: any,
    validResourceIds: any[],
  ): Promise<void> {
    let vmResponse: any;

    if (this.isCorpnetResource(resource)) {
      vmResponse = await this.computeWrapperService.getBlueVMStatus(
        resource?.resourcesDetails?.id,
      );
    } else if (this.hasVMwareResource(resource)) {
      vmResponse = await this.computeWrapperService.getLatestStatusFromVMWare(
        resource?.resourcesDetails['domain'] as string,
        resource?.resourcesDetails['datacenter'] as string,
        resource.resourcesDetails['cloud']?.id as string,
        resource.resourcesDetails['hostName'] as string,
      );
    } else {
      vmResponse = await this.computeWrapperService.getVMStatus(
        resource?.resourcesDetails?.id,
      );
    }

    this.logger.log(
      'latest status found, now updating resources collection with resourceDetails:',
      vmResponse,
    );
    let updatedResource;
    if (this.hasVMwareResource(resource)) {
      updatedResource = await this.resourcesRepository.findOneByIdAndUpate(
        resource.resourceId,
        {
          resourcesDetails: {
            ...resource.resourcesDetails,
            ...vmResponse.resourceDetails,
          },
          status: vmResponse.status,
          refreshedAt: new Date(),
        },
      );
    } else {
      updatedResource = await this.resourcesRepository.findOneByIdAndUpate(
        resource.resourceId,
        {
          resourcesDetails: vmResponse.instance,
          refreshedAt: new Date(),
          status: vmResponse.instance.status,
        },
      );
    }

    validResourceIds.push(updatedResource);
  }

  private async handleResourceError(
    err: any,
    resource: any,
    validResourceIds: any[],
  ): Promise<void> {
    this.logger.log('error in refreshing resource', err);

    if (err?.status === 404) {
      this.logger.log('VM not found, updating status to DELETED');
      resource.resourcesDetails.status = 'DELETED';

      const errorResource = await this.resourcesRepository.findOneByIdAndUpate(
        resource.resourceId,
        {
          resourcesDetails: resource.resourcesDetails,
          refreshedAt: new Date(),
        },
      );

      validResourceIds.push(errorResource);
    } else {
      throw err;
    }
  }

  async getLatestStatus(resourceId: string): Promise<ResourcesEntity> {
    const resource: any =
      await this.resourcesRepository.findOneByResourceId(resourceId);
    this.logger.log(`${resourceId} found!`);
    if (
      resource?.catalogLevel03 ===
        ServiceCatalogName.PROVISION_VIRTUAL_SERVER ||
      resource?.catalogLevel03 === ServiceCatalogName.BULK_VM_IMPORT
    ) {
      try {
        this.logger.log(
          'getting latest status from compute service for instance:',
          resource.resourcesDetails.id,
        );
        const vmResponse = this.isCorpnetResource(resource)
          ? await this.computeWrapperService.getBlueVMStatus(
              resource.resourcesDetails.id,
            )
          : await this.computeWrapperService.getVMStatus(
              resource.resourcesDetails.id,
            );

        this.logger.log(
          'latest status found now updating resources collection with resourceDetails:',
          vmResponse,
        );
        await this.resourcesRepository.findOneByIdAndUpate(resourceId, {
          resourcesDetails: vmResponse.instance,
          refreshedAt: new Date(),
        });
        return this.getResourceByResourceId(resourceId);
      } catch (err) {
        this.logger.log('error in refreshing resource', err);
        if (err?.status == 404) {
          //if vm is already deleted it gives 404 from morphues, update the status in nebula as deleted
          resource.resourcesDetails.status = 'DELETED';
          return await this.resourcesRepository.findOneByIdAndUpate(
            resourceId,
            {
              resourcesDetails: resource.resourcesDetails,
              refreshedAt: new Date(),
            },
          );
        } else throw err;
      }
    }
  }

  async deleteResource(resourceIds, deeplinkUrl) {
    const messages: Message[] = [];
    this.logger.debug(`Delete Resource with ids ${resourceIds}`);

    const req = RequestContext.currentContext.req;
    const requestorEmail = req.user?.email;
    const requestorPID = req?.user?.userId || null;

    for (const resourceId of resourceIds) {
      const resource = await this.getResourceById(resourceId);

      if (
        resource.catalogType.toLowerCase() === corpnet.LINUX_CORPNET ||
        resource.catalogType.toLowerCase() === corpnet.WINDOWS_CORPNET
      ) {
        this.logger.log(`cannot delete the selected resource ${resourceId}`);
        throw new BadRequestException(
          `cannot delete the selected resource ${resourceId}`,
        );
      }
      if (
        !this.checkCatalogLevel3(resource) &&
        !this.checkDbResource(resource)
      ) {
        this.logger.debug(`Invalid resource ids${resourceId}`);
        throw new BadRequestException(`Invalid resource Ids${resourceId}`);
      }

      if (!this.isDeleted(resource.status)) {
        this.logger.debug(
          `Resource already been requested to be deleted id: ${resourceId}`,
        );
        throw new BadRequestException(
          `Resource already been requested to be deleted id: ${resourceId}`,
        );
      }

      if (this.checkDbResource(resource)) {
        this.buildMessageByCatalogTypeForDB(
          resource,
          resourceId,
          requestorEmail,
          requestorPID,
          messages,
          deeplinkUrl,
        );

        for (const childId of resource.childId) {
          const childResource = await this.getResourceById(childId);
          this.buildMessageByCatalogType(
            childResource,
            childId,
            requestorEmail,
            requestorPID,
            messages,
            deeplinkUrl,
          );
        }
      } else if (this.checkSpecResource(resource)) {
        this.buildMessageByCatalogTypeForSpec(
          resource,
          resourceId,
          requestorEmail,
          requestorPID,
          messages,
          deeplinkUrl,
        );
      } else {
        this.buildMessageByCatalogType(
          resource,
          resourceId,
          requestorEmail,
          requestorPID,
          messages,
          deeplinkUrl,
        );
      }
    }
    const requestTypes = messages.map((types) => types.requestType);
    const uniqueRequestTypes = [...new Set(requestTypes)];

    if (
      uniqueRequestTypes.includes(RequestType.DELETE_VM) &&
      uniqueRequestTypes.length > 1
    ) {
      throw new BadRequestException(
        'please check and select same type of RED VMs',
      );
    }

    if (requestTypes.includes(RequestType.DELETE_VM)) {
      this.logger.log('deleteVm resources passing to Queue', messages);
      for (const item of messages) {
        const resourceId = item.resourceId;
        await this.updateResourceDetailsStatusByResourceId(
          resourceId,
          DeleteVM.DELETING,
        );
      }
      const newServiceRequest = await this.serviceRequestChange(
        messages,
        false,
      );
      return newServiceRequest;
    } else if (requestTypes.includes(RequestType.DELETE_DB)) {
      this.logger.log('deleteDB resources passing to Queue', messages);

      const newServiceRequest = await this.serviceRequestChangeDb(messages);
      return newServiceRequest;
    } else if (requestTypes.includes(RequestType.DELETE_PUBLIC_CLOUD)) {
      this.logger.log(
        'Delete Public Cloud resources passing to Queue',
        messages,
      );
      for (const item of messages) {
        const resourceId = item.resourceId;
        await this.updateResourceDetailsStatusByResourceId(
          resourceId,
          DeleteDB.DELETING,
        );
      }
      const newServiceRequest =
        await this.serviceRequestDeletePublicCloud(messages);
      return newServiceRequest;
    } else if (requestTypes.includes(RequestType.DELETE_VM_VMWARE)) {
      this.logger.log('delete VMWARE Vm resources passing to Queue', messages);
      for (const item of messages) {
        const resourceId = item.resourceId;
        await this.updateResourceDetailsStatusByResourceId(
          resourceId,
          DeleteVM.DELETING,
        );
      }
      const newServiceRequest = await this.serviceRequestChange(messages, true);
      return newServiceRequest;
    } else {
      for (const item of messages) {
        const resourceId = item.resourceId;
        await this.updateResourceDetailsStatusByResourceId(
          resourceId,
          DeleteVM.DELETING,
        );
      }
      const groupMessages = this.groupBy(messages, (item) => item.requestType);
      this.logger.log('groupMessages in response at', groupMessages);
      for (const key in groupMessages) {
        const queueMessage = groupMessages[key];
        await this.sendMessagesToQueue(queueMessage, key as RequestType);
      }
    }

    return {
      successCode: HttpStatus.OK,
      message: 'Resource deletion request has been successfully queued',
    };
  }

  groupBy<T>(array: T[], key: (item: T) => string): Record<string, T[]> {
    return array.reduce(
      (result, currentItem) => {
        const groupKey = key(currentItem);
        if (!result[groupKey]) {
          result[groupKey] = [];
        }
        result[groupKey].push(currentItem);
        return result;
      },
      {} as Record<string, T[]>,
    );
  }

  async getResourceById(resourceId: string): Promise<ResourcesEntity> {
    return await this.resourcesRepository.findOneByResourceId(resourceId);
  }

  private checkCatalogLevel3(resource: ResourcesEntity): boolean {
    return (
      resource?.catalogLevel03 ===
        ServiceCatalogName.PROVISION_VIRTUAL_SERVER ||
      resource?.catalogLevel03 === ServiceCatalogName.BULK_VM_IMPORT ||
      resource?.catalogLevel03 === ServiceCatalogName.S3 ||
      resource?.catalogLevel03 === ServiceCatalogName.NFS ||
      resource?.catalogLevel03 === ServiceCatalogName.PROVISION_MONGO_DB ||
      resource?.catalogLevel03 === ServiceCatalogName.PROVISION_POSTGRES_DB ||
      resource?.catalogLevel03 === ServiceCatalogName.PROVISION_REDIS_DB ||
      resource?.catalogLevel03 === ServiceCatalogName.PROVISION_ORACLE_DB ||
      resource?.catalogLevel03 === ServiceCatalogName.SPEC_FLOW_S3 ||
      resource?.catalogLevel03 === ServiceCatalogName.SPEC_FLOW_EC2 ||
      resource?.catalogLevel03 === ServiceCatalogName.SPEC_FLOW_EKS
    );
  }

  private checkDbResource(resource: ResourcesEntity): boolean {
    if (
      resource?.catalogLevel03 === ServiceCatalogName.PROVISION_MONGO_DB ||
      resource?.catalogLevel03 === ServiceCatalogName.PROVISION_POSTGRES_DB ||
      resource?.catalogLevel03 === ServiceCatalogName.PROVISION_REDIS_DB ||
      resource?.catalogLevel03 === ServiceCatalogName.PROVISION_ORACLE_DB
    ) {
      return !!DbRequestTypes[resource?.catalogType];
    } else {
      return false;
    }
  }

  private checkSpecResource(resource: ResourcesEntity): boolean {
    if (
      resource?.catalogLevel03 === ServiceCatalogName.SPEC_FLOW_EC2 ||
      resource?.catalogLevel03 === ServiceCatalogName.SPEC_FLOW_S3 ||
      resource?.catalogLevel03 === ServiceCatalogName.SPEC_FLOW_EKS
    ) {
      return true;
    } else {
      return false;
    }
  }

  private buildMessageByCatalogType(
    resource: ResourcesEntity,
    resourceId: string,
    requestorEmail: string,
    requestorPID: string,
    messages: Message[],
    deeplinkUrl: string,
  ): void {
    const resourcesDetails: any = resource.resourcesDetails;
    const message: Message = {
      resourceId: resourceId,
      hostName: resource.resourcesName,
      IPv4Address: resourcesDetails?.config?.customOptions?.IPV4Addr,
      IPv6Address: resourcesDetails?.config?.customOptions?.IPV6Addr,
      requestorEmail: requestorEmail,
      vmId: (resourcesDetails as ResourcesDetailsDto)?.id,
      requestorPID: requestorPID,
      requestType: this.requestTypes(
        resource?.catalogType?.trim().toLowerCase(),
      ),
      bucketName: resourcesDetails?.bucket?.name,
      fileSystemName:
        resource?.catalogType.toLowerCase() === CatalogTypes.NFS
          ? resourcesDetails?.name
          : {},
      deeplinkUrl: deeplinkUrl,
      domain: resourcesDetails?.domain,
    };
    messages.push(message);
  }

  private buildMessageByCatalogTypeForDB(
    resource: ResourcesEntity,
    resourceId: string,
    requestorEmail: string,
    requestorPID: string,
    messages: Message[],
    deeplinkUrl: string,
  ): void {
    const resourcesDetails: any = resource.resourcesDetails;
    const message: Message = {
      resourceId: resourceId,
      requestId: resource.requestId,
      dbParams: resourcesDetails.DBParams,
      childId: resource.childId,
      catalogType: resource.catalogType,
      hostName: resource.resourcesName,
      IPv4Address: resourcesDetails.DBParams?.hosts
        ? resourcesDetails.DBParams?.hosts
            .map((host) => {
              return host.host_ip.join(', ');
            })
            .join(', ')
        : resourcesDetails.DBParams?.host_ip?.join(', '),
      IPv6Address: resourcesDetails?.ipv6Addr?.join(', '),
      requestorEmail: requestorEmail,
      requestorPID: requestorPID,
      requestType: this.requestTypes(
        resource?.catalogType?.trim().toLowerCase(),
      ),
      deeplinkUrl: deeplinkUrl,
    };
    messages.push(message);
  }

  private buildMessageByCatalogTypeForSpec(
    resource: ResourcesEntity,
    resourceId: string,
    requestorEmail: string,
    requestorPID: string,
    messages: Message[],
    deeplinkUrl: string,
  ): void {
    const resourcesDetails: any = resource.resourcesDetails;
    const message: DeleteSpecMessage = {
      resourceId: resourceId,
      requestId: resource.requestId,
      projectId: resourcesDetails.projectId,
      requestorEmail: requestorEmail,
      requestorPID: requestorPID,
      requestType: this.requestTypes(
        resource?.catalogType?.trim().toLowerCase(),
      ),
      deeplinkUrl: deeplinkUrl,
    };
    messages.push(message);
  }

  private async serviceRequestChange(payload, isVMWareResource) {
    const serviceRequest = {
      metadata: {
        serviceCatalog: {
          catalogName: isVMWareResource
            ? ServiceCatalogName.DELETE_VM_VMWARE
            : ServiceCatalogName.DELETE_VM,
          catalogType: ServiceCatalogType.IAAS,
        },
      },
      payload: payload,
      requestType: payload[0].requestType,

      status: RequestStatus.PENDING_APPROVAL,
    };
    this.logger.debug('serviceRequest for Delete VM ', serviceRequest);
    const dbResponse = await this.assetsService.create(serviceRequest);

    const queued =
      await this.assetsService.queueIfApprovalNotRequired(dbResponse);

    return {
      id: dbResponse.serviceRequestId,
      message: `VM Deletion Request submitted for ${
        queued ? 'processing' : 'approval'
      }`,
    };
  }

  private async serviceRequestChangeDb(payload) {
    const serviceRequest = {
      metadata: {
        serviceCatalog: {
          catalogName: ServiceCatalogName.DELETE_DB,
          catalogType: ServiceCatalogType.DBAAS,
        },
      },
      payload: payload,
      requestType: payload[0].requestType,
      status: RequestStatus.PENDING_APPROVAL,
    };

    this.logger.debug('serviceRequest for Delete DB ', serviceRequest);
    const dbResponse = await this.assetsService.create(serviceRequest);

    const queued =
      await this.assetsService.queueIfApprovalNotRequired(dbResponse);

    return {
      id: dbResponse.serviceRequestId,
      message: `DB Deletion Request submitted for ${
        queued ? 'processing' : 'approval'
      }`,
    };
  }

  private async serviceRequestDeletePublicCloud(payload) {
    const serviceRequest = {
      metadata: {
        serviceCatalog: {
          catalogName: ServiceCatalogName.DELETE_PUBLIC_CLOUD,
          catalogType: ServiceCatalogType.Specflow,
        },
      },
      payload: payload,
      requestType: payload[0].requestType,
      status: RequestStatus.PENDING_APPROVAL,
    };

    this.logger.debug(
      'serviceRequest to delete public cloud asset ',
      serviceRequest,
    );
    const dbResponse = await this.assetsService.create(serviceRequest);

    const queued =
      await this.assetsService.queueIfApprovalNotRequired(dbResponse);

    return {
      id: dbResponse.serviceRequestId,
      message: `Asset Deletion Request submitted for ${
        queued ? 'processing' : 'approval'
      }`,
    };
  }

  private requestTypes(catalogType): RequestType {
    switch (catalogType) {
      case CatalogTypes.LINUX_8_9:
      case CatalogTypes.UBUNTU:
      case CatalogTypes.WINDOWS:
        return RequestType.DELETE_VM;
      case CatalogTypes.LINUX_CORPNET:
      case CatalogTypes.WINDOWS_CORPNET:
        return RequestType.DELETE_BLUE_VM;
      case CatalogTypes.S3:
        return RequestType.DELETE_S3;
      case CatalogTypes.NFS:
        return RequestType.DELETE_NFS;
      case CatalogTypes.MONGODB_6:
      case CatalogTypes.MONGODB_7:
      case CatalogTypes.POSTGRESDB_15:
      case CatalogTypes.POSTGRESDB_16:
      case CatalogTypes.REDISDB_6:
      case CatalogTypes.REDISDB_7:
      case CatalogTypes.ORACLEDB_19C:
      case CatalogTypes.ORACLEDB_21C:
        return RequestType.DELETE_DB;
      case CatalogTypes.SPECFLOW:
        return RequestType.DELETE_PUBLIC_CLOUD;
      case CatalogTypes.LINUXV3:
      case CatalogTypes.LINUXV3ADMIN:
      case CatalogTypes.UBUNTUV3:
      case CatalogTypes.UBUNTUV3ADMIN:
      case CatalogTypes.WINDOWSV3:
      case CatalogTypes.WINDOWSV3ADMIN:
        return RequestType.DELETE_VM_VMWARE;
    }
  }

  private isCorpnetResource(resource: ResourcesEntity): boolean {
    const catalogType = resource?.catalogType?.trim().toLowerCase();
    return (
      catalogType === CATALOGTYPE.LINUX_CORPNET ||
      catalogType === CATALOGTYPE.WINDOWS_CORPNET
    );
  }
  private hasVMwareResource(resource: ResourcesEntity): boolean {
    const catalogType = resource?.catalogType?.trim();
    return (
      catalogType === CATALOGV3.linux ||
      catalogType === CATALOGV3.linuxAdmin ||
      catalogType === CATALOGV3.ubuntu ||
      catalogType === CATALOGV3.ubuntuAdmin ||
      catalogType === CATALOGV3.windows ||
      catalogType === CATALOGV3.windowsAdmin
    );
  }

  private async sendMessagesToQueue(
    messages: Message[],
    requestType: RequestType,
  ) {
    if (messages.length > 0) {
      this.logger.debug('Pushing message to rmqService', messages);
      await this.rmqService.pushMessage(requestType, messages);
    }
  }

  async getResourceByResourceId(
    resourceId: string,
    extendedFetch: boolean = false,
  ): Promise<ResourcesEntity | ExtendedResource> {
    const req = RequestContext.currentContext.req;

    const resource =
      await this.resourcesRepository.findOneByResourceId(resourceId);
    if (resource && req.headers.authorization) {
      // check if it has auth headers which means call not made from n8n
      const userEnvsWithPermissions =
        await this.iamService.findUserEnvironmentsWithPermissions();
      const env = userEnvsWithPermissions.find(
        (envPerm) => envPerm.envId === resource.platformContext.envId,
      );
      const deleteFlag =
        env?.permissions?.includes(PermissionKey.DELETE) &&
        this.isDeleted(resource['status']) &&
        resource.catalogType.toLowerCase() !== corpnet.LINUX_CORPNET &&
        resource.catalogType.toLowerCase() !== corpnet.WINDOWS_CORPNET;
      resource['projectName'] = env?.projectName || '';
      resource.catalogLevel03 === ServiceCatalogName.PROVISION_VIRTUAL_SERVER
        ? (resource['enableDelete'] = deleteFlag)
        : null;

      const updateFlag = this.isUpdateEnable(resource, env);
      resource['projectName'] = env?.projectName || '';
      resource.catalogLevel03 === ServiceCatalogName.PROVISION_VIRTUAL_SERVER
        ? (resource['enableUpdate'] = updateFlag)
        : null;
    }
    if (!resource) return null;

    if (!extendedFetch) return resource;

    const extendedResource = await this.getExtendedResource(resource);
    return extendedResource;
  }

  async updateResourceByResourceId(resourceId: string, updateFields: object) {
    return await this.resourcesRepository.findOneByIdAndUpate(
      resourceId,
      updateFields,
    );
  }

  async updateResourceDetailsStatusByResourceId(
    resourceId: string,
    status: string,
  ) {
    this.logger.debug(
      `Updating the resource with id ${resourceId} with status ${status}`,
    );
    return await this.resourcesRepository.updateResourceStatus(
      resourceId,
      status,
    );
  }

  private async getExtendedResource(
    resource: ResourcesEntity,
  ): Promise<ExtendedResource> {
    const serviceRequest = await this.assetsService.findByServiceRequestId(
      resource.requestId,
    );

    const catalogL4 = await this.iamService.findCatalogLevel4ItemByRequestType(
      serviceRequest.requestType,
    );

    let resourceType = ResourceType.DB_VM;
    if (
      [
        RequestType.CREATE_VM_LINUX89,
        RequestType.CREATE_VM_WINDOWS,
        RequestType.CREATE_VM_LINUX89,
        RequestType.CREATE_VM_LINUX7,
      ].includes(serviceRequest.requestType)
    ) {
      resourceType = ResourceType.STANDALONE_VM;
    }
    const extensions = { serviceRequest, catalogL4, resourceType };

    const extendedResource = { ...resource, extensions };
    return extendedResource;
  }

  private async checkVmStatusForAction(
    instanceId: number,
    resourceId: string,
    action: string,
    requiredStatus: string[],
    resourceDetails: any,
    resource: ResourcesEntity,
  ) {
    try {
      const vmResponse = this.isCorpnetResource(resource)
        ? await this.computeWrapperService.getBlueVMStatus(instanceId)
        : await this.computeWrapperService.getVMStatus(instanceId);

      this.logger.debug(
        'Received the latest status of VM',
        vmResponse?.instance?.status,
      );
      if (requiredStatus.includes(vmResponse?.instance?.status.toLowerCase())) {
        return true;
      } else {
        this.logger.debug(
          `Instance is not in required status to perform action, currently in ${vmResponse.instance.status || vmResponse.status} status`,
        );
        await this.resourcesRepository.findOneByIdAndUpate(resourceId, {
          resourcesDetails: vmResponse.instance,
          refreshedAt: new Date(),
        });
        throw new BadRequestException(
          `Instance is not in required status to perform action, current status is ${vmResponse.instance.status}`,
        );
      }
    } catch (err) {
      if (err?.status === 404) {
        this.logger.debug(
          'Updating the status of VM to DELETED in case if its not in morpheus',
        );
        //if vm is already deleted it gives 404 from morphues, update the status in nebula as deleted
        resourceDetails.status = 'DELETED';
        await this.resourcesRepository.findOneByIdAndUpate(resourceId, {
          resourcesDetails: resourceDetails,
          refreshedAt: new Date(),
        });

        throw new BadRequestException(
          `Instance Not found, Hence cannot ${action} this resource`,
        );
      } else {
        this.logger.debug('Action failed with error', err);
        throw err;
      }
    }
  }

  private async checkVMWareStatusForAction(
    instanceId: number,
    resourceId: string,
    action: string,
    requiredStatus: string[],
    resourceDetails: any,
    resource: ResourcesEntity,
  ) {
    try {
      const vmResponse =
        await this.computeWrapperService.getLatestStatusFromVMWare(
          resource?.resourcesDetails['domain'] as string,
          resource?.resourcesDetails['datacenter'] as string,
          resource.resourcesDetails['cloud']?.id as string,
          resource.resourcesDetails['hostName'] as string,
        );
      this.logger.debug('Received the latest status of VM', vmResponse?.status);
      if (requiredStatus.includes(vmResponse?.status.toLowerCase())) {
        return true;
      } else {
        this.logger.debug(
          `Instance is not in required status to perform action, currently in ${vmResponse.status} status`,
        );
        await this.resourcesRepository.findOneByIdAndUpate(resourceId, {
          resourcesDetails: {
            ...resource.resourcesDetails,
            ...vmResponse.resourceDetails,
          },
          status: vmResponse.status,
          refreshedAt: new Date(),
        });
        throw new BadRequestException(
          `Instance is not in required status to perform action, current status is ${vmResponse.status}`,
        );
      }
    } catch (err) {
      if (err?.status === 500) {
        this.logger.debug(
          'Updating the status of VM to DELETED in case if its not in vCenter',
        );
        //if vm is already deleted it gives 500 from vCenter, update the status in nebula as deleted
        await this.resourcesRepository.findOneByIdAndUpate(resourceId, {
          status: 'DELETED',
          refreshedAt: new Date(),
        });

        throw new BadRequestException(
          `Instance Not found, Hence cannot ${action} this resource`,
        );
      } else {
        this.logger.debug('Action failed with error', err);
        throw err;
      }
    }
  }
  private getRequestDetails() {
    const req = RequestContext.currentContext.req;
    return {
      requestorEmail: req.user?.email,
      requestorPID: req?.user?.userId || null,
    };
  }

  async updateAction(createdAction: any) {
    await Promise.all(
      createdAction.map((data) =>
        this.actionService.update({
          actionId: data.actionId,
          entityId: data.entityId,
          status: ActionStatus.REQUEST_RAISED,
        }),
      ),
    );
  }

  private async processVmResources(
    resourceIds: string[],
    action: VMAction,
    requestType: RequestType,
    requiredStatuses: string[],
    checkVmStatusForAction: (
      vmId: string,
      resourceId: string,
      action: string,
      requiredStatuses: string[],
      resourceDetails: any,
      resource: ResourcesEntity,
    ) => Promise<boolean>,
    rmqService: any,
    logger: any,
    getRequestDetails: () => { requestorEmail: string; requestorPID: string },
    resourcesRepository: any,
    createdAction?: any,
  ): Promise<ResourceUpdateSuccessDto> {
    const validResourceIds: string[] = [];

    const requestorEmail = getRequestDetails().requestorEmail;
    const requestorPID = getRequestDetails().requestorPID;

    if (resourceIds.length) {
      this.logger.log('resource fetched successfully!');
      await Promise.all(
        resourceIds.map(async (resourceId) => {
          const response: any =
            await resourcesRepository.findOneByResourceId(resourceId);
          if (
            response?.catalogLevel03 ===
            ServiceCatalogName.PROVISION_VIRTUAL_SERVER
          ) {
            const canEnableAction = this.hasVMwareResource(response)
              ? await this.checkVMWareStatusForAction(
                  response?.resourcesDetails.id,
                  resourceId,
                  action,
                  requiredStatuses,
                  response?.resourcesDetails,
                  response,
                )
              : await checkVmStatusForAction(
                  response?.resourcesDetails.id,
                  resourceId,
                  action,
                  requiredStatuses,
                  response?.resourcesDetails,
                  response,
                );

            if (canEnableAction) {
              validResourceIds.push(resourceId);
            } else {
              logger.debug(
                `Action cannot be enabled for resourceId: ${resourceId}`,
              );
            }
          } else {
            logger.debug(
              `Cannot perform action as it's not a VM resource for resourceId: ${resourceId}`,
            );
          }
        }),
      );
    } else {
      this.logger.log('resourceIds empty!');
    }

    // Create and push the message if valid resources exist
    if (validResourceIds.length > 0) {
      const message: any = {
        resourceIds: validResourceIds,
        requestorEmail,
        requestorPID,
        createdAction,
      };

      logger.debug(`Pushing valid resourceIds to queue for ${action}`, message);

      await rmqService.pushMessage(requestType, message);
      if(createdAction){
         await this.updateAction(createdAction);
      }

      return {
        successCode: HttpStatus.OK,
        message: `VM ${action} request(s) for ${validResourceIds.length} resourceIDs have been successfully queued`,
      };
    } else {
      throw new BadRequestException(`No valid resources found for ${action}.`);
    }
  }
  async startVmResources(
    resourceIds: string[],
    createdAction?: any,
  ): Promise<ResourceUpdateSuccessDto> {
    return this.processVmResources(
      resourceIds,
      VMAction.START,
      RequestType.START_VM,
      [RequiredStatus.START, RequiredStatus.STARTNOTRUNNING],
      this.checkVmStatusForAction.bind(this),
      this.rmqService,
      this.logger,
      this.getRequestDetails.bind(this),
      this.resourcesRepository,
      createdAction,
    );
  }

  async stopVmResources(
    resourceIds: string[],
    createdAction?: any,
  ): Promise<ResourceUpdateSuccessDto> {
    return this.processVmResources(
      resourceIds,
      VMAction.STOP,
      RequestType.STOP_VM,
      [RequiredStatus.STOP, RequiredStatus.FAILED],
      this.checkVmStatusForAction.bind(this),
      this.rmqService,
      this.logger,
      this.getRequestDetails.bind(this),
      this.resourcesRepository,
      createdAction,
    );
  }
  async resetVmResources(
    resourceIds: string[],
    createdAction?: any,
  ): Promise<ResourceUpdateSuccessDto> {
    return this.processVmResources(
      resourceIds,
      VMAction.RESET,
      RequestType.RESET_VM,
      [RequiredStatus.RESTART, RequiredStatus.FAILED],
      this.checkVmStatusForAction.bind(this),
      this.rmqService,
      this.logger,
      this.getRequestDetails.bind(this),
      this.resourcesRepository,
      createdAction,
    );
  }
  async suspendVMResources(
    resourceIds: string[],
    createdAction?: any,
  ): Promise<ResourceUpdateSuccessDto> {
    return this.processVmResources(
      resourceIds,
      VMAction.SUSPEND,
      RequestType.SUSPEND_VM,
      [RequiredStatus.RESTART, RequiredStatus.FAILED],
      this.checkVmStatusForAction.bind(this),
      this.rmqService,
      this.logger,
      this.getRequestDetails.bind(this),
      this.resourcesRepository,
      createdAction,
    );
  }

  async restartVmResources(
    resourceIds: string[],
    createdAction?: any,
  ): Promise<ResourceUpdateSuccessDto> {
    return this.processVmResources(
      resourceIds,
      VMAction.RESTART,
      RequestType.RESTART_VM,
      [RequiredStatus.RESTART, RequiredStatus.FAILED],
      this.checkVmStatusForAction.bind(this),
      this.rmqService,
      this.logger,
      this.getRequestDetails.bind(this),
      this.resourcesRepository,
      createdAction,
    );
  }

  private getActionType(action: string) {
    switch (action.toLowerCase()) {
      case VMAction.RESTART:
        return ActionType.RESTART_VM;
      case VMAction.START:
        return ActionType.START_VM;
      case VMAction.STOP:
        return ActionType.STOP_VM;
      case VMAction.RESET:
        return ActionType.RESET_VM;
      case VMAction.SUSPEND:
        return ActionType.SUSPEND_VM;
      default:
        throw new Error(`Unsupported VM action: ${action}`);
    }
  }

  async createAction(action: string, resourceIds: string[]) {
   return await Promise.all(
      resourceIds.map((data) =>
        this.actionService.create({
          actionTypeId: this.getActionType(action),
          entityId: data,
          entityType: COLLECTIONS.RESOURCES,
          status: ActionStatus.STARTED,
        }),
      ),
    );
  }

  async triggerAction(action: string, resourceIds: string[]) {
    const createAction = await this.createAction(action, resourceIds);
    switch (action.toLowerCase()) {
      case VMAction.RESTART:
        return this.restartVmResources(resourceIds, createAction);
      case VMAction.START:
        return this.startVmResources(resourceIds, createAction);
      case VMAction.STOP:
        return this.stopVmResources(resourceIds, createAction);
      case VMAction.RESET:
        return this.resetVmResources(resourceIds, createAction);
      case VMAction.SUSPEND:
        return this.suspendVMResources(resourceIds, createAction);
      default:
        throw new Error(`Unsupported VM action: ${action}`);
    }
  }

  async updateResourceAccessKey(resourceId: string) {
    const resourceIdDetails: any = await this.getResourceById(resourceId);
    this.logger.log('resource received using resourceId', resourceIdDetails);
    const updatedKeys = resourceIdDetails?.resourcesDetails?.accessKey.map(
      (subArray) =>
        subArray.map((item) => ({
          ...item,
          secret_access_key: SecretAccessKey.SECRETACCESSKEY,
        })),
    );
    this.logger.debug('updated secretAcccessKey field', updatedKeys);
    const response = await this.resourcesRepository.updateResourceAccessKey(
      resourceId,
      updatedKeys,
      IS_COPIED_FIELD,
    );
    this.logger.log(
      'response received after updating with secretkey',
      response,
    );
    return response;
  }

  async regenerateKeys(resourceId: string, regenerateUser) {
    const resourceIdDetails: any = await this.getResourceById(resourceId);
    const accessKeys = resourceIdDetails?.resourcesDetails?.accessKey; //filter
    const dataCenterName = resourceIdDetails?.resourcesDetails?.dataCenterName;
    this.logger.log(
      `calling the regenating key method for dataCenter${dataCenterName}`,
    );
    const regenerateFilter = [];
    accessKeys.forEach((subarray) => {
      return regenerateUser.filter((item) => {
        if (
          subarray[0].user.name.split('/')[1] == item.userName &&
          item.regenerateCheck
        ) {
          regenerateFilter.push(subarray);
        }
      });
    });

    const requestorEmail = this.getRequestDetails().requestorEmail;
    const requestorPID = this.getRequestDetails().requestorPID;

    if (!regenerateFilter.length) {
      throw new BadRequestException(`No valid resources found`);
    }

    // Delete old keys
    const deletionPromises = regenerateFilter.map((subArray) =>
      this.storageS3Service.deleteAccessKeys(subArray[0].name, dataCenterName),
    );
    await Promise.all(deletionPromises);

    // Generate new keys
    const generationPromises = regenerateFilter.map((subArray) =>
      this.storageS3Service.generateAccessKeys({
        user: { id: subArray[0].user.id },
        dataCenterName,
      }),
    );
    const regeneratedKeys = await Promise.all(generationPromises);

    //db update
    const updatedKeys = regenerateFilter.map((subArray, index) =>
      subArray.map((item, idx) => ({
        ...item,
        name: regeneratedKeys[index].items[idx].name,
        secret_access_key: this.encryptionService.encrypt(
          regeneratedKeys[index].items[idx].secret_access_key,
        ),
      })),
    );
    const newAccessKeys = [];
    accessKeys.forEach((e) => {
      const up = updatedKeys.filter(
        (x) => x[0].user.name === e[0].user.name,
      )[0];
      if (up?.length) {
        newAccessKeys.push(up);
      } else newAccessKeys.push(e);
    });
    const response = await this.resourcesRepository.updateResourceAccessKey(
      resourceId,
      newAccessKeys,
      IS_REGENERATED_FIELD,
    );

    this.logger.log('response received after updating with new keys', response);

    const message: any = {
      resourceId,
      requestorEmail,
      requestorPID,
      regenerateUser,
    };
    await this.rmqService.pushMessage(RequestType.REGENERATE_KEYS, message);

    return response;
  }

  async modifyResource(resourceId: string, data: ModifyResourceDto) {
    this.logger.log(
      'getting resource details from db for modifying resource:',
      resourceId,
    );
    const resource = await this.getResourceByResourceId(resourceId);

    const modifyData = {
      ...data.resourceData,
      resourceId,
      requestId: resource.requestId,
      PID: this.getUserDetail().requestorPID,
    };
    this.logger.log(
      'Modifying resource for ',
      resourceId,
      'with catalogType:',
      resource.catalogType,
    );
    switch (resource.catalogType.toLowerCase()) {
      case CatalogTypes.S3.toLowerCase():
        return await this.storageS3Service.modifyS3Bucket(modifyData);

      // add  request types for other Resources here

      default:
        throw new BadRequestException(
          'Modify is not configured for the requested resource:',
          resource.catalogType,
        );
    }
  }

  private resourcePayload(resource, dataCenterName) {
    return {
      bucketName: resource.resourcesDetails?.bucket?.name,
      fileSystemName: resource.resourcesDetails?.name,
      payload: {
        nfs: {
          v3Enabled: resource.resourcesDetails?.nfs?.v3_enabled,
          v41Enabled: resource.resourcesDetails?.nfs?.v4_1_enabled,
        },
        dataCenterName: dataCenterName,
      },
    };
  }

  async restoreStorage(resourceId: string, data: RestoreResourceDto) {
    const resource: any = await this.getResourceById(resourceId);
    const dataCenterName = resource?.resourcesDetails?.dataCenterName;
    this.logger.log('datacenter in service to restore storage', dataCenterName);
    const restorePayload = this.resourcePayload(resource, dataCenterName);
    const catalogType = resource?.catalogType?.trim().toLowerCase();
    switch (catalogType) {
      case CatalogTypes.S3:
        this.logger.log('calling restore S3');
        return await this.restoreS3(
          restorePayload.bucketName,
          resourceId,
          resource,
          catalogType,
          data,
          dataCenterName,
        );
      case CatalogTypes.NFS:
        this.logger.log('calling restore NFS');
        return await this.restoreNFS(
          restorePayload.fileSystemName,
          restorePayload.payload,
          resourceId,
          catalogType,
          resource,
          data,
        );
      default:
        this.logger.error(catalogType, 'wrong catalogType');
        throw new BadRequestException(`wrong CatalogType ${catalogType}`);
    }
  }

  async restoreS3(
    bucketName,
    resourceId,
    resource,
    catalogType,
    data,
    dataCenterName,
  ) {
    let status;
    let message;
    try {
      this.logger.log('calling storageS3Service to restore bucket');
      await this.storageS3Service.restoreBucket(bucketName, dataCenterName);
      status = StorageStatus.ACTIVE;
      message = `${catalogType} Storage restored sucessfully`;
    } catch (error) {
      status = StorageStatus.FAILED;
      message = error?.response?.errors[0]?.message;
      this.logger.error(error, 'error occured while restoring S3 storage');
    }
    this.logger.log('updating the DB with restore status');
    await this.updateResourceDetailsStatusByResourceId(resourceId, status);
    this.logger.log('sending notification after restore');
    await this.sendNotification(resource, status, data);
    return {
      statusCode:
        status === StorageStatus.ACTIVE
          ? HttpStatus.OK
          : HttpStatus.BAD_REQUEST,
      message: message,
    };
  }

  async restoreNFS(
    fileSystemName,
    payload,
    resourceId,
    catalogType,
    resource,
    data,
  ) {
    let status;
    let message;
    try {
      this.logger.log('calling storageNFSService to restore fileSystem');
      await this.storageNFSService.restoreNFS(fileSystemName, payload);
      status = StorageStatus.ACTIVE;
      message = `${catalogType} Storage restored sucessfully`;
    } catch (error) {
      status = StorageStatus.FAILED;
      message = error?.response?.errors[0].message;
      this.logger.error(error, 'error occured while restoring NFS storage');
    }
    this.logger.log('updating the DB with restore status');
    await this.updateResourceDetailsStatusByResourceId(resourceId, status);
    this.logger.log('sending notification after restore');
    await this.sendNotification(resource, status, data);
    return {
      statusCode:
        status === StorageStatus.ACTIVE
          ? HttpStatus.OK
          : HttpStatus.BAD_REQUEST,
      message: message,
    };
  }

  private getUserDetail() {
    const req = RequestContext.currentContext.req;
    return {
      requestorEmail: req.user?.email,
      requestorPID: req?.user?.userId || null,
    };
  }

  async sendNotification(resource, status, data) {
    const userDetail = this.getUserDetail();
    try {
      this.logger.log(
        'calling integration service to send notification for restore storage',
      );
      await this.integrationNotificationService.notifyPureStorageResourceRestoreToIntegrationApi(
        resource,
        status,
        {
          id: resource.requestId,
          deeplinkUrl: data.deeplinkUrl,
          userEmail: userDetail.requestorEmail,
          PID: userDetail.requestorPID,
        },
      );
    } catch (error) {
      this.logger.error(
        error,
        'Failed to send notification while restore Pure storage',
      );
    }
  }

  async getResourceDataByRequestIdAndStatus(
    requestId: string,
    status: string,
  ): Promise<ResourcesEntity[]> {
    const resources =
      await this.resourcesRepository.findResourcesByRequestIdAndStatus(
        requestId,
        status,
      );
    return resources;
  }

  async findByServiceRequestId(
    serviceRequestId: string,
  ): Promise<ResourcesEntity[]> {
    return await this.resourcesRepository.findByServiceRequestId(
      serviceRequestId,
    );
  }

  async findByServiceRequestIdAndStatus(
    serviceRequestId: string,
    status: string[],
  ): Promise<ResourcesEntity[]> {
    return await this.resourcesRepository.findByServiceRequestIdAndStatus(
      serviceRequestId,
      status,
    );
  }

  async getResourcesByServiceRequestId(
    serviceRequestId: string,
  ): Promise<RequestedVMsDataDto[]> {
    let availableHostNames = [];
    const final_result = [];
    let ipAddresses: IpAddressesDto = { ipv4Address: '', ipv6Address: '' };
    const serviceRequest: any =
      await this.assetsService.findByServiceRequestId(serviceRequestId);

    if (serviceRequest && serviceRequest.payload) {
      availableHostNames = serviceRequest.payload.availableHostNames ?? [];
    }

    const resources: ResourcesEntity[] =
      await this.findByServiceRequestIdAndStatus(serviceRequestId, [
        'SUCCESS',
        'FAILED',
        'PROVISIONING',
        'notRunning',
      ]);

    availableHostNames?.forEach((hostName) => {
      const resourceData: ResourcesEntity = resources.find(
        (item: ResourcesEntity) =>
          (
            item.resourcesDetails as Record<string, any>
          )?.config?.hostName?.toLowerCase() ||
          (
            item.resourcesDetails as Record<string, any>
          )?.hostName?.toLowerCase() === hostName.toLowerCase(),
      );
      if (serviceRequest?.downstreamResponseData?.ipReservation) {
        const reservation =
          serviceRequest.downstreamResponseData.ipReservation.find(
            (res) => res.hostName === hostName,
          );
        ipAddresses = reservation
          ? reservation.ipAddress
          : { ipv4Address: '', ipv6Address: '' };
      }

      if (resourceData) {
        final_result.push({
          hostName: hostName,
          status: resourceData.status,
          resourceId: resourceData.resourceId,
          ipAddress: ipAddresses,
        });
      } else {
        final_result.push({
          hostName: hostName,
          status: 'FAILED',
          resourceId: '',
          ipAddress: ipAddresses,
        });
      }
    });
    return final_result;
  }

  async getResourcesCountByEnvId(envId: string) {
    return await this.resourcesRepository.getResourcesCountByEnvId(envId);
  }

  async quotaChekerForResource(data: quotaResourceDTO) {
    const {
      projectId,
      catalogLevel03,
      quota,
      requestedQuota,
      vmCount,
      serviceRequestId,
      envId,
    } = data;

    this.logger.log(
      `Fetching resources for projectId: ${projectId}, catalogLevel03: ${catalogLevel03}`,
    );

    const resources =
      await this.resourcesRepository.getResourcesByprojectIdAndCataloglevel03(
        projectId,
        catalogLevel03,
        envId,
      );

    this.logger.log(
      `Fetched ${resources.length} resources for projectId: ${projectId}, catalogLevel03: ${catalogLevel03}`,
    );

    const getNumericValue = (val: any) => Number(val || 0);

    const windowsResources = resources.filter(
      (r) =>
        typeof r.catalogType === 'string' && /windows/i.test(r.catalogType),
    );

    const linuxUbuntuResources = resources.filter(
      (r) => !windowsResources.includes(r),
    );

    const linuxUbuntuUsage = linuxUbuntuResources
      .map((resource) => resource?.resourcesDetails?.config?.customOptions)
      .filter(Boolean)
      .reduce(
        (acc, options) => {
          const {
            customMemory = 0,
            customCores = 0,
            root = 0,
            home = 0,
            opt = 0,
            var: varDir = 0,
            var_log = 0,
            var_log_audit = 0,
            var_tmp = 0,
            tmp = 0,
          } = options;

          acc.memory += getNumericValue(customMemory);
          acc.cpu += getNumericValue(customCores);
          acc.storage +=
            getNumericValue(root) +
            getNumericValue(home) +
            getNumericValue(opt) +
            getNumericValue(varDir) +
            getNumericValue(var_log) +
            getNumericValue(var_log_audit) +
            getNumericValue(var_tmp) +
            getNumericValue(tmp);

          return acc;
        },
        { memory: 0, cpu: 0, storage: 0 },
      );

    const windowsUsage = windowsResources
      .map((resource) => resource?.resourcesDetails?.config?.customOptions)
      .filter(Boolean)
      .reduce(
        (acc, options) => {
          const {
            customMemory = 0,
            customCores = 0,
            customVolume = 0,
          } = options;

          acc.memory += getNumericValue(customMemory);
          acc.cpu += getNumericValue(customCores);
          acc.storage += getNumericValue(customVolume);

          return acc;
        },
        { memory: 0, cpu: 0, storage: 0 },
      );

    // Combine Linux Ubuntu Windows usage
    const totalUsedMemory = linuxUbuntuUsage.memory + windowsUsage.memory;
    const totalUsedCores = linuxUbuntuUsage.cpu + windowsUsage.cpu;
    const totalUsedStorage = linuxUbuntuUsage.storage + windowsUsage.storage;

    const requestedStorage = requestedQuota.storage * vmCount;
    const requestedMemory = requestedQuota.memory * vmCount;
    const requestedCpu = requestedQuota.cpu * vmCount;

    const availableStorage = Number(quota.storage) - totalUsedStorage;
    const availableMemory = Number(quota.memory) - totalUsedMemory;
    const availableCpu = Number(quota.cpu) - totalUsedCores;

    const quotaAvailable =
      availableStorage >= requestedStorage &&
      availableMemory >= requestedMemory &&
      availableCpu >= requestedCpu;

    this.logger
      .log(`Quota check for projectId: ${projectId}, catalogLevel03: ${catalogLevel03} — 
      Requested: storage=${requestedStorage}, memory=${requestedMemory}, cpu=${requestedCpu} | 
      Available: storage=${availableStorage}, memory=${availableMemory}, cpu=${availableCpu} | 
     `);

    const errMessage = !quotaAvailable
      ? `Quota exceeded: ${
          availableStorage < requestedStorage ? VM_COMPUTE.STORAGE : ''
        } ${availableMemory < requestedMemory ? VM_COMPUTE.MEMORY : ''} ${
          availableCpu < requestedCpu ? VM_COMPUTE.CPU : ''
        }`
      : '';

    return {
      serviceRequestId,
      envId,
      vmCount,
      availableQuota: {
        storage: availableStorage,
        cpu: availableCpu,
        memory: availableMemory,
      },
      requestedQuota: {
        storage: requestedStorage,
        cpu: requestedCpu,
        memory: requestedMemory,
      },
      utilisedQuota: {
        storage: totalUsedStorage,
        cpu: totalUsedCores,
        memory: totalUsedMemory,
      },
      quotaExceeded: !quotaAvailable,
      projectId,
      cataloglevel03: catalogLevel03,
      quota,
      errMessage,
    };
  }

  async getNamespaceResources() {
    try {
      const userEnvsWithPermissions =
        await this.iamService.findUserEnvironmentsWithPermissions();
      const envIds = userEnvsWithPermissions.map((envPerm) => envPerm.envId);
      const namespaceResources =
        await this.resourcesRepository.getNamespaceResources(envIds);
      return namespaceResources;
    } catch (err) {
      this.logger.error(err, 'Failed to  fetch');
      throw err;
    }
  }
}
