import { Injectable } from '@nestjs/common';
import { baseConfig } from './mockData/baseConfigMock';
import { BaseConfigResponseDto } from './dto/baseConfig.response.dto';
import { complianceConfig } from './mockData/complianceConfig.response';
import { ComplianceConfigResponseDto } from './dto/complianceConfig.response.dto';
import { BaseConfigRequestDto } from './dto/baseConfig.request.dto';
import { ComplianceConfigRequestDto } from './dto/complianceConfig.request.dto';
import { GoldenConfigWrapperService } from '../golden-config-wrapper/golden-config-wrapper.service';
import { GoldenConfigHeaderDto } from './dto/goldenConfig.header.request.dto';
import { ConfigTemplateRequestDto } from './dto/configTemplate.request.dto';
import { FirewallPolicyDto } from './dto/firewallPolicy.request.dto';

@Injectable()
export class GoldenConfigService {
  constructor(private readonly service: GoldenConfigWrapperService) {}

  async getBaseConfig(): Promise<BaseConfigResponseDto> {
    return await this.service.getBaseConfig();
  }

  async postBaseConfig(payload: BaseConfigRequestDto) {
    return await this.service.postBaseConfig(payload);
  }

  async getBaseConfigTemplate(headers: object): Promise<BaseConfigResponseDto> {
    return await this.service.getBaseConfigTemplate(headers);
  }

  async postBaseConfigTemplate(payload: BaseConfigRequestDto) {
    return await this.service.postBaseConfigTemplate(payload);
  }

  async getComplianceConfig(headers: object): Promise<ComplianceConfigResponseDto> {
    return await this.service.getComplianceConfig(headers);
  }

  async postComplianceConfig(payload: ComplianceConfigRequestDto) {
    return await this.service.postComplianceConfig(payload);
  }

  async postComplianceTemplate(payload: ComplianceConfigRequestDto) {
    return await this.service.postComplianceTemplate(payload);
  }

  async getCommonFirewallPolicy(headers: object) {
    return await this.service.getCommonFirewallPolicy(headers);
  }

  async postCommonFirewallPolicy(payload: FirewallPolicyDto) {
    return await this.service.postCommonFirewallPolicy(payload);
  }

  async getCommonFirewallComponent(headers: object) {
    return await this.service.getCommonFirewallComponent(headers);
  }

  async postCommonFirewallComponent(payload: FirewallPolicyDto) {
    return await this.service.postCommonFirewallComponent(payload);
  }
}
