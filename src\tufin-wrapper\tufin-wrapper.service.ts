import {
  Inject,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import { AxiosInstance } from 'axios';
import { withResponseErrorHandler } from '../utils/helpers';
import { LoggerService } from '../loggers/logger.service';
import { SecureChangeRequestDto } from '../security/firewallv2/dto/secure.change.dto';

@Injectable()
export class TufinWrapperService {
  constructor(
    @Inject('TUFIN_SERVICE_API')
    private readonly tufinServiceApi: AxiosInstance,
    private readonly logger: LoggerService,
  ) {}

  async getAclConfig(ipAddress: string): Promise<string> {
    this.logger.log(`Fetching Acl config for ipAddress: ${ipAddress}`);
    return await withResponseErrorHandler(
      this.tufinServiceApi.get(`firewall/acl/config`, {
        params: { ipAddress },
      }),
    );
  }

  async cancelTufinTicket(tufinTicketId: string) {
    //call tufin service to close ticket.
    try {
      this.logger.log(`call tufin service to cancel ticket ${tufinTicketId}`);
      const tufinTicketStatusResponse = await withResponseErrorHandler(
        this.tufinServiceApi.put(
          `firewall/tufin/ticket/cancel/${tufinTicketId}`,
        ),
      );

      this.logger.log(
        `Tufin api response for ticket cancel: ${JSON.stringify(tufinTicketStatusResponse)}`,
      );
      return tufinTicketStatusResponse;
    } catch (error) {
      this.logger.error(
        error,
        `Error while cancelling tufin ticket ${tufinTicketId}`,
      );
      throw new InternalServerErrorException(
        error,
        'error while cancelling tufin ticket',
      );
    }
  }

  async createSecureChange(data: SecureChangeRequestDto) {
    this.logger.log(`Received request to create tufin ticket ${data}`);
    const tufinTicketStatusResponse = await withResponseErrorHandler(
      this.tufinServiceApi.post(`firewall/tufin/ticket`, data),
    );

    return tufinTicketStatusResponse;
  }
}
