import { EncryptPasswordResponseDto } from './encrypt.response.dto';

describe('Encrypt Response DTO', () => {
  it('should create an instance with correct properties', () => {
    const request = new EncryptPasswordResponseDto();
    request.encryptedPassword = 'testing';

    expect(request).toBeInstanceOf(EncryptPasswordResponseDto);
    expect(request.encryptedPassword).toBe('testing');
  });
});
