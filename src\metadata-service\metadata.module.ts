import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AxiosInstance } from 'axios';
import { ENVIRONMENT_VARS } from '../utils/constants';
import { createAxiosInstance } from '../utils/helpers';
import { TokenService } from '../auth/token.service';
import { REQUEST } from '@nestjs/core';
import { AuthModule } from '../auth/auth.module';
import { MetadataService } from './metadata.service';

@Module({
  imports: [AuthModule],
  controllers: [],
  providers: [
    MetadataService,
    {
      provide: 'METADATA_SERVICE_API',
      inject: [ConfigService, TokenService, REQUEST],
      useFactory: async (
        configService: ConfigService,
        tokenService: TokenService,
        req: Request,
      ): Promise<AxiosInstance> => {
        const baseUrlKey = configService.get(
          ENVIRONMENT_VARS.METADATA_SERVICE_BASE_URL,
        );
        const nebulaHeader = req.headers['x-nebula-authorization']
          ? (req.headers['x-nebula-authorization'] as string)
          : (req.headers['x-client-jwt'] as string);
        return await createAxiosInstance(
          configService,
          tokenService,
          nebulaHeader,
          baseUrlKey,
        );
      },
    },
  ],
  exports: [MetadataService],
})
export class MetadataModule {}
