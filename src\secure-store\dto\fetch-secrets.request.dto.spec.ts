import { validate } from 'class-validator';
import { FetchSecretRequestDto } from './fetch-secrets.request.dto';

describe('FetchSecretRequestDto', () => {
  it('should pass validation with valid input', async () => {
    const dto = new FetchSecretRequestDto();
    dto.version = '123';
    dto.type = 'Rotatable';

    const errors = await validate(dto);
    expect(errors.length).toBe(0);
  });

  it('should fail validation with missing fields', async () => {
    const dto = new FetchSecretRequestDto();
    dto.version = 'abc';
    dto.type = 'Tom';
    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
  });
});
