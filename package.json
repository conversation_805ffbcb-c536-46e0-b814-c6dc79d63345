{"name": "microservice", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\"", "lint:fix": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/axios": "^3.0.1", "@nestjs/cache-manager": "^2.2.2", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.0.0", "@nestjs/mapped-types": "*", "@nestjs/microservices": "^10.2.7", "@nestjs/mongoose": "^10.0.1", "@nestjs/platform-express": "^10.4.7", "@nestjs/swagger": "^7.1.13", "@types/multer": "^1.4.11", "amqp-connection-manager": "^4.1.14", "amqplib": "^0.10.3", "axios": "^1.6.0", "body-parser": "^1.20.2", "cache-manager": "^5.5.1", "cache-manager-redis-yet": "^5.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "dayjs": "^1.11.13", "exceljs": "^4.4.0", "express-mongo-sanitize": "^2.2.0", "file-type": "^19.6.0", "generate-password": "^1.7.1", "https-proxy-agent": "^7.0.5", "ip": "^2.0.0", "ip-address": "^9.0.5", "ip-num": "^1.5.1", "ip-range-check": "^0.2.0", "jest-mock-extended": "^3.0.5", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "mongo-sanitize": "^1.1.0", "mongodb-memory-server": "^10.1.4", "mongoose": "^7.5.4", "mongoose-aggregate-paginate-v2": "^1.1.2", "nest-winston": "^1.9.4", "nestjs-pino": "^4.1.0", "nestjs-request-context": "^3.0.0", "pino-noir": "^2.2.1", "redis": "^4.6.13", "redis-server": "^1.2.2", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "uuidv4": "^6.2.13", "winston": "^3.11.0", "yamljs": "^0.3.0"}, "devDependencies": {"@automock/adapters.nestjs": "^2.1.0", "@automock/jest": "^2.1.0", "@golevelup/ts-jest": "^0.4.0", "@nestjs/cli": "^10.0.0", "@nestjs/common": "^10.4.19", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/cron": "^2.4.3", "@types/express": "^4.17.17", "@types/ip": "^1.1.3", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.4", "@types/lodash": "^4.17.7", "@types/node": "^20.3.1", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.7.0", "jest-junit": "^16.0.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.4.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "overrides": {"superagent": {"formidable": "^3.5.1"}, "semver": "^7.6.0", "follow-redirects": "^1.15.6"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "reporters": ["default", ["jest-junit", {"outputDirectory": "./coverage", "outputName": "junit.xml"}]], "rootDir": ".", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "moduleNameMapper": {"^src/(.*)$": "<rootDir>/src/$1"}, "collectCoverageFrom": ["**/src/secure-store/devices/**/*controller.ts", "!**/*.spec.(t|j)s"], "coveragePathIgnorePatterns": ["node_modules", "dist", "db-scripts", "test-config", "interfaces", "jestGlobalMocks.ts", ".module.ts", "main.ts", "index.ts", ".mock.ts", ".filter.ts", ".middleware.ts", "../src/utils/nest-mongo/errors"], "coverageDirectory": "coverage", "testEnvironment": "node", "testMatch": ["**/src/secure-store/**/*.spec.ts", "**/src/action/**/*.spec.ts", "**/src/ztp/*.spec.ts"]}}