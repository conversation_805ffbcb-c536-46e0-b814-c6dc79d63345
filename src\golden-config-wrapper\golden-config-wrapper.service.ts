import { Inject, Injectable } from '@nestjs/common';
import { BaseConfigResponseDto } from '../golden-config/dto/baseConfig.response.dto';
import { ComplianceConfigResponseDto } from '../golden-config/dto/complianceConfig.response.dto';
import { AxiosInstance, AxiosRequestConfig } from 'axios';
import { withResponseErrorHandler } from '../utils/helpers';
import { BaseConfigRequestDto } from '../golden-config/dto/baseConfig.request.dto';
import { ComplianceConfigRequestDto } from '../golden-config/dto/complianceConfig.request.dto';
import { ConfigTemplateRequestDto } from '../golden-config/dto/configTemplate.request.dto';
import { FirewallPolicyDto } from '../golden-config/dto/firewallPolicy.request.dto';

@Injectable()
export class GoldenConfigWrapperService {
  constructor(
    @Inject('GOLDEN_CONFIG_API')
    private readonly goldenConfigServiceApi: AxiosInstance,
    @Inject('NEBULA_GOLDEN_CONFIG_API')
    private readonly nebulaGoldenConfigServiceApi: AxiosInstance,
  ) {}

  async getBaseConfig(): Promise<BaseConfigResponseDto> {
    return withResponseErrorHandler(this.nebulaGoldenConfigServiceApi.get(`/config`));
  }

  async postBaseConfig(payload: BaseConfigRequestDto) {
    return withResponseErrorHandler(
      this.goldenConfigServiceApi.post(`/config`, payload),
    );
  }

  async getBaseConfigTemplate(header: object): Promise<BaseConfigResponseDto> {
    const config: AxiosRequestConfig = header;
    return withResponseErrorHandler(
      this.goldenConfigServiceApi.get(`/config-template`, config),
    );
  }

  async postBaseConfigTemplate(payload: BaseConfigRequestDto) {
    return await withResponseErrorHandler(
      this.goldenConfigServiceApi.post(`/config-template`, payload),
    );
  }

  async getComplianceConfig(header: object): Promise<ComplianceConfigResponseDto> {
    const config: AxiosRequestConfig = header;
    return withResponseErrorHandler(
      this.goldenConfigServiceApi.get(`/compliance-config`, config),
    );
  }

  async postComplianceConfig(payload: ComplianceConfigRequestDto) {
    return withResponseErrorHandler(
      this.goldenConfigServiceApi.post(`/compliance-config`, payload),
    );
  }

  async postComplianceTemplate(payload: ComplianceConfigRequestDto) {
    return withResponseErrorHandler(
      this.goldenConfigServiceApi.post(`/compliance-template`, payload),
    );
  }

  async getCommonFirewallPolicy(header: object) {
    const config: AxiosRequestConfig = header;
    return withResponseErrorHandler(
      this.goldenConfigServiceApi.get(`/common-firewall-policy`, config),
    );
  }

  async postCommonFirewallPolicy(payload: FirewallPolicyDto) {
    return withResponseErrorHandler(
      this.goldenConfigServiceApi.post(`/common-firewall-policy`, payload),
    );
  }

  async getCommonFirewallComponent(header: object) {
    const config: AxiosRequestConfig = header;
    return withResponseErrorHandler(
      this.goldenConfigServiceApi.get(`/common-firewall-policy-component`, header),
    );
  }

  async postCommonFirewallComponent(payload: FirewallPolicyDto) {
    return withResponseErrorHandler(
      this.goldenConfigServiceApi.post(
        `/common-firewall-policy-component`,
        payload,
      ),
    );
  }
}
