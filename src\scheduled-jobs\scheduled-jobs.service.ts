import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { SecretsMetadataService } from '../secure-store/secretsMetadata/secretsMetadata.service';
import { SecureStoreService } from '../secure-store/secure-store.service';
import { RotateSecretRequestDto } from '../secure-store/dto/rotate-secret.request.dto';
import { ENVIRONMENT_VARS } from 'src/utils/constants';
import { ConfigService } from '@nestjs/config';
import { IntegrationNotificationService } from '../naas/integration.notification.service';
import {
  ROTATION_STATUS,
  TOKEN_STATUS,
} from '../secure-store/enums/secret_rotation_request_type';
import { ValidUSStateCodes } from 'src/capacity-planning/utils/types';
import { ResourcesService } from '../resources/resources.service';
import { MultiEnvIAMService } from '../multi-env/iam/iam.service';
import { LoggerService } from '../loggers/logger.service';
import { DevicesService } from '../secure-store/devices/providers/devices.service';
import { Logger } from 'nestjs-pino';

@Injectable()
export class ScheduledJobsService {
  constructor(
    private readonly secretsMetadataService: SecretsMetadataService,
    private readonly secureStoreService: SecureStoreService,
    private configService: ConfigService,
    private readonly integrationNotificationService: IntegrationNotificationService,
    private readonly resourcesService: ResourcesService,
    private readonly multiEnvIAMService: MultiEnvIAMService,
    private readonly loggerService: LoggerService,
    private readonly devicesService: DevicesService,
  ) {}

  async rotateSecretsJob() {
    const secretsRotationInterval = this.configService.get(
      ENVIRONMENT_VARS.SECRETS_ROTATION_INTERVAL_IN_MINS,
    );
    const secretsForRotationDue =
      await this.secretsMetadataService.fetchSecretsForRotationDue(
        secretsRotationInterval,
      );
    if (secretsForRotationDue?.length) {
      const secretIds = secretsForRotationDue.map((secret) => secret.secretId);

      const devices = await this.devicesService.findBySecretIds(secretIds);
      let secretIdsWithDevices = [];
      if (devices?.length) {
        const devicesMap = {};
        devices.forEach((device) => {
          const secretId = device.secretId[0];
          devicesMap[secretId] = true;
        });
        secretIds.forEach((id) => {
          if (devicesMap[id]) {
            secretIdsWithDevices.push(id);
          } else {
            this.loggerService.error(
              `[${new Date().toISOString()}] SecretId ${id}, is not linked to a device.`,
            );
          }
        });
      }
      if (!secretIdsWithDevices?.length) {
        this.loggerService.error(
          `[${new Date().toISOString()}] SecretId(s) ${secretIds.join(',')}, are not linked to any devices.`,
        );
        return;
      }
      const DueSecretSet = new Set(secretIdsWithDevices);
      const DueSecrets = secretsForRotationDue.filter((secret) =>
        DueSecretSet.has(secret.secretId),
      );

      DueSecrets.forEach(async (secretsMetadata) => {
        const {
          secretId,
          type,
          vaultNamespace,
          vaultPath,
          devicePasswordKey,
          notifyBeforeTokenExpiry,
          resourceId,
        } = secretsMetadata;

        if (notifyBeforeTokenExpiry) {
          this.resourcesService
            .getResourceByResourceId(resourceId)
            .then(async (resource) => {
              await this.multiEnvIAMService
                .getProjectAppEnvByEnvId(resource.platformContext.envId)
                .then(async (project) => {
                  const { emailDistribution: emailList, projectName } = project;
                  await this.secureStoreService.secretRoatationNotification(
                    secretId,
                    emailList,
                    {
                      project: projectName,
                      namespace: vaultNamespace,
                      path: vaultPath,
                      vaultSecretKey: devicePasswordKey,
                    },
                    TOKEN_STATUS.EXPIRING,
                  );
                })
                .catch((error) => {
                  this.loggerService.error(
                    `[${new Date().toISOString()}] Error in fetching projects ${error}}`,
                  );
                });
            })
            .catch((error) => {
              this.loggerService.error(
                `[${new Date().toISOString()}] Error in fetching resources ${error}}`,
              );
            });
        }
      });

      let newPasswordsList = [];

      const newPasswords = await Promise.allSettled(
        secretIdsWithDevices.map((id) =>
          this.secureStoreService
            .generateUniquePassword(id)
            .then((res) => ({ id, res }))
            .catch((error) => {
              this.loggerService.error(
                `[${new Date().toISOString()}] Failed to generate password for ID ${id}.`,
              );
              return null;
            }),
        ),
      );

      newPasswordsList = newPasswords
        .filter((result) => result.status === 'fulfilled' && result.value)
        .flatMap((result: any) => result?.value.res);

      const payloadToRotatePassword = newPasswordsList.map(
        (npl: Record<string, string>) => {
          const rotatePayloadObj = {};
          Object.entries(npl).forEach(([key, val]) => {
            if (key === 'secretId') {
              rotatePayloadObj['secretId'] = val;
            } else {
              rotatePayloadObj['newPassword'] = val;
            }
          });
          return rotatePayloadObj;
        },
      );
      await Promise.allSettled(
        payloadToRotatePassword.map((payload) =>
          this.secureStoreService
            .createRotateSecretRequest([payload] as RotateSecretRequestDto[])
            .then(() =>
              this.loggerService.log(
                `[${new Date().toISOString()}] Rotation completed for secret ID ${payload['secretId']}`,
              ),
            )
            .catch(() =>
              this.loggerService.error(
                `[${new Date().toISOString()}] Rotation failed for secret ID ${payload['secretId']}`,
              ),
            ),
        ),
      );
    }
  }

  async reactivateSecrets() {
    const reactivateSecretsDuration = this.configService.get(
      ENVIRONMENT_VARS.REACTIVATE_SECRET_DURATION_HRS,
    );
    let reactivateSecrets = await this.secretsMetadataService.reactivateSecrets(
      reactivateSecretsDuration,
    );
    if (reactivateSecrets.length)
      return await Promise.all(
        reactivateSecrets.map(async (secret) => {
          await this.secureStoreService.cleanupTemporarySecrets(
            secret.secretId,
          );
        }),
      );
  }

  async renewVaultTokens() {
    const tokens =
      await this.secretsMetadataService.getAllAutoRenewVaultTokens();
    if (tokens.length) {
      let tokensToBeRenewed = [];
      tokens.forEach((token) => {
        let renewalDate = token.renewedAt ? token.renewedAt : token.createdAt;
        let tokenLifespan = token.expiryDate.getTime() - renewalDate.getTime();
        let tokenCurrentAge = new Date().getTime() - renewalDate.getTime();
        let ratio = tokenCurrentAge / tokenLifespan;
        if (
          ratio >=
          this.configService.get(ENVIRONMENT_VARS.VAULT_TOKEN_RENEWAL_RATIO)
        ) {
          tokensToBeRenewed.push({
            secretId: token.secretId,
            key: token.devicePasswordKey,
          });
        }
      });
      this.loggerService.log(`${tokensToBeRenewed} Tokens found for renewal`);
      if (tokensToBeRenewed.length) {
        let tokensMetaData =
          await this.secureStoreService.renewBulkTokens(tokensToBeRenewed);
        if (tokensMetaData.length) {
          tokensMetaData.forEach(async (tokenData) => {
            try {
              //Update token only if it is renewed
              if (tokenData.value.renewed) {
                this.loggerService.log(
                  `${tokenData.value.secretId} Token renewed successfully`,
                );
                await this.secretsMetadataService.updateRenewedTokenMetaData(
                  tokenData.value.secretId,
                  tokenData.value.renewalDate,
                  tokenData.value.expiryDate,
                );
              } else {
                this.loggerService.error(
                  `Token renewal failed for : ${tokenData.value.secretId}`,
                );
              }
            } catch (error) {
              this.loggerService.error('Token renewal upadte failed: ', error);
            }
          });
        }
      }
    }
  }
}
