#MongoDB connection string #REQUIRED
#Temporary MongoDB for local development only, replace john-doe with your name to have your own database 
MONGODB_URI=mongodb+srv://[user_name]:[password]@cluster0.yp4tttn.mongodb.net/john-doe-db

#Port to run the application #REQUIRED 
PORT=3000

#RabbitMQ connection string #REQUIRED
#Temporary RabbitMQ instance for local development only 
RMQ_URI=amqps://[user_name]:[password]@gull.rmq.cloudamqp.com/xiubxbfu

#Message queue name for ip-management-service #REQUIRED
#For local development delete john-doe and add your_name/pid to make it unique accross dev team
RMQ_IPAM_QUEUE=ipm-queue-john-doe

#Boolean value for the message durability in RMQ_IPAM_QUEUE #TYPE=boolean #OPTIONAL #Default=true
RMQ_IPAM_QUEUE_DURABLE=true

#Message queue name for monitoring-service #REQUIRED
#For local development delete john-doe and add your_name/pid to make it unique accross dev team
RMQ_MONITORING_QUEUE=monitoring-queue-john-doe

#Boolean value for the message durability in RMQ_MONITORING_QUEUE #TYPE=boolean #OPTIONAL #Default=true
RMQ_MONITORING_QUEUE_DURABLE=true

#Message queue name for workflow-orchestrator-service #REQUIRED
#For local development delete john-doe and add your_name/pid to make it unique accross dev team
RMQ_WF_ORCHESTRATOR_QUEUE=wforch-queue-john-doe

#Boolean value for the message durability in RMQ_WF_ORCHESTRATOR_QUEUE #TYPE=boolean #OPTIONAL #Default=true
RMQ_WF_ORCHESTRATOR_QUEUE_DURABLE=true

#Seperate Queue for Firewall
RMQ_FIREWALL_QUEUE=itential-workflow-queue

#Boolean value for the message durability in RMQ_FIREWALL_QUEUE #TYPE=boolean #OPTIONAL #Default=true
RMQ_FIREWALL_QUEUE_DURABLE=false

#Base url for ip-management service #REQUIRED 
IPM_SERVICE_BASE_URL=http://localhost:3001/naas/v1

#Base url for monitoring-service #REQUIRED 
MONITORING_SERVICE_BASE_URL=http://localhost:3002/oaas/v1

#Base url for itential-service #REQUIRED 
ITENTIAL_SERVICE_BASE_URL=http://localhost:3003/itential/v1

#Base url for integration-api-service #REQUIRED
INTEGRATION_API_SERVICE_BASE_URL=http://localhost:8088/integration-api/v1

#Global routing prefix for this application (cloud-api-service) #REQUIRED
GLOBAL_ROUTE_PREFIX=nebula-api 

#Message queue name for compute - VM Creation use cases
RMQ_COMPUTE_QUEUE=nebula.cloudapi.request.compute

#Boolean value for the message durability in RMQ_COMPUTE_QUEUE #TYPE=boolean #OPTIONAL #Default=true
RMQ_COMPUTE_QUEUE_DURABLE=true

#Object storage microservice base url
OBJECTSTORAGE_BASE_URL=https://nebula-dev.stage.charter.com/nebula-object-store-service/api/1.0

#Object storage microservice username
OBJECTSTORAGE_USERNAME=PSFBSAZRPIBOMANHCGIKDMOIOLIBLPDEHCCJMBCMLF

#Object storage microservice password
OBJECTSTORAGE_PASSWORD=#####

#Object storage bucket name
OBJECTSTORAGE_BUCKET=nebula-coudersport

#Object storage path for ip-block file
OBJECTSTORAGE_PATH=stamp-dev/naas/ip-block

#Object storage path for ztp file #REQUIRED
OBJECTSTORAGE_ZTP_PATH=stamp-dev/naas/security/ztp

#Object storage path for firewall file #REQUIRED
OBJECTSTORAGE_FIREWALL_PATH=stamp-dev/naas/security/firewall


#Base url for compute-service #REQUIRED 
COMPUTE_API_BASE_URL=http://localhost:3007/compute

#Count of Total Available VM #REQUIRED
VM_MAX_COUNT=5

#Morpheus User Email (Since service accounts doesn't have email in JWT token, so below config variable is used to fetch groups only for service accounts)
#The email should change in prod env
MORPHEUS_EMAIL=<EMAIL>

#Morpheus Default vm password
VM_DEFAULT_PASSWORD=#####

#Password Ecryption key(length should be 32 character) #STRING #REQUIRED
PASSWORD_ENCRYPTION_KEY=#####

CACHING_SERVICE_BASE_URL=http://localhost:6010/caching/v1

REQUESTER_TEMPLATE_ID=1001

# Redis server host
REDIS_HOST=************

# Redis server port
REDIS_PORT=6379

# Cache time to live in milli-seconds
CACHE_TTL=30000

#enable or disbale cache
ENABLE_CACHE=0

#Default request body size limit for the API exposed by this service
REQUEST_BODY_SIZE_LIMIT=1mb

#Message queue name for compute - VM deletion use cases
RMQ_DELETE_VM_QUEUE=nebula.cloudapi.delete.vm.request

#Boolean value for the message durability in RMQ_DELETE_VM_QUEUE #TYPE=boolean #OPTIONAL #Default=true
RMQ_DELETE_VM_QUEUE_DURABLE=true


RMQ_DELETE_PACE_VM_QUEUE=pacevmware.delete.queue.dev
RMQ_DELETE_PACE_VM_QUEUE_DURABLEE=true

#To prevent/allow user to approve own request
APPROVE_OWN_REQUEST=true


#Max firewall rules file size limit.
MAX_FIREWALL_RULES_FILE_SIZE_LIMIT_IN_MB=1

#Base url for golden-config-service #REQUIRED 
GOLDEN_CONFIG_SERVICE_BASE_URL=https://nebula-dev.stage.charter.com/golden-config-service/api/1.0

#Length of generated password for api /dbaas/provision-db
DB_PASSWORD_LENGTH=12

HOSTNAME_ENVIRONMENT=ld

APPROVER_NOTIFICATION_TEMPLATE_ID=1002

LOG_LEVEL=trace

ENVIRONMENT_BASE_URL=https://nebula-dev.stage.charter.com
# for addDisks in VM
MAX_DISKVALUE=1000

NODE_TLS_REJECT_UNAUTHORIZED=0

SECRETS_MANAGEMENT_SERVICE_BASE_URL= https://nebula-dev.stage.charter.com/secrets-management-service/v1
# METADATA_SERVICE_BASE_URL
METADATA_SERVICE_BASE_URL=https://nebula-dev.stage.charter.com/metadata-service/v1

# Capacity Planning Service Base URL
CAPACITY_PLANNING_SERVICE_BASE_URL=https://nebula-dev.stage.charter.com/capacity-planning-service/v1

# Nebula Metrics Service Base URL
NEBULA_METRICS_SERVICE_BASE_URL=https://nebula-dev.stage.charter.com/metrics-api

RMQ_COMMON_FIREWALL_POLICY_QUEUE=nebula.cloudapi.request.common.firewall.dev
RMQ_COMMON_FIREWALL_POLICY_QUEUE_DURABLE=true

RMQ_FIREWALL_V2_QUEUE= nebula.cloudapi.request.firewall.v2.dev
RMQ_FIREWALL_V2_QUEUE_DURABLE=true

#delay for response after VM start/stop or restart
VM_START_STOP_DELAY=3000
VM_RESTART_DELAY=5000

RMQ_STORAGE_S3_QUEUE= nebula.cloudapi.storage.s3.request
RMQ_STORAGE_S3_QUEUE_DURABLE=true

RMQ_STORAGE_NFS_QUEUE= nebula.cloudapi.storage.nfs.request
RMQ_STORAGE_NFS_QUEUE_DURABLE=true

#stop start restart VM queue
RMQ_START_STOP_RESTART_VM_QUEUE=nebula.cloudapi.stopstartrestart.vm.request
RMQ_START_STOP_RESTART_VM_QUEUE_DURABLE=true

RMQ_BLUE_COMPUTE_QUEUE=nebula.cloudapi.request.blue.compute.dev
RMQ_BLUE_COMPUTE_QUEUE_DURABLE=true

RMQ_DELETE_BLUE_VM_QUEUE=nebula.cloudapi.delete.blue.vm.request
RMQ_DELETE_BLUE_VM_QUEUE_DURABLE=true

# MaxDiskValue for Blue Vm is 10TB
BLUE_MAX_DISKVALUE=10000

STORAGE_SERVICE_BASE_URL=http://localhost:3060/storage-service/v1

#nebula tufin management service details
TUFIN_SERVICE_BASE_URL=https://nebula-dev.stage.charter.com/tufin/v1

COMMON_FIREWALL_SERVICE_BASE_URL=https://nebula-dev.stage.charter.com/common-firewall-policy/v1

DEFAULT_CREATED_BY_FOR_CHILD=nebulaDAP

# AWS create sub account queue
RMQ_AWS_CREATE_SUBACCOUNT_QUEUE=aws-create-subaccount
RMQ_AWS_CREATE_SUBACCOUNT_QUEUE_DURABLE=true

ACTIVITY_LOGS_SERVICE_BASE_URL=https://nebula-dev.stage.charter.com/activity-log-producer

UPDATED_TIME_MINUTES=30
RISK_ANALYSIS_LOG_STEP=10
GIT_PIPELINE_LOG_STEP=2
DAP_UPDATE_LOG_STEP=5
DAP_DEPLOYMENT_LOG_STEP=7
DAP_DEPLOYMENT_CHILD_LOG_STEP=3
COMPLETING_LOG_FW2_CODE=NEB-EVENT-FIREWALLV2-17001
COMPLETING_LOG_FW2_NAME="Completing Service Request - Nebula"
TICKET_MANAGEMENT_SERVICE_BASE_URL=https://nebula-dev.stage.charter.com/ticket-management-service

JIRA_API_BASE_URL=http://localhost:3006/jira-service/v1

RMQ_AWS_CREATE_SUBACCOUNT_GROUP_QUEUE=aws-sub-account-groups
RMQ_AWS_CREATE_SUBACCOUNT_GROUP_QUEUE_DURABLE=true

#Object Storage path for F5 load balancer
OBJECTSTORAGE_F5_LOAD_BALANCER_PATH=stamp-dev/naas/security/loadbalancer

RMQ_REGENERATE_KEYS_QUEUE=regenerate.accesskeys
RMQ_REGENERATE_KEYS_QUEUE_DURABLE=true

RMQ_MANAGE_SUB_ACCOUNT_GROUP_PERMISSION_QUEUE=nebula.cloudapi.request.manage.group.permission
RMQ_MANAGE_SUB_ACCOUNT_GROUP_PERMISSION_QUEUE_DURABLE=true

RMQ_MANAGE_PERMISSION_SET_CREATE_QUEUE=nebula.cloudapi.request.manage.permission.set.create
RMQ_MANAGE_PERMISSION_SET_CREATE_QUEUE_DURABLE=true

RMQ_MANAGE_PERMISSION_SET_REMOVE_QUEUE=nebula.cloudapi.request.manage.permission.set.remove
RMQ_MANAGE_PERMISSION_SET_REMOVE_QUEUE_DURABLE=true

RMQ_MODIFY_STORAGE_S3_QUEUE=nebula.cloudapi.storage.s3.modify.dev
RMQ_MODIFY_STORAGE_S3_QUEUE_DURABLE=true
RMQ_DELETE_S3_QUEUE=nebula.cloudapi.storage.s3.delete.dev
RMQ_DELETE_S3_QUEUE_DURABLE=true

INTERNAL_CERTIFICATE_QUEUE=nebula.cloudapi.create.internal.certificate.request.dev
INTERNAL_CERTIFICATE_QUEUE_DURABLE=true
RMQ_F5_LOAD_BALANCE_QUEUE_DURABLE=true
RMQ_F5_LOAD_BALANCE_QUEUE=nebula.cloudapi.f5.load.balancer.request.dev
RMQ_DELETE_NFS_QUEUE=nebula.cloudapi.storage.nfs.delete.dev
RMQ_DELETE_NFS_QUEUE_DURABLE=true

RMQ_MODIFY_NFS_QUEUE=modify.nfs.queue
RMQ_MODIFY_NFS_QUEUE_DURABLE=true

LOAD_BALANCER_SERVICE_BASE_URL=https://nebula-dev.stage.charter.com/load-balancer
RMQ_WORKFLOW_RETRY_QUEUE=nebula.cloudapi.workflow.vm.retry.dev
RMQ_WORKFLOW_RETRY_QUEUE_DURABLE=true
RMQ_AWS_CREATE_USER_QUEUE=nebula.cloudapi.awssubaacount.user.request
RMQ_AWS_CREATE_USER_QUEUE_DURABLE=true

QUALYS_SERVER_URL=https://qagpublic.qg1.apps.qualys.com/CloudAgent/
QUALYS_CUSTOMER_ID=5b139214-9dd0-489a-83cb-834191633748

SKIP_SELF_APPROVAL_CATALOGS=FIREWALL,FIREWALL_V2
RMQ_CHARTER_LAB_COMPUTE_QUEUE = charter_lab_queue
RMQ_CHARTER_LAB_COMPUTE_QUEUE_DURABLE = true
CHARTER_LAB_INFOBLOX_ENABLED = false
MAX_RETRY_COUNT=10

RMQ_DNP_QUEUE=nebula.cloudapi.request.dnp.dev
RMQ_DNP_QUEUE_DURABLE=true
AWS_MANAGEMENT_BASE_URL=https://nebula-dev.stage.charter.com/aws-management-service/v1

X_FORWARD_FOR= No
PERSISTENCE=Cookie
SOURCE_NAT=Auto Map
SSL_OFFLOAD=Yes
SSL_SETUP=NEBULA-DEV-STAMP.CHARTER.COM
HEALTH_MONITOR_NAME=No
HEALTH_MONITOR_STRING_SEND= https
IDLE_TIMEOUT=Default
VLANS_AND_TUNNELS=vLans
PRIORITY_GROUP_AND_ACTIVATION=NO
SUBDOMAIN=CPE
DOMAIN=CHARTER.COM
XXX = 300
RMQ_FIREWALL_MIGRATION_QUEUE=nebula.cloudapi.firewall.v1.migrate.dev
RMQ_FIREWALL_MIGRATION_QUEUE_DURABLE=true

QUALYS_ALLOWED_LAYOUT=rhel8.6
CENTRIFY_ALLOWED_LAYOUT=rhel8.6
TANIUM_ALLOWED_LAYOUT=rhel8.6,rhel8.8,rhel9.0
FIREWALL_V1_REQUEST_STATUS=PENDING_APPROVAL
SUBREQUEST_APPROVER_NOTIFICATION_TEMPLATE_ID=1012
OBJECTSTORAGE_LOAD_BALANCER_PATH=stamp-dev/loadbalancer/f5
OBJECTSTORAGE_FIREWALL_ALL_RULES_PATH=stamp-dev/naas/security/firewall/allRules

RMQ_DELETE_DB_QUEUE=nebula.cloudapi.delete.db.request
RMQ_DELETE_DB_QUEUE_DURABLE=true

RMQ_BULK_VM_IMPORT_QUEUE =nebula.cloudapi.vm.import.request
RMQ_BULK_VM_IMPORT_QUEUE_DURABLE=true

RMQ_ONBOARD_PROJECT_QUEUE=nebula.cloudapi.request.onboard.project.dev
RMQ_ONBOARD_PROJECT_QUEUE_DURABLE=true

RMQ_CATALOG_ACCESS_REQUEST_QUEUE=nebula.cloudapi.catalog.access.request.dev
RMQ_CATALOG_ACCESS_REQUEST_QUEUE_DURABLE=true

MIGRATED_V2_DEEPLINK_URL=/iaas/security/networkaccessrequest/addfirewallrulesv2

RMQ_RESUBMIT_FIREWALL_V2_REQUEST_QUEUE=nebula.cloudapi.resubmit.request.firewall.v2.dev
RMQ_RESUBMIT_FIREWALL_V2_REQUEST_QUEUE_DURABLE=true
MIGRATED_V1_PROJECT=firewall migration project

APPROVALS_EMAIL_URL=/v2/approvals/

PROCESSING_START_LINE_FOR_DFM_WITH_INSTRUCTIONS=12
PROCESSING_START_LINE_FOR_DFM_WITHOUT_INSTRUCTIONS=11
RMQ_SPEC_FLOW_QUEUE=nebula.cloudapi.git.createspecflowasset.dev
RMQ_SPEC_FLOW_QUEUE_DURABLE=true
RMQ_DELETE_PUBLIC_CLOUD_QUEUE=nebula.cloudapi.git.deletepubliccloudasset.dev
RMQ_DELETE_PUBLIC_CLOUD_QUEUE_DURABLE=true
#While mergeing into develop
#APPROVALS_EMAIL_URL=/approvals?requestID=
BLUE_MORPHEUS_CACHE_THRESHOLD_MINUTES=1440

RMQ_ONBOARD_GROUP_QUEUE=nebula.cloudapi.request.onboard.group.dev
RMQ_ONBOARD_GROUP_QUEUE_DURABLE=true

TAGGING_SERVICE_API_BASE_URL=http://localhost:3011/tagging-service
APPROVER_COMMENT_NOTIFICATION_TEMPLATE_ID=1013
CMDB_SERVICE_BASE_URL=http://localhost:9000/cmdb-service
 AWS_IPAM_API_BASE_URL=https://api-dev-cloudcage.crossgov.spectrum.net

CHERWELL_RETRY_REQUESTING_USER=svc-nebula-cherwell
REMEDY_RETRY_REQUESTING_USER=svc-nebula-remedy
#split length for firewall rules while performing optimization
 OPTIMIZATION_SPLIT_LENGTH=1800

VMWARE_COMPUTE_QUEUE=nebula.cloudapi.request.vmware.compute.dev
OPTIMIZATION_SPLIT_LENGTH=1800
 AWS_IPAM_REFRESH_MINUTES=30
RMQ_VMWARE_COMPUTE_QUEUE=nebula.vmware.queue.dev
AWS_IPAM_PROXY_HOST=proxy.agproxy.geo-x.spectrum.com
AWS_IPAM_PROXY_PORT=3128
AWS_IPAM_PROXY_PROTOCOL=http
AWS_IPAM_API_BASE_URL=https://api-cloudcage.crossgov.spectrum.net
VMWARE_ADAPTER_SERVICE_API_BASE_URL=https://nebula-dev.stage.charter.com/nebula-vmware-service/api/1.0/

# RMQ for PUBLIC CLOUD
RMQ_CREATE_PUBLIC_CLOUD_VPC_QUEUE=nebula.cloudapi.git.createpubliccloudasset.dev
RMQ_CREATE_PUBLIC_CLOUD_VPC_QUEUE_DURABLE=true

ENABLE_DNP_SERVICE=true
DNPDAP_SERVICE_BASE_URL=https://localhost:8088/dnp-rapnor/v1/

RMQ_DELETE_VM_VMWARE_QUEUE=vmware.delete.queue.dev
RMQ_DELETE_VM_VMWARE_QUEUE_DURABLE=true
RMQ_CREATE_NAMESPACE_QUEUE=nebula.cloudapi.request.create.namespace.dev
RMQ_CREATE_NAMESPACE_QUEUE_DURABLE=true
RMQ_RECONFIGURE_VM=vmware.vm.reconfigure.dev
RMQ_RECONFIGURE_VM_DURABLE=true

RMQ_PACE_RECONFIGURE_VM_QUEUE=pacevmware.vm.reconfigure.dev
RMQ_RECONFIGURE_PACE_VM_QUEUE_DURABLE=true

RMQ_VMWARE_LAB_COMPUTE_QUEUE=nebula.cloudapi.pacelab.compute.dev
RMQ_VMWARE_LAB_COMPUTE_QUEUE_DURABLE=true

RMQ_VMWARE_LAB_COMPUTE_QUEUE=nebula.cloudapi.pacelab.compute.dev
RMQ_VMWARE_LAB_COMPUTE_QUEUE_DURABLE=true

APPEND_CONSOLE_URL=https://**************:8443/console/

TOTAL_MEMORY_VALUE=1000

RMQ_ZTP_QUEUE_DURABLE=true
RMQ_ZTP_QUEUE=nebula.cloudapi.ztp.dev

DAP_DNP_ACITIVITY_LOG_SEQUENCE=3
ADHOC_LOCATION=Adhoc-Password
ADHOC_NAMESPACE=nebula-stamp
REACTIVATE_SECRET_DURATION_HRS=2
VAULT_MASTER_NAMESPACE=nebula-stamp
MASTER_TOKEN_FOLDER=namespaces
VAULT_TOKEN_RENEWAL_RATIO=0.67
CERTIFICATE_MANAGEMENT_SERVICE_BASE_URL=http://localhost:8015/nebula-certificate-management-service/v1
WATCHER_SERVICE_BASE_URL="asd"
RMQ_EDIT_VM_VMWARE_QUEUE=vmware.vm.edit.dev
RMQ_EDIT_VM_VMWARE_QUEUE_DURABLE= true
RMQ_EDIT_PACE_VM_VMWARE_QUEUE=pacevmware.vm.edit.dev
RMQ_EDIT_PACE_VM_VMWARE_QUEUE_DURABLE= true

RETRY_MAX_COUNT=3
RETRY_TIME_DELAY=10000

RMQ_WORKFLOW_RETRY_VMWARE_QUEUE=nebula.cloudapi.workflow.vmware.retry.dev
RMQ_WORKFLOW_RETRY_VMWARE_QUEUE_DURABLE=true

SECRETS_ROTATION_INTERVAL_IN_MINS=5
TOKEN_EXPIRY_NOTIFICATION_TEMPLATE_ID=1014
SECRET_UNAVAILABLE_MESSAGE="Vault secret history not available"
FW_CREATE_JIRA_TICKET_STEP=14
FW_CANCEL_JIRA_TICKET_STEP=26
FW_CLOSE_JIRA_TICKET_STEP=21
FW_CANCEL_TUFIN_TICKET_STEP=25

VMWARE_ADMIN_SERVICE_BASE_URL=https://nebula-dev.stage.charter.com/vmware-admin-service/v1/
VALID_SPECIAL_CHARS="!@$%^&*+-_?=#"



RED_VM_DOMAINS=CUSTOMERNET
PACE_VM_DOMAINS=PACE
DELAY_FAILED_SECRET_ROTATION_HOURS="24"
SECRET_PASSWORD_MIN_LENGTH=6

#ENVIRONMENT based on Dev/Prod/QA/Stage
ENVIRONMENT= Dev