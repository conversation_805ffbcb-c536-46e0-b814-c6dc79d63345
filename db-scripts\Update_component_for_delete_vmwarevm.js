const session = db.getMongo().startSession();
session.startTransaction();

try {
  const vmConfig = db.menvcataloglevel04.findOne({ shortName: "deletevmvmware" });

  if (!vmConfig) {
    print("No document found with shortName 'deletevmvmware'. Aborting transaction.");
    session.abortTransaction();
  } else {
    const result = db.menvcataloglevel04.updateOne(
      { shortName: "deletevmvmware", component: "CreateVirtualMachine" },
      { $set: { component: "CreateVmwareVM" } }
    );

    print(`${result.modifiedCount} document(s) updated successfully.`);
    session.commitTransaction();
  }
} catch (e) {
  print("Error occurred:", e);
  session.abortTransaction();
} finally {
  session.endSession();
}
