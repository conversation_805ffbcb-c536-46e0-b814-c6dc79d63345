const session = db.getMongo().startSession();

session.startTransaction();

try {
  db.n8nconfig.updateOne(
    { workflow: 'VMWARE-Create-PROD-EAST' },
    {
      $set: {
        vmDeleteWorkflow: 'P5yPFtrZ3XKdBeX2',
        paceDeleteWorkflow: '',
      },
    },
  );

  db.n8nconfig.updateOne(
    { workflow: 'VMWARE-Create-PROD-WEST' },
    {
      $set: {
        vmDeleteWorkflow: 'P5yPFtrZ3XKdBeX2',
        paceDeleteWorkflow: '',
      },
    },
  );

  print(`Document was updated`);
  session.commitTransaction();
} catch (e) {
  session.abortTransaction();

  print('Transaction aborted due to error', e);
} finally {
  session.endSession();
}
