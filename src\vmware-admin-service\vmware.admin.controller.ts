import {
  Body,
  Controller,
  Post,
  Get,
  Delete,
  Put,
  Param,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { ApiBearerAuth, ApiSecurity, ApiTags } from '@nestjs/swagger';
import { PermissionGuard } from 'src/iam/permission.guard';
import { VmwareAdminService } from './vmware.admin.service';
import { VlookupDTO, VlookupDTOResponseDto } from './dto/vlookup.dto';
import { LoggerService } from '../loggers/logger.service';
import {
  SaveVCenterLookupDto,
  SaveVCenterLookupResponseDto,
} from './dto/save.vlookup.dto';
import { GetAllVcenterResponseDto } from './dto/getvCenterResponse.dto';
import { UpdateVmwareCloudDto } from './dto/updatevCenter.dto';

@ApiTags('Vmware-Admin')
@ApiBearerAuth()
@ApiSecurity('x-nebula-authorization')
@Controller('vmware-admin-service')
@UseGuards(PermissionGuard)
@UsePipes(new ValidationPipe({ whitelist: true, stopAtFirstError: true }))
export class VmwareAdminController {
  constructor(
    private readonly vmwareadminService: VmwareAdminService,
    private readonly logger: LoggerService,
  ) {}

  @Post('vCenter/lookup')
  @UsePipes(new ValidationPipe())
  async vCenterLookup(
    @Body() vlookupDTO: VlookupDTO,
  ): Promise<VlookupDTOResponseDto> {
    this.logger.log('passing vlookupRequestDto to vmware admin service');
    return await this.vmwareadminService.vLookup(vlookupDTO);
  }

  @Post('vCenters/lookup/refresh')
  @UsePipes(new ValidationPipe())
  async vCenterLookupRefresh(
    @Body() vlookupDTO: VlookupDTO,
  ): Promise<VlookupDTOResponseDto> {
    this.logger.log('passing vlookupRequestDto to vmware admin service');
    return await this.vmwareadminService.vLookupRefresh(vlookupDTO);
  }
  
  @Post('vCenter/lookup/save')
  @UsePipes(new ValidationPipe())
  async saveVcenterLookup(
    @Body() saveVlookupDTO: SaveVCenterLookupDto,
  ): Promise<SaveVCenterLookupResponseDto> {
    this.logger.log('passing vlookupRequestDto to vmware admin service');
    return await this.vmwareadminService.saveVlookUp(saveVlookupDTO);
  }

  @Get('vCenters')
  @UsePipes(new ValidationPipe())
  async getAllvCenters(): Promise<GetAllVcenterResponseDto[]> {
    this.logger.log('calling service method to get all the  vCenter');
    return await this.vmwareadminService.getAllVcenter();
  }

  @Get('vCenters/:cloudId')
  @UsePipes(new ValidationPipe())
  async getvCenterClustersHostsDatastoreOSLayouts(
    @Param('cloudId') cloudId: string,
  ) {
    this.logger.log(
      'calling service method to get all the  vCenter for cloudId - ',
      cloudId,
    );
    return await this.vmwareadminService.getvCenterClustersHostsDatastoreOSLayouts(
      cloudId,
    );
  }

  @Delete('vCenters')
  @UsePipes(new ValidationPipe())
  async deletevCenters(@Body() deletevCentersIds: string[]) {
    this.logger.log('calling service method to get all the  vCenter');
    return await this.vmwareadminService.deletevCenters(deletevCentersIds);
  }

  @Put('vCenters')
  @UsePipes(new ValidationPipe())
  async updatevCenters(@Body() updateVmwareCloudDto: UpdateVmwareCloudDto[]) {
    this.logger.log(
      'calling service method to update vCenters',
      updateVmwareCloudDto,
    );
    return await this.vmwareadminService.updatevCenter(updateVmwareCloudDto);
  }
}
