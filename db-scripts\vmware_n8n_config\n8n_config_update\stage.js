const session = db.getMongo().startSession();

session.startTransaction();

try {
  db.n8nconfig.updateOne(
    { workflow: 'VMWARE-Create-STG' },
    {
      $set: {
        vmDeleteWorkflow: '4Un8dPQQGRq5lpdD',
        paceDeleteWorkflow: '',
      },
    },
  );

  print(`Document was updated`);
  session.commitTransaction();
} catch (e) {
  session.abortTransaction();

  print('Transaction aborted due to error', e);
} finally {
  session.endSession();
}
