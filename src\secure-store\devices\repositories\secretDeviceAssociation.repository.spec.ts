import { SecretDeviceAssociationRepository } from './secretDeviceAssociation.repository';
import { GenericResourceTypes, CounterTypes } from '../../../types';
import { InternalServerErrorException } from '@nestjs/common';

jest.mock('mongoose', () => {
  const actualMongoose = jest.requireActual('mongoose');

  const mockModelInstance = {
    findOneAndUpdate: jest.fn(),
    create: jest.fn(),
    bulkWrite: jest.fn(),
    find: jest.fn(),
  };

  const mockModel = jest.fn().mockReturnValue(mockModelInstance);

  return {
    ...actualMongoose,
    connect: jest.fn().mockResolvedValue({
      model: mockModel,
    }),
    model: mockModel,
  };
});

describe('SecretDeviceAssociationRepository', () => {
  let repository: SecretDeviceAssociationRepository;

  beforeEach(() => {
    repository = new SecretDeviceAssociationRepository();
    jest.clearAllMocks();
  });

  it('should return generated secret association ID', () => {
    const id = repository.getSecretAssociationId(1001);
    expect(id).toBe(`${GenericResourceTypes.SecretDeviceAssociation}-1001`);
  });

  it('should get count and create counter if not found', async () => {
    const mockModelInstance = require('mongoose').model();
    mockModelInstance.findOneAndUpdate.mockResolvedValue(null);
    mockModelInstance.create.mockResolvedValue([{ counter: 1001 }]);

    const count = await repository.getCount(1);
    expect(count).toBe(1001);
    expect(mockModelInstance.create).toHaveBeenCalledWith([
      { type: CounterTypes.SECRET_DEVICE_ASSOCIATION, counter: 1001 },
    ]);
  });

  it('should get count and update counter if found', async () => {
    const mockModelInstance = require('mongoose').model();
    mockModelInstance.findOneAndUpdate.mockResolvedValue({ counter: 1002 });

    const count = await repository.getCount(1);
    expect(count).toBe(1002);
    expect(mockModelInstance.findOneAndUpdate).toHaveBeenCalledWith(
      { type: CounterTypes.SECRET_DEVICE_ASSOCIATION },
      { $inc: { counter: 1 } },
      { new: true },
    );
  });

  it('should bulk update successfully', async () => {
    const mockModelInstance = require('mongoose').model();
    mockModelInstance.bulkWrite.mockResolvedValue({});
    await expect(
      repository.bulkUpdate([{ updateOne: {} }]),
    ).resolves.not.toThrow();
  });

  it('should throw error on bulk update failure', async () => {
    const mockModelInstance = require('mongoose').model();
    mockModelInstance.bulkWrite.mockRejectedValue(new Error('fail'));
    await expect(repository.bulkUpdate([{ updateOne: {} }])).rejects.toThrow(
      InternalServerErrorException,
    );
  });

  it('should update many documents', async () => {
    const mockModelInstance = require('mongoose').model();
    mockModelInstance.bulkWrite.mockResolvedValue({ modifiedCount: 1 });
    const result = await repository.updateMany([
      {
        secretAssociationId: 'id1',
        deviceId: [124],
        secretId: ['s1'],
        updatedBy: 'user1',
        sourceSystem: 'keyguard',
      },
    ]);
    expect(result.modifiedCount).toBe(1);
  });

  it('should throw error on update many failure', async () => {
    const mockModelInstance = require('mongoose').model();
    mockModelInstance.bulkWrite.mockRejectedValue(new Error('fail'));
    await expect(
      repository.updateMany([
        {
          secretAssociationId: 'id1',
          deviceId: [766],
          secretId: ['s1'],
          updatedBy: 'user1',
          sourceSystem: 'keyguard',
        },
      ]),
    ).rejects.toThrow(InternalServerErrorException);
  });

  it('should find by secret and device IDs', async () => {
    const mockModelInstance = require('mongoose').model();
    mockModelInstance.find.mockResolvedValue([
      {
        _id: 'mocked-id',
        secretId: 's1',
        deviceId: 'd1',
        createdAt: new Date(),
        updatedAt: new Date(),
        toJSON: () => ({
          _id: 'mocked-id',
          secretId: 's1',
          deviceId: 'd1',
          createdAt: new Date(),
          updatedAt: new Date(),
        }),
      },
    ]);
    const result = await repository.findBySecretIdsAndDeviceIds(['s1'], [1]);
    expect(result.length).toBe(1);
  });

  it('should throw error on find failure', async () => {
    const mockModelInstance = require('mongoose').model();
    mockModelInstance.find.mockRejectedValue(new Error('fail'));
    await expect(
      repository.findBySecretIdsAndDeviceIds(['s1'], [1]),
    ).rejects.toThrow(InternalServerErrorException);
  });
});
