const session = db.getMongo().startSession();
session.startTransaction();

function runScript() {
  const paceDoc = db.n8nconfig.findOne({ workflow: 'VMWARE-Create-QA' });

  if (paceDoc) {
    // Remove the _id field to avoid duplicate key error
    delete paceDoc._id;

    // Change the name to "BLUE"
    paceDoc.workflow = 'PACE-VMWARE-Create-QA';

    // Insert the new document
    db.n8nconfig.insertOne(paceDoc);
  } else {
    print('vmware create doc not found in n8n config');
  }

  session.commitTransaction();
  print(`<PERSON>ript ran successfully`);
}

try {
  runScript();
} catch (e) {
  session.abortTransaction();
  print('Transaction aborted due to error', e);
} finally {
  session.endSession();
}
