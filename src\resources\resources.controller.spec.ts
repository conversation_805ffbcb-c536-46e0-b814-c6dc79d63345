import { Test, TestingModule } from '@nestjs/testing';
import { ResourcesController } from './resources.controller';
import { ResourcesService } from './resources.service';
import { ResourcesEntity } from '../naas/entities/resources.entity';
import {
  BadRequestException,
  HttpStatus,
  InternalServerErrorException,
} from '@nestjs/common';
import { PermissionGuard } from '../iam/permission.guard';
import { IamService } from '../iam/iam.service';
import { LoggerService } from '../loggers/logger.service';

type MockPermissionGuard = Partial<Record<keyof PermissionGuard, jest.Mock>>;
type MockIamService = Partial<Record<keyof IamService, jest.Mock>>;
describe('ResourcesController', () => {
  let controller: ResourcesController;
  let resourcesService: ResourcesService;
  let permissionGuard: MockPermissionGuard;
  let mockIamService: MockIamService;

  // Mock data for DTO/entity shape compliance
  const mockResource = {
    resourcesName: 'new resource',
    createdBy: 'user1',
    requestId: '2',
    status: 'SUCCESS',
    catalogType: 'linux 7',
    resourcesDetails: {},
    updatedBy: '',
    projectId: '',
    catalogLevel03: '',
    platformContext: { envId: 'env-1', catalogId: 'cat-1', domainId: 'dom-1' },
  };

  const mockResourceEntity = {
    id: '1',
    resourcesName: 'test-1',
    platformContext: { envId: 'env-1', catalogId: 'cat-1', domainId: 'dom-1' },
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'user1',
    updatedBy: '',
    requestId: '2',
    status: 'SUCCESS',
    catalogType: 'linux 7',
    resourcesDetails: {},
    projectId: '',
    catalogLevel03: '',
    resourceId: 'res-1',
    inventoryId: 123, // changed from string to number
    childId: ['child-1'], // changed from string to string array
    domain: 'domain-1',
    datacenter: 'dc-1',
    adminGroup: [''],
    // ...add  other required fields from ResourcesEntity
  };

  const mockCreateResourceResponse = {
    id: '1',
    resourceId: 'res-1',
    message: 'Resource created successfully',
  };

  const mockUpdateSuccess = {
    id: '2',
    message: 'Resource updated successfully',
  };

  const mockResourceUpdateSuccess = {
    successCode: HttpStatus.OK,
    message: 'Resource updated successfully',
  };

  const mockResourceStartSuccess = {
    successCode: HttpStatus.OK,
    message: 'started',
  };
  const mockResourceStopSuccess = {
    successCode: HttpStatus.OK,
    message: 'stopped',
  };
  const mockResourceRestartSuccess = {
    successCode: HttpStatus.OK,
    message: 'restarted',
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ResourcesController],
      providers: [
        {
          provide: ResourcesService,
          useValue: {
            getResourcesList: jest
              .fn()
              .mockResolvedValue([mockResourceEntity, mockResourceEntity]),
            updateResourcesSystemUpdate: jest.fn(),
            createResource: jest.fn(),
            getResource: jest.fn(),
            deleteResource: jest.fn(),
            stopVmResources: jest.fn(),
            startVmResources: jest.fn(),
            restartVmResources: jest.fn(),
            getLatestStatus: jest.fn(),
          },
        },
        {
          provide: PermissionGuard,
          useValue: {
            canActivate: jest.fn(),
          },
        },
        {
          provide: IamService,
          useValue: {
            findCatalogL4Permissions: jest.fn(),
            findCatalogLevel4ByRequestType: jest.fn(),
            findAllowedRequestTypeByPermissionKey: jest.fn(),
            findUserProjects: jest.fn(),
          },
        },
        {
          provide: LoggerService,
          useValue: {
            log: jest.fn(),
            error: jest.fn(),
            debug: jest.fn(),
            info: jest.fn(),
            fatal: jest.fn(),
            warn: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<ResourcesController>(ResourcesController);
    resourcesService = module.get<ResourcesService>(ResourcesService);
    permissionGuard = module.get<MockPermissionGuard>(PermissionGuard);
    mockIamService = module.get<MockIamService>(IamService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getResourcesList', () => {
    it('should call service to fetch resources', async () => {
      await controller.getResourcesList();
      expect(resourcesService.getResourcesList).toHaveBeenCalled();
    });
    it('should throw the caught error', async () => {
      jest
        .spyOn(resourcesService, 'getResourcesList')
        .mockImplementation(() =>
          Promise.reject(new InternalServerErrorException()),
        );
      await expect(controller.getResourcesList()).rejects.toThrow(
        new InternalServerErrorException(),
      );
    });
  });

  describe('createResource', () => {
    it('should call service to create resources', async () => {
      jest
        .spyOn(resourcesService, 'createResource')
        .mockImplementation(() => Promise.resolve(mockCreateResourceResponse));
      const response = await controller.createResource(mockResource);
      expect(response).toEqual(mockCreateResourceResponse);
      expect(resourcesService.createResource).toHaveBeenCalledWith(
        mockResource,
      );
    });
    it('should throw the caught error', async () => {
      jest
        .spyOn(resourcesService, 'createResource')
        .mockImplementation(() =>
          Promise.reject(new InternalServerErrorException()),
        );
      await expect(controller.createResource(mockResource)).rejects.toThrow(
        new InternalServerErrorException(),
      );
    });
  });

  describe('updateResourcesSystemUpdate', () => {
    const mockResourceName = 'test-1';
    it('should call service to update resources', async () => {
      jest
        .spyOn(resourcesService, 'updateResourcesSystemUpdate')
        .mockImplementation(() => Promise.resolve(mockUpdateSuccess));
      const response = await controller.updateResourcesSystemUpdate(
        mockResourceName,
        {},
      );
      expect(response).toEqual(mockUpdateSuccess);
      expect(resourcesService.updateResourcesSystemUpdate).toHaveBeenCalled();
    });
    it('should throw the caught error', async () => {
      jest
        .spyOn(resourcesService, 'updateResourcesSystemUpdate')
        .mockImplementation(() =>
          Promise.reject(new InternalServerErrorException()),
        );
      await expect(
        controller.updateResourcesSystemUpdate(mockResourceName, {}),
      ).rejects.toThrow(new InternalServerErrorException());
    });
  });

  describe('getResources', () => {
    const mockResourceName = 'test-1';
    it('should call service to get resources', async () => {
      jest
        .spyOn(resourcesService, 'getResource')
        .mockImplementation(() => Promise.resolve(mockResourceEntity));
      const response = await controller.getResources(mockResourceName);
      expect(response).toEqual(mockResourceEntity);
      expect(resourcesService.getResource).toHaveBeenCalledWith(
        mockResourceName,
      );
    });
    it('should throw the caught error', async () => {
      jest
        .spyOn(resourcesService, 'getResource')
        .mockImplementation(() =>
          Promise.reject(new InternalServerErrorException()),
        );
      await expect(controller.getResources(mockResourceName)).rejects.toThrow(
        new InternalServerErrorException(),
      );
    });
  });

  describe('deleteResource', () => {
    it('should call service to delete resources', async () => {
      const mockBody = { resourceIds: ['id1', 'id2'], deeplinkUrl: 'url' };
      const mockResponse = {
        id: '2',
        message: 'deleted',
        successCode: HttpStatus.OK,
      };
      jest
        .spyOn(resourcesService, 'deleteResource')
        .mockImplementation(() => Promise.resolve(mockResponse));
      const response = await controller.deleteResource(mockBody);
      expect(response).toEqual(mockResponse);
      expect(resourcesService.deleteResource).toHaveBeenCalledWith(
        mockBody.resourceIds,
        mockBody.deeplinkUrl,
      );
    });
    it('should throw BadRequestException for invalid resourceIds', async () => {
      await expect(
        controller.deleteResource({ resourceIds: [] }),
      ).rejects.toThrow(BadRequestException);
    });
    it('should throw the caught error', async () => {
      const mockBody = { resourceIds: ['id1'], deeplinkUrl: 'url' };
      jest
        .spyOn(resourcesService, 'deleteResource')
        .mockImplementation(() =>
          Promise.reject(new InternalServerErrorException()),
        );
      await expect(controller.deleteResource(mockBody)).rejects.toThrow(
        new InternalServerErrorException(),
      );
    });
  });

  describe('stopVmResources', () => {
    it('should call service to stop VMs', async () => {
      const mockDto = { resourceIds: ['id1', 'id2'] };
      jest
        .spyOn(resourcesService, 'stopVmResources')
        .mockImplementation(() => Promise.resolve(mockResourceStopSuccess));
      const response = await controller.stopVmResources(mockDto);
      expect(response).toEqual(mockResourceStopSuccess);
      expect(resourcesService.stopVmResources).toHaveBeenCalledWith(
        mockDto.resourceIds,
      );
    });
    it('should throw the caught error', async () => {
      const mockDto = { resourceIds: ['id1'] };
      jest
        .spyOn(resourcesService, 'stopVmResources')
        .mockImplementation(() =>
          Promise.reject(new InternalServerErrorException()),
        );
      await expect(controller.stopVmResources(mockDto)).rejects.toThrow(
        new InternalServerErrorException(),
      );
    });
  });

  describe('startVmResources', () => {
    it('should call service to start VMs', async () => {
      const mockDto = { resourceIds: ['id1', 'id2'] };
      jest
        .spyOn(resourcesService, 'startVmResources')
        .mockImplementation(() => Promise.resolve(mockResourceStartSuccess));
      const response = await controller.startVmResources(mockDto);
      expect(response).toEqual(mockResourceStartSuccess);
      expect(resourcesService.startVmResources).toHaveBeenCalledWith(
        mockDto.resourceIds,
      );
    });
    it('should throw the caught error', async () => {
      const mockDto = { resourceIds: ['id1'] };
      jest
        .spyOn(resourcesService, 'startVmResources')
        .mockImplementation(() =>
          Promise.reject(new InternalServerErrorException()),
        );
      await expect(controller.startVmResources(mockDto)).rejects.toThrow(
        new InternalServerErrorException(),
      );
    });
  });

  describe('restartVmResources', () => {
    it('should call service to restart VMs', async () => {
      const mockDto = { resourceIds: ['id1', 'id2'] };
      jest
        .spyOn(resourcesService, 'restartVmResources')
        .mockImplementation(() => Promise.resolve(mockResourceRestartSuccess));
      const response = await controller.restartVmResources(mockDto);
      expect(response).toEqual(mockResourceRestartSuccess);
      expect(resourcesService.restartVmResources).toHaveBeenCalledWith(
        mockDto.resourceIds,
      );
    });
    it('should throw the caught error', async () => {
      const mockDto = { resourceIds: ['id1'] };
      jest
        .spyOn(resourcesService, 'restartVmResources')
        .mockImplementation(() =>
          Promise.reject(new InternalServerErrorException()),
        );
      await expect(controller.restartVmResources(mockDto)).rejects.toThrow(
        new InternalServerErrorException(),
      );
    });
  });

  describe('getLatestStatus', () => {
    it('should call service to get latest status', async () => {
      const resourceId = 'id1';
      jest
        .spyOn(resourcesService, 'getLatestStatus')
        .mockImplementation(() => Promise.resolve(mockResourceEntity));
      const response = await controller.getLatestStatus(resourceId);
      expect(response).toEqual(mockResourceEntity);
      expect(resourcesService.getLatestStatus).toHaveBeenCalledWith(resourceId);
    });
    it('should throw the caught error', async () => {
      const resourceId = 'id1';
      jest
        .spyOn(resourcesService, 'getLatestStatus')
        .mockImplementation(() =>
          Promise.reject(new InternalServerErrorException()),
        );
      await expect(controller.getLatestStatus(resourceId)).rejects.toThrow(
        new InternalServerErrorException(),
      );
    });
  });
});
