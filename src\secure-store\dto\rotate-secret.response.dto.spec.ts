import { RotateSecretResponseDto } from './rotate-secret.response.dto';

describe('RotateSecretResponseDto', () => {
  it('should instantiate with a message', () => {
    const response = new RotateSecretResponseDto();
    response.message = 'Secret rotated successfully';

    expect(response.message).toBe('Secret rotated successfully');
  });

  it('should allow updating the message', () => {
    const response = new RotateSecretResponseDto();
    response.message = 'Initial message';
    response.message = 'Updated message';

    expect(response.message).toBe('Updated message');
  });
});
