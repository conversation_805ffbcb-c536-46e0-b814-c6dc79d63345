// add all env related constants here
export const E<PERSON><PERSON>RONMENT_VARS = {
  SECRETS_ROTATION_INTERVAL_IN_MINS: 'SECRETS_ROTATION_INTERVAL_IN_MINS',
  WATCHER_SERVICE_BASE_URL: 'WATCHER_SERVICE_BASE_URL',
  NEBULA_VMWARE_SERVICE_BASE_URL: 'NEBULA_VMWARE_SERVICE_BASE_URL',
  PASSWORD_ENCRYPTION_KEY: 'PASSWORD_ENCRYPTION_KEY',
  GLOBAL_ROUTE_PREFIX: 'GLOBAL_ROUTE_PREFIX',
  COMPUTE_API_BASE_URL: 'COMPUTE_API_BASE_URL',
  IAM_SERVICE_BASE_URL: 'IAM_SERVICE_BASE_URL',
  TAGGING_SERVICE_API_BASE_URL: 'TAGGING_SERVICE_API_BASE_URL',
  GOLD<PERSON>_CONFIG_SERVICE_BASE_URL: 'GOLD<PERSON>_CONFIG_SERVICE_BASE_URL',
  METADATA_SERVICE_BASE_URL: 'METADATA_SERVICE_BASE_URL',
  PORT: 'PORT',
  RMQ_URI: 'RMQ_URI',
  RMQ_IPAM_QUEUE: 'RMQ_IPAM_QUEUE',
  RMQ_IPAM_QUEUE_DURABLE: 'RMQ_IPAM_QUEUE_DURABLE',
  RMQ_MONITORING_QUEUE: 'RMQ_MONITORING_QUEUE',
  RMQ_MONITORING_QUEUE_DURABLE: 'RMQ_MONITORING_QUEUE_DURABLE',
  RMQ_WF_ORCHESTRATOR_QUEUE: 'RMQ_WF_ORCHESTRATOR_QUEUE',
  RMQ_WF_ORCHESTRATOR_QUEUE_DURABLE: 'RMQ_WF_ORCHESTRATOR_QUEUE_DURABLE',
  RMQ_COMPUTE_QUEUE: 'RMQ_COMPUTE_QUEUE',
  RMQ_COMPUTE_QUEUE_DURABLE: 'RMQ_COMPUTE_QUEUE_DURABLE',
  RMQ_CHARTER_LAB_COMPUTE_QUEUE: 'RMQ_CHARTER_LAB_COMPUTE_QUEUE',
  RMQ_CHARTER_LAB_COMPUTE_QUEUE_DURABLE:
    'RMQ_CHARTER_LAB_COMPUTE_QUEUE_DURABLE',
  RMQ_VMWARE_COMPUTE_QUEUE: 'RMQ_VMWARE_COMPUTE_QUEUE',
  RMQ_VMWARE_COMPUTE_QUEUE_DURABLE: 'RMQ_VMWARE_COMPUTE_QUEUE__DURABLE',
  RMQ_VMWARE_LAB_COMPUTE_QUEUE: 'RMQ_VMWARE_LAB_COMPUTE_QUEUE',
  RMQ_VMWARE_LAB_COMPUTE_QUEUE_DURABLE: 'RMQ_VMWARE_LAB_COMPUTE_QUEUE_DURABLE',
  RMQ_BLUE_COMPUTE_QUEUE: 'RMQ_BLUE_COMPUTE_QUEUE',
  RMQ_BLUE_COMPUTE_QUEUE_DURABLE: 'RMQ_BLUE_COMPUTE_QUEUE_DURABLE',
  RMQ_FIREWALL_QUEUE: 'RMQ_FIREWALL_QUEUE',
  RMQ_FIREWALL_QUEUE_DURABLE: 'RMQ_FIREWALL_QUEUE_DURABLE',
  APPROVALS_SERVICE_BASE_URL: 'APPROVALS_SERVICE_BASE_URL',
  INTEGRATION_API_SERVICE_BASE_URL: 'INTEGRATION_API_SERVICE_BASE_URL',
  CACHING_SERVICE_BASE_URL: 'CACHING_SERVICE_BASE_URL',
  ENABLE_CACHE: 'ENABLE_CACHE',
  RMQ_DELETE_VM_QUEUE: 'RMQ_DELETE_VM_QUEUE',
  RMQ_DELETE_VM_QUEUE_DURABLE: 'RMQ_DELETE_VM_QUEUE_DURABLE',
  RMQ_DELETE_PACE_VM_QUEUE: 'RMQ_DELETE_PACE_VM_QUEUE',
  RMQ_DELETE_VM_VMWARE_QUEUE: 'RMQ_DELETE_VM_VMWARE_QUEUE',
  RMQ_DELETE_PACE_VM_QUEUE_DURABLE: 'RMQ_DELETE_PACE_VM_QUEUE_DURABLE',
  RMQ_DELETE_VM_VMWARE_QUEUE_DURABLE: 'RMQ_DELETE_VM_VMWARE_QUEUE_DURABLE',
  RMQ_DELETE_BLUE_VM_QUEUE: 'RMQ_DELETE_BLUE_VM_QUEUE',
  RMQ_DELETE_BLUE_VM_QUEUE_DURABLE: 'RMQ_DELETE_BLUE_VM_QUEUE_DURABLE',
  RMQ_CREATE_DB_QUEUE: 'RMQ_CREATE_DB_QUEUE',
  RMQ_CREATE_DB_QUEUE_DURABLE: 'RMQ_CREATE_DB_QUEUE_DURABLE',
  APPROVE_OWN_REQUEST: 'APPROVE_OWN_REQUEST',
  LOG_LEVEL: 'LOG_LEVEL',
  ENVIRONMENT_BASE_URL: 'ENVIRONMENT_BASE_URL',
  CAPACITY_PLANNING_SERVICE_BASE_URL: 'CAPACITY_PLANNING_SERVICE_BASE_URL',
  NEBULA_METRICS_SERVICE_BASE_URL: 'NEBULA_METRICS_SERVICE_BASE_URL',
  RMQ_COMMON_FIREWALL_POLICY_QUEUE: 'RMQ_COMMON_FIREWALL_POLICY_QUEUE',
  RMQ_COMMON_FIREWALL_POLICY_QUEUE_DURABLE:
    'RMQ_COMMON_FIREWALL_POLICY_QUEUE_DURABLE',
  RMQ_FIREWALL_V2_QUEUE: 'RMQ_FIREWALL_V2_QUEUE',
  RMQ_FIREWALL_V2_QUEUE_DURABLE: 'RMQ_FIREWALL_V2_QUEUE_DURABLE',
  VM_START_STOP_DELAY: 'VM_START_STOP_DELAY',
  VM_RESTART_DELAY: 'VM_RESTART_DELAY',
  RMQ_STORAGE_S3_QUEUE: 'RMQ_STORAGE_S3_QUEUE',
  RMQ_STORAGE_S3_QUEUE_DURABLE: 'RMQ_STORAGE_S3_QUEUE_DURABLE',
  RMQ_MODIFY_STORAGE_S3_QUEUE: 'RMQ_MODIFY_STORAGE_S3_QUEUE',
  RMQ_MODIFY_STORAGE_S3_QUEUE_DURABLE: 'RMQ_MODIFY_STORAGE_S3_QUEUE_DURABLE',
  RMQ_STORAGE_NFS_QUEUE: 'RMQ_STORAGE_NFS_QUEUE',
  RMQ_STORAGE_NFS_QUEUE_DURABLE: 'RMQ_STORAGE_NFS_QUEUE_DURABLE',
  RMQ_START_STOP_RESTART_VM_QUEUE: 'RMQ_START_STOP_RESTART_VM_QUEUE',
  RMQ_START_STOP_RESTART_VM_QUEUE_DURABLE:
    'RMQ_START_STOP_RESTART_VM_QUEUE_DURABLE',
  DEFAULT_CREATED_BY_FOR_CHILD: 'DEFAULT_CREATED_BY_FOR_CHILD',
  RMQ_AWS_CREATE_SUBACCOUNT_QUEUE: 'RMQ_AWS_CREATE_SUBACCOUNT_QUEUE',
  RMQ_AWS_CREATE_SUBACCOUNT_QUEUE_DURABLE:
    'RMQ_AWS_CREATE_SUBACCOUNT_QUEUE_DURABLE',
  ACTIVITY_LOGS_SERVICE_BASE_URL: 'ACTIVITY_LOGS_SERVICE_BASE_URL',
  RISK_ANALYSIS_LOG_STEP: 'RISK_ANALYSIS_LOG_STEP',
  GIT_PIPELINE_LOG_STEP: 'GIT_PIPELINE_LOG_STEP',
  DAP_UPDATE_LOG_STEP: 'DAP_UPDATE_LOG_STEP',
  DAP_DEPLOYMENT_LOG_STEP: 'DAP_DEPLOYMENT_LOG_STEP',
  DAP_DEPLOYMENT_CHILD_LOG_STEP: 'DAP_DEPLOYMENT_CHILD_LOG_STEP',
  DESIGNER_RESULTS_LOG_STEP: 'DESIGNER_RESULTS_LOG_STEP',
  COMPLETING_LOG_FW2_CODE: 'COMPLETING_LOG_FW2_CODE',
  COMPLETING_LOG_FW2_NAME: 'COMPLETING_LOG_FW2_NAME',
  RMQ_AWS_CREATE_SUBACCOUNT_GROUP_QUEUE:
    'RMQ_AWS_CREATE_SUBACCOUNT_GROUP_QUEUE',
  RMQ_AWS_CREATE_SUBACCOUNT_GROUP_QUEUE_DURABLE:
    'RMQ_AWS_CREATE_SUBACCOUNT_GROUP_QUEUE_DURABLE',
  RMQ_AWS_CREATE_USER_QUEUE: 'RMQ_AWS_CREATE_USER_QUEUE',
  RMQ_AWS_CREATE_USER_QUEUE_DURABLE: 'RMQ_AWS_CREATE_USER_QUEUE_DURABLE',
  RMQ_F5_LOAD_BALANCE_QUEUE: 'RMQ_F5_LOAD_BALANCE_QUEUE',
  TICKET_MANAGEMENT_SERVICE_BASE_URL: 'TICKET_MANAGEMENT_SERVICE_BASE_URL',
  RMQ_REGENERATE_KEYS_QUEUE: 'RMQ_REGENERATE_KEYS_QUEUE',
  RMQ_REGENERATE_KEYS_QUEUE_DURABLE: 'RMQ_REGENERATE_KEYS_QUEUE_DURABLE',
  RMQ_MANAGE_SUB_ACCOUNT_GROUP_PERMISSION_QUEUE:
    'RMQ_MANAGE_SUB_ACCOUNT_GROUP_PERMISSION_QUEUE',
  RMQ_MANAGE_SUB_ACCOUNT_GROUP_PERMISSION_QUEUE_DURABLE:
    'RMQ_MANAGE_SUB_ACCOUNT_GROUP_PERMISSION_QUEUE_DURABLE',
  RMQ_MANAGE_PERMISSION_SET_CREATE_QUEUE:
    'RMQ_MANAGE_PERMISSION_SET_CREATE_QUEUE',
  RMQ_MANAGE_PERMISSION_SET_CREATE_QUEUE_DURABLE:
    'RMQ_MANAGE_PERMISSION_SET_CREATE_QUEUE_DURABLE',
  RMQ_MANAGE_PERMISSION_SET_REMOVE_QUEUE:
    'RMQ_MANAGE_PERMISSION_SET_REMOVE_QUEUE',
  RMQ_MANAGE_PERMISSION_SET_REMOVE_QUEUE_DURABLE:
    'RMQ_MANAGE_PERMISSION_SET_REMOVE_QUEUE_DURABLE',
  RMQ_DELETE_S3_QUEUE: 'RMQ_DELETE_S3_QUEUE',
  RMQ_DELETE_S3_QUEUE_DURABLE: 'RMQ_DELETE_S3_QUEUE_DURABLE',
  RMQ_INTERNAL_CERTIFICATE_QUEUE: 'RMQ_INTERNAL_CERTIFICATE_QUEUE',
  RMQ_INTERNAL_CERTIFICATE_QUEUE_DURABLE:
    'RMQ_INTERNAL_CERTIFICATE_QUEUE_DURABLE',
  RMQ_F5_LOAD_BALANCE_QUEUE_DURABLE: 'RMQ_F5_LOAD_BALANCE_QUEUE_DURABLE',
  RMQ_DELETE_NFS_QUEUE: 'RMQ_DELETE_NFS_QUEUE',
  RMQ_DELETE_NFS_QUEUE_DURABLE: 'RMQ_DELETE_NFS_QUEUE_DURABLE',
  RMQ_MODIFY_NFS_QUEUE: 'RMQ_MODIFY_NFS_QUEUE',
  RMQ_MODIFY_NFS_QUEUE_DURABLE: 'RMQ_MODIFY_NFS_QUEUE_DURABLE',
  RMQ_WORKFLOW_RETRY_QUEUE: 'RMQ_WORKFLOW_RETRY_QUEUE',
  RMQ_WORKFLOW_RETRY_QUEUE_DURABLE: 'RMQ_WORKFLOW_RETRY_QUEUE_DURABLE',
  RMQ_WORKFLOW_RETRY_VMWARE_QUEUE: 'RMQ_WORKFLOW_RETRY_VMWARE_QUEUE',
  RMQ_WORKFLOW_RETRY_VMWARE_QUEUE_DURABLE:
    'RMQ_WORKFLOW_RETRY_VMWARE_QUEUE_DURABLE',
  QUALYS_CUSTOMER_ID: 'QUALYS_CUSTOMER_ID',
  QUALYS_SERVER_URL: 'QUALYS_SERVER_URL',
  SKIP_SELF_APPROVAL_CATALOGS: 'SKIP_SELF_APPROVAL_CATALOGS',
  JIRA_API_BASE_URL: 'JIRA_API_BASE_URL',
  CHARTER_LAB_INFOBLOX_ENABLED: 'CHARTER_LAB_INFOBLOX_ENABLED',
  IPM_SERVICE_BASE_URL: 'IPM_SERVICE_BASE_URL',
  ITENTIAL_SERVICE_BASE_URL: 'ITENTIAL_SERVICE_BASE_URL',
  COMMON_FIREWALL_SERVICE_BASE_URL: 'COMMON_FIREWALL_SERVICE_BASE_URL',
  MONITORING_SERVICE_BASE_URL: 'MONITORING_SERVICE_BASE_URL',
  STORAGE_SERVICE_BASE_URL: 'STORAGE_SERVICE_BASE_URL',
  TUFIN_SERVICE_BASE_URL: 'TUFIN_SERVICE_BASE_URL',
  PINXT_TOKEN_AUTHORIZATION: 'PINXT_TOKEN_AUTHORIZATION',
  PINXT_TOKEN_URL: 'PINXT_TOKEN_URL',
  USE_PROXY: 'USE_PROXY',
  PROXY_HOST: 'PROXY_HOST',
  PROXY_PORT: 'PROXY_PORT',
  PROXY_PROTOCOL: 'PROXY_PROTOCOL',
  NON_PINXT_CLIENTS: 'NON_PINXT_CLIENTS',
  RMQ_DNP_QUEUE: 'RMQ_DNP_QUEUE',
  RMQ_DNP_QUEUE_DURABLE: 'RMQ_DNP_QUEUE_DURABLE',
  LOAD_BALANCER_VIP_NAME: 'LOAD_BALANCER_VIP_NAME',
  X_FORWARD_FOR: 'X_FORWARD_FOR',
  PERSISTENCE: 'PERSISTENCE',
  SOURCE_NAT: 'SOURCE_NAT',
  SSL_OFFLOAD: 'SSL_OFFLOAD',
  SSL_SETUP: 'SSL_SETUP',
  HEALTH_MONITOR_NAME: 'HEALTH_MONITOR_NAME',
  HEALTH_MONITOR_STRING_SEND: 'HEALTH_MONITOR_STRING_SEND',
  IDLE_TIMEOUT: 'IDLE_TIMEOUT',
  VLANS_AND_TUNNELS: 'VLANS_AND_TUNNELS',
  PRIORITY_GROUP_AND_ACTIVATION: 'PRIORITY_GROUP_AND_ACTIVATION',
  FORWARDING_PORT_POOL_MEMBER: 'FORWARDING_PORT_POOL_MEMBER',
  LOAD_BALANCER_POOL_MEMBERS: 'LOAD_BALANCER_POOL_MEMBERS',
  RMQ_FIREWALL_MIGRATION_QUEUE: 'RMQ_FIREWALL_MIGRATION_QUEUE',
  RMQ_FIREWALL_MIGRATION_QUEUE_DURABLE: 'RMQ_FIREWALL_MIGRATION_QUEUE_DURABLE',
  MAX_RETRY_COUNT: 'MAX_RETRY_COUNT',
  SUBDOMAIN: 'SUBDOMAIN',
  DOMAIN: 'DOMAIN',
  XXX: 'XXX',
  QUALYS_ALLOWED_LAYOUT: 'QUALYS_ALLOWED_LAYOUT',
  CENTRIFY_ALLOWED_LAYOUT: 'CENTRIFY_ALLOWED_LAYOUT',
  TANIUM_ALLOWED_LAYOUT: 'TANIUM_ALLOWED_LAYOUT',
  FIREWALL_V1_REQUEST_STATUS: 'FIREWALL_V1_REQUEST_STATUS',
  RMQ_DELETE_DB_QUEUE: 'RMQ_DELETE_DB_QUEUE',
  RMQ_DELETE_DB_QUEUE_DURABLE: 'RMQ_DELETE_DB_QUEUE_DURABLE',
  AWS_MANAGEMENT_BASE_URL: 'AWS_MANAGEMENT_BASE_URL',
  RMQ_BULK_VM_IMPORT_QUEUE: 'RMQ_BULK_VM_IMPORT_QUEUE',
  RMQ_BULK_VM_IMPORT_QUEUE_DURABLE: 'RMQ_BULK_VM_IMPORT_QUEUE_DURABLE',
  RMQ_ONBOARD_PROJECT_QUEUE: 'RMQ_ONBOARD_PROJECT_QUEUE',
  RMQ_ONBOARD_PROJECT_QUEUE_DURABLE: 'RMQ_ONBOARD_PROJECT_QUEUE_DURABLE',
  RMQ_CATALOG_ACCESS_REQUEST_QUEUE: 'RMQ_CATALOG_ACCESS_REQUEST_QUEUE',
  RMQ_CATALOG_ACCESS_REQUEST_QUEUE_DURABLE:
    'RMQ_CATALOG_ACCESS_REQUEST_QUEUE_DURABLE',
  MIGRATED_V2_DEEPLINK_URL: 'MIGRATED_V2_DEEPLINK_URL',
  RMQ_RESUBMIT_FIREWALL_V2_REQUEST_QUEUE:
    'RMQ_RESUBMIT_FIREWALL_V2_REQUEST_QUEUE',
  RMQ_RESUBMIT_FIREWALL_V2_REQUEST_QUEUE_DURABLE:
    'RMQ_RESUBMIT_FIREWALL_V2_REQUEST_QUEUE_DURABLE',
  APPROVALS_EMAIL_URL: 'APPROVALS_EMAIL_URL',
  PROCESSING_START_LINE_FOR_DFM_WITH_INSTRUCTIONS:
    'PROCESSING_START_LINE_FOR_DFM_WITH_INSTRUCTIONS',
  PROCESSING_START_LINE_FOR_DFM_WITHOUT_INSTRUCTIONS:
    'PROCESSING_START_LINE_FOR_DFM_WITHOUT_INSTRUCTIONS',
  RMQ_ONBOARD_GROUP_QUEUE: 'RMQ_ONBOARD_GROUP_QUEUE',
  RMQ_ONBOARD_GROUP_QUEUE_DURABLE: 'RMQ_ONBOARD_GROUP_QUEUE_DURABLE',
  RMQ_CREATE_NAMESPACE_QUEUE: 'RMQ_CREATE_NAMESPACE_QUEUE',
  RMQ_CREATE_NAMESPACE_QUEUE_DURABLE: 'RMQ_CREATE_NAMESPACE_QUEUE_DURABLE',
  CMDB_SERVICE_BASE_URL: 'CMDB_SERVICE_BASE_URL',
  MIGRATED_V1_PROJECT: 'MIGRATED_V1_PROJECT',
  RMQ_SPEC_FLOW_QUEUE: 'RMQ_SPEC_FLOW_QUEUE',
  RMQ_SPEC_FLOW_QUEUE_DURABLE: 'RMQ_SPEC_FLOW_QUEUE_DURABLE',
  RMQ_CREATE_PUBLIC_CLOUD_VPC_QUEUE: 'RMQ_CREATE_PUBLIC_CLOUD_VPC_QUEUE',
  RMQ_CREATE_PUBLIC_CLOUD_VPC_QUEUE_DURABLE:
    'RMQ_CREATE_PUBLIC_CLOUD_VPC_QUEUE_DURABLE',
  CHERWELL_RETRY_REQUESTING_USER: 'CHERWELL_RETRY_REQUESTING_USER',
  REMEDY_RETRY_REQUESTING_USER: 'REMEDY_RETRY_REQUESTING_USER',
  OPTIMIZATION_SPLIT_LENGTH: 'OPTIMIZATION_SPLIT_LENGTH',
  VMWARE_ADAPTER_SERVICE_API_BASE_URL: 'VMWARE_ADAPTER_SERVICE_API_BASE_URL',
  VMWARE_MAX_CLUSTERES: 'VMWARE_MAX_CLUSTERES',
  VMWARE_MAX_LAYOUTS: 'VMWARE_MAX_LAYOUTS',
  VMWARE_MAX_NETWORKS: 'VMWARE_MAX_NETWORKS',
  VM_SEQUENCE_NUMBER_MAX_VALUE: 'VM_SEQUENCE_NUMBER_MAX_VALUE',
  SEARCH_SEQUENCE_NUMBER_RETRY_COUNT: 'SEARCH_SEQUENCE_NUMBER_RETRY_COUNT',
  AWS_IPAM_API_BASE_URL: 'AWS_IPAM_API_BASE_URL',
  AWS_IPAM_REFRESH_MINUTES: 'AWS_IPAM_REFRESH_MINUTES',
  USE_AWS_IPAM_PROXY: 'USE_AWS_IPAM_PROXY',
  RMQ_DELETE_PUBLIC_CLOUD_QUEUE: 'RMQ_DELETE_PUBLIC_CLOUD_QUEUE',
  RMQ_DELETE_PUBLIC_CLOUD_QUEUE_DURABLE:
    'RMQ_DELETE_PUBLIC_CLOUD_QUEUE_DURABLE',
  AWS_IPAM_PROXY_HOST: 'AWS_IPAM_PROXY_HOST',
  AWS_IPAM_PROXY_PORT: 'AWS_IPAM_PROXY_PORT',
  AWS_IPAM_PROXY_PROTOCOL: 'AWS_IPAM_PROXY_PROTOCOL',
  SECRETS_MANAGEMENT_SERVICE_BASE_URL: 'SECRETS_MANAGEMENT_SERVICE_BASE_URL',
  ENABLE_DNP_SERVICE: 'ENABLE_DNP_SERVICE',
  DNPDAP_SERVICE_BASE_URL: 'DNPDAP_SERVICE_BASE_URL',
  RMQ_RECONFIGURE_VM_QUEUE: 'RMQ_RECONFIGURE_VM_QUEUE',
  RMQ_RECONFIGURE_VM_QUEUE_DURABLE: 'RMQ_RECONFIGURE_VM_QUEUE_DURABLE',
  RMQ_ZTP_QUEUE: 'RMQ_ZTP_QUEUE',
  RMQ_ZTP_QUEUE_DURABLE: 'RMQ_ZTP_QUEUE_DURABLE',
  RMQ_PACE_RECONFIGURE_VM_QUEUE: 'RMQ_PACE_RECONFIGURE_VM_QUEUE',
  RMQ_RECONFIGURE_PACE_VM_QUEUE_DURABLE:
    'RMQ_RECONFIGURE_PACE_VM_QUEUE_DURABLE',
  DAP_DNP_ACITIVITY_LOG_SEQUENCE: 'DAP_DNP_ACITIVITY_LOG_SEQUENCE',
  ADHOC_LOCATION: 'ADHOC_LOCATION',
  ADHOC_NAMESPACE: 'ADHOC_NAMESPACE',
  CERTIFICATE_MANAGEMENT_SERVICE_BASE_URL:
    'CERTIFICATE_MANAGEMENT_SERVICE_BASE_URL',
  RMQ_EDIT_VM_VMWARE_QUEUE: 'RMQ_EDIT_VM_VMWARE_QUEUE',
  RMQ_EDIT_VM_VMWARE_QUEUE_DURABLE: 'RMQ_EDIT_VM_VMWARE_QUEUE_DURABLE',
  RMQ_EDIT_PACE_VM_VMWARE_QUEUE: 'RMQ_EDIT_PACE_VM_VMWARE_QUEUE',
  RMQ_EDIT_PACE_VM_VMWARE_QUEUE_DURABLE:
    'RMQ_EDIT_PACE_VM_VMWARE_QUEUE_DURABLE',
  RETRY_MAX_COUNT: 'RETRY_MAX_COUNT',
  RETRY_TIME_DELAY: 'RETRY_TIME_DELAY',
  REACTIVATE_SECRET_DURATION_HRS: 'REACTIVATE_SECRET_DURATION_HRS',
  MASTER_TOKEN_FOLDER: 'MASTER_TOKEN_FOLDER',
  VAULT_MASTER_NAMESPACE: 'VAULT_MASTER_NAMESPACE',
  VAULT_TOKEN_RENEWAL_RATIO: 'VAULT_TOKEN_RENEWAL_RATIO',
  SECRET_UNAVAILABLE_MESSAGE: 'SECRET_UNAVAILABLE_MESSAGE',
  FW_CREATE_JIRA_TICKET_STEP: 'FW_CREATE_JIRA_TICKET_STEP',
  FW_CANCEL_JIRA_TICKET_STEP: 'FW_CANCEL_JIRA_TICKET_STEP',
  FW_CLOSE_JIRA_TICKET_STEP: 'FW_CLOSE_JIRA_TICKET_STEP',
  FW_CANCEL_TUFIN_TICKET_STEP: 'FW_CANCEL_TUFIN_TICKET_STEP',
  RED_VM_DOMAINS: 'RED_VM_DOMAINS',
  PACE_VM_DOMAINS: 'PACE_VM_DOMAINS',
  VALID_SPECIAL_CHARS: 'VALID_SPECIAL_CHARS',
  DELAY_FAILED_SECRET_ROTATION_HOURS: 'DELAY_FAILED_SECRET_ROTATION_HOURS',
  ENVIRONMENT: 'ENVIRONMENT',
  SECRET_PASSWORD_MIN_LENGTH: 'SECRET_PASSWORD_MIN_LENGTH',
};

export const NEBULA_CONFIG_KEYS = {
  CRQ_FEATURE_TOGGLE_UI: 'CRQ_FEATURE_TOGGLE_UI',
};
export const SENSITIVE_FIELDS = ['password', 'dbPassword'];

export enum VMAction {
  START = 'start',
  STOP = 'stop',
  RESTART = 'restart',
  RESET = 'reset',
  SUSPEND = 'suspend',
}

export enum RequestType {
  START_VM = 'START_VM',
  STOP_VM = 'STOP_VM',
  RESTART_VM = 'RESTART_VM',
}

export enum ServiceCatalogName {
  PROVISION_VIRTUAL_SERVER = 'PROVISION_VIRTUAL_SERVER',
}

export enum DeleteVM {
  DELETING = 'DELETING',
}

export enum DeleteDB {
  DELETING = 'DELETING',
}

//this is for required state as stopped vms can only be started and vice-versa
export enum RequiredStatus {
  START = 'stopped',
  SUSPEND = 'running',
  RESET = 'running',
  STOP = 'running',
  // eslint-disable-next-line @typescript-eslint/no-duplicate-enum-values
  RESTART = 'running',
  FAILED = 'failed',
  STARTNOTRUNNING = 'notrunning',
}

export enum endStatus {
  DELETED = 'deleted',
  RUNNING = 'running',
  STOPPED = 'stopped',
}

export enum JiraStatus {
  MANAGER_REVIEW = 'Manager Review',
  INPROGRESS = 'In Progress',
  FALLOUT = 'Fallout',
  RESOLVED = 'Resolved',
  CLOSED = 'Closed',
  CANCELLED = 'Cancelled',
  DENIED = 'Denied',

  NEW = 'New',
  CBO_INTAKE = 'CBO Intake',
  PENDING_REVIEW = 'Pending Review',
  APPROVED_FOR_WORK = 'Approved for Work',
  VALIDATION = 'Validation',
  COMPLETED_WITH_CUSTOMER_VALIDATION = 'Completed with Customer Validation',
  COMPLETED_WITHOUT_CUSTOMER_VALIDATION = 'Completed without Customer Validation',
  COMPLETED = 'Completed',
  ON_HOLD = 'On Hold',
  REOPENED_INVESTIGATE = [
    'Reopened - Investigate',
    'Reopened - Investigate',
  ] as any,
  REOPENED_CUSTOMER_REQUEST = [
    'Reopened – Customer Request',
    'Reopened - Customer Request',
  ] as any,
  OPERATIONAL_SUPPORT = ['Operational Support - Cpr'] as any,
  REOPENED_REWORK = ['Reopened – Rework', 'Reopened - Rework'] as any,
  IMPLEMENTATION = 'Implementation',
  OE_REVIEW = 'OE Review',
  PD_REVIEW = 'P&D Review',
  PENDING_REQUESTOR = 'Pending/Waiting Requestor',
  IN_DEVELOPMENT = 'In Development',
  IN_REVIEW = 'In Review',
  READY_TO_WORK = 'Ready to Work',
  UNDER_REVIEW = 'Under Review',
  REJECTED = 'Rejected',
  INTAKE = 'Intake',
  In_QUEUE = ['in queue', 'Back to In Queue'] as any,
  PENDING_FW_IMPLEMENTATION = 'PENDING FW IMPLEMENTATION',
  READY_FOR_TEST = 'Ready For Test',
  PENDING_CUSTOMER_INPUT = 'Pending Customer Input',
  REOPENED = 'Reopened',
  CREATE_TICKET = 'Create Ticket',
  UPDATE_PARENT_TICKET = 'Update Parent Ticket',
  BACK_TO_INPROGRESS = 'Back to In Progress',
}

export const INTAKE_REQUEST_FEATURE = {
  project: 'NEBULATAKE',
  issueType: 'Nebula Intake Request',
  type: 'New Feature Request',
  message: 'feature request is submitted',
};

export enum IpModes {
  STATIC_MANUAL = 'static_manual',
  STATIC_AUTO = 'static_auto',
  DHCP = 'dhcp',
}

export enum SecretAccessKey {
  SECRETACCESSKEY = '#######',
}

export enum TicketType {
  CHERWELL = 'Cherwell',
  REMEDY = 'Remedy',
}

export const CrqValidationStatus = {
  true: 'Valid',
  false: 'Invalid',
};

export const BooleanString = {
  true: 'true',
  false: 'false',
};

export const SUCCESS_RESPONSE_TEXT: string = 'ok';

export const NO_ACL_DESIGNER_ERROR: string =
  'No ACL is attached to this interface';

export const IS_COPIED_FIELD = true;
export const IS_REGENERATED_FIELD = false;

export enum FirewallV1MigrationStatus {
  COMPLETED = 'Migrated',
  FAILED = 'Migration failed',
  STARTED = 'Started',
  NOT_STARTED = 'Not Started',
}
export const excludeFieldsInTotal = [
  'customMemory',
  'customCores',
  'memory',
  'vmSequence',
  'total',
];

export const MORPHEUS_CONSTANTS = {
  BACKUP_OPTIONS: 'Backup Options',
  APPLICATION_ENVIRONMENT: 'Application Environment',
  APP_TIER: 'apptier',
  PATCH_CYCLE: 'PatchCycles',
};

export enum DapStatus {
  SUCCESSFUL = 'successful',
  FALIED = 'failed',
  PARTIALLY_SUCCESSFUL = 'partially successful',
}
export enum CATALOGV3 {
  linux = 'Linux 8 & 9 V3',
  linuxAdmin = 'Linux 8 & 9 V3 Admin',
  ubuntu = 'Ubuntu V3',
  ubuntuAdmin = 'Ubuntu V3 Admin',
  windows = 'Windows V3',
  windowsAdmin = 'Windows V3 Admin',
}

export const CUSTOM_SIZE = 'custom_size';

export const COLLECTIONS = {
  SECRETS_METADATA: 'secretsmetadatas',
  RESOURCES: 'resources',
};
