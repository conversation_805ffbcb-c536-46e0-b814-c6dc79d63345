import { SecretsPoliciesRepository } from './secretsPolicies.repository';
import { SecretsPolicies } from './schemas/secretsPolicies.schema';

describe('SecretsPoliciesRepository', () => {
  let repository: SecretsPoliciesRepository;
  let mockModel: any;

  beforeEach(() => {
    repository = new SecretsPoliciesRepository();

    mockModel = {
      create: jest.fn(),
      find: jest.fn(),
      findOne: jest.fn(),
      collection: { name: 'secretspolicies' },
    };

    jest.spyOn(repository as any, 'getSecretsPoliciesModel').mockResolvedValue(mockModel);
  });

  it('should create a secret policy', async () => {
    const payload: SecretsPolicies = {
      secretId: 'secr',
      type: 'rotate'
    };

    mockModel.create.mockResolvedValue({
      _id: 'mock-id',
      ...payload,
      toJSON: () => ({
        _id: 'mock-id',
        ...payload,
      }),
    });

    const result = await repository.create(payload);
    expect(mockModel.create).toHaveBeenCalledWith(expect.any(Object));
    expect(result.secretId).toBe('secr');
  });

  it('should get a policy by single policyId', async () => {
    mockModel.findOne.mockResolvedValue({
      _id: 'mock-id',
      policyId: 'policy-1',
      toJSON: () => ({
        _id: 'mock-id',
        policyId: 'policy-1',
      }),
    });

    const result = await repository.getPolicy('policy-1');
    expect(mockModel.findOne).toHaveBeenCalledWith({ policyId: 'policy-1' });
    expect(result.policyId).toBe('policy-1');
  });

  it('should get policies by multiple policyIds', async () => {
    mockModel.find.mockResolvedValue([
      {
        _id: 'mock-id-1',
        policyId: 'policy-1',
        toJSON: () => ({
          _id: 'mock-id-1',
          policyId: 'policy-1',
        }),
      },
      {
        _id: 'mock-id-2',
        policyId: 'policy-2',
        toJSON: () => ({
          _id: 'mock-id-2',
          policyId: 'policy-2',
        }),
      },
    ]);

    const result = await repository.getPolicy(['policy-1', 'policy-2']);
    expect(mockModel.find).toHaveBeenCalledWith({
      policyId: { $in: ['policy-1', 'policy-2'] },
      active: true,
    });
    expect(result.length).toBe(2);
  });

  it('should get password policies by policy names', async () => {
    mockModel.find.mockResolvedValue([
      {
        policyId: 'policy-1',
        policyName: 'Policy One',
        description: 'Description One',
      },
      {
        policyId: 'policy-2',
        policyName: 'Policy Two',
        description: 'Description Two',
      },
    ]);

    const result = await repository.getPasswordPolicies(['Policy One', 'Policy Two']);
    expect(mockModel.find).toHaveBeenCalledWith(
      { policyName: { $in: ['Policy One', 'Policy Two'] } },
      { _id: 0, policyId: 1, policyName: 1, description: 1 }
    );
    expect(result.length).toBe(2);
  });
});
