import { SecretsPoliciesRepository } from './secretsPolicies.repository';
import { SecretsPolicies } from './schemas/secretsPolicies.schema';

describe('SecretsPoliciesRepository', () => {
  let repository: SecretsPoliciesRepository;
  let mockModel: any;

  beforeEach(() => {
    repository = new SecretsPoliciesRepository();

    mockModel = {
      create: jest.fn(),
      find: jest.fn(),
      findOne: jest.fn(),
      collection: { name: 'secretspolicies' },
    };

    jest
      .spyOn(repository as any, 'getSecretsPoliciesModel')
      .mockResolvedValue(mockModel);
  });

  it('should get a policy by single policyId', async () => {
    mockModel.findOne.mockResolvedValue({
      _id: 'mock-id',
      policyId: 'policy-1',
      toJSON: () => ({
        _id: 'mock-id',
        policyId: 'policy-1',
      }),
    });

    const result = await repository.getPolicy('policy-1');
    expect(mockModel.findOne).toHaveBeenCalledWith({ policyId: 'policy-1' });
    expect(result.policyId).toBe('policy-1');
  });

  it('should get policies by multiple policyIds', async () => {
    mockModel.find.mockResolvedValue([
      {
        _id: 'mock-id-1',
        policyId: 'policy-1',
        toJSON: () => ({
          _id: 'mock-id-1',
          policyId: 'policy-1',
        }),
      },
      {
        _id: 'mock-id-2',
        policyId: 'policy-2',
        toJSON: () => ({
          _id: 'mock-id-2',
          policyId: 'policy-2',
        }),
      },
    ]);

    const result = await repository.getPolicy(['policy-1', 'policy-2']);
    expect(mockModel.find).toHaveBeenCalledWith({
      policyId: { $in: ['policy-1', 'policy-2'] },
      status: "ACTIVE",
    });
    expect(result.length).toBe(2);
  });

  describe('getSecretPoliciesWithResourceId', () => {
    it('should return secret policies for a given resourceId', async () => {
      const mockResourceId = 'resource-123';
      const mockPolicies = [
        {
          _id: '686619cbd68e443c316c063d',
          policyId: 'NEB-VAULT-PASSWORD-POLICY-12234',
          policyName: 'TestUI2',
          type: 'VAULT-PASSWORD-POLICY',
          description: 'Password should have 20  chars',
          policyRules: {
            passwordDescription: 'Password should have 20  chars',
            acceptedSpecialCharacters: '!@#$%',
            totalCharactersLength: 30,
            specialCharactersCount: 5,
            lowerCaseLettersCount: 10,
            upperCaseLettersCount: 3,
            numericalCharactersCount: 5,
            _id: '686619cbd68e443c316c063e',
          },
          resourceId: 'NEB-RES-VAULT-NAMESPACE-8716',
          status: 'ACTIVE',
          createdAt: '2025-07-03T05:48:59.198+00:00',
          updatedAt: '2025-07-03T05:48:59.198+00:00',
          __v: 0,
        },
      ];

      mockModel.find.mockResolvedValue([
        {
          _id: '686619cbd68e443c316c063d',
          policyId: 'NEB-VAULT-PASSWORD-POLICY-12234',
          policyName: 'TestUI2',
          type: 'VAULT-PASSWORD-POLICY',
          description: 'Password should have 20  chars',
          policyRules: {
            passwordDescription: 'Password should have 20  chars',
            acceptedSpecialCharacters: '!@#$%',
            totalCharactersLength: 30,
            specialCharactersCount: 5,
            lowerCaseLettersCount: 10,
            upperCaseLettersCount: 3,
            numericalCharactersCount: 5,
            _id: '686619cbd68e443c316c063e',
          },
          resourceId: 'NEB-RES-VAULT-NAMESPACE-8716',
          status: 'ACTIVE',
          createdAt: '2025-07-03T05:48:59.198+00:00',
          updatedAt: '2025-07-03T05:48:59.198+00:00',
          __v: 0,
        },
      ]);

      const result =
        await repository.getSecretPoliciesWithResourceId(mockResourceId);
      expect(mockModel.find).toHaveBeenCalledWith({
        resourceId: mockResourceId,
      });
      expect(result).toEqual(mockPolicies);
    });
  });
});
