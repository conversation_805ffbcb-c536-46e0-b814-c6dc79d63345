import { Injectable, NotFoundException } from '@nestjs/common';
import { DapUpdateWebhookHandler } from './dap-update-webhook.handler';
import { WebhookHandlerInterface } from './webhook-handler.interface';
import { DapUpdateRequestDto } from './dto/dap-update.request.dto';
import { GitUpdateWebhookHandler } from './git-update-webhook.handler';
import { GitUpdateRequestDto } from './dto/git-update.request.dto';
import { WEBHOOK_REQUEST_TYPE } from '../types';
import { DapDeploymentUpdateWebhookHandler } from './dap-deployment-update-webhook.handler';
import { DapDeployementUpdateRequestDto } from './dto/dap-deployment-update.request.dto';
import { DapWebhookRequestDto } from './dto/dnp-dap-update.dto';
import { DnpDapUpdateWebhookHandler } from './dnp-dap-update-webhook.handler';
import { LoggerService } from '../loggers/logger.service';
import { ActivityLogDto } from './dto/awx-activity-log.dto';
import { AWXActivityWebhookHandler } from './awx-activity-webhook.handler';
import { WebhookRequestDto } from './dto/webhook.request.dto';

@Injectable()
export class WebhookHandlerFactory {
  constructor(
    private readonly dapUpdateWebhookHandler: DapUpdateWebhookHandler,
    private readonly gitUpdateWebhookHandler: GitUpdateWebhookHandler,
    private readonly dapDeploymentWebhookHandler: DapDeploymentUpdateWebhookHandler,
    private readonly dnpdapWebhookHandler: DnpDapUpdateWebhookHandler,
    private readonly awxWebhookHandler: AWXActivityWebhookHandler,
    private readonly logger: LoggerService,
  ) {}

  getHandlerAndDto(requestType: WEBHOOK_REQUEST_TYPE): {
    handler: WebhookHandlerInterface<any, any>;
    dto: new () => any;
  } {
    switch (requestType) {
      case WEBHOOK_REQUEST_TYPE.DAP_UPDATES:
        return {
          handler: this.dapUpdateWebhookHandler,
          dto: DapUpdateRequestDto, // Return the DTO type for DAP updates
        };
      case WEBHOOK_REQUEST_TYPE.GIT_UPDATES:
        return {
          handler: this.gitUpdateWebhookHandler,
          dto: GitUpdateRequestDto,
        };
      case WEBHOOK_REQUEST_TYPE.DAP_DEPLOYMENT_UPDATE:
        return {
          handler: this.dapDeploymentWebhookHandler,
          dto: DapDeployementUpdateRequestDto,
        };
      case WEBHOOK_REQUEST_TYPE.DNP_DAP_UPDATE:
        return {
          handler: this.dnpdapWebhookHandler,
          dto: DapWebhookRequestDto,
        };
      case WEBHOOK_REQUEST_TYPE.VM_CONFIGURE:
        return {
          handler: this.awxWebhookHandler,
          dto: WebhookRequestDto,
        };
      default:
        this.logger.log('No handler found for the requestType:', requestType);
        throw new NotFoundException(
          `Handler not found for requestType: ${requestType}`,
        );
    }
    // Add more handlers and DTOs as needed for different request types
  }
}
