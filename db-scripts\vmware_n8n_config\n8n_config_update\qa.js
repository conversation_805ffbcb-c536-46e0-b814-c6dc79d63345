const session = db.getMongo().startSession();

session.startTransaction();

try {
  db.n8nconfig.updateOne(
    { workflow: 'VMWARE-Create-QA' },
    {
      $set: {
        vmDeleteWorkflow: 'MdlSPWFbeJAw8HSJ',
        paceDeleteWorkflow: 'V9CAqkaGLCjsR1BG',
      },
    },
  );

  print(`Document was updated`);
  session.commitTransaction();
} catch (e) {
  session.abortTransaction();

  print('Transaction aborted due to error', e);
} finally {
  session.endSession();
}
