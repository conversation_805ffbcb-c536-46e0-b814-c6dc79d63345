const invalidEmailDlGroups = db['menvgroups']
  .find({
    emailDistribution: {
      $elemMatch: {
        $not: {
          $regex: /@(spectrum|charter)\.com$/i,
        },
      },
    },
  })
  .toArray()

const recordsToUpdate = invalidEmailDlGroups.map((group) => {
  const updatedEmails = group.emailDistribution.map((email) => {
    return `${email.split('@')[0]}@charter.com`
  })
  return {
    updateOne: {
      filter: { _id: group._id },
      update: { $set: { emailDistribution: updatedEmails } },
    },
  }
})

db['menvgroups'].bulkWrite(recordsToUpdate)
