import { Injectable } from '@nestjs/common';
import { <PERSON><PERSON> } from '@nestjs/schedule';
import { CronTimings } from './util';
import { SecretsMetadataService } from '../secure-store/secretsMetadata/secretsMetadata.service';
import { SecureStoreService } from 'src/secure-store/secure-store.service';
import { ENVIRONMENT_VARS } from 'src/utils/constants';
import { ConfigService } from '@nestjs/config';
@Injectable()
export class SecretsRenewalJobsService {
  constructor(
    private configService: ConfigService,
    private readonly secretsMetadataService: SecretsMetadataService,
    private readonly secureStoreService: SecureStoreService,
  ) {}

  @Cron(CronTimings.EVERY_5_MINUTES)
  async reactivateSecrets() {
    const reactivateSecretsDuration = this.configService.get(
      ENVIRONMENT_VARS.REACTIVATE_SECRET_DURATION_HRS,
    );
    let reactivateSecrets = await this.secretsMetadataService.reactivateSecrets(
      reactivateSecretsDuration,
    );
    if (reactivateSecrets.length)
      return await Promise.all(
        reactivateSecrets.map(async (secret) => {
          await this.secureStoreService.cleanupTemporarySecrets(
            secret.secretId,
          );
        }),
      );
  }

  @Cron(CronTimings.EVERY_DAY_AT_MIDNIGHT)
  async renewVaultTokens() {
    const tokens =
      await this.secretsMetadataService.getAllAutoRenewVaultTokens();
    if (tokens.length) {
      let tokensToBeRenewed = [];
      tokens.forEach((token) => {
        let renewalDate = token.renewedAt ? token.renewedAt : token.createdAt;
        let tokenLifespan = token.expiryDate.getTime() - renewalDate.getTime();
        let tokenCurrentAge = new Date().getTime() - renewalDate.getTime();
        let ratio = tokenCurrentAge / tokenLifespan;
        if (
          ratio >=
          this.configService.get(ENVIRONMENT_VARS.VAULT_TOKEN_RENEWAL_RATIO)
        ) {
          tokensToBeRenewed.push({
            secretId: token.secretId,
            key: token.devicePasswordKey,
          });
        }
      });
      if (tokensToBeRenewed.length) {
        let tokensMetaData =
          await this.secureStoreService.renewBulkTokens(tokensToBeRenewed);
        if (tokensMetaData.length) {
          await Promise.all(
            tokensMetaData.map((tokenData) => {
              this.secretsMetadataService.updateRenewedTokenMetaData(
                tokenData.secretId,
                tokenData.renewalDate,
                tokenData.expiryDate,
              );
            }),
          );
        }
      }
    }
  }
}
