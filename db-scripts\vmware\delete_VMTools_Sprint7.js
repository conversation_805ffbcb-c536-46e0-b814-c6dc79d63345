const session = db.getMongo().startSession();
session.startTransaction();

function runScript() {
  const linux8and9Config = db.menvcataloglevel04.findOne({
    shortName: 'linux8&9v3',
  });

  const linux8and9AdminConfig = db.menvcataloglevel04.findOne({
    shortName: 'linux8&9v3admin',
  });
  const ubuntuConfig = db.menvcataloglevel04.findOne({
    shortName: 'ubuntuv3',
  });

  const ubuntuAdminConfig = db.menvcataloglevel04.findOne({
    shortName: 'ubuntuv3admin',
  });

  const windowsConfig = db.menvcataloglevel04.findOne({
    shortName: 'windowsv3',
  });

  const windowsAdminConfig = db.menvcataloglevel04.findOne({
    shortName: 'windowsv3admin',
  });

  if (
    !linux8and9Config ||
    !linux8and9AdminConfig ||
    !ubuntuConfig ||
    !ubuntuAdminConfig ||
    !windowsConfig ||
    !windowsAdminConfig
  ) {
    print('Catalog document does not exists, aborting the transaction');
    session.abortTransaction();
    return;
  }

  /**-----------Linux Steps-------------------- */

  const linux89CatalogSteps = {
    catalogLevel04Id: linux8and9Config._id,
    version: 2,
    active: true,
    activityLogSteps: [
      {
        name: 'Receive VM Request - Nebula',
        eventCode: 'NEB-EVENT-VM-CREATE-1001',
        sequence: 1,
      },
      {
        name: 'Approve Request - Nebula',
        eventCode: 'NEB-EVENT-APPROVAL-1001',
        sequence: 2,
      },
      {
        name: 'Quota Validation - Nebula',
        eventCode: 'NEB-EVENT-QUOTA-1011',
        sequence: 3,
      },
      {
        name: 'Validate VLan - Infoblox',
        eventCode: 'NEB-EVENT-VLAN-2001',
        sequence: 4,
      },
      {
        name: 'IP Validation (Static Manual) - Infoblox',
        eventCode: 'NEB-EVENT-IP-VALIDATION-3011',
        sequence: 5,
      },
      {
        name: 'Reserve IP - Infoblox',
        eventCode: 'NEB-EVENT-RESERVE-IP-89001',
        sequence: 6,
      },
      {
        name: 'DNS Validation - DIRRT',
        eventCode: 'NEB-EVENT-DNS-3012',
        sequence: 7,
      },
      {
        name: 'Trace and Ping Validation - Infoblox',
        eventCode: 'NEB-EVENT-TRACE-PING-30133',
        sequence: 8,
      },
      {
        name: 'Vcenter Validation - Vcenter',
        eventCode: 'NEB-EVENT-VCENTER-3015',
        sequence: 9,
      },
      {
        name: 'Create VM(s) - VSphere',
        eventCode: 'NEB-EVENT-VSPHERE-89012',
        sequence: 10,
      },
      {
        name: 'Check VM Status - VSphere',
        eventCode: 'NEB-EVENT-VM-STATUS-89013',
        sequence: 11,
      },
      {
        name: 'VM-Resource Created - Nebula',
        eventCode: 'NEB-EVENT-VM-RESOURCE-89015',
        sequence: 12,
      },
      {
        name: 'Configure VM - AWX',
        eventCode: 'NEB-EVENT-VM-CONFIG-89018',
        sequence: 13,
      },
      {
        name: 'Check VM Configure Status - AWX',
        eventCode: 'NEB-EVENT-VM-AWX-STATUS-89029',
        sequence: 14,
      },
      {
        name: 'Update Resource(s) Metadata - Nebula',
        eventCode: 'NEB-EVENT-METADATA-UPDATE-89021',
        sequence: 15,
      },
      {
        name: 'Publish Resource Inventory Data - Granite',
        eventCode: 'NEB-EVENT-INVENTORY-PUBLISH-89030',
        sequence: 16,
      },
      {
        name: 'Notify Compliance Platform - Legacy Compliance',
        eventCode: 'NEB-EVENT-NOTIFY-PLATFORM-89031',
        sequence: 17,
      },
      {
        name: 'Install Compliance tool - Tanium',
        eventCode: 'NEB-EVENT-TANIMUM-INS-89032',
        sequence: 18,
      },
      {
        name: 'Onboard into Observability Tool - Science Logic',
        eventCode: 'NEB-EVENT-SCIENCE-LOGIC-89033',
        sequence: 19,
      },
      {
        name: 'Install Observability tool - Splunk',
        eventCode: 'NEB-EVENT-SPLUNK-INS-89034',
        sequence: 20,
      },
      {
        name: 'Install Security tools - Crowd Strike',
        eventCode: 'NEB-EVENT-CROWD-INS-89035',
        sequence: 21,
      },
      {
        name: 'Install Security tools - Centrify',
        eventCode: 'NEB-EVENT-CENTRIFY-INS-89036',
        sequence: 22,
      },
      {
        name: 'Install Security tools - Qualys',
        eventCode: 'NEB-EVENT-QUALYS-INS-89039',
        sequence: 23,
      },
      {
        name: 'Nebula DIRRT Integration',
        eventCode: 'NEB-EVENT-DIRRT-INT-89040',
        sequence: 24,
      },
      {
        name: 'Complete Request Processing - Nebula',
        eventCode: 'NEB-EVENT-VM-9001',
        sequence: 25,
      },
    ],
  };

  const linux89AdminCatalogSteps = {
    catalogLevel04Id: linux8and9AdminConfig._id,
    version: 2,
    active: true,
    activityLogSteps: [
      {
        name: 'Receive VM Request - Nebula',
        eventCode: 'NEB-EVENT-VM-CREATE-1001',
        sequence: 1,
      },
      {
        name: 'Approve Request - Nebula',
        eventCode: 'NEB-EVENT-APPROVAL-1001',
        sequence: 2,
      },
      {
        name: 'Quota Validation - Nebula',
        eventCode: 'NEB-EVENT-QUOTA-1011',
        sequence: 3,
      },
      {
        name: 'Validate VLan - Infoblox',
        eventCode: 'NEB-EVENT-VLAN-2001',
        sequence: 4,
      },
      {
        name: 'IP Validation (Static Manual) - Infoblox',
        eventCode: 'NEB-EVENT-IP-VALIDATION-3011',
        sequence: 5,
      },
      {
        name: 'Reserve IP - Infoblox',
        eventCode: 'NEB-EVENT-RESERVE-IP-89001',
        sequence: 6,
      },
      {
        name: 'DNS Validation - DIRRT',
        eventCode: 'NEB-EVENT-DNS-3012',
        sequence: 7,
      },
      {
        name: 'Trace and Ping Validation - Infoblox',
        eventCode: 'NEB-EVENT-TRACE-PING-30133',
        sequence: 8,
      },
      {
        name: 'Vcenter Validation - Vcenter',
        eventCode: 'NEB-EVENT-VCENTER-3015',
        sequence: 9,
      },
      {
        name: 'Create VM(s) - VSphere',
        eventCode: 'NEB-EVENT-VSPHERE-89012',
        sequence: 10,
      },
      {
        name: 'Check VM Status - VSphere',
        eventCode: 'NEB-EVENT-VM-STATUS-89013',
        sequence: 11,
      },
      {
        name: 'VM-Resource Created - Nebula',
        eventCode: 'NEB-EVENT-VM-RESOURCE-89015',
        sequence: 12,
      },
      {
        name: 'Configure VM - AWX',
        eventCode: 'NEB-EVENT-VM-CONFIG-89018',
        sequence: 13,
      },
      {
        name: 'Check VM Configure Status - AWX',
        eventCode: 'NEB-EVENT-VM-AWX-STATUS-89029',
        sequence: 14,
      },
      {
        name: 'Update Resource(s) Metadata - Nebula',
        eventCode: 'NEB-EVENT-METADATA-UPDATE-89021',
        sequence: 15,
      },
      {
        name: 'Publish Resource Inventory Data - Granite',
        eventCode: 'NEB-EVENT-INVENTORY-PUBLISH-89030',
        sequence: 16,
      },
      {
        name: 'Notify Compliance Platform - Legacy Compliance',
        eventCode: 'NEB-EVENT-NOTIFY-PLATFORM-89031',
        sequence: 17,
      },
      {
        name: 'Install Compliance tool - Tanium',
        eventCode: 'NEB-EVENT-TANIMUM-INS-89032',
        sequence: 18,
      },
      {
        name: 'Onboard into Observability Tool - Science Logic',
        eventCode: 'NEB-EVENT-SCIENCE-LOGIC-89033',
        sequence: 19,
      },
      {
        name: 'Install Observability tool - Splunk',
        eventCode: 'NEB-EVENT-SPLUNK-INS-89034',
        sequence: 20,
      },
      {
        name: 'Install Security tools - Crowd Strike',
        eventCode: 'NEB-EVENT-CROWD-INS-89035',
        sequence: 21,
      },
      {
        name: 'Install Security tools - Centrify',
        eventCode: 'NEB-EVENT-CENTRIFY-INS-89036',
        sequence: 22,
      },
      {
        name: 'Install Security tools - Qualys',
        eventCode: 'NEB-EVENT-QUALYS-INS-89039',
        sequence: 23,
      },
      {
        name: 'Nebula DIRRT Integration',
        eventCode: 'NEB-EVENT-DIRRT-INT-89040',
        sequence: 24,
      },
      {
        name: 'Complete Request Processing - Nebula',
        eventCode: 'NEB-EVENT-VM-9001',
        sequence: 25,
      },
    ],
  };

  /**------------------UBUNTU Steps---------------------- */
  const ubuntuCatalogSteps = {
    catalogLevel04Id: ubuntuConfig._id,
    version: 2,
    active: true,
    activityLogSteps: [
      {
        name: 'Receive VM Request - Nebula',
        eventCode: 'NEB-EVENT-VM-CREATE-1001',
        sequence: 1,
      },
      {
        name: 'Approve Request - Nebula',
        eventCode: 'NEB-EVENT-APPROVAL-1001',
        sequence: 2,
      },
      {
        name: 'Quota Validation - Nebula',
        eventCode: 'NEB-EVENT-QUOTA-1011',
        sequence: 3,
      },
      {
        name: 'Validate VLan - Infoblox',
        eventCode: 'NEB-EVENT-VLAN-2001',
        sequence: 4,
      },
      {
        name: 'IP Validation (Static Manual) - Infoblox',
        eventCode: 'NEB-EVENT-IP-VALIDATION-3011',
        sequence: 5,
      },
      {
        name: 'Reserve IP - Infoblox',
        eventCode: 'NEB-EVENT-RESERVE-IP-89001',
        sequence: 6,
      },
      {
        name: 'DNS Validation - DIRRT',
        eventCode: 'NEB-EVENT-DNS-3012',
        sequence: 7,
      },
      {
        name: 'Trace and Ping Validation - Infoblox',
        eventCode: 'NEB-EVENT-TRACE-PING-30133',
        sequence: 8,
      },
      {
        name: 'Vcenter Validation - Vcenter',
        eventCode: 'NEB-EVENT-VCENTER-3015',
        sequence: 9,
      },
      {
        name: 'Create VM(s) - VSphere',
        eventCode: 'NEB-EVENT-VSPHERE-89012',
        sequence: 10,
      },
      {
        name: 'Check VM Status - VSphere',
        eventCode: 'NEB-EVENT-VM-STATUS-89013',
        sequence: 11,
      },
      {
        name: 'VM-Resource Created - Nebula',
        eventCode: 'NEB-EVENT-VM-RESOURCE-89015',
        sequence: 12,
      },
      {
        name: 'Configure VM - AWX',
        eventCode: 'NEB-EVENT-VM-CONFIG-89018',
        sequence: 13,
      },
      {
        name: 'Check VM Configure Status - AWX',
        eventCode: 'NEB-EVENT-VM-AWX-STATUS-89029',
        sequence: 14,
      },
      {
        name: 'Update Resource(s) Metadata - Nebula',
        eventCode: 'NEB-EVENT-METADATA-UPDATE-89021',
        sequence: 15,
      },
      {
        name: 'Publish Resource Inventory Data - Granite',
        eventCode: 'NEB-EVENT-INVENTORY-PUBLISH-89030',
        sequence: 16,
      },
      {
        name: 'Notify Compliance Platform - Legacy Compliance',
        eventCode: 'NEB-EVENT-NOTIFY-PLATFORM-89031',
        sequence: 17,
      },
      {
        name: 'Install Compliance tool - Tanium',
        eventCode: 'NEB-EVENT-TANIMUM-INS-89032',
        sequence: 18,
      },
      {
        name: 'Onboard into Observability Tool - Science Logic',
        eventCode: 'NEB-EVENT-SCIENCE-LOGIC-89033',
        sequence: 19,
      },
      {
        name: 'Install Observability tool - Splunk',
        eventCode: 'NEB-EVENT-SPLUNK-INS-89034',
        sequence: 20,
      },
      {
        name: 'Install Security tools - Crowd Strike',
        eventCode: 'NEB-EVENT-CROWD-INS-89035',
        sequence: 21,
      },
      {
        name: 'Install Security tools - Centrify',
        eventCode: 'NEB-EVENT-CENTRIFY-INS-89036',
        sequence: 22,
      },
      {
        name: 'Install Security tools - Qualys',
        eventCode: 'NEB-EVENT-QUALYS-INS-89039',
        sequence: 23,
      },
      {
        name: 'Nebula DIRRT Integration',
        eventCode: 'NEB-EVENT-DIRRT-INT-89040',
        sequence: 24,
      },
      {
        name: 'Complete Request Processing - Nebula',
        eventCode: 'NEB-EVENT-VM-9001',
        sequence: 25,
      },
    ],
  };

  const ubuntuAdminCatalogSteps = {
    catalogLevel04Id: ubuntuAdminConfig._id,
    version: 2,
    active: true,
    activityLogSteps: [
      {
        name: 'Receive VM Request - Nebula',
        eventCode: 'NEB-EVENT-VM-CREATE-1001',
        sequence: 1,
      },
      {
        name: 'Approve Request - Nebula',
        eventCode: 'NEB-EVENT-APPROVAL-1001',
        sequence: 2,
      },
      {
        name: 'Quota Validation - Nebula',
        eventCode: 'NEB-EVENT-QUOTA-1011',
        sequence: 3,
      },
      {
        name: 'Validate VLan - Infoblox',
        eventCode: 'NEB-EVENT-VLAN-2001',
        sequence: 4,
      },
      {
        name: 'IP Validation (Static Manual) - Infoblox',
        eventCode: 'NEB-EVENT-IP-VALIDATION-3011',
        sequence: 5,
      },
      {
        name: 'Reserve IP - Infoblox',
        eventCode: 'NEB-EVENT-RESERVE-IP-89001',
        sequence: 6,
      },
      {
        name: 'DNS Validation - DIRRT',
        eventCode: 'NEB-EVENT-DNS-3012',
        sequence: 7,
      },
      {
        name: 'Trace and Ping Validation - Infoblox',
        eventCode: 'NEB-EVENT-TRACE-PING-30133',
        sequence: 8,
      },
      {
        name: 'Vcenter Validation - Vcenter',
        eventCode: 'NEB-EVENT-VCENTER-3015',
        sequence: 9,
      },
      {
        name: 'Create VM(s) - VSphere',
        eventCode: 'NEB-EVENT-VSPHERE-89012',
        sequence: 10,
      },
      {
        name: 'Check VM Status - VSphere',
        eventCode: 'NEB-EVENT-VM-STATUS-89013',
        sequence: 11,
      },
      {
        name: 'VM-Resource Created - Nebula',
        eventCode: 'NEB-EVENT-VM-RESOURCE-89015',
        sequence: 12,
      },
      {
        name: 'Configure VM - AWX',
        eventCode: 'NEB-EVENT-VM-CONFIG-89018',
        sequence: 13,
      },
      {
        name: 'Check VM Configure Status - AWX',
        eventCode: 'NEB-EVENT-VM-AWX-STATUS-89029',
        sequence: 14,
      },
      {
        name: 'Update Resource(s) Metadata - Nebula',
        eventCode: 'NEB-EVENT-METADATA-UPDATE-89021',
        sequence: 15,
      },
      {
        name: 'Publish Resource Inventory Data - Granite',
        eventCode: 'NEB-EVENT-INVENTORY-PUBLISH-89030',
        sequence: 16,
      },
      {
        name: 'Notify Compliance Platform - Legacy Compliance',
        eventCode: 'NEB-EVENT-NOTIFY-PLATFORM-89031',
        sequence: 17,
      },
      {
        name: 'Install Compliance tool - Tanium',
        eventCode: 'NEB-EVENT-TANIMUM-INS-89032',
        sequence: 18,
      },
      {
        name: 'Onboard into Observability Tool - Science Logic',
        eventCode: 'NEB-EVENT-SCIENCE-LOGIC-89033',
        sequence: 19,
      },
      {
        name: 'Install Observability tool - Splunk',
        eventCode: 'NEB-EVENT-SPLUNK-INS-89034',
        sequence: 20,
      },
      {
        name: 'Install Security tools - Crowd Strike',
        eventCode: 'NEB-EVENT-CROWD-INS-89035',
        sequence: 21,
      },
      {
        name: 'Install Security tools - Centrify',
        eventCode: 'NEB-EVENT-CENTRIFY-INS-89036',
        sequence: 22,
      },
      {
        name: 'Install Security tools - Qualys',
        eventCode: 'NEB-EVENT-QUALYS-INS-89039',
        sequence: 23,
      },
      {
        name: 'Nebula DIRRT Integration',
        eventCode: 'NEB-EVENT-DIRRT-INT-89040',
        sequence: 24,
      },
      {
        name: 'Complete Request Processing - Nebula',
        eventCode: 'NEB-EVENT-VM-9001',
        sequence: 25,
      },
    ],
  };

  /**-----------------Windows Steps------------------- */

  const windowsCatalogSteps = {
    catalogLevel04Id: windowsConfig._id,
    version: 2,
    active: true,
    activityLogSteps: [
      {
        name: 'Receive VM Request - Nebula',
        eventCode: 'NEB-EVENT-VM-CREATE-1001',
        sequence: 1,
      },
      {
        name: 'Approve Request - Nebula',
        eventCode: 'NEB-EVENT-APPROVAL-1001',
        sequence: 2,
      },
      {
        name: 'Quota Validation - Nebula',
        eventCode: 'NEB-EVENT-QUOTA-1011',
        sequence: 3,
      },
      {
        name: 'Validate VLan - Infoblox',
        eventCode: 'NEB-EVENT-VLAN-2001',
        sequence: 4,
      },
      {
        name: 'IP Validation (Static Manual) - Infoblox',
        eventCode: 'NEB-EVENT-IP-VALIDATION-3011',
        sequence: 5,
      },
      {
        name: 'Reserve IP - Infoblox',
        eventCode: 'NEB-EVENT-RESERVE-IP-89001',
        sequence: 6,
      },
      {
        name: 'DNS Validation - DIRRT',
        eventCode: 'NEB-EVENT-DNS-3012',
        sequence: 7,
      },
      {
        name: 'Trace and Ping Validation - Infoblox',
        eventCode: 'NEB-EVENT-TRACE-PING-30133',
        sequence: 8,
      },
      {
        name: 'Vcenter Validation - Vcenter',
        eventCode: 'NEB-EVENT-VCENTER-3015',
        sequence: 9,
      },
      {
        name: 'Create VM(s) - VSphere',
        eventCode: 'NEB-EVENT-VSPHERE-89012',
        sequence: 10,
      },
      {
        name: 'Check VM Status - VSphere',
        eventCode: 'NEB-EVENT-VM-STATUS-89013',
        sequence: 11,
      },
      {
        name: 'VM-Resource Created - Nebula',
        eventCode: 'NEB-EVENT-VM-RESOURCE-89015',
        sequence: 12,
      },
      {
        name: 'Configure VM - AWX',
        eventCode: 'NEB-EVENT-VM-CONFIG-89018',
        sequence: 13,
      },
      {
        name: 'Check VM Configure Status - AWX',
        eventCode: 'NEB-EVENT-VM-AWX-STATUS-89029',
        sequence: 14,
      },
      {
        name: 'Update Resource(s) Metadata - Nebula',
        eventCode: 'NEB-EVENT-METADATA-UPDATE-89021',
        sequence: 15,
      },
      {
        name: 'Publish Resource Inventory Data - Granite',
        eventCode: 'NEB-EVENT-INVENTORY-PUBLISH-89030',
        sequence: 16,
      },
      {
        name: 'Notify Compliance Platform - Legacy Compliance',
        eventCode: 'NEB-EVENT-NOTIFY-PLATFORM-89031',
        sequence: 17,
      },
      {
        name: 'Install Compliance tool - Tanium',
        eventCode: 'NEB-EVENT-TANIMUM-INS-89032',
        sequence: 18,
      },
      {
        name: 'Onboard into Observability Tool - Science Logic',
        eventCode: 'NEB-EVENT-SCIENCE-LOGIC-89033',
        sequence: 19,
      },
      {
        name: 'Install Observability tool - Splunk',
        eventCode: 'NEB-EVENT-SPLUNK-INS-89034',
        sequence: 20,
      },
      {
        name: 'Install Security tools - Crowd Strike',
        eventCode: 'NEB-EVENT-CROWD-INS-89035',
        sequence: 21,
      },
      {
        name: 'Install Security tools - Centrify',
        eventCode: 'NEB-EVENT-CENTRIFY-INS-89036',
        sequence: 22,
      },
      {
        name: 'Install Security tools - Qualys',
        eventCode: 'NEB-EVENT-QUALYS-INS-89039',
        sequence: 23,
      },
      {
        name: 'Nebula DIRRT Integration',
        eventCode: 'NEB-EVENT-DIRRT-INT-89040',
        sequence: 24,
      },
      {
        name: 'Complete Request Processing - Nebula',
        eventCode: 'NEB-EVENT-VM-9001',
        sequence: 25,
      },
    ],
  };

  const windowsAdminCatalogSteps = {
    catalogLevel04Id: windowsAdminConfig._id,
    version: 2,
    active: true,
    activityLogSteps: [
      {
        name: 'Receive VM Request - Nebula',
        eventCode: 'NEB-EVENT-VM-CREATE-1001',
        sequence: 1,
      },
      {
        name: 'Approve Request - Nebula',
        eventCode: 'NEB-EVENT-APPROVAL-1001',
        sequence: 2,
      },
      {
        name: 'Quota Validation - Nebula',
        eventCode: 'NEB-EVENT-QUOTA-1011',
        sequence: 3,
      },
      {
        name: 'Validate VLan - Infoblox',
        eventCode: 'NEB-EVENT-VLAN-2001',
        sequence: 4,
      },
      {
        name: 'IP Validation (Static Manual) - Infoblox',
        eventCode: 'NEB-EVENT-IP-VALIDATION-3011',
        sequence: 5,
      },
      {
        name: 'Reserve IP - Infoblox',
        eventCode: 'NEB-EVENT-RESERVE-IP-89001',
        sequence: 6,
      },
      {
        name: 'DNS Validation - DIRRT',
        eventCode: 'NEB-EVENT-DNS-3012',
        sequence: 7,
      },
      {
        name: 'Trace and Ping Validation - Infoblox',
        eventCode: 'NEB-EVENT-TRACE-PING-30133',
        sequence: 8,
      },
      {
        name: 'Vcenter Validation - Vcenter',
        eventCode: 'NEB-EVENT-VCENTER-3015',
        sequence: 9,
      },
      {
        name: 'Create VM(s) - VSphere',
        eventCode: 'NEB-EVENT-VSPHERE-89012',
        sequence: 10,
      },
      {
        name: 'Check VM Status - VSphere',
        eventCode: 'NEB-EVENT-VM-STATUS-89013',
        sequence: 11,
      },
      {
        name: 'VM-Resource Created - Nebula',
        eventCode: 'NEB-EVENT-VM-RESOURCE-89015',
        sequence: 12,
      },
      {
        name: 'Configure VM - AWX',
        eventCode: 'NEB-EVENT-VM-CONFIG-89018',
        sequence: 13,
      },
      {
        name: 'Check VM Configure Status - AWX',
        eventCode: 'NEB-EVENT-VM-AWX-STATUS-89029',
        sequence: 14,
      },
      {
        name: 'Update Resource(s) Metadata - Nebula',
        eventCode: 'NEB-EVENT-METADATA-UPDATE-89021',
        sequence: 15,
      },
      {
        name: 'Publish Resource Inventory Data - Granite',
        eventCode: 'NEB-EVENT-INVENTORY-PUBLISH-89030',
        sequence: 16,
      },
      {
        name: 'Notify Compliance Platform - Legacy Compliance',
        eventCode: 'NEB-EVENT-NOTIFY-PLATFORM-89031',
        sequence: 17,
      },
      {
        name: 'Install Compliance tool - Tanium',
        eventCode: 'NEB-EVENT-TANIMUM-INS-89032',
        sequence: 18,
      },
      {
        name: 'Onboard into Observability Tool - Science Logic',
        eventCode: 'NEB-EVENT-SCIENCE-LOGIC-89033',
        sequence: 19,
      },
      {
        name: 'Install Observability tool - Splunk',
        eventCode: 'NEB-EVENT-SPLUNK-INS-89034',
        sequence: 20,
      },
      {
        name: 'Install Security tools - Crowd Strike',
        eventCode: 'NEB-EVENT-CROWD-INS-89035',
        sequence: 21,
      },
      {
        name: 'Install Security tools - Centrify',
        eventCode: 'NEB-EVENT-CENTRIFY-INS-89036',
        sequence: 22,
      },
      {
        name: 'Install Security tools - Qualys',
        eventCode: 'NEB-EVENT-QUALYS-INS-89039',
        sequence: 23,
      },
      {
        name: 'Nebula DIRRT Integration',
        eventCode: 'NEB-EVENT-DIRRT-INT-89040',
        sequence: 24,
      },
      {
        name: 'Complete Request Processing - Nebula',
        eventCode: 'NEB-EVENT-VM-9001',
        sequence: 25,
      },
    ],
  };

  /**----------------------END------------------------ */

  db.catalogsteps.updateOne(
    { active: true, catalogLevel04Id: linux8and9Config._id },
    { $set: { active: false } },
    { upsert: false },
  );

  db.catalogsteps.updateOne(
    { active: true, catalogLevel04Id: linux8and9AdminConfig._id },
    { $set: { active: false } },
    { upsert: false },
  );

  db.catalogsteps.updateOne(
    { active: true, catalogLevel04Id: ubuntuConfig._id },
    { $set: { active: false } },
    { upsert: false },
  );

  db.catalogsteps.updateOne(
    { active: true, catalogLevel04Id: ubuntuAdminConfig._id },
    { $set: { active: false } },
    { upsert: false },
  );

  db.catalogsteps.updateOne(
    { active: true, catalogLevel04Id: windowsConfig._id },
    { $set: { active: false } },
    { upsert: false },
  );

  db.catalogsteps.updateOne(
    { active: true, catalogLevel04Id: windowsAdminConfig._id },
    { $set: { active: false } },
    { upsert: false },
  );

  db.catalogsteps.insertOne(linux89CatalogSteps);
  db.catalogsteps.insertOne(linux89AdminCatalogSteps);
  db.catalogsteps.insertOne(ubuntuCatalogSteps);
  db.catalogsteps.insertOne(ubuntuAdminCatalogSteps);
  db.catalogsteps.insertOne(windowsCatalogSteps);
  db.catalogsteps.insertOne(windowsAdminCatalogSteps);
  session.commitTransaction();
  print(`Script ran successfully`);
}

try {
  runScript();
} catch (e) {
  session.abortTransaction();
  print('Transaction aborted due to error', e);
} finally {
  session.endSession();
}
