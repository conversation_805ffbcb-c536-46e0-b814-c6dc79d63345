import { Test, TestingModule } from '@nestjs/testing';
import { SecretsPoliciesController } from './secretsPolicies.controller';
import { SecretsPoliciesService } from './secretsPolicies.service';
import { SecretsPoliciesDto } from './dto/secretPolicies.dto';
import { AuthenticationGuard } from '../../auth/authentication.guard';

describe('SecretsPoliciesController', () => {
  let controller: SecretsPoliciesController;
  let service: SecretsPoliciesService;

  const mockSecretsPoliciesService = {
    create: jest.fn(),
  };

  const mockGuard = {
    canActivate: jest.fn(() => true),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [SecretsPoliciesController],
      providers: [
        {
          provide: SecretsPoliciesService,
          useValue: mockSecretsPoliciesService,
        },
      ],
    })
      .overrideGuard(AuthenticationGuard)
      .useValue(mockGuard)
      .compile();

    controller = module.get<SecretsPoliciesController>(
      SecretsPoliciesController,
    );
    service = module.get<SecretsPoliciesService>(SecretsPoliciesService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    const dto: SecretsPoliciesDto = {
      policyName: 'Test',
      type: '',
      status: 'ACTIVE',
      resourceId: '684ac0d69079ed49f914f4d1',
      policyRules: {
        passwordDescription: 'Test',
        acceptedSpecialCharacters: '!,@,#,$',
        totalCharactersLength: 12,
        specialCharactersCount: 3,
        lowerCaseLettersCount: 3,
        upperCaseLettersCount: 3,
        numericalCharactersCount: 3,
      },
    };

    it('should call service.create and return result', async () => {
      const result = { id: 'abc123', ...dto };
      mockSecretsPoliciesService.create.mockResolvedValue(result);

      const response = await controller.create(dto);

      expect(service.create).toHaveBeenCalledWith(dto);
      expect(response).toEqual(result);
    });

    it('should handle service.create throwing an error', async () => {
      mockSecretsPoliciesService.create.mockRejectedValue(
        new Error('Service error'),
      );

      await expect(controller.create(dto)).rejects.toThrow('Service error');
    });
  });

  describe('AuthenticationGuard', () => {
    it('should allow access when guard returns true', async () => {
      mockGuard.canActivate.mockReturnValue(true);
      expect(mockGuard.canActivate()).toBe(true);
    });

    it('should block access when guard returns false', async () => {
      mockGuard.canActivate.mockReturnValue(false);
      expect(mockGuard.canActivate()).toBe(false);
    });
  });
});
