import { Test, TestingModule } from '@nestjs/testing';
import { SecretsMetadataController } from './secretsMetadata.controller';
import { SecretsMetadataService } from './secretsMetadata.service';
import { CreateSecretMetaDataDto } from './dto/secretsMetadata.dto';
import { AuthenticationGuard } from '../../auth/authentication.guard';
import { SecretType, Status } from './types/secretsMetadata.enum';

describe('SecretsMetadataController', () => {
  let controller: SecretsMetadataController;
  let service: SecretsMetadataService;

  const mockSecretsMetadataService = {
    create: jest.fn(),
  };

  const mockGuard = {
    canActivate: jest.fn(() => true),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [SecretsMetadataController],
      providers: [
        {
          provide: SecretsMetadataService,
          useValue: mockSecretsMetadataService,
        },
      ],
    })
      .overrideGuard(AuthenticationGuard)
      .useValue(mockGuard)
      .compile();

    controller = module.get<SecretsMetadataController>(SecretsMetadataController);
    service = module.get<SecretsMetadataService>(SecretsMetadataService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    const dto: CreateSecretMetaDataDto = {
      type: SecretType.ROTATABLE_SECRET,
      resourceId: '',
      status: Status.SUCCESS,
      active: false,
      vaultPath: ''
    };

    it('should call service.create and return result', async () => {
      const result = { id: 'xyz789', ...dto };
      mockSecretsMetadataService.create.mockResolvedValue(result);

      const response = await controller.create(dto);

      expect(service.create).toHaveBeenCalledWith(dto);
      expect(response).toEqual(result);
    });

    it('should handle service.create throwing an error', async () => {
      mockSecretsMetadataService.create.mockRejectedValue(
        new Error('Service error'),
      );

      await expect(controller.create(dto)).rejects.toThrow('Service error');
    });
  });

  describe('AuthenticationGuard', () => {
    it('should allow access when guard returns true', () => {
      mockGuard.canActivate.mockReturnValue(true);
      expect(mockGuard.canActivate()).toBe(true);
    });

    it('should block access when guard returns false', () => {
      mockGuard.canActivate.mockReturnValue(false);
      expect(mockGuard.canActivate()).toBe(false);
    });
  });
});

