import { Inject, Injectable } from '@nestjs/common';
import { AxiosInstance } from 'axios';
import { withResponseErrorHandler } from 'src/utils/helpers';

@Injectable()
export class MetadataService {
  constructor(
    @Inject('METADATA_SERVICE_API')
    private readonly metadataServiceAPI: AxiosInstance,
  ) {}

  async getDeviceSettingsByEnvId(envId: string): Promise<any> {
    return withResponseErrorHandler(
      this.metadataServiceAPI.get(`reference-data/types/environments/${envId}`),
    );
  }
}
