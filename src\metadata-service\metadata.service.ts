import { Inject, Injectable } from '@nestjs/common';
import { AxiosInstance } from 'axios';
import { withResponseErrorHandler } from 'src/utils/helpers';
import { DevicesRequestResponseDto } from '../naas/dto/common/device.response.dto';

@Injectable()
export class MetadataService {
  constructor(
    @Inject('METADATA_SERVICE_API')
    private readonly metadataServiceAPI: AxiosInstance,
  ) {}

  async getDeviceSettingsByEnvId(envId: string): Promise<any> {
    return withResponseErrorHandler(
      this.metadataServiceAPI.get(`reference-data/types/environments/${envId}`),
    );
  }

  async getDeviceDetailsById(deviceId: number): Promise<DevicesRequestResponseDto[]>{
    return await withResponseErrorHandler(
      this.metadataServiceAPI.get(`/devices/deviceId/${deviceId}`)
    )
  }
}
